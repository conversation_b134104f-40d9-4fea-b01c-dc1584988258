@if($notifications->count() > 0)
<div class="time-label">
    <a href="#" class="btn btn-danger btn-sm p-1" id="mark-all">Mark all as Read</a>
</div>
@endif
@forelse($notifications as $notification)
<div class="main-cls">
    <i class="far fa-bell bg-blue"></i>
    <div class="timeline-item">
        <span class="time">
            <i class="fas fa-clock"></i> {{ date_formatter($notification->created_at) }}
        </span>
        <h3 class="timeline-header"><a data-toggle="tooltip" title="Mark as Read." data-id="{{ $notification->id }}" class="mark-as-read" href="{{ $notification->data['url'] }}">{{$notification->data['message']}}</a>
        </h3>
    </div>
</div>
@empty
<div>
    <i class="far fa-bell bg-blue"></i>
    <div class="timeline-item">
        <span class="time">
        </span>
        <h3 class="timeline-header"><a>There are no new notifications</a>
        </h3>
    </div>
</div>
@endforelse

{{ $notifications->render() }}