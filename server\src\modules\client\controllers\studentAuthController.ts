import { Request, Response } from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import {
  createStudent,
  findStudentByEmail,
  getAllStudent,
  getAllStudentCount,
  getAllStudentsCounts,
  updateStudent,
  updateStudentPassword,
  updateStudentResetToken,
} from '../services/studentAuthServices';
import { sendError, sendSuccess } from '@/utils/response';
import crypto from 'crypto';
import dotenv from 'dotenv';
import axios from 'axios';
import prisma from '@/config/prismaClient';
import { Status } from '@prisma/client';
import { transporter } from "@/utils/email";
import {
  createVerificationEmailTemplate,
  createPasswordResetTemplate,
  createCongratulationsTemplate,
  createAdminNotificationTemplate
} from '@/utils/emailTemplates';
import { findUserByEmail } from '../services/authService';
import { getReferralLinkByCode, createReferral } from '../services/referralService';

dotenv.config();

const JWT_SECRET = process.env.JWT_SECRET || 'secret123';
const COOKIE_NAME = "student_jwt";

// Register a new student
export const registerStudent = async (req: Request, res: Response): Promise<any> => {
  const { firstName, lastName, email, contact, password, captcha, referralCode } = req.body;
  const clientType = req.headers['client-type'];

  try {
    const existingStudent = await findStudentByEmail(email);
    if (existingStudent) {
      return sendError(res, 'Student already exists', 400);
    }

    const existingUser = await findUserByEmail(email);
    if (existingUser) {
      return sendError(res, 'User already exists as a tutor', 400);
    }

    if (clientType !== "mobile-app") {
    if (!captcha) {
      return sendError(
        res,
        "CAPTCHA is required for browser registration",
        400
      );
    }

    const reCAPTCHASecretKey = process.env.CAPTCHA_SECRET_KEY;
    const reCAPTCHAResponse = captcha;

    const reCAPTCHAValidationResponse = await axios.post(
      'https://www.google.com/recaptcha/api/siteverify',
      null,
      {
        params: {
          secret: reCAPTCHASecretKey,
          response: reCAPTCHAResponse,
        },
      }
    );

    if (!reCAPTCHAValidationResponse.data.success) {
      return sendError(res, "reCAPTCHA validation failed", 400);
    }
  }

    const hashedPassword = await bcrypt.hash(password, 10);
    const student = await createStudent(firstName, lastName, email, contact, hashedPassword);

    // Handle referral tracking
    if (referralCode) {
      try {
        const referralLink = await getReferralLinkByCode(referralCode);
        if (referralLink && referralLink.isActive) {
          await createReferral(
            referralLink.id,
            student.id,
            'STUDENT',
            `${firstName} ${lastName}`,
            email
          );
        }
      } catch (error) {
        console.log('Referral tracking error:', error);
        // Don't fail registration if referral tracking fails
      }
    }

    const token = jwt.sign({ id: student.id, email: student.email }, JWT_SECRET, {
      expiresIn: '1h',
    });

    const verifyUrl = `${process.env.FRONTEND_URL}/verify-email?token=${token}&userType=student`;

    // Use the new email template for verification
    const verificationEmailHtml = createVerificationEmailTemplate(verifyUrl, false);

    // Send verification email
    await transporter.sendMail({
      from: process.env.EMAIL_USER,
      to: email,
      subject: 'Verify Your Email - Uest.in',
      html: verificationEmailHtml,
    });

    // Send congratulations email to the student
    const congratulationsEmailHtml = createCongratulationsTemplate(`${firstName} ${lastName}`);
    await transporter.sendMail({
      from: process.env.EMAIL_USER,
      to: email,
      subject: 'Welcome to Uest.in!',
      html: congratulationsEmailHtml,
    });

    // Send notification email to admin
    const adminNotificationHtml = createAdminNotificationTemplate({
      firstName,
      lastName,
      email,
      contact
    });

    // Get admin email from environment variable or use a default
    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';

    await transporter.sendMail({
      from: process.env.EMAIL_USER,
      to: adminEmail,
      subject: 'New Student Registration',
      html: adminNotificationHtml,
    });

    return sendSuccess(res, { user: student, token }, 'Verification email sent. please verify your email.');
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || "server error",
    });
  }
}

export const verifyEmail = async (req: Request, res: Response): Promise<any> => {
  const { token } = req.body;

  try {
    const decoded = jwt.verify(token, JWT_SECRET) as { id: string; email: string };
    const student = await findStudentByEmail(decoded.email);

    if (!student) {
      return sendError(res, 'Student not found', 404);
    }

    if (student.isVerified) {
      return sendError(res, 'Email already verified', 400);
    }

    await prisma.student.update({
      where: { id: student.id },
      data: { isVerified: true },
    });

    return sendSuccess(res, {}, 'Email verified successfully');
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || "Invalid Token",
    });
  }
}

// Login a student
export const loginStudent = async (req: Request, res: Response): Promise<any> => {
  const { email, password, captcha } = req.body;
  const clientType = req.headers['client-type'];

  try {
    const student = await findStudentByEmail(email);
    if (!student) {
      return sendError(res, 'Student not found', 404);
    }

    const isverified = student.isVerified;
    if (!isverified) {
      return sendError(res, 'Please verify your email', 401);
    }

    const isValid = await bcrypt.compare(password, student.password);
    if (!isValid) {
      return sendError(res, 'Invalid credentials', 401);
    }

    if (clientType !== "mobile-app") {
    if (!captcha) {
      return sendError(
        res,
        "CAPTCHA is required for browser registration",
        400
      );
    }

    const reCAPTCHASecretKey = process.env.CAPTCHA_SECRET_KEY;
    const reCAPTCHAResponse = captcha;

    const reCAPTCHAValidationResponse = await axios.post(
      'https://www.google.com/recaptcha/api/siteverify',
      null,
      {
        params: {
          secret: reCAPTCHASecretKey,
          response: reCAPTCHAResponse,
        },
      }
    );

    if (!reCAPTCHAValidationResponse.data.success) {
      return sendError(res, "reCAPTCHA validation failed", 403);
    }
  }

    const token = jwt.sign({ id: student.id, email: student.email }, JWT_SECRET, {
      expiresIn: '1h',
    });

    if (clientType === 'mobile-app') {
      return sendSuccess(res, { user: student, token }, 'Login successful');
    } else {
      res.cookie(COOKIE_NAME, token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
        maxAge: 24 * 60 * 60 * 1000, // 1 day
      });
      return sendSuccess(res, { user: student, token }, 'Login successful');
    }
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || "Server Error",
    });
  }
};

export const resendVerificationEmail = async (req: Request, res: Response): Promise<any> => {
  const { email } = req.body;

  const user = await findStudentByEmail(email);
  if (!user) return sendError(res, "User not found", 404);
  if (user.isVerified) return sendError(res, "Email already verified", 400);

  const token = jwt.sign({ id: user.id, email: user.email }, JWT_SECRET, {
    expiresIn: "1d",
  });

  const verificationURL = `${process.env.FRONTEND_URL}/verify-email?token=${token}&email=${email}&userType=student`;

  // Use the new email template for verification resend
  const emailHtml = createVerificationEmailTemplate(verificationURL, true);

  await transporter.sendMail({
    from: process.env.EMAIL_USER,
    to: email,
    subject: "Email Verification : Uest.in",
    html: emailHtml,
  });

  return sendSuccess(res, {}, 'Verification email sent');
};

// Forgot password
export const forgotPasswordController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { email } = req.body;
    if (!email) {
      return sendError(res, 'Email is required', 400);
    }

    const student = await findStudentByEmail(email);
    if (!student) {
      return sendError(res, 'Student not found', 404);
    }

    const token = crypto.randomBytes(32).toString('hex');
    const expiry = new Date(Date.now() + 3600000); // 1 hour expiry

    await updateStudentResetToken(email, token, expiry);

    const resetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${token}&email=${email}`;

    // Use the new email template for password reset
    const emailHtml = createPasswordResetTemplate(resetUrl);

    const mailOptions = {
      from: process.env.EMAIL_USER,
      to: email,
      subject: 'Password Reset Request : Uest.in',
      html: emailHtml,
    };

    await transporter.sendMail(mailOptions);
    return sendSuccess(res, {}, 'Password reset email sent');
  } catch (error: any) {
    console.error('Forgot password error:', error);
    return sendError(res, 'Server error', 500);
  }
};

// Reset password
export const resetPasswordController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { email, token, newPassword } = req.body;
    if (!email || !token || !newPassword) {
      return sendError(res, 'Missing required fields', 400);
    }

    const student = await findStudentByEmail(email);
    if (!student || student.resetToken !== token || !student.resetTokenExpiry) {
      return sendError(res, 'Invalid or expired token', 400);
    }

    if (student.resetTokenExpiry < new Date()) {
      return sendError(res, 'Token expired', 400);
    }

    const hashedPassword = await bcrypt.hash(newPassword, 10);

    await updateStudentPassword(email, hashedPassword);
    await updateStudentResetToken(email, null, null);

    return sendSuccess(res, {}, 'Password reset successful');
  } catch (error: any) {
    console.error('Reset password error:', error);
    return sendError(res, 'Server error', 500);
  }
};

export const getAllStudentController = async (req: Request, res: Response): Promise<any> => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const includeProfile = req.query.includeProfile === 'true';
    const skip = (page - 1) * limit;

    const name = req.query.name as string | undefined;
    const email = req.query.email as string | undefined;
    const contact = req.query.contact as string | undefined;
    const statusParam = req.query.status as string | undefined;

    // Validate status parameter
    const validStatuses: Status[] = ['PENDING', 'APPROVED', 'REJECTED'];
    const status = statusParam && validStatuses.includes(statusParam as Status) ? statusParam as Status : undefined;

    const [students, total] = await Promise.all([
      getAllStudent(skip, limit, includeProfile, { name, email, contact, status }),
      prisma.student.count({
        where: {
          ...(name && {
            OR: [
              { firstName: { contains: name, mode: 'insensitive' } },
              { lastName: { contains: name, mode: 'insensitive' } },
            ],
          }),
          ...(email && { email: { contains: email, mode: 'insensitive' } }),
          ...(contact && { contact: { contains: contact, mode: 'insensitive' } }),
          ...(status && {
            profile: {
              status: status,
            },
          }),
        },
      }),
    ]);

    res.status(200).json({
      students,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    });
  } catch (error) {
    console.log(error);
    res.status(500).json({ message: 'Failed to get students' });
  }
};

// Get current student data
export const getCurrentStudentController = async (req: Request, res: Response): Promise<any> => {
  try {
    const studentId = req.student?.id;
    if (!studentId) {
      return sendError(res, 'Unauthorized', 401);
    }

    const student = await prisma.student.findUnique({
      where: { id: studentId },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        contact: true,
        isVerified: true,
        createdAt: true,
        updatedAt: true,
      }
    });

    if (!student) {
      return sendError(res, 'Student not found', 404);
    }

    // Get student coins
    const coins = await prisma.uestCoins.findFirst({
      where: {
        modelType: 'STUDENT',
        modelId: studentId,
      },
    });

    const studentWithCoins = {
      ...student,
      coins: coins?.coins ?? 0,
    };

    return sendSuccess(res, studentWithCoins, 'Student data retrieved successfully');
  } catch (error) {
    console.error('Error getting current student:', error);
    return sendError(res, 'Failed to get student data', 500);
  }
}

// Update student data
export const updateStudentController = async (req: Request, res: Response): Promise<any> => {
  try {
    const studentId = req.student?.id;
    if (!studentId) {
      return sendError(res, 'Unauthorized', 401);
    }

    const { firstName, lastName, contact } = req.body;

    if (!firstName && !lastName && !contact) {
      return sendError(res, 'At least one field is required for update', 400);
    }

    const updateData: {
      firstName?: string;
      lastName?: string;
      contact?: string;
    } = {};

    if (firstName) updateData.firstName = firstName;
    if (lastName) updateData.lastName = lastName;
    if (contact) updateData.contact = contact;

    const updatedStudent = await updateStudent(studentId, updateData);

    return sendSuccess(res, updatedStudent, 'Student data updated successfully');
  } catch (error) {
    console.error('Error updating student:', error);
    return sendError(res, 'Failed to update student data', 500);
  }
}

export const getCountStudent = async (_req: Request, res: Response) => {
  try {
    const total = await getAllStudentCount();
    res.status(200).json(total);
  } catch (error) {
    console.log(error);
    res.status(500).json({ message: 'Failed to get students count' });
  }
}

export const getAllStudentCountsController = async (_req: Request, res: Response) => {
  try {
    const counts = await getAllStudentsCounts();
    res.status(200).json(counts);
  } catch (error) {
    console.log(error);
    res.status(500).json({ message: 'Failed to get student counts' });
  }
};