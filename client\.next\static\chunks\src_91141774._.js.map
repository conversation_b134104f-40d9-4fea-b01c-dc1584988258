{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from 'clsx';\r\nimport { twMerge } from 'tailwind-merge';\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\nexport const truncateThought = (text: string, wordLimit: number = 5): string => {\r\n  const words = text.trim().split(/\\s+/);\r\n  if (words.length <= wordLimit) return text;\r\n  return words.slice(0, wordLimit).join(' ') + '...';\r\n};\r\n\r\nexport const setStudentAuthToken = (token: string) => {\r\n  localStorage.setItem('studentToken', token);\r\n};\r\n\r\nexport const getStudentAuthToken = (): string | null => {\r\n  return localStorage.getItem('studentToken');\r\n};\r\n\r\nexport const clearStudentAuthToken = () => {\r\n  localStorage.removeItem('studentToken');\r\n};\r\n\r\nexport const isStudentAuthenticated = (): boolean => {\r\n  return !!getStudentAuthToken();\r\n};"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,MAAM,kBAAkB,CAAC,MAAc,YAAoB,CAAC;IACjE,MAAM,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC;IAChC,IAAI,MAAM,MAAM,IAAI,WAAW,OAAO;IACtC,OAAO,MAAM,KAAK,CAAC,GAAG,WAAW,IAAI,CAAC,OAAO;AAC/C;AAEO,MAAM,sBAAsB,CAAC;IAClC,aAAa,OAAO,CAAC,gBAAgB;AACvC;AAEO,MAAM,sBAAsB;IACjC,OAAO,aAAa,OAAO,CAAC;AAC9B;AAEO,MAAM,wBAAwB;IACnC,aAAa,UAAU,CAAC;AAC1B;AAEO,MAAM,yBAAyB;IACpC,OAAO,CAAC,CAAC;AACX", "debugId": null}}, {"offset": {"line": 48, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: 'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',\r\n        destructive:\r\n          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\r\n        outline:\r\n          'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50',\r\n        secondary: 'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',\r\n        ghost: 'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',\r\n        link: 'text-primary underline-offset-4 hover:underline',\r\n      },\r\n      size: {\r\n        default: 'h-9 px-4 py-2 has-[>svg]:px-3',\r\n        sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',\r\n        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',\r\n        icon: 'size-9',\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default',\r\n      size: 'default',\r\n    },\r\n  }\r\n);\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<'button'> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean;\r\n  }) {\r\n  const Comp = asChild ? Slot : 'button';\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/store/hooks.ts"], "sourcesContent": ["import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';\r\nimport type { RootState, AppDispatch } from './index';\r\n\r\n// Use throughout your app instead of plain `useDispatch` and `useSelector`\r\nexport const useAppDispatch = () => useDispatch<AppDispatch>();\r\nexport const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAIO,MAAM,iBAAiB;;IAAM,OAAA,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;AAAe;GAAhD;;QAAuB,4JAAA,CAAA,cAAW;;;AACxC,MAAM,iBAAkD,4JAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/ProfileCompletionIndicator.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { Button } from '@/components/ui/button';\r\nimport { User, ArrowRight } from 'lucide-react';\r\nimport { useSelector } from 'react-redux';\r\nimport { RootState } from '@/store';\r\n\r\nconst ProfileCompletionIndicator = () => {\r\n  const router = useRouter();\r\n  const { profileData } = useSelector((state: RootState) => state.studentProfile);\r\n\r\n  // Check if student is logged in\r\n  const isLoggedIn = typeof window !== 'undefined' && localStorage.getItem('studentToken') !== null;\r\n\r\n  // Check if student has any profile data - only check if profile ID exists\r\n  const hasProfile = profileData?.profile?.id !== undefined;\r\n\r\n  // Only show the indicator if student is logged in and doesn't have a profile\r\n  if (!isLoggedIn || hasProfile) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div className=\"my-4 mx-10 sm:px-4\">\r\n      <div className=\"bg-white dark:bg-gray-900 border border-orange-200 overflow-hidden dark:border-orange-900\">\r\n        <div className=\"px-3 py-1.5 flex items-center justify-between\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          <div className=\"bg-[#ff914d] p-1.5 rounded-full\">\r\n            <User className=\"h-3 w-3 text-white\" />\r\n          </div>\r\n          <p className=\"text-xs font-medium text-gray-800 dark:text-gray-200\">\r\n            Please Complete your profile \r\n          </p>\r\n        </div>\r\n        <Button\r\n          onClick={() => router.push('/student/profile')}\r\n          className=\"bg-[#ff914d] hover:bg-[#e07c3a] text-white text-xs px-2 py-0.5 h-6 min-w-0\"\r\n          size=\"sm\"\r\n        >\r\n         Complete now <ArrowRight className=\"h-3 w-3\" />\r\n        </Button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProfileCompletionIndicator;\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AACA;;;AANA;;;;;AASA,MAAM,6BAA6B;;IACjC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;kDAAE,CAAC,QAAqB,MAAM,cAAc;;IAE9E,gCAAgC;IAChC,MAAM,aAAa,aAAkB,eAAe,aAAa,OAAO,CAAC,oBAAoB;IAE7F,0EAA0E;IAC1E,MAAM,aAAa,aAAa,SAAS,OAAO;IAEhD,6EAA6E;IAC7E,IAAI,CAAC,cAAc,YAAY;QAC7B,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAElB,6LAAC;gCAAE,WAAU;0CAAuD;;;;;;;;;;;;kCAItE,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS,IAAM,OAAO,IAAI,CAAC;wBAC3B,WAAU;wBACV,MAAK;;4BACN;0CACa,6LAAC,qNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5C;GAtCM;;QACW,qIAAA,CAAA,YAAS;QACA,4JAAA,CAAA,cAAW;;;KAF/B;uCAwCS", "debugId": null}}, {"offset": {"line": 260, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/avatar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as AvatarPrimitive from '@radix-ui/react-avatar';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Avatar({ className, ...props }: React.ComponentProps<typeof AvatarPrimitive.Root>) {\r\n  return (\r\n    <AvatarPrimitive.Root\r\n      data-slot=\"avatar\"\r\n      className={cn('relative flex size-8 shrink-0 overflow-hidden rounded-full', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AvatarImage({ className, ...props }: React.ComponentProps<typeof AvatarPrimitive.Image>) {\r\n  return (\r\n    <AvatarPrimitive.Image\r\n      data-slot=\"avatar-image\"\r\n      className={cn('aspect-square size-full', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AvatarFallback({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\r\n  return (\r\n    <AvatarPrimitive.Fallback\r\n      data-slot=\"avatar-fallback\"\r\n      className={cn('bg-muted flex size-full items-center justify-center rounded-full', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback };\r\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EAAE,SAAS,EAAE,GAAG,OAA0D;IACxF,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8DAA8D;QAC3E,GAAG,KAAK;;;;;;AAGf;KARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAA2D;IAC9F,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,6LAAC,qKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oEAAoE;QACjF,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 322, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/popover.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as PopoverPrimitive from '@radix-ui/react-popover';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Popover({ ...props }: React.ComponentProps<typeof PopoverPrimitive.Root>) {\r\n  return <PopoverPrimitive.Root data-slot=\"popover\" {...props} />;\r\n}\r\n\r\nfunction PopoverTrigger({ ...props }: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\r\n  return <PopoverPrimitive.Trigger data-slot=\"popover-trigger\" {...props} />;\r\n}\r\n\r\nfunction PopoverContent({\r\n  className,\r\n  align = 'center',\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\r\n  return (\r\n    <PopoverPrimitive.Portal>\r\n      <PopoverPrimitive.Content\r\n        data-slot=\"popover-content\"\r\n        align={align}\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden',\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </PopoverPrimitive.Portal>\r\n  );\r\n}\r\n\r\nfunction PopoverAnchor({ ...props }: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\r\n  return <PopoverPrimitive.Anchor data-slot=\"popover-anchor\" {...props} />;\r\n}\r\n\r\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor };\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,QAAQ,EAAE,GAAG,OAA2D;IAC/E,qBAAO,6LAAC,sKAAA,CAAA,OAAqB;QAAC,aAAU;QAAW,GAAG,KAAK;;;;;;AAC7D;KAFS;AAIT,SAAS,eAAe,EAAE,GAAG,OAA8D;IACzF,qBAAO,6LAAC,sKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;MAFS;AAIT,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,GAAG,OACmD;IACtD,qBACE,6LAAC,sKAAA,CAAA,SAAuB;kBACtB,cAAA,6LAAC,sKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,keACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MApBS;AAsBT,SAAS,cAAc,EAAE,GAAG,OAA6D;IACvF,qBAAO,6LAAC,sKAAA,CAAA,SAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAFS", "debugId": null}}, {"offset": {"line": 403, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/studentAuthServices.ts"], "sourcesContent": ["import {\r\n    StudentRegisterData,\r\n    StudentLoginData,\r\n    AuthResponse,\r\n    GoogleAuthData,\r\n} from '@/lib/types';\r\nimport { axiosInstance } from '@/lib/axios';\r\n\r\nexport const registerStudent = async (StudentRegisterData: StudentRegisterData) => {\r\n    const response = await axiosInstance.post(`/student/register`, StudentRegisterData);\r\n    return response.data\r\n}\r\n\r\nexport const loginStudent = async (StudentLoginData: StudentLoginData) => {\r\n    const response = await axiosInstance.post(`/student/login`, StudentLoginData);\r\n    return response.data;\r\n}\r\n\r\nexport const googleAuthStudent = async (googleAuthData: GoogleAuthData) => {\r\n    const response = await axiosInstance.post(`/student/google-auth`, googleAuthData);\r\n    return response.data;\r\n};\r\n\r\nexport const logoutStudent = async (): Promise<AuthResponse> => {\r\n    // Clear student token from localStorage\r\n    localStorage.removeItem('studentToken');\r\n    localStorage.removeItem('student_data');\r\n\r\n    return {\r\n        success: true,\r\n        message: 'Logged out successfully',\r\n    };\r\n};\r\n\r\nexport const forgotPassword = async (email: string) => {\r\n    const response = await axiosInstance.post(`/student/forgot-password`, { email });\r\n    return response.data;\r\n}\r\n\r\nexport const studentverifyEmail = async (token: string) => {\r\n    const response = await axiosInstance.post(`/student/verify-email`, { token });\r\n    return response.data;\r\n};\r\n\r\nexport const studentresendVerificationEmail = async (email: string) => {\r\n    const response = await axiosInstance.post(`/student/resend-verification`, { email });\r\n    return response.data;\r\n};\r\n\r\nexport const resetStudentPassword = async (email: string, token: string, newPassword: string) => {\r\n    const response = await axiosInstance.post(`/student/reset-password`, {\r\n        email,\r\n        token,\r\n        newPassword,\r\n    });\r\n    return response.data;\r\n};"], "names": [], "mappings": ";;;;;;;;;;AAMA;;AAEO,MAAM,kBAAkB,OAAO;IAClC,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,iBAAiB,CAAC,EAAE;IAC/D,OAAO,SAAS,IAAI;AACxB;AAEO,MAAM,eAAe,OAAO;IAC/B,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,EAAE;IAC5D,OAAO,SAAS,IAAI;AACxB;AAEO,MAAM,oBAAoB,OAAO;IACpC,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,EAAE;IAClE,OAAO,SAAS,IAAI;AACxB;AAEO,MAAM,gBAAgB;IACzB,wCAAwC;IACxC,aAAa,UAAU,CAAC;IACxB,aAAa,UAAU,CAAC;IAExB,OAAO;QACH,SAAS;QACT,SAAS;IACb;AACJ;AAEO,MAAM,iBAAiB,OAAO;IACjC,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,wBAAwB,CAAC,EAAE;QAAE;IAAM;IAC9E,OAAO,SAAS,IAAI;AACxB;AAEO,MAAM,qBAAqB,OAAO;IACrC,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,qBAAqB,CAAC,EAAE;QAAE;IAAM;IAC3E,OAAO,SAAS,IAAI;AACxB;AAEO,MAAM,iCAAiC,OAAO;IACjD,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,4BAA4B,CAAC,EAAE;QAAE;IAAM;IAClF,OAAO,SAAS,IAAI;AACxB;AAEO,MAAM,uBAAuB,OAAO,OAAe,OAAe;IACrE,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,uBAAuB,CAAC,EAAE;QACjE;QACA;QACA;IACJ;IACA,OAAO,SAAS,IAAI;AACxB", "debugId": null}}, {"offset": {"line": 471, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/AuthService.ts"], "sourcesContent": ["import { axiosInstance } from '../lib/axios';\r\n\r\ninterface LoginData {\r\n  email: string;\r\n  password: string;\r\n}\r\n\r\nexport async function registerUser(data: unknown) {\r\n  const response = await axiosInstance.post('/auth-client/register', data);\r\n  return response.data;\r\n}\r\n\r\nexport async function loginUser(data: LoginData) {\r\n  const response = await axiosInstance.post('/auth-client/login', data);\r\n  return response.data;\r\n}\r\n\r\nexport function logoutUser(): void {\r\n  localStorage.removeItem('token');\r\n}\r\n\r\nexport const forgotPassword = async (email: string) => {\r\n  const response = await axiosInstance.post(`/auth-client/forgot-password`, { email });\r\n  return response.data;\r\n};\r\n\r\nexport const generateJWT = async (email: string | undefined, password : string | undefined) => {\r\n  const response = await axiosInstance.post(`/auth-client/generate-jwt`, { email, password });\r\n  return response.data;\r\n};\r\n\r\nexport const resetPassword = async (email: string, token: string, newPassword: string) => {\r\n  const response = await axiosInstance.post(`/auth-client/reset-password`, {\r\n    email,\r\n    token,\r\n    newPassword,\r\n  });\r\n  return response.data;\r\n};\r\n\r\nexport const verifyEmail = async (token: string) => {\r\n  const response = await axiosInstance.get(`/auth-client/verify-email`, { params: { token } });\r\n  return response.data;\r\n};\r\n\r\nexport const resendVerificationEmail = async (email: string) => {\r\n  const response = await axiosInstance.post(`/auth-client/resend-verification`, { email });\r\n  return response.data;\r\n};"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AAOO,eAAe,aAAa,IAAa;IAC9C,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,yBAAyB;IACnE,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,UAAU,IAAe;IAC7C,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,sBAAsB;IAChE,OAAO,SAAS,IAAI;AACtB;AAEO,SAAS;IACd,aAAa,UAAU,CAAC;AAC1B;AAEO,MAAM,iBAAiB,OAAO;IACnC,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,4BAA4B,CAAC,EAAE;QAAE;IAAM;IAClF,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,cAAc,OAAO,OAA2B;IAC3D,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,yBAAyB,CAAC,EAAE;QAAE;QAAO;IAAS;IACzF,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,gBAAgB,OAAO,OAAe,OAAe;IAChE,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,2BAA2B,CAAC,EAAE;QACvE;QACA;QACA;IACF;IACA,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,cAAc,OAAO;IAChC,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,yBAAyB,CAAC,EAAE;QAAE,QAAQ;YAAE;QAAM;IAAE;IAC1F,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,0BAA0B,OAAO;IAC5C,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,gCAAgC,CAAC,EAAE;QAAE;IAAM;IACtF,OAAO,SAAS,IAAI;AACtB", "debugId": null}}, {"offset": {"line": 538, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/Header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from \"next/link\";\r\nimport Image from \"next/image\";\r\nimport { Menu, X, User, ShoppingBag, Briefcase, Share2, UserCircle, ChevronRight, MessageSquare } from \"lucide-react\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { RootState } from \"@/store\";\r\nimport { useAppDispatch } from \"@/store/hooks\";\r\nimport { useEffect, useState, useRef } from \"react\";\r\nimport { GraduationCap, Flame } from \"lucide-react\";\r\nimport { isStudentAuthenticated, clearStudentAuthToken } from \"@/lib/utils\";\r\nimport ProfileCompletionIndicator from \"./ProfileCompletionIndicator\";\r\nimport {\r\n  Avatar,\r\n  AvatarFallback,\r\n} from \"@/components/ui/avatar\";\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from \"@/components/ui/popover\";\r\nimport { toast } from \"sonner\";\r\nimport { logoutStudent } from \"@/services/studentAuthServices\";\r\nimport { clearUser } from \"@/store/slices/userSlice\";\r\nimport { clearStudentProfileData } from \"@/store/slices/studentProfileSlice\";\r\nimport { fetchStudentProfile } from \"@/store/thunks/studentProfileThunks\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { axiosInstance } from \"@/lib/axios\";\r\nimport { motion, useMotionValue, useAnimationFrame } from \"framer-motion\";\r\nimport { generateJWT } from \"@/services/AuthService\";\r\n\r\nconst Header = () => {\r\n  const { isAuthenticated, user } = useSelector((state: RootState) => state.user);\r\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\r\n  const [isStudentLoggedIn, setIsStudentLoggedIn] = useState(false);\r\n  const [studentData, setStudentData] = useState<any>(null);\r\n  const dispatch = useAppDispatch();\r\n  const router = useRouter();\r\n\r\n  const contentRef = useRef<HTMLDivElement>(null);\r\n  const [contentWidth, setContentWidth] = useState(0);\r\n  const [isHovering, setIsHovering] = useState(false);\r\n  const x = useMotionValue(0); // Motion value to control the x position\r\n\r\n  const speed = (contentWidth / 20);\r\n\r\n  useEffect(() => {\r\n    const isLoggedIn = isStudentAuthenticated();\r\n    setIsStudentLoggedIn(isLoggedIn);\r\n\r\n    if (isLoggedIn) {\r\n      const storedData = localStorage.getItem('student_data');\r\n      if (storedData) {\r\n        setStudentData(JSON.parse(storedData));\r\n      }\r\n\r\n      dispatch(fetchStudentProfile());\r\n    }\r\n\r\n    const handleStorageChange = () => {\r\n      const newLoginStatus = isStudentAuthenticated();\r\n      setIsStudentLoggedIn(newLoginStatus);\r\n\r\n      if (newLoginStatus) {\r\n        const storedData = localStorage.getItem('student_data');\r\n        if (storedData) {\r\n          setStudentData(JSON.parse(storedData));\r\n        }\r\n\r\n        dispatch(fetchStudentProfile());\r\n      } else {\r\n        setStudentData(null);\r\n      }\r\n    };\r\n\r\n    window.addEventListener('storage', handleStorageChange);\r\n\r\n    if (contentRef.current) {\r\n      const width = contentRef.current.getBoundingClientRect().width;\r\n      setContentWidth(width);\r\n    }\r\n\r\n    return () => {\r\n      window.removeEventListener('storage', handleStorageChange);\r\n    };\r\n  }, [dispatch]);\r\n\r\n  // Custom animation loop using useAnimationFrame\r\n  useAnimationFrame((time, delta) => {\r\n    if (isHovering || contentWidth === 0) return;\r\n    const currentX = x.get();\r\n    const deltaX = (speed * delta) / 1000;\r\n    let newX = currentX - deltaX;\r\n    if (newX <= -contentWidth) {\r\n      newX = 0;\r\n    }\r\n    x.set(newX);\r\n  });\r\n\r\n  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);\r\n\r\n  const handleStudentLogout = async () => {\r\n    try {\r\n      const response = await logoutStudent();\r\n\r\n      if (response.success !== false) {\r\n        // Clear local state\r\n        clearStudentAuthToken();\r\n        setIsStudentLoggedIn(false);\r\n        setStudentData(null);\r\n        localStorage.removeItem('student_data');\r\n\r\n        // Clear Redux state\r\n        dispatch(clearStudentProfileData());\r\n\r\n        toast.success(\"Logged out successfully\");\r\n        window.dispatchEvent(new Event('storage'));\r\n      } else {\r\n        toast.error(response.message || \"Failed to logout\");\r\n        clearStudentAuthToken();\r\n        setIsStudentLoggedIn(false);\r\n        setStudentData(null);\r\n        localStorage.removeItem('student_data');\r\n        dispatch(clearStudentProfileData());\r\n      }\r\n    } catch (error) {\r\n      console.log(\"Failed to logout\", error);\r\n      toast.error(\"Failed to logout\");\r\n      localStorage.removeItem('student_data');\r\n      clearStudentAuthToken();\r\n      setIsStudentLoggedIn(false);\r\n      setStudentData(null);\r\n      dispatch(clearStudentProfileData());\r\n    }\r\n  };\r\n\r\n  const accessClassDashboard = async () => {\r\n    try {\r\n      const response = await generateJWT(user?.email, user?.password);\r\n\r\n      if (response.success) {\r\n        const { token } = response.data;\r\n        const redirectUrl = process.env.NEXT_PUBLIC_RANNDASS_URL + `/login-class-link?uid=${user?.id}&token=${token}`;\r\n        window.location.href = redirectUrl;\r\n\r\n      } else {\r\n        toast.error(response.message || \"Failed to generate token\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Failed to generate token\", error);\r\n      toast.error(\"Failed to generate token\");\r\n    }\r\n  };\r\n  \r\n  const navLinks = [\r\n    { href: \"/verified-classes\", label: \"Find Tutor\", icon: <GraduationCap className=\"w-4 h-4\" /> },\r\n    { href: \"/uwhiz\", label: \"U - Whiz\", icon: <Flame className=\"w-4 h-4\" />, isNew: true },\r\n    { href: \"/careers\", label: \"Career\", icon: <Briefcase className=\"w-4 h-4\" />},\r\n  ];\r\n\r\n  const bannerContent = (\r\n    <div className=\"inline-flex items-center space-x-4 whitespace-nowrap\">\r\n      <span className=\"text-sm md:text-xl font-semibold text-black\">\r\n        U Whiz – Super Kids Exam is live! Win ₹1,00,000 – So hurry up, Apply now and be a champion\r\n      </span>\r\n      <button\r\n        className=\"inline-flex items-center justify-center rounded-md font-bold bg-white text-black px-3 py-1 text-sm hover:bg-[#FD904B] hover:text-black transition\"\r\n        style={{ border: '2px solid black' }}\r\n        onClick={() => router.push(`/uwhiz-info/${1}`)}\r\n      >\r\n        Apply Now <ChevronRight className=\"ml-2 h-4 w-4\" />\r\n      </button>\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <>\r\n      <header className=\"sticky top-0 z-50 w-full bg-black overflow-x-hidden\">\r\n        <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"flex h-20 items-center justify-between\">\r\n            <Link\r\n              href=\"/\"\r\n              className=\"flex items-center space-x-2 transition-transform hover:scale-105\"\r\n            >\r\n              <Image\r\n                src=\"/logo_black.png\"\r\n                alt=\"Preply Logo\"\r\n                width={200}\r\n                height={50}\r\n                className=\"rounded-sm\"\r\n              />\r\n            </Link>\r\n\r\n            <nav className=\"hidden md:flex items-center space-x-4\">\r\n              {navLinks.map((link) => (\r\n                <Link\r\n                  key={link.href}\r\n                  href={link.href}\r\n                  className=\"relative flex items-center gap-2 border border-gray-700 rounded-md px-4 py-2 text-sm font-medium text-gray-300 transition-all hover:border-orange-500 hover:text-orange-400\"\r\n                >\r\n                  {link.icon}\r\n                  {link.label}\r\n                  {link.label === \"Find School\" && (\r\n                    <span className=\"ml-2 text-xs px-2 py-0.5 rounded-full bg-yellow-500 text-black\">\r\n                      Coming Soon\r\n                    </span>\r\n                  )}\r\n                  {link.isNew && (\r\n                    <span className=\"ml-2 text-xs px-2 py-0.5 rounded-full bg-orange-500 text-white animate-pulse\">\r\n                      Trending\r\n                    </span>\r\n                  )}\r\n                </Link>\r\n              ))}\r\n            </nav>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"icon\"\r\n              className=\"md:hidden text-orange-400 hover:bg-orange-500/10\"\r\n              onClick={toggleMenu}\r\n            >\r\n              {isMenuOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\r\n            </Button>\r\n\r\n            {/* Desktop Actions */}\r\n            <div className=\"hidden md:flex items-center space-x-4\">\r\n              {isAuthenticated && (\r\n                <Link href=\"/coins\" passHref>\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    size=\"icon\"\r\n                    className=\"relative rounded-full group bg-black\"\r\n                  >\r\n                    <div className=\"absolute rounded-full inset-0 opacity-0 group-hover:opacity-20 transition-opacity\" />\r\n                    <div className=\"relative z-10 \">\r\n                      <Image\r\n                        src=\"/uest_coin.png\"\r\n                        alt=\"Coin Icon\"\r\n                        width={60}\r\n                        height={60}\r\n                        className=\"object-contain\"\r\n                      />\r\n                    </div>\r\n                  </Button>\r\n                </Link>\r\n              )}\r\n\r\n              <div className=\"h-8 border-l border-orange-500/20\" />\r\n\r\n              {isAuthenticated && (\r\n                <Popover>\r\n                  <PopoverTrigger asChild>\r\n                    <Avatar className=\"cursor-pointer border-2 border-[#ff914d] hover:border-[#ff914d]/80 transition-colors\">\r\n                      <AvatarFallback className=\"bg-white text-black\">\r\n                        {user?.firstName && user?.lastName\r\n                        ? `${user.firstName[0]}${user.lastName[0]}`.toUpperCase()\r\n                          : \"CT\"}\r\n                      </AvatarFallback>\r\n                    </Avatar>\r\n                  </PopoverTrigger>\r\n                  <PopoverContent className=\"w-64 bg-white\">\r\n                    <div className=\"flex items-center gap-3 mb-3 pb-2 border-b\">\r\n                      <Avatar className=\"h-12 w-12 border-2 border-[#ff914d]\">\r\n                        <AvatarFallback className=\"bg-white text-black\">\r\n                          {user?.firstName && user?.lastName\r\n                          ? `${user.firstName[0]}${user.lastName[0]}`.toUpperCase()\r\n                            : \"CT\"}\r\n                        </AvatarFallback>\r\n                      </Avatar>\r\n                      <div>\r\n                        <p className=\"font-medium text-black\">\r\n                          {user?.firstName && user?.lastName\r\n                            ? `${user.firstName} ${user.lastName}`\r\n                            : user?.className || \"Class Account\"}\r\n                        </p>\r\n                        <p className=\"text-xs text-gray-600\">{user?.email || \"<EMAIL>\"}</p>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"space-y-2\">\r\n                      <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                        <Link href=\"/classes/profile\" className=\"flex items-center\">\r\n                          <User className=\"mr-2 h-4 w-4\" />\r\n                          <span>Profile</span>\r\n                        </Link>\r\n                      </Button>\r\n                      <Button onClick={() => accessClassDashboard()} className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                          <User className=\"mr-2 h-4 w-4\" />\r\n                          <span>My Dashboard</span>\r\n                      </Button>\r\n                      <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                        <Link href=\"/classes/referral-dashboard\" className=\"flex items-center\">\r\n                          <Share2 className=\"mr-2 h-4 w-4\" />\r\n                          <span>Referral Dashboard</span>\r\n                        </Link>\r\n                      </Button>\r\n\r\n                      <Button\r\n                        variant=\"outline\"\r\n                        className=\"w-full border-[#ff914d] text-[#ff914d] hover:bg-[#ff914d]/10\"\r\n                        onClick={async () => {\r\n                          try {\r\n                            const response = await axiosInstance.post(\"/auth-client/logout\", {});\r\n                            if (response.data.success) {\r\n                              router.push(\"/\");\r\n                              dispatch(clearUser());\r\n                              localStorage.removeItem(\"token\");\r\n                              toast.success(\"Logged out successfully\");\r\n                            }\r\n                          } catch (error) {\r\n                            console.error(\"Logout error:\", error);\r\n                            toast.error(\"Failed to logout\");\r\n                          }\r\n                        }}\r\n                      >\r\n                        Logout\r\n                      </Button>\r\n                    </div>\r\n                  </PopoverContent>\r\n                </Popover>\r\n              )}\r\n\r\n              {!isAuthenticated && !isStudentLoggedIn && (\r\n                <Button\r\n                  className=\"bg-customOrange hover:bg-[#E88143] text-white mr-4\"\r\n                  asChild\r\n                >\r\n                  <Link href=\"/class/login\">Join as a Tutor/Class</Link>\r\n                </Button>\r\n              )}\r\n\r\n              {!isAuthenticated && !isStudentLoggedIn && (\r\n                <Button\r\n                  variant=\"outline\"\r\n                  className=\"bg-black border-orange-500 hover:bg-orange-900/50 text-white hover:text-white\"\r\n                  asChild\r\n                >\r\n                  <Link href=\"/student/login\">Student Login</Link>\r\n                </Button>\r\n              )}\r\n\r\n              {isStudentLoggedIn && (\r\n                <Link href=\"/coins\" passHref>\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    size=\"icon\"\r\n                    className=\"relative rounded-full group bg-black\"\r\n                  >\r\n                    <div className=\"absolute rounded-full inset-0 opacity-0 group-hover:opacity-20 transition-opacity\" />\r\n                    <div className=\"relative z-10 \">\r\n                      <Image\r\n                        src=\"/uest_coin.png\"\r\n                        alt=\"Coin Icon\"\r\n                        width={60}\r\n                        height={60}\r\n                        className=\"object-contain\"\r\n                      />\r\n                    </div>\r\n                  </Button>\r\n                </Link>\r\n              )}\r\n\r\n              {isStudentLoggedIn && (\r\n                <Popover>\r\n                  <PopoverTrigger asChild>\r\n                    <Avatar className=\"cursor-pointer border-2 border-[#ff914d] hover:border-[#ff914d]/80 transition-colors\">\r\n                      <AvatarFallback className=\"bg-white text-black\">\r\n                        {studentData?.firstName && studentData?.lastName\r\n                        ? `${studentData.firstName[0]}${studentData.lastName[0]}`.toUpperCase()\r\n                          : \"ST\"}\r\n                      </AvatarFallback>\r\n                    </Avatar>\r\n                  </PopoverTrigger>\r\n                  <PopoverContent className=\"w-64 bg-white\">\r\n                    <div className=\"flex items-center gap-3 mb-3 pb-2 border-b\">\r\n                      <Avatar className=\"h-12 w-12 border-2 border-[#ff914d]\">\r\n                        <AvatarFallback className=\"bg-white text-black\">\r\n                          {studentData?.firstName && studentData?.lastName\r\n                          ? `${studentData.firstName[0]}${studentData.lastName[0]}`.toUpperCase()\r\n                            : \"ST\"}\r\n                        </AvatarFallback>\r\n                      </Avatar>\r\n                      <div>\r\n                        <p className=\"font-medium text-black\">\r\n                          {studentData?.firstName && studentData?.lastName\r\n                            ? `${studentData.firstName} ${studentData.lastName}`\r\n                            : \"Student Account\"}\r\n                        </p>\r\n                        <p className=\"text-xs text-gray-600\">{studentData?.email || \"<EMAIL>\"}</p>\r\n                      </div>\r\n                    </div>\r\n\r\n                  <div className=\"space-y-2\">\r\n                    <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                      <Link href=\"/student/profile\" className=\"flex items-center\">\r\n                        <UserCircle className=\"mr-2 h-4 w-4\" />\r\n                        <span>Profile</span>\r\n                      </Link>\r\n                    </Button>\r\n                    <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                      <Link href=\"/student/wishlist\" className=\"flex items-center\">\r\n                        <ShoppingBag className=\"mr-2 h-4 w-4\" />\r\n                        <span>My Wishlist</span>\r\n                      </Link>\r\n                    </Button>\r\n                    <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                      <Link href=\"/student/chat\" className=\"flex items-center\">\r\n                        <MessageSquare className=\"mr-2 h-4 w-4\" />\r\n                        <span>Chat</span>\r\n                      </Link>\r\n                    </Button>\r\n                    <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                      <Link href=\"/student/referral-dashboard\" className=\"flex items-center\">\r\n                        <Share2 className=\"mr-2 h-4 w-4\" />\r\n                        <span>Referral Dashboard</span>\r\n                      </Link>\r\n                    </Button>\r\n                    {/* Referral dashboard removed - only available for Classes */}\r\n\r\n                      <Button variant=\"outline\" className=\"w-full border-[#ff914d] text-[#ff914d] hover:bg-[#ff914d]/10\" onClick={handleStudentLogout}>\r\n                        Logout\r\n                      </Button>\r\n                    </div>\r\n                  </PopoverContent>\r\n                </Popover>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"w-screen bg-[#FD904B] border-y border-black relative mt-1\">\r\n            <div className=\"absolute top-0 right-0 h-full w-[20vw] bg-[#FD904B] block md:hidden z-0\"></div>\r\n            <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 relative z-10 overflow-hidden\">\r\n              <motion.div\r\n                className=\"inline-flex py-2 px-4\"\r\n                style={{ x }} // Bind the x position to the motion value\r\n                onMouseEnter={() => setIsHovering(true)}\r\n                onMouseLeave={() => setIsHovering(false)}\r\n              >\r\n                <div ref={contentRef} className=\"inline-flex items-center space-x-4 whitespace-nowrap pr-8\">\r\n                  {bannerContent}\r\n                </div>\r\n                <div className=\"inline-flex items-center space-x-4 whitespace-nowrap pr-8\">\r\n                  {bannerContent}\r\n                </div>\r\n              </motion.div>\r\n            </div>\r\n          </div>\r\n      </header>\r\n\r\n      <div>\r\n      {/* Mobile Sidebar Menu */}\r\n      <div\r\n        className={`fixed inset-y-0 right-0 z-50 w-72 bg-black/95 shadow-2xl transform transition-all duration-300 ease-in-out md:hidden border-l border-orange-500/20 ${isMenuOpen ? \"translate-x-0\" : \"translate-x-full\"\r\n          }`}\r\n      >\r\n        <div className=\"flex flex-col h-full p-6\">\r\n          {/* Close Button */}\r\n          <div className=\"flex justify-end\">\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"icon\"\r\n              className=\"text-orange-400 hover:bg-orange-500/10 rounded-full\"\r\n              onClick={toggleMenu}\r\n            >\r\n              <X className=\"h-6 w-6\" />\r\n            </Button>\r\n          </div>\r\n\r\n\r\n\r\n          {/* Mobile Navigation */}\r\n            <nav className=\"flex flex-col space-y-2 mt-8\">\r\n              {navLinks.map((link) => (\r\n                <Link\r\n                  key={link.href}\r\n                  href={link.href}\r\n                  className=\"px-4 py-3 border text-base font-medium text-gray-300 hover:text-orange-400 hover:bg-orange-500/10 rounded-lg transition-colors flex justify-between items-center\"\r\n                  onClick={toggleMenu}\r\n                >\r\n                  <div className=\"flex items-center gap-3\">\r\n                    {link.icon}\r\n                    <span>{link.label}</span>\r\n                  </div>\r\n                  {link.label === \"Find School\" && (\r\n                    <span className=\"ml-2 text-xs px-2 py-0.5 rounded-full bg-yellow-500 text-black animate-pulse\">\r\n                      Coming Soon\r\n                    </span>\r\n                  )}\r\n                  {link.isNew && (\r\n                    <span className=\"ml-2 text-xs px-2 py-0.5 rounded-full bg-orange-500 text-white\">\r\n                      New\r\n                    </span>\r\n                  )}\r\n                </Link>\r\n              ))}\r\n            </nav>\r\n\r\n          {/* Mobile Actions */}\r\n            <div className=\"mt-auto space-y-4\">\r\n              {isAuthenticated && (\r\n                <>\r\n                  <Link href=\"/classes/profile\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"w-full group relative border-orange-500 hover:border-orange-400 bg-black\"\r\n                      onClick={toggleMenu}\r\n                    >\r\n                      <div className=\"absolute inset-0 bg-gradient-to-r from-orange-500 to-yellow-500 opacity-0 group-hover:opacity-20 transition-opacity\" />\r\n                      <div className=\"relative z-10 flex items-center justify-center gap-3 py-2\">\r\n                        <div className=\"p-1.5 rounded-full bg-gradient-to-br from-orange-500 to-yellow-500\">\r\n                          <User className=\"h-5 w-5 text-white\" />\r\n                        </div>\r\n                        <span className=\"font-medium text-gray-300\">Dashboard</span>\r\n                      </div>\r\n                    </Button>\r\n                  </Link>\r\n\r\n                  <Link href=\"/classes/referral-dashboard\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"w-full group relative border-orange-500 hover:border-orange-400 mt-3 bg-black\"\r\n                      onClick={toggleMenu}\r\n                    >\r\n                      <div className=\"absolute inset-0 bg-gradient-to-r from-orange-500 to-yellow-500 opacity-0 group-hover:opacity-20 transition-opacity\" />\r\n                      <div className=\"relative z-10 flex items-center justify-center gap-3 py-2\">\r\n                        <div className=\"p-1.5 rounded-full bg-gradient-to-br from-orange-500 to-yellow-500\">\r\n                          <Share2 className=\"h-5 w-5 text-white\" />\r\n                        </div>\r\n                        <span className=\"font-medium text-gray-300\">Referral Dashboard</span>\r\n                      </div>\r\n                    </Button>\r\n                  </Link>\r\n\r\n                <Link href=\"/coins\" passHref>\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    className=\"w-full group relative border-orange-500 hover:border-orange-400 bg-black mt-3\"\r\n                    onClick={toggleMenu}\r\n                  >\r\n                    <div className=\"absolute inset-0 bg-gradient-to-r from-orange-500 to-yellow-500 opacity-0 group-hover:opacity-20 transition-opacity\" />\r\n                    <div className=\"relative z-10 flex items-center justify-center gap-3 py-2\">\r\n                      <div className=\"p-1.5 rounded-full bg-gradient-to-br from-orange-500 to-yellow-500\">\r\n                        <Image\r\n                          src=\"/uest_coin.png\"\r\n                          alt=\"Coin Icon\"\r\n                          width={20}\r\n                          height={20}\r\n                          className=\"object-contain\"\r\n                        />\r\n                      </div>\r\n                      <span className=\"font-medium text-gray-300\">My Coins</span>\r\n                    </div>\r\n                  </Button>\r\n                </Link>\r\n\r\n                <Button\r\n                  variant=\"outline\"\r\n                  className=\"w-full border-orange-500 text-orange-500 hover:bg-orange-500/10 mt-3\"\r\n                  onClick={async () => {\r\n                    try {\r\n                      const response = await axiosInstance.post(\"/auth-client/logout\", {});\r\n                      if (response.data.success) {\r\n                        router.push(\"/\");\r\n                        dispatch(clearUser());\r\n                        localStorage.removeItem(\"token\");\r\n                        toast.success(\"Logged out successfully\");\r\n                      }\r\n                    } catch (error) {\r\n                      console.error(\"Logout error:\", error);\r\n                      toast.error(\"Failed to logout\");\r\n                    }\r\n                    toggleMenu();\r\n                  }}\r\n                >\r\n                  <div className=\"flex items-center justify-center gap-3\">\r\n                    <User className=\"h-5 w-5\" />\r\n                    <span>Logout</span>\r\n                  </div>\r\n                </Button>\r\n              </>\r\n            )}\r\n\r\n              {isStudentLoggedIn && (\r\n                <>\r\n                  {studentData?.firstName && studentData?.lastName && (\r\n                    <div className=\"p-3 border border-[#ff914d]/20 rounded-lg bg-white\">\r\n                      <div className=\"flex items-center gap-3\">\r\n                        <Avatar className=\"h-12 w-12 border-2 border-[#ff914d]\">\r\n                          <AvatarFallback className=\"bg-white text-black\">\r\n                            {(`${studentData.firstName[0]}${studentData.lastName[0]}`).toUpperCase()}\r\n                          </AvatarFallback>\r\n                        </Avatar>\r\n                        <div>\r\n                          <p className=\"font-medium text-black\">{`${studentData.firstName} ${studentData.lastName}`}</p>\r\n                          <p className=\"text-xs text-gray-600\">{studentData.email}</p>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                <Button\r\n                  asChild\r\n                  className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\"\r\n                  onClick={toggleMenu}\r\n                >\r\n                  <Link href=\"/student/profile\" className=\"flex items-center justify-center gap-3\">\r\n                    <UserCircle className=\"h-5 w-5\" />\r\n                    <span>Profile</span>\r\n                  </Link>\r\n                </Button>\r\n\r\n                <Button\r\n                  asChild\r\n                  className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\"\r\n                  onClick={toggleMenu}\r\n                >\r\n                  <Link href=\"/student/chat\" className=\"flex items-center justify-center gap-3\">\r\n                    <MessageSquare className=\"h-5 w-5\" />\r\n                    <span>Chat</span>\r\n                  </Link>\r\n                </Button>\r\n\r\n                <Button\r\n                  asChild\r\n                  className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\"\r\n                  onClick={toggleMenu}\r\n                >\r\n                  <Link href=\"/student/wishlist\" className=\"flex items-center justify-center gap-3\">\r\n                    <ShoppingBag className=\"h-5 w-5\" />\r\n                    <span>My Wishlist</span>\r\n                  </Link>\r\n                </Button>\r\n\r\n                <Button\r\n                  asChild\r\n                  className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\"\r\n                  onClick={toggleMenu}\r\n                >\r\n                  <Link href=\"/student/referral-dashboard\" className=\"flex items-center justify-center gap-3\">\r\n                    <Share2 className=\"h-5 w-5\" />\r\n                    <span>Referral Dashboard</span>\r\n                  </Link>\r\n                </Button>\r\n\r\n                  <Link href=\"/coins\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"w-full group relative border-orange-500 hover:border-orange-400 bg-black mb-3\"\r\n                      onClick={toggleMenu}\r\n                    >\r\n                      <div className=\"absolute inset-0 bg-gradient-to-r from-orange-500 to-yellow-500 opacity-0 group-hover:opacity-20 transition-opacity\" />\r\n                      <div className=\"relative z-10 flex items-center justify-center gap-3\">\r\n                        <div className=\"p-1.5 rounded-full bg-gradient-to-br from-orange-500 to-yellow-500\">\r\n                          <Image\r\n                            src=\"/uest_coin.png\"\r\n                            alt=\"Coin Icon\"\r\n                            width={20}\r\n                            height={20}\r\n                            className=\"object-contain\"\r\n                          />\r\n                        </div>\r\n                        <span className=\"font-medium text-gray-300\">My Coins</span>\r\n                      </div>\r\n                    </Button>\r\n                  </Link>\r\n\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    className=\"w-full border-[#ff914d] text-[#ff914d] hover:bg-[#ff914d]/10\"\r\n                    onClick={() => {\r\n                      handleStudentLogout();\r\n                      toggleMenu();\r\n                    }}\r\n                  >\r\n                    <div className=\"flex items-center justify-center gap-3\">\r\n                      <User className=\"h-5 w-5\" />\r\n                      <span>Logout</span>\r\n                    </div>\r\n                  </Button>\r\n                </>\r\n              )}\r\n\r\n              {!isAuthenticated && !isStudentLoggedIn && (\r\n                <div className=\"space-y-3 pt-3\">\r\n                  <Button\r\n                    variant=\"default\"\r\n                    className=\"w-full bg-orange-500 hover:bg-orange-600\"\r\n                    asChild\r\n                  >\r\n                    <Link href=\"/class/login\" onClick={toggleMenu}>\r\n                      Tutor/Classes Login\r\n                    </Link>\r\n                  </Button>\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    className=\"w-full border-customOrange text-orange-500 hover:bg-orange\"\r\n                    asChild\r\n                  >\r\n                    <Link href=\"/student/login\" onClick={toggleMenu}>\r\n                      Student Login\r\n                    </Link>\r\n                  </Button>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {isStudentLoggedIn && (\r\n          <ProfileCompletionIndicator />\r\n        )}\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Header;\r\n"], "names": [], "mappings": ";;;AA+I4B;;AA7I5B;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AAIA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;;;AA9BA;;;;;;;;;;;;;;;;;;;;;;AAgCA,MAAM,SAAS;;IACb,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;8BAAE,CAAC,QAAqB,MAAM,IAAI;;IAC9E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACpD,MAAM,WAAW,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAC9B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,IAAI,CAAA,GAAA,qLAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,yCAAyC;IAEtE,MAAM,QAAS,eAAe;IAE9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM,aAAa,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD;YACxC,qBAAqB;YAErB,IAAI,YAAY;gBACd,MAAM,aAAa,aAAa,OAAO,CAAC;gBACxC,IAAI,YAAY;oBACd,eAAe,KAAK,KAAK,CAAC;gBAC5B;gBAEA,SAAS,CAAA,GAAA,iJAAA,CAAA,sBAAmB,AAAD;YAC7B;YAEA,MAAM;wDAAsB;oBAC1B,MAAM,iBAAiB,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD;oBAC5C,qBAAqB;oBAErB,IAAI,gBAAgB;wBAClB,MAAM,aAAa,aAAa,OAAO,CAAC;wBACxC,IAAI,YAAY;4BACd,eAAe,KAAK,KAAK,CAAC;wBAC5B;wBAEA,SAAS,CAAA,GAAA,iJAAA,CAAA,sBAAmB,AAAD;oBAC7B,OAAO;wBACL,eAAe;oBACjB;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YAEnC,IAAI,WAAW,OAAO,EAAE;gBACtB,MAAM,QAAQ,WAAW,OAAO,CAAC,qBAAqB,GAAG,KAAK;gBAC9D,gBAAgB;YAClB;YAEA;oCAAO;oBACL,OAAO,mBAAmB,CAAC,WAAW;gBACxC;;QACF;2BAAG;QAAC;KAAS;IAEb,gDAAgD;IAChD,CAAA,GAAA,wLAAA,CAAA,oBAAiB,AAAD;oCAAE,CAAC,MAAM;YACvB,IAAI,cAAc,iBAAiB,GAAG;YACtC,MAAM,WAAW,EAAE,GAAG;YACtB,MAAM,SAAS,AAAC,QAAQ,QAAS;YACjC,IAAI,OAAO,WAAW;YACtB,IAAI,QAAQ,CAAC,cAAc;gBACzB,OAAO;YACT;YACA,EAAE,GAAG,CAAC;QACR;;IAEA,MAAM,aAAa,IAAM,cAAc,CAAC;IAExC,MAAM,sBAAsB;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD;YAEnC,IAAI,SAAS,OAAO,KAAK,OAAO;gBAC9B,oBAAoB;gBACpB,CAAA,GAAA,sHAAA,CAAA,wBAAqB,AAAD;gBACpB,qBAAqB;gBACrB,eAAe;gBACf,aAAa,UAAU,CAAC;gBAExB,oBAAoB;gBACpB,SAAS,CAAA,GAAA,gJAAA,CAAA,0BAAuB,AAAD;gBAE/B,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,aAAa,CAAC,IAAI,MAAM;YACjC,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,SAAS,OAAO,IAAI;gBAChC,CAAA,GAAA,sHAAA,CAAA,wBAAqB,AAAD;gBACpB,qBAAqB;gBACrB,eAAe;gBACf,aAAa,UAAU,CAAC;gBACxB,SAAS,CAAA,GAAA,gJAAA,CAAA,0BAAuB,AAAD;YACjC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,oBAAoB;YAChC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,aAAa,UAAU,CAAC;YACxB,CAAA,GAAA,sHAAA,CAAA,wBAAqB,AAAD;YACpB,qBAAqB;YACrB,eAAe;YACf,SAAS,CAAA,GAAA,gJAAA,CAAA,0BAAuB,AAAD;QACjC;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,iIAAA,CAAA,cAAW,AAAD,EAAE,MAAM,OAAO,MAAM;YAEtD,IAAI,SAAS,OAAO,EAAE;gBACpB,MAAM,EAAE,KAAK,EAAE,GAAG,SAAS,IAAI;gBAC/B,MAAM,cAAc,4DAAuC,CAAC,sBAAsB,EAAE,MAAM,GAAG,OAAO,EAAE,OAAO;gBAC7G,OAAO,QAAQ,CAAC,IAAI,GAAG;YAEzB,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,SAAS,OAAO,IAAI;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,WAAW;QACf;YAAE,MAAM;YAAqB,OAAO;YAAc,oBAAM,6LAAC,2NAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;QAAa;QAC9F;YAAE,MAAM;YAAU,OAAO;YAAY,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YAAc,OAAO;QAAK;QACtF;YAAE,MAAM;YAAY,OAAO;YAAU,oBAAM,6LAAC,+MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;QAAY;KAC7E;IAED,MAAM,8BACJ,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAK,WAAU;0BAA8C;;;;;;0BAG9D,6LAAC;gBACC,WAAU;gBACV,OAAO;oBAAE,QAAQ;gBAAkB;gBACnC,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,YAAY,EAAE,GAAG;;oBAC9C;kCACW,6LAAC,yNAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;;;;;;;;;;;;;IAKxC,qBACE;;0BACE,6LAAC;gBAAO,WAAU;;kCAChB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CAEV,cAAA,6LAAC,gIAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;8CAId,6LAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAU;;gDAET,KAAK,IAAI;gDACT,KAAK,KAAK;gDACV,KAAK,KAAK,KAAK,+BACd,6LAAC;oDAAK,WAAU;8DAAiE;;;;;;gDAIlF,KAAK,KAAK,kBACT,6LAAC;oDAAK,WAAU;8DAA+E;;;;;;;2CAZ5F,KAAK,IAAI;;;;;;;;;;8CAmBpB,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;8CAER,2BAAa,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;6DAAe,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAI5D,6LAAC;oCAAI,WAAU;;wCACZ,iCACC,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,QAAQ;sDAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;;kEAEV,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4DACJ,KAAI;4DACJ,KAAI;4DACJ,OAAO;4DACP,QAAQ;4DACR,WAAU;;;;;;;;;;;;;;;;;;;;;;sDAOpB,6LAAC;4CAAI,WAAU;;;;;;wCAEd,iCACC,6LAAC,sIAAA,CAAA,UAAO;;8DACN,6LAAC,sIAAA,CAAA,iBAAc;oDAAC,OAAO;8DACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDAAC,WAAU;kEAChB,cAAA,6LAAC,qIAAA,CAAA,iBAAc;4DAAC,WAAU;sEACvB,MAAM,aAAa,MAAM,WACxB,GAAG,KAAK,SAAS,CAAC,EAAE,GAAG,KAAK,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACnD;;;;;;;;;;;;;;;;8DAIV,6LAAC,sIAAA,CAAA,iBAAc;oDAAC,WAAU;;sEACxB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qIAAA,CAAA,SAAM;oEAAC,WAAU;8EAChB,cAAA,6LAAC,qIAAA,CAAA,iBAAc;wEAAC,WAAU;kFACvB,MAAM,aAAa,MAAM,WACxB,GAAG,KAAK,SAAS,CAAC,EAAE,GAAG,KAAK,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACnD;;;;;;;;;;;8EAGR,6LAAC;;sFACC,6LAAC;4EAAE,WAAU;sFACV,MAAM,aAAa,MAAM,WACtB,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE,GACpC,MAAM,aAAa;;;;;;sFAEzB,6LAAC;4EAAE,WAAU;sFAAyB,MAAM,SAAS;;;;;;;;;;;;;;;;;;sEAIzD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qIAAA,CAAA,SAAM;oEAAC,OAAO;oEAAC,WAAU;8EACxB,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEAAC,MAAK;wEAAmB,WAAU;;0FACtC,6LAAC,qMAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;0FAChB,6LAAC;0FAAK;;;;;;;;;;;;;;;;;8EAGV,6LAAC,qIAAA,CAAA,SAAM;oEAAC,SAAS,IAAM;oEAAwB,WAAU;;sFACrD,6LAAC,qMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;sFAChB,6LAAC;sFAAK;;;;;;;;;;;;8EAEV,6LAAC,qIAAA,CAAA,SAAM;oEAAC,OAAO;oEAAC,WAAU;8EACxB,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEAAC,MAAK;wEAA8B,WAAU;;0FACjD,6LAAC,6MAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;0FAClB,6LAAC;0FAAK;;;;;;;;;;;;;;;;;8EAIV,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,WAAU;oEACV,SAAS;wEACP,IAAI;4EACF,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,uBAAuB,CAAC;4EAClE,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gFACzB,OAAO,IAAI,CAAC;gFACZ,SAAS,CAAA,GAAA,sIAAA,CAAA,YAAS,AAAD;gFACjB,aAAa,UAAU,CAAC;gFACxB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;4EAChB;wEACF,EAAE,OAAO,OAAO;4EACd,QAAQ,KAAK,CAAC,iBAAiB;4EAC/B,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wEACd;oEACF;8EACD;;;;;;;;;;;;;;;;;;;;;;;;wCAQR,CAAC,mBAAmB,CAAC,mCACpB,6LAAC,qIAAA,CAAA,SAAM;4CACL,WAAU;4CACV,OAAO;sDAEP,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;0DAAe;;;;;;;;;;;wCAI7B,CAAC,mBAAmB,CAAC,mCACpB,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;4CACV,OAAO;sDAEP,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;0DAAiB;;;;;;;;;;;wCAI/B,mCACC,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,QAAQ;sDAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;;kEAEV,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4DACJ,KAAI;4DACJ,KAAI;4DACJ,OAAO;4DACP,QAAQ;4DACR,WAAU;;;;;;;;;;;;;;;;;;;;;;wCAOnB,mCACC,6LAAC,sIAAA,CAAA,UAAO;;8DACN,6LAAC,sIAAA,CAAA,iBAAc;oDAAC,OAAO;8DACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDAAC,WAAU;kEAChB,cAAA,6LAAC,qIAAA,CAAA,iBAAc;4DAAC,WAAU;sEACvB,aAAa,aAAa,aAAa,WACtC,GAAG,YAAY,SAAS,CAAC,EAAE,GAAG,YAAY,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACjE;;;;;;;;;;;;;;;;8DAIV,6LAAC,sIAAA,CAAA,iBAAc;oDAAC,WAAU;;sEACxB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qIAAA,CAAA,SAAM;oEAAC,WAAU;8EAChB,cAAA,6LAAC,qIAAA,CAAA,iBAAc;wEAAC,WAAU;kFACvB,aAAa,aAAa,aAAa,WACtC,GAAG,YAAY,SAAS,CAAC,EAAE,GAAG,YAAY,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACjE;;;;;;;;;;;8EAGR,6LAAC;;sFACC,6LAAC;4EAAE,WAAU;sFACV,aAAa,aAAa,aAAa,WACpC,GAAG,YAAY,SAAS,CAAC,CAAC,EAAE,YAAY,QAAQ,EAAE,GAClD;;;;;;sFAEN,6LAAC;4EAAE,WAAU;sFAAyB,aAAa,SAAS;;;;;;;;;;;;;;;;;;sEAIlE,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qIAAA,CAAA,SAAM;oEAAC,OAAO;oEAAC,WAAU;8EACxB,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEAAC,MAAK;wEAAmB,WAAU;;0FACtC,6LAAC,qNAAA,CAAA,aAAU;gFAAC,WAAU;;;;;;0FACtB,6LAAC;0FAAK;;;;;;;;;;;;;;;;;8EAGV,6LAAC,qIAAA,CAAA,SAAM;oEAAC,OAAO;oEAAC,WAAU;8EACxB,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEAAC,MAAK;wEAAoB,WAAU;;0FACvC,6LAAC,uNAAA,CAAA,cAAW;gFAAC,WAAU;;;;;;0FACvB,6LAAC;0FAAK;;;;;;;;;;;;;;;;;8EAGV,6LAAC,qIAAA,CAAA,SAAM;oEAAC,OAAO;oEAAC,WAAU;8EACxB,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEAAC,MAAK;wEAAgB,WAAU;;0FACnC,6LAAC,2NAAA,CAAA,gBAAa;gFAAC,WAAU;;;;;;0FACzB,6LAAC;0FAAK;;;;;;;;;;;;;;;;;8EAGV,6LAAC,qIAAA,CAAA,SAAM;oEAAC,OAAO;oEAAC,WAAU;8EACxB,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEAAC,MAAK;wEAA8B,WAAU;;0FACjD,6LAAC,6MAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;0FAClB,6LAAC;0FAAK;;;;;;;;;;;;;;;;;8EAKR,6LAAC,qIAAA,CAAA,SAAM;oEAAC,SAAQ;oEAAU,WAAU;oEAA+D,SAAS;8EAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAU/I,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,OAAO;wCAAE;oCAAE;oCACX,cAAc,IAAM,cAAc;oCAClC,cAAc,IAAM,cAAc;;sDAElC,6LAAC;4CAAI,KAAK;4CAAY,WAAU;sDAC7B;;;;;;sDAEH,6LAAC;4CAAI,WAAU;sDACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOb,6LAAC;;kCAED,6LAAC;wBACC,WAAW,CAAC,mJAAmJ,EAAE,aAAa,kBAAkB,oBAC5L;kCAEJ,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS;kDAET,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAOf,6LAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAU;4CACV,SAAS;;8DAET,6LAAC;oDAAI,WAAU;;wDACZ,KAAK,IAAI;sEACV,6LAAC;sEAAM,KAAK,KAAK;;;;;;;;;;;;gDAElB,KAAK,KAAK,KAAK,+BACd,6LAAC;oDAAK,WAAU;8DAA+E;;;;;;gDAIhG,KAAK,KAAK,kBACT,6LAAC;oDAAK,WAAU;8DAAiE;;;;;;;2CAf9E,KAAK,IAAI;;;;;;;;;;8CAwBpB,6LAAC;oCAAI,WAAU;;wCACZ,iCACC;;8DACE,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAmB,QAAQ;8DACpC,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;;0EAET,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;;;;;;kFAElB,6LAAC;wEAAK,WAAU;kFAA4B;;;;;;;;;;;;;;;;;;;;;;;8DAKlD,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAA8B,QAAQ;8DAC/C,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;;0EAET,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,6MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;;;;;;kFAEpB,6LAAC;wEAAK,WAAU;kFAA4B;;;;;;;;;;;;;;;;;;;;;;;8DAKpD,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,QAAQ;8DAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;;0EAET,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4EACJ,KAAI;4EACJ,KAAI;4EACJ,OAAO;4EACP,QAAQ;4EACR,WAAU;;;;;;;;;;;kFAGd,6LAAC;wEAAK,WAAU;kFAA4B;;;;;;;;;;;;;;;;;;;;;;;8DAKlD,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,SAAS;wDACP,IAAI;4DACF,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,uBAAuB,CAAC;4DAClE,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gEACzB,OAAO,IAAI,CAAC;gEACZ,SAAS,CAAA,GAAA,sIAAA,CAAA,YAAS,AAAD;gEACjB,aAAa,UAAU,CAAC;gEACxB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;4DAChB;wDACF,EAAE,OAAO,OAAO;4DACd,QAAQ,KAAK,CAAC,iBAAiB;4DAC/B,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wDACd;wDACA;oDACF;8DAEA,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,6LAAC;0EAAK;;;;;;;;;;;;;;;;;;;wCAMX,mCACC;;gDACG,aAAa,aAAa,aAAa,0BACtC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qIAAA,CAAA,SAAM;gEAAC,WAAU;0EAChB,cAAA,6LAAC,qIAAA,CAAA,iBAAc;oEAAC,WAAU;8EACvB,AAAC,GAAG,YAAY,SAAS,CAAC,EAAE,GAAG,YAAY,QAAQ,CAAC,EAAE,EAAE,CAAE,WAAW;;;;;;;;;;;0EAG1E,6LAAC;;kFACC,6LAAC;wEAAE,WAAU;kFAA0B,GAAG,YAAY,SAAS,CAAC,CAAC,EAAE,YAAY,QAAQ,EAAE;;;;;;kFACzF,6LAAC;wEAAE,WAAU;kFAAyB,YAAY,KAAK;;;;;;;;;;;;;;;;;;;;;;;8DAMjE,6LAAC,qIAAA,CAAA,SAAM;oDACL,OAAO;oDACP,WAAU;oDACV,SAAS;8DAET,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAmB,WAAU;;0EACtC,6LAAC,qNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;0EACtB,6LAAC;0EAAK;;;;;;;;;;;;;;;;;8DAIV,6LAAC,qIAAA,CAAA,SAAM;oDACL,OAAO;oDACP,WAAU;oDACV,SAAS;8DAET,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAgB,WAAU;;0EACnC,6LAAC,2NAAA,CAAA,gBAAa;gEAAC,WAAU;;;;;;0EACzB,6LAAC;0EAAK;;;;;;;;;;;;;;;;;8DAIV,6LAAC,qIAAA,CAAA,SAAM;oDACL,OAAO;oDACP,WAAU;oDACV,SAAS;8DAET,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAoB,WAAU;;0EACvC,6LAAC,uNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,6LAAC;0EAAK;;;;;;;;;;;;;;;;;8DAIV,6LAAC,qIAAA,CAAA,SAAM;oDACL,OAAO;oDACP,WAAU;oDACV,SAAS;8DAET,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAA8B,WAAU;;0EACjD,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,6LAAC;0EAAK;;;;;;;;;;;;;;;;;8DAIR,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,QAAQ;8DAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;;0EAET,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4EACJ,KAAI;4EACJ,KAAI;4EACJ,OAAO;4EACP,QAAQ;4EACR,WAAU;;;;;;;;;;;kFAGd,6LAAC;wEAAK,WAAU;kFAA4B;;;;;;;;;;;;;;;;;;;;;;;8DAKlD,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,SAAS;wDACP;wDACA;oDACF;8DAEA,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,6LAAC;0EAAK;;;;;;;;;;;;;;;;;;;wCAMb,CAAC,mBAAmB,CAAC,mCACpB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,OAAO;8DAEP,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAe,SAAS;kEAAY;;;;;;;;;;;8DAIjD,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,OAAO;8DAEP,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAiB,SAAS;kEAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAU5D,mCACC,6LAAC,0JAAA,CAAA,UAA0B;;;;;;;;;;;;;AAKrC;GA3qBM;;QAC8B,4JAAA,CAAA,cAAW;QAI5B,wHAAA,CAAA,iBAAc;QAChB,qIAAA,CAAA,YAAS;QAKd,qLAAA,CAAA,iBAAc;QA8CxB,wLAAA,CAAA,oBAAiB;;;KAzDb;uCA6qBS", "debugId": null}}, {"offset": {"line": 2219, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/Footer.tsx"], "sourcesContent": ["'use client';\r\nimport React from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport {\r\n  FaFacebookF,\r\n  FaTwitter,\r\n  FaInstagram,\r\n  FaLinkedinIn,\r\n  FaTumblr,\r\n  FaPinterestP,\r\n  FaFilePdf,\r\n  FaEnvelope,\r\n} from 'react-icons/fa';\r\n\r\nconst Footer = () => {\r\n  return (\r\n    <footer className=\"bg-black text-gray-300 px-6 py-16\">\r\n      <div className=\"container mx-auto max-w-7xl space-y-16\">\r\n        <div className=\"flex flex-col md:flex-row items-center justify-between gap-6\">\r\n          <Link href=\"/\" className=\"flex items-center gap-2\">\r\n            <Image\r\n              src=\"/logo_black.png\"\r\n              alt=\"Logo\"\r\n              width={200}\r\n              height={40}\r\n              className=\"object-contain\"\r\n            />\r\n          </Link>\r\n\r\n          <div className=\"flex flex-wrap justify-center gap-1\">\r\n            {[\r\n              { href: 'mailto:<EMAIL>', icon: FaEnvelope, label: 'Email Us' },\r\n              {\r\n                href: 'https://www.uest.in/wp-content/uploads/2025/03/Welcome-A-UEST-Product-1.pdf',\r\n                icon: FaFilePdf,\r\n                label: 'Brochure',\r\n              },\r\n              {\r\n                href: 'https://x.com/uest189161?t=hLD2wWnt_Zf5b5rTnkSl2Q&s=09',\r\n                icon: FaTwitter,\r\n                label: 'Twitter',\r\n              },\r\n              {\r\n                href: 'https://www.facebook.com/share/1FNYcyqawH/',\r\n                icon: FaFacebookF,\r\n                label: 'Facebook',\r\n              },\r\n              {\r\n                href: 'https://www.instagram.com/uest_edtech?igsh=MWljYWt5YnQyeW9kdg==',\r\n                icon: FaInstagram,\r\n                label: 'Instagram',\r\n              },\r\n              {\r\n                href: 'https://www.linkedin.com/company/uest-edtech/',\r\n                icon: FaLinkedinIn,\r\n                label: 'LinkedIn',\r\n              },\r\n              { href: 'https://pin.it/1Di0EFtAa', icon: FaPinterestP, label: 'Pinterest' },\r\n              {\r\n                href: 'https://www.tumblr.com/uestedtech?source=share',\r\n                icon: FaTumblr,\r\n                label: 'Tumblr',\r\n              },\r\n            ].map(({ href, icon: Icon, label }) => (\r\n              <div key={label} className=\"flex flex-col items-center\">\r\n                <Link\r\n                  href={href}\r\n                  className=\"flex items-center justify-center w-12 h-12 hover:border-gray-400 transition\"\r\n                  title={label}\r\n                >\r\n                  <Icon className=\"text-xl text-white hover:text-gray-400 transition\" />\r\n                </Link>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10\">\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">About</h3>\r\n            <ul className=\"space-y-2 text-sm\">\r\n              <li>\r\n                <Link href=\"/verified-classes\" className=\"hover:text-white transition\">\r\n                  Find Tutors\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/support\" className=\"hover:text-white transition\">\r\n                  Support\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"#\" className=\"hover:text-white transition\">\r\n                  Pricing\r\n                </Link>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">For Students</h3>\r\n            <ul className=\"space-y-2 text-sm\">\r\n              <li>\r\n                <Link href=\"/verified-classes\" className=\"hover:text-white transition\">\r\n                  Find Online Tutor\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/support\" className=\"hover:text-white transition\">\r\n                  FAQs\r\n                </Link>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">Contact</h3>\r\n            <address className=\"not-italic text-sm space-y-1 leading-relaxed\">\r\n              <p>Head Office</p>\r\n              <p>86, 87 Capital Market, Morbi - 363641</p>\r\n              <p>Contact: +91 96 877 877 88</p>\r\n              <p>Email: <EMAIL></p>\r\n            </address>\r\n          </div>\r\n\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">Apps</h3>\r\n           <Link href=\"https://play.google.com/store/apps/details?id=com.uest\" target=\"_blank\">\r\n            <Image\r\n              src=\"/playstore.png\"\r\n              alt=\"Google Play Store\"\r\n              width={180}\r\n              height={50}\r\n              className=\"object-contain\"\r\n            />\r\n          </Link>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Bottom Bar */}\r\n        <div className=\"border-t border-gray-800 pt-6 text-sm flex flex-col md:flex-row justify-between items-center gap-4\">\r\n          <p>© 2025 uest.in. All rights reserved.</p>\r\n          <div className=\"flex gap-4\">\r\n            <Link href=\"/terms-and-conditions\" className=\"hover:text-white transition\">\r\n              Terms & Conditions\r\n            </Link>\r\n            <Link href=\"/privacy-policy\" className=\"hover:text-white transition\">\r\n              Privacy Policy\r\n            </Link>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n};\r\n\r\nexport default Footer;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAeA,MAAM,SAAS;IACb,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCACvB,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;;;;;;sCAId,6LAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,MAAM;oCAA8B,MAAM,iJAAA,CAAA,aAAU;oCAAE,OAAO;gCAAW;gCAC1E;oCACE,MAAM;oCACN,MAAM,iJAAA,CAAA,YAAS;oCACf,OAAO;gCACT;gCACA;oCACE,MAAM;oCACN,MAAM,iJAAA,CAAA,YAAS;oCACf,OAAO;gCACT;gCACA;oCACE,MAAM;oCACN,MAAM,iJAAA,CAAA,cAAW;oCACjB,OAAO;gCACT;gCACA;oCACE,MAAM;oCACN,MAAM,iJAAA,CAAA,cAAW;oCACjB,OAAO;gCACT;gCACA;oCACE,MAAM;oCACN,MAAM,iJAAA,CAAA,eAAY;oCAClB,OAAO;gCACT;gCACA;oCAAE,MAAM;oCAA4B,MAAM,iJAAA,CAAA,eAAY;oCAAE,OAAO;gCAAY;gCAC3E;oCACE,MAAM;oCACN,MAAM,iJAAA,CAAA,WAAQ;oCACd,OAAO;gCACT;6BACD,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,iBAChC,6LAAC;oCAAgB,WAAU;8CACzB,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAM;wCACN,WAAU;wCACV,OAAO;kDAEP,cAAA,6LAAC;4CAAK,WAAU;;;;;;;;;;;mCANV;;;;;;;;;;;;;;;;8BAahB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAoB,WAAU;0DAA8B;;;;;;;;;;;sDAIzE,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAA8B;;;;;;;;;;;sDAIhE,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;0DAA8B;;;;;;;;;;;;;;;;;;;;;;;sCAO7D,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAoB,WAAU;0DAA8B;;;;;;;;;;;sDAIzE,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAA8B;;;;;;;;;;;;;;;;;;;;;;;sCAOpE,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,6LAAC;oCAAQ,WAAU;;sDACjB,6LAAC;sDAAE;;;;;;sDACH,6LAAC;sDAAE;;;;;;sDACH,6LAAC;sDAAE;;;;;;sDACH,6LAAC;sDAAE;;;;;;;;;;;;;;;;;;sCAIP,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACvD,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAyD,QAAO;8CAC1E,cAAA,6LAAC,gIAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAOhB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;sCAAE;;;;;;sCACH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAwB,WAAU;8CAA8B;;;;;;8CAG3E,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAkB,WAAU;8CAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjF;KA5IM;uCA8IS", "debugId": null}}, {"offset": {"line": 2626, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/lib/helper.ts"], "sourcesContent": ["import { FormId, completeForm } from '@/store/slices/formProgressSlice';\r\nimport { AppDispatch } from '@/store';\r\n/* eslint-disable */\r\nexport const evaluateCompletedForms = (classData: any, dispatch: AppDispatch) => {\r\n  if (classData.contactNo) {\r\n    dispatch(completeForm(FormId.PROFILE));\r\n  }\r\n\r\n  if (classData.ClassAbout?.tutorBio?.length > 50) {\r\n    dispatch(completeForm(FormId.DESCRIPTION));\r\n  }\r\n\r\n  if (classData.ClassAbout?.profilePhoto && classData.ClassAbout?.classesLogo) {\r\n    dispatch(completeForm(FormId.PHOTO_LOGO));\r\n  }\r\n\r\n  if (classData.education?.length > 0) {\r\n    dispatch(completeForm(FormId.EDUCATION));\r\n  }\r\n\r\n  if (classData.certificates?.length > 0) {\r\n    dispatch(completeForm(FormId.CERTIFICATES));\r\n  }\r\n\r\n  if (classData.experience?.length > 0) {\r\n    dispatch(completeForm(FormId.EXPERIENCE));\r\n  }\r\n\r\n  if (classData.tuitionClasses?.length > 0) {\r\n    dispatch(completeForm(FormId.TUTIONCLASS));\r\n  }\r\n};\r\n\r\nexport function convertTo24HourFormat(time12h: string): string {\r\n  if (!time12h) return '';\r\n  const [time, modifier] = time12h.split(' ');\r\n\r\n  let [hours, minutes] = time.split(':');\r\n\r\n  if (hours === '12') hours = '00';\r\n  if (modifier === 'PM') hours = String(parseInt(hours) + 12);\r\n\r\n  return `${hours.padStart(2, '0')}:${minutes}`;\r\n}\r\n\r\nexport const safeParseArray = (value: any): string[] => {\r\n  if (!value) return [];\r\n  try {\r\n    const parsed = typeof value === 'string' ? JSON.parse(value) : value;\r\n    return Array.isArray(parsed) ? parsed : [parsed];\r\n  } catch {\r\n    return [value]; // fallback to treating as plain string\r\n  }\r\n};\r\n\r\nexport const parseAndJoinArray = (value: any): string => {\r\n  try {\r\n    const parsed = typeof value === 'string' ? JSON.parse(value) : value;\r\n    return Array.isArray(parsed) ? parsed.join(', ') : parsed || 'N/A';\r\n  } catch {\r\n    return value || 'N/A';\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;;AAAA;;AAGO,MAAM,yBAAyB,CAAC,WAAgB;IACrD,IAAI,UAAU,SAAS,EAAE;QACvB,SAAS,CAAA,GAAA,8IAAA,CAAA,eAAY,AAAD,EAAE,8IAAA,CAAA,SAAM,CAAC,OAAO;IACtC;IAEA,IAAI,UAAU,UAAU,EAAE,UAAU,SAAS,IAAI;QAC/C,SAAS,CAAA,GAAA,8IAAA,CAAA,eAAY,AAAD,EAAE,8IAAA,CAAA,SAAM,CAAC,WAAW;IAC1C;IAEA,IAAI,UAAU,UAAU,EAAE,gBAAgB,UAAU,UAAU,EAAE,aAAa;QAC3E,SAAS,CAAA,GAAA,8IAAA,CAAA,eAAY,AAAD,EAAE,8IAAA,CAAA,SAAM,CAAC,UAAU;IACzC;IAEA,IAAI,UAAU,SAAS,EAAE,SAAS,GAAG;QACnC,SAAS,CAAA,GAAA,8IAAA,CAAA,eAAY,AAAD,EAAE,8IAAA,CAAA,SAAM,CAAC,SAAS;IACxC;IAEA,IAAI,UAAU,YAAY,EAAE,SAAS,GAAG;QACtC,SAAS,CAAA,GAAA,8IAAA,CAAA,eAAY,AAAD,EAAE,8IAAA,CAAA,SAAM,CAAC,YAAY;IAC3C;IAEA,IAAI,UAAU,UAAU,EAAE,SAAS,GAAG;QACpC,SAAS,CAAA,GAAA,8IAAA,CAAA,eAAY,AAAD,EAAE,8IAAA,CAAA,SAAM,CAAC,UAAU;IACzC;IAEA,IAAI,UAAU,cAAc,EAAE,SAAS,GAAG;QACxC,SAAS,CAAA,GAAA,8IAAA,CAAA,eAAY,AAAD,EAAE,8IAAA,CAAA,SAAM,CAAC,WAAW;IAC1C;AACF;AAEO,SAAS,sBAAsB,OAAe;IACnD,IAAI,CAAC,SAAS,OAAO;IACrB,MAAM,CAAC,MAAM,SAAS,GAAG,QAAQ,KAAK,CAAC;IAEvC,IAAI,CAAC,OAAO,QAAQ,GAAG,KAAK,KAAK,CAAC;IAElC,IAAI,UAAU,MAAM,QAAQ;IAC5B,IAAI,aAAa,MAAM,QAAQ,OAAO,SAAS,SAAS;IAExD,OAAO,GAAG,MAAM,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,SAAS;AAC/C;AAEO,MAAM,iBAAiB,CAAC;IAC7B,IAAI,CAAC,OAAO,OAAO,EAAE;IACrB,IAAI;QACF,MAAM,SAAS,OAAO,UAAU,WAAW,KAAK,KAAK,CAAC,SAAS;QAC/D,OAAO,MAAM,OAAO,CAAC,UAAU,SAAS;YAAC;SAAO;IAClD,EAAE,OAAM;QACN,OAAO;YAAC;SAAM,EAAE,uCAAuC;IACzD;AACF;AAEO,MAAM,oBAAoB,CAAC;IAChC,IAAI;QACF,MAAM,SAAS,OAAO,UAAU,WAAW,KAAK,KAAK,CAAC,SAAS;QAC/D,OAAO,MAAM,OAAO,CAAC,UAAU,OAAO,IAAI,CAAC,QAAQ,UAAU;IAC/D,EAAE,OAAM;QACN,OAAO,SAAS;IAClB;AACF", "debugId": null}}, {"offset": {"line": 2695, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/select.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as SelectPrimitive from '@radix-ui/react-select';\r\nimport { CheckIcon, ChevronDownIcon, ChevronUpIcon } from 'lucide-react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Select({ ...props }: React.ComponentProps<typeof SelectPrimitive.Root>) {\r\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />;\r\n}\r\n\r\nfunction SelectGroup({ ...props }: React.ComponentProps<typeof SelectPrimitive.Group>) {\r\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />;\r\n}\r\n\r\nfunction SelectValue({ ...props }: React.ComponentProps<typeof SelectPrimitive.Value>) {\r\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />;\r\n}\r\n\r\nfunction SelectTrigger({\r\n  className,\r\n  size = 'default',\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\r\n  size?: 'sm' | 'default';\r\n}) {\r\n  return (\r\n    <SelectPrimitive.Trigger\r\n      data-slot=\"select-trigger\"\r\n      data-size={size}\r\n      className={cn(\r\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <SelectPrimitive.Icon asChild>\r\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\r\n      </SelectPrimitive.Icon>\r\n    </SelectPrimitive.Trigger>\r\n  );\r\n}\r\n\r\nfunction SelectContent({\r\n  className,\r\n  children,\r\n  position = 'popper',\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\r\n  return (\r\n    <SelectPrimitive.Portal>\r\n      <SelectPrimitive.Content\r\n        data-slot=\"select-content\"\r\n        className={cn(\r\n          'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md',\r\n          position === 'popper' &&\r\n            'data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1',\r\n          className\r\n        )}\r\n        position={position}\r\n        {...props}\r\n      >\r\n        <SelectScrollUpButton />\r\n        <SelectPrimitive.Viewport\r\n          className={cn(\r\n            'p-1',\r\n            position === 'popper' &&\r\n              'h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1'\r\n          )}\r\n        >\r\n          {children}\r\n        </SelectPrimitive.Viewport>\r\n        <SelectScrollDownButton />\r\n      </SelectPrimitive.Content>\r\n    </SelectPrimitive.Portal>\r\n  );\r\n}\r\n\r\nfunction SelectLabel({ className, ...props }: React.ComponentProps<typeof SelectPrimitive.Label>) {\r\n  return (\r\n    <SelectPrimitive.Label\r\n      data-slot=\"select-label\"\r\n      className={cn('text-muted-foreground px-2 py-1.5 text-xs', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SelectItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\r\n  return (\r\n    <SelectPrimitive.Item\r\n      data-slot=\"select-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\r\n        <SelectPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </SelectPrimitive.ItemIndicator>\r\n      </span>\r\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n    </SelectPrimitive.Item>\r\n  );\r\n}\r\n\r\nfunction SelectSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\r\n  return (\r\n    <SelectPrimitive.Separator\r\n      data-slot=\"select-separator\"\r\n      className={cn('bg-border pointer-events-none -mx-1 my-1 h-px', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SelectScrollUpButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollUpButton\r\n      data-slot=\"select-scroll-up-button\"\r\n      className={cn('flex cursor-default items-center justify-center py-1', className)}\r\n      {...props}\r\n    >\r\n      <ChevronUpIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollUpButton>\r\n  );\r\n}\r\n\r\nfunction SelectScrollDownButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollDownButton\r\n      data-slot=\"select-scroll-down-button\"\r\n      className={cn('flex cursor-default items-center justify-center py-1', className)}\r\n      {...props}\r\n    >\r\n      <ChevronDownIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollDownButton>\r\n  );\r\n}\r\n\r\nexport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectScrollDownButton,\r\n  SelectScrollUpButton,\r\n  SelectSeparator,\r\n  SelectTrigger,\r\n  SelectValue,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EAAE,GAAG,OAA0D;IAC7E,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAFS;AAIT,SAAS,YAAY,EAAE,GAAG,OAA2D;IACnF,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAFS;AAIT,SAAS,YAAY,EAAE,GAAG,OAA2D;IACnF,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAFS;AAIT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAA2D;IAC9F,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wDAAwD;QACrE,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAbS;AAeT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wDAAwD;QACrE,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAbS", "debugId": null}}, {"offset": {"line": 2944, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/alert-dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { buttonVariants } from \"@/components/ui/button\"\r\n\r\nfunction AlertDialog({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Root>) {\r\n  return <AlertDialogPrimitive.Root data-slot=\"alert-dialog\" {...props} />\r\n}\r\n\r\nfunction AlertDialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Trigger>) {\r\n  return (\r\n    <AlertDialogPrimitive.Trigger data-slot=\"alert-dialog-trigger\" {...props} />\r\n  )\r\n}\r\n\r\nfunction AlertDialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Portal>) {\r\n  return (\r\n    <AlertDialogPrimitive.Portal data-slot=\"alert-dialog-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction AlertDialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Overlay>) {\r\n  return (\r\n    <AlertDialogPrimitive.Overlay\r\n      data-slot=\"alert-dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Content>) {\r\n  return (\r\n    <AlertDialogPortal>\r\n      <AlertDialogOverlay />\r\n      <AlertDialogPrimitive.Content\r\n        data-slot=\"alert-dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </AlertDialogPortal>\r\n  )\r\n}\r\n\r\nfunction AlertDialogHeader({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogFooter({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-dialog-footer\"\r\n      className={cn(\r\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Title>) {\r\n  return (\r\n    <AlertDialogPrimitive.Title\r\n      data-slot=\"alert-dialog-title\"\r\n      className={cn(\"text-lg font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Description>) {\r\n  return (\r\n    <AlertDialogPrimitive.Description\r\n      data-slot=\"alert-dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogAction({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Action>) {\r\n  return (\r\n    <AlertDialogPrimitive.Action\r\n      className={cn(buttonVariants(), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogCancel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Cancel>) {\r\n  return (\r\n    <AlertDialogPrimitive.Cancel\r\n      className={cn(buttonVariants({ variant: \"outline\" }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  AlertDialog,\r\n  AlertDialogPortal,\r\n  AlertDialogOverlay,\r\n  AlertDialogTrigger,\r\n  AlertDialogContent,\r\n  AlertDialogHeader,\r\n  AlertDialogFooter,\r\n  AlertDialogTitle,\r\n  AlertDialogDescription,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAGA;AAEA;AACA;AANA;;;;;AAQA,SAAS,YAAY,EACnB,GAAG,OACoD;IACvD,qBAAO,6LAAC,8KAAA,CAAA,OAAyB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AACtE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,6LAAC,8KAAA,CAAA,UAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,6LAAC,8KAAA,CAAA,SAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,6LAAC,8KAAA,CAAA,UAA4B;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,8KAAA,CAAA,UAA4B;gBAC3B,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIjB;MAjBS;AAmBT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,6LAAC,8KAAA,CAAA,QAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,8KAAA,CAAA,cAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,8KAAA,CAAA,SAA2B;QAC1B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,KAAK;QAC/B,GAAG,KAAK;;;;;;AAGf;MAVS;AAYT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,8KAAA,CAAA,SAA2B;QAC1B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,EAAE;YAAE,SAAS;QAAU,IAAI;QACrD,GAAG,KAAK;;;;;;AAGf;OAVS", "debugId": null}}, {"offset": {"line": 3130, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/label.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as LabelPrimitive from '@radix-ui/react-label';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Label({ className, ...props }: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  return (\r\n    <LabelPrimitive.Root\r\n      data-slot=\"label\"\r\n      className={cn(\r\n        'flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Label };\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAyD;IACtF,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 3164, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/form.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as LabelPrimitive from '@radix-ui/react-label';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport {\r\n  Controller,\r\n  FormProvider,\r\n  useFormContext,\r\n  useFormState,\r\n  type ControllerProps,\r\n  type FieldPath,\r\n  type FieldValues,\r\n} from 'react-hook-form';\r\n\r\nimport { cn } from '@/lib/utils';\r\nimport { Label } from '@/components/ui/label';\r\n\r\nconst Form = FormProvider;\r\n\r\ntype FormFieldContextValue<\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n> = {\r\n  name: TName;\r\n};\r\n\r\nconst FormFieldContext = React.createContext<FormFieldContextValue>({} as FormFieldContextValue);\r\n\r\nconst FormField = <\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n>({\r\n  ...props\r\n}: ControllerProps<TFieldValues, TName>) => {\r\n  return (\r\n    <FormFieldContext.Provider value={{ name: props.name }}>\r\n      <Controller {...props} />\r\n    </FormFieldContext.Provider>\r\n  );\r\n};\r\n\r\nconst useFormField = () => {\r\n  const fieldContext = React.useContext(FormFieldContext);\r\n  const itemContext = React.useContext(FormItemContext);\r\n  const { getFieldState } = useFormContext();\r\n  const formState = useFormState({ name: fieldContext.name });\r\n  const fieldState = getFieldState(fieldContext.name, formState);\r\n\r\n  if (!fieldContext) {\r\n    throw new Error('useFormField should be used within <FormField>');\r\n  }\r\n\r\n  const { id } = itemContext;\r\n\r\n  return {\r\n    id,\r\n    name: fieldContext.name,\r\n    formItemId: `${id}-form-item`,\r\n    formDescriptionId: `${id}-form-item-description`,\r\n    formMessageId: `${id}-form-item-message`,\r\n    ...fieldState,\r\n  };\r\n};\r\n\r\ntype FormItemContextValue = {\r\n  id: string;\r\n};\r\n\r\nconst FormItemContext = React.createContext<FormItemContextValue>({} as FormItemContextValue);\r\n\r\nfunction FormItem({ className, ...props }: React.ComponentProps<'div'>) {\r\n  const id = React.useId();\r\n\r\n  return (\r\n    <FormItemContext.Provider value={{ id }}>\r\n      <div data-slot=\"form-item\" className={cn('grid gap-2', className)} {...props} />\r\n    </FormItemContext.Provider>\r\n  );\r\n}\r\n\r\nfunction FormLabel({ className, ...props }: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  const { error, formItemId } = useFormField();\r\n\r\n  return (\r\n    <Label\r\n      data-slot=\"form-label\"\r\n      data-error={!!error}\r\n      className={cn('data-[error=true]:text-destructive', className)}\r\n      htmlFor={formItemId}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction FormControl({ ...props }: React.ComponentProps<typeof Slot>) {\r\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField();\r\n\r\n  return (\r\n    <Slot\r\n      data-slot=\"form-control\"\r\n      id={formItemId}\r\n      aria-describedby={!error ? `${formDescriptionId}` : `${formDescriptionId} ${formMessageId}`}\r\n      aria-invalid={!!error}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction FormDescription({ className, ...props }: React.ComponentProps<'p'>) {\r\n  const { formDescriptionId } = useFormField();\r\n\r\n  return (\r\n    <p\r\n      data-slot=\"form-description\"\r\n      id={formDescriptionId}\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction FormMessage({ className, ...props }: React.ComponentProps<'p'>) {\r\n  const { error, formMessageId } = useFormField();\r\n  const body = error ? String(error?.message ?? '') : props.children;\r\n\r\n  if (!body) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <p\r\n      data-slot=\"form-message\"\r\n      id={formMessageId}\r\n      className={cn('text-destructive text-sm', className)}\r\n      {...props}\r\n    >\r\n      {body}\r\n    </p>\r\n  );\r\n}\r\n\r\nexport {\r\n  useFormField,\r\n  Form,\r\n  FormItem,\r\n  FormLabel,\r\n  FormControl,\r\n  FormDescription,\r\n  FormMessage,\r\n  FormField,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAEA;AACA;AAUA;AACA;;;AAhBA;;;;;;AAkBA,MAAM,OAAO,iKAAA,CAAA,eAAY;AASzB,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAyB,CAAC;AAErE,MAAM,YAAY,CAGhB,EACA,GAAG,OACkC;IACrC,qBACE,6LAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,6LAAC,iKAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;KAXM;AAaN,MAAM,eAAe;;IACnB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;IACtC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;IACrC,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,iBAAc,AAAD;IACvC,MAAM,YAAY,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD,EAAE;QAAE,MAAM,aAAa,IAAI;IAAC;IACzD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;GArBM;;QAGsB,iKAAA,CAAA,iBAAc;QACtB,iKAAA,CAAA,eAAY;;;AAuBhC,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAwB,CAAC;AAEnE,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;;IACpE,MAAM,KAAK,CAAA,GAAA,6JAAA,CAAA,QAAW,AAAD;IAErB,qBACE,6LAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,6LAAC;YAAI,aAAU;YAAY,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAa,GAAG,KAAK;;;;;;;;;;;AAGlF;IARS;MAAA;AAUT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAyD;;IAC1F,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,6LAAC,oIAAA,CAAA,QAAK;QACJ,aAAU;QACV,cAAY,CAAC,CAAC;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACpD,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;IAZS;;QACuB;;;MADvB;AAcT,SAAS,YAAY,EAAE,GAAG,OAA0C;;IAClE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;IAEhE,qBACE,6LAAC,mKAAA,CAAA,OAAI;QACH,aAAU;QACV,IAAI;QACJ,oBAAkB,CAAC,QAAQ,GAAG,mBAAmB,GAAG,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAC3F,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;IAZS;;QACyD;;;MADzD;AAcT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAkC;;IACzE,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;IAXS;;QACuB;;;MADvB;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAkC;;IACrE,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW,MAAM,MAAM,QAAQ;IAElE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,6LAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;kBAER;;;;;;AAGP;IAlBS;;QAC0B;;;MAD1B", "debugId": null}}, {"offset": {"line": 3366, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/reviewsApi.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\n\r\nexport const getReviewsByClassId = async (classId: string, page: number = 1, limit: number = 5) => {\r\n  try {\r\n    const response = await axiosInstance.get(`/reviews/class/${classId}`, {\r\n      params: { page, limit }\r\n    });\r\n    return response.data;\r\n  } catch (error: any) {\r\n    throw new Error(`Failed to fetch reviews for class: ${error.message}`);\r\n  }\r\n};\r\n\r\nexport const getAverageRating = async (classId: string) => {\r\n  try {\r\n    const response = await axiosInstance.get(`/reviews/average/${classId}`);\r\n    return response.data.averageRating;\r\n  } catch (error: any) {\r\n    throw new Error(`Failed to get average rating: ${error.message}`);\r\n  }\r\n};\r\n\r\nexport const createReview = async (data: {\r\n  classId: string;\r\n  rating: number;\r\n  message: string;\r\n  studentName?: string;\r\n  studentId?: string;\r\n}) => {\r\n  try {\r\n    const response = await axiosInstance.post('/reviews', data);\r\n    return response.data;\r\n  } catch (error: any) {\r\n    const errorMessage = error.response?.data?.message || error.message;\r\n    const alreadyReviewed = error.response?.data?.alreadyReviewed || false;\r\n\r\n    const customError = new Error(errorMessage);\r\n    (customError as any).alreadyReviewed = alreadyReviewed;\r\n\r\n    throw customError;\r\n  }\r\n};\r\n\r\nexport const deleteReview = async (id: string) => {\r\n  try {\r\n    const response = await axiosInstance.delete(`/reviews/${id}`);\r\n    return response.data;\r\n  } catch (error: any) {\r\n    throw new Error(`Failed to delete review: ${error.message}`);\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEO,MAAM,sBAAsB,OAAO,SAAiB,OAAe,CAAC,EAAE,QAAgB,CAAC;IAC5F,IAAI;QACF,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,SAAS,EAAE;YACpE,QAAQ;gBAAE;gBAAM;YAAM;QACxB;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,MAAM,OAAO,EAAE;IACvE;AACF;AAEO,MAAM,mBAAmB,OAAO;IACrC,IAAI;QACF,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,iBAAiB,EAAE,SAAS;QACtE,OAAO,SAAS,IAAI,CAAC,aAAa;IACpC,EAAE,OAAO,OAAY;QACnB,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,MAAM,OAAO,EAAE;IAClE;AACF;AAEO,MAAM,eAAe,OAAO;IAOjC,IAAI;QACF,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,YAAY;QACtD,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO;QACnE,MAAM,kBAAkB,MAAM,QAAQ,EAAE,MAAM,mBAAmB;QAEjE,MAAM,cAAc,IAAI,MAAM;QAC7B,YAAoB,eAAe,GAAG;QAEvC,MAAM;IACR;AACF;AAEO,MAAM,eAAe,OAAO;IACjC,IAAI;QACF,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,IAAI;QAC5D,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,MAAM,OAAO,EAAE;IAC7D;AACF", "debugId": null}}, {"offset": {"line": 3424, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/ReviewsSection.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState, useEffect, useCallback } from \"react\";\r\nimport { Star, Trash2, Filter } from \"lucide-react\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { toast } from \"sonner\";\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from \"@/components/ui/alert-dialog\";\r\nimport { Testimonial } from '@/lib/types';\r\n\r\n\r\nimport { z } from \"zod\";\r\nimport { useForm } from \"react-hook-form\";\r\nimport { zodResolver } from \"@hookform/resolvers/zod\";\r\nimport {\r\n  Form,\r\n  FormControl,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  FormMessage,\r\n} from \"@/components/ui/form\";\r\nimport { getReviewsByClassId, createReview, deleteReview } from \"@/services/reviewsApi\";\r\nimport { isStudentAuthenticated } from \"@/lib/utils\";\r\nimport { Avatar, AvatarFallback } from \"@/components/ui/avatar\";\r\n\r\nconst reviewFormSchema = z.object({\r\n  message: z\r\n    .string()\r\n    .min(10, \"Message must be at least 10 characters\")\r\n    .max(500, \"Message cannot exceed 500 characters\"),\r\n  rating: z.number().min(1, \"Please select a rating\"),\r\n  classId: z.string().min(1, \"Class ID is required\"),\r\n});\r\n\r\ntype ReviewFormValues = z.infer<typeof reviewFormSchema>;\r\n\r\ninterface ReviewsSectionProps {\r\n  classId: string;\r\n  userData: any;\r\n  onReviewSubmit?: () => void;\r\n}\r\n\r\nconst ReviewsSection: React.FC<ReviewsSectionProps> = ({ classId, userData, onReviewSubmit }) => {\r\n  const [rating, setRating] = useState(0);\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);\r\n  const [reviewToDelete, setReviewToDelete] = useState<string | null>(null);\r\n  const [filterType, setFilterType] = useState<'ALL' | 'ME'>('ALL');\r\n  const [filteredReviews, setFilteredReviews] = useState<Testimonial[]>([]);\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [hasMore, setHasMore] = useState(false);\r\n  const [isLoadingMore, setIsLoadingMore] = useState(false);\r\n  const [isStudentLoggedIn, setIsStudentLoggedIn] = useState(false);\r\n  useEffect(() => {\r\n    setIsStudentLoggedIn(isStudentAuthenticated());\r\n\r\n    const handleStorageChange = () => {\r\n      setIsStudentLoggedIn(isStudentAuthenticated());\r\n    };\r\n\r\n    window.addEventListener('storage', handleStorageChange);\r\n    return () => {\r\n      window.removeEventListener('storage', handleStorageChange);\r\n    };\r\n  }, []);\r\n\r\n  const form = useForm<ReviewFormValues>({\r\n    resolver: zodResolver(reviewFormSchema),\r\n    defaultValues: {\r\n      message: \"\",\r\n      rating: 0,\r\n      classId: classId,\r\n    },\r\n  });\r\n\r\n  const fetchReviews = useCallback(async (page = 1, filterType: 'ALL' | 'ME' = 'ALL') => {\r\n    try {\r\n      const response = await getReviewsByClassId(classId, page, 5);\r\n\r\n      let userFullName = '';\r\n      const studentDataStr = localStorage.getItem('student_data');\r\n\r\n      if (studentDataStr) {\r\n        try {\r\n          const studentData = JSON.parse(studentDataStr);\r\n          if (studentData.firstName && studentData.lastName) {\r\n            userFullName = `${studentData.firstName} ${studentData.lastName}`;\r\n          }\r\n        } catch (e) {\r\n          console.error('Error parsing student data:', e);\r\n        }\r\n      }\r\n\r\n      if (!userFullName && userData && userData.firstName && userData.lastName) {\r\n        userFullName = `${userData.firstName} ${userData.lastName}`;\r\n      }\r\n\r\n      const processedReviews = response.reviews.map((review: any) => {\r\n        if (review.studentName && !review.message.startsWith(review.studentName)) {\r\n          return {\r\n            ...review,\r\n            message: `${review.studentName}: ${review.message}`\r\n          };\r\n        }\r\n        return review;\r\n      });\r\n\r\n      if (page === 1) {\r\n        if (filterType === 'ALL') {\r\n          setFilteredReviews(processedReviews);\r\n        } else if (filterType === 'ME' && userFullName) {\r\n          const filtered = processedReviews.filter((review: Testimonial) => {\r\n            if (review.studentId) {\r\n              const currentStudentData = JSON.parse(studentDataStr || '{}');\r\n              return review.studentId === currentStudentData.id;\r\n            } else {\r\n              const author = review.message.split(':')[0];\r\n              return author === userFullName;\r\n            }\r\n          });\r\n          setFilteredReviews(filtered);\r\n        }\r\n      } else {\r\n        if (filterType === 'ALL') {\r\n          setFilteredReviews(prev => [...prev, ...processedReviews]);\r\n        } else if (filterType === 'ME' && userFullName) {\r\n          const filtered = processedReviews.filter((review: Testimonial) => {\r\n            if (review.studentId) {\r\n              const currentStudentData = JSON.parse(studentDataStr || '{}');\r\n              return review.studentId === currentStudentData.id;\r\n            } else {\r\n              const author = review.message.split(':')[0];\r\n              return author === userFullName;\r\n            }\r\n          });\r\n          setFilteredReviews(prev => [...prev, ...filtered]);\r\n        }\r\n      }\r\n\r\n      setHasMore(response.hasMore);\r\n      setCurrentPage(response.currentPage);\r\n\r\n      return response;\r\n    } catch (error) {\r\n      console.error('Error fetching reviews:', error);\r\n      toast.error('Failed to fetch reviews');\r\n      return null;\r\n    }\r\n  }, [classId, userData]);\r\n\r\n  const handleLoadMore = async () => {\r\n    setIsLoadingMore(true);\r\n    try {\r\n      await fetchReviews(currentPage + 1, filterType);\r\n    } finally {\r\n      setIsLoadingMore(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    setCurrentPage(1);\r\n    fetchReviews(1, filterType);\r\n  }, [filterType, classId, fetchReviews]);\r\n\r\n  const openDeleteDialog = (reviewId: string) => {\r\n    setReviewToDelete(reviewId);\r\n    setIsDeleteDialogOpen(true);\r\n  };\r\n\r\n  const handleDeleteTestimonial = async () => {\r\n    if (!reviewToDelete) {\r\n      console.error('No review ID to delete');\r\n      return;\r\n    }\r\n\r\n    if (!isStudentAuthenticated()) {\r\n      toast.error('You must be logged in to delete a review');\r\n      setIsDeleteDialogOpen(false);\r\n      setReviewToDelete(null);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      await deleteReview(reviewToDelete);\r\n      toast.success('Review deleted successfully!');\r\n\r\n      await fetchReviews(1, filterType);\r\n\r\n      if (onReviewSubmit) {\r\n        onReviewSubmit();\r\n      }\r\n    } catch (error: any) {\r\n      console.error('Error deleting review:', error);\r\n      toast.error(error.message || 'Failed to delete review');\r\n    } finally {\r\n      setIsDeleteDialogOpen(false);\r\n      setReviewToDelete(null);\r\n    }\r\n  };\r\n\r\n  const handleStarClick = (selectedRating: number) => {\r\n    setRating(selectedRating);\r\n    form.setValue(\"rating\", selectedRating);\r\n  };\r\n\r\n  const onSubmit = async (values: ReviewFormValues) => {\r\n    setIsSubmitting(true);\r\n\r\n    try {\r\n      const studentDataStr = localStorage.getItem('student_data');\r\n      let studentName = '';\r\n      let studentId = '';\r\n\r\n      if (studentDataStr) {\r\n        try {\r\n          const studentData = JSON.parse(studentDataStr);\r\n          if (studentData.firstName && studentData.lastName) {\r\n            studentName = `${studentData.firstName} ${studentData.lastName}`;\r\n            studentId = studentData.id;\r\n          }\r\n        } catch (e) {\r\n          console.error('Error parsing student data:', e);\r\n        }\r\n      }\r\n\r\n      if (!studentName && userData && userData.firstName && userData.lastName) {\r\n        studentName = `${userData.firstName} ${userData.lastName}`;\r\n      }\r\n\r\n      if (!isStudentAuthenticated()) {\r\n        toast.error('Only students can submit reviews');\r\n        return;\r\n      }\r\n\r\n      const reviewData = {\r\n        classId: values.classId,\r\n        rating: values.rating,\r\n        message: values.message,\r\n        studentName: studentName,\r\n        studentId: studentId\r\n      };\r\n\r\n      await createReview(reviewData);\r\n\r\n      toast.success('Review submitted successfully! The class owner will be notified.');\r\n\r\n      // Reset form\r\n      form.reset({\r\n        message: \"\",\r\n        rating: 0,\r\n        classId: classId\r\n      });\r\n      setRating(0);\r\n\r\n      await fetchReviews(1, filterType);\r\n\r\n      if (onReviewSubmit) {\r\n        onReviewSubmit();\r\n      }\r\n    } catch (error: any) {\r\n      console.error('Error submitting review:', error);\r\n      toast.error(error.message || 'Failed to submit review');\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>\r\n        <AlertDialogContent className=\"dark:bg-siderbar\">\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>Are you sure?</AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              This action cannot be undone. This will permanently delete your testimonial.\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel className=\"dark:bg-gray-700 dark:text-white dark:hover:bg-gray-600\">Cancel</AlertDialogCancel>\r\n            <AlertDialogAction\r\n              onClick={handleDeleteTestimonial}\r\n              className=\"bg-red-500 text-white hover:bg-red-600\"\r\n            >\r\n              Delete\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n\r\n      <div className=\"w-full max-w-9xl mx-auto\">\r\n        {isStudentLoggedIn ? (\r\n          <div className=\"dark:bg-siderbar border rounded-lg shadow-md p-6 mb-8\">\r\n            <h2 className=\"text-2xl font-semibold mb-6\">Write a Review</h2>\r\n\r\n            <Form {...form}>\r\n              <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6\">\r\n                <div className=\"mb-6\">\r\n                  <label className=\"block text-sm font-medium mb-2\">Your Rating</label>\r\n                  <div className=\"flex gap-1\">\r\n                    {[1, 2, 3, 4, 5].map((star) => (\r\n                      <button\r\n                        key={star}\r\n                        type=\"button\"\r\n                        onClick={() => handleStarClick(star)}\r\n                        className=\"focus:outline-none\"\r\n                      >\r\n                        <Star\r\n                          className={`w-8 h-8 ${star <= rating\r\n                            ? 'fill-[#FD904B] text-[#FD904B]'\r\n                            : 'text-gray-300'\r\n                            }`}\r\n                        />\r\n                      </button>\r\n                    ))}\r\n                  </div>\r\n                  {form.formState.errors.rating && (\r\n                    <p className=\"text-red-500 text-sm mt-1\">{form.formState.errors.rating.message}</p>\r\n                  )}\r\n                </div>\r\n\r\n                <FormField\r\n                  control={form.control}\r\n                  name=\"message\"\r\n                  render={({ field }) => (\r\n                    <FormItem className=\"mb-6\">\r\n                      <FormLabel className=\"block text-sm font-medium mb-2\">Your Message</FormLabel>\r\n                      <FormControl>\r\n                        <textarea\r\n                          {...field}\r\n                          className=\"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#FD904B] focus:border-transparent\"\r\n                          rows={4}\r\n                          placeholder=\"Share your experience (10-500 characters)...\"\r\n                        />\r\n                      </FormControl>\r\n                      <FormMessage />\r\n                    </FormItem>\r\n                  )}\r\n                />\r\n\r\n                <button\r\n                  type=\"submit\"\r\n                  disabled={isSubmitting}\r\n                  className=\"w-full bg-[#FD904B] text-white py-2 px-4 rounded-lg hover:bg-[#FD904B]/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\r\n                >\r\n                  {isSubmitting ? 'Submitting...' : 'Submit Review'}\r\n                </button>\r\n              </form>\r\n            </Form>\r\n          </div>\r\n        ) : (\r\n          <div className=\"dark:bg-siderbar border rounded-lg shadow-md p-6 mb-8 text-center\">\r\n            <p className=\"text-lg mb-4\">Please log in as a student to write a review</p>\r\n          </div>\r\n        )}\r\n\r\n        <div>\r\n          <div className=\"flex justify-between items-center mb-4\">\r\n            <h2 className=\"text-2xl font-semibold\">Class Reviews</h2>\r\n            {isStudentLoggedIn && (\r\n              <div className=\"flex items-center gap-2\">\r\n                <Filter className=\"h-4 w-4 text-muted-foreground\" />\r\n                <Select value={filterType} onValueChange={(value: 'ALL' | 'ME') => setFilterType(value)}>\r\n                  <SelectTrigger className=\"w-[120px]\">\r\n                    <SelectValue placeholder=\"Filter\" />\r\n                  </SelectTrigger>\r\n                  <SelectContent>\r\n                    <SelectItem value=\"ALL\">All Reviews</SelectItem>\r\n                    <SelectItem value=\"ME\">My Reviews</SelectItem>\r\n                  </SelectContent>\r\n                </Select>\r\n              </div>\r\n            )}\r\n          </div>\r\n          {filteredReviews.length > 0 ? (\r\n            <div className=\"space-y-4\">\r\n              {filteredReviews.map((review: Testimonial) => (\r\n                <div key={review.id} className=\"dark:bg-slidebar border border-gray-400 rounded-lg shadow-sm p-4\">\r\n                  <div className=\"flex items-center gap-3 mb-4\">\r\n                    <div className=\"relative w-12 h-12 rounded-full overflow-hidden border-2 border-[#FD904B]/80 flex justify-center items-center\">\r\n                      <Avatar>\r\n                        <AvatarFallback className=\"bg-white text-black\">\r\n                          {review?.student?.firstName && review?.student?.lastName\r\n                            ? `${review.student.firstName[0]}${review.student.lastName[0]}`.toUpperCase()\r\n                            : \"ST\"}\r\n                        </AvatarFallback>\r\n                      </Avatar>\r\n                    </div>\r\n                    <div className=\"flex-1\">\r\n                      <div className=\"flex justify-between items-center\">\r\n                        <h4 className=\"font-medium text-gray-600 dark:text-white\">\r\n                          {review.studentName || review.message.split(':')[0]}\r\n                        </h4>\r\n                        {isStudentLoggedIn && (() => {\r\n                          let userFullName = '';\r\n                          let userId = '';\r\n                          try {\r\n                            const studentDataStr = localStorage.getItem('student_data');\r\n                            if (studentDataStr) {\r\n                              const studentData = JSON.parse(studentDataStr);\r\n                              if (studentData.firstName && studentData.lastName) {\r\n                                userFullName = `${studentData.firstName} ${studentData.lastName}`;\r\n                                userId = studentData.id;\r\n                              }\r\n                            }\r\n                          } catch (e) {\r\n                            console.error('Error parsing student data:', e);\r\n                          }\r\n\r\n                          const isAuthor =\r\n                            (review.studentId && userId && review.studentId === userId) ||\r\n                            (userFullName && (\r\n                              (review.studentName && review.studentName === userFullName) ||\r\n                              review.message.split(':')[0] === userFullName\r\n                            ));\r\n\r\n                          return isAuthor ? (\r\n                            <button\r\n                              onClick={() => openDeleteDialog(review.id)}\r\n                              className=\"text-red-500 hover:text-red-700\"\r\n                            >\r\n                              <Trash2 className=\"h-4 w-4\" />\r\n                            </button>\r\n                          ) : null;\r\n                        })()}\r\n                      </div>\r\n                      <div className=\"flex items-center gap-1 mt-1\">\r\n                        {[1, 2, 3, 4, 5].map((_, i) => (\r\n                          <Star\r\n                            key={i}\r\n                            className={`w-4 h-4 ${i < review.rating ? 'fill-[#FD904B] text-[#FD904B]' : 'text-gray-300'}`}\r\n                          />\r\n                        ))}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <p className=\"text-gray-700 dark:text-gray-300 break-words mb-3\">\r\n                    {review.message.includes(':') ?\r\n                      <>\r\n                        <span>{review.message.split(':').slice(1).join(':').trim()}</span>\r\n                      </>\r\n                      : review.message\r\n                    }\r\n                  </p>\r\n                  <div className=\"flex justify-between items-center text-sm text-gray-500\">\r\n                    <span>Posted on {new Date(review.createdAt).toLocaleDateString()}</span>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n\r\n              {hasMore && (\r\n                <div className=\"flex justify-center mt-6\">\r\n                  <Button\r\n                    onClick={handleLoadMore}\r\n                    variant=\"outline\"\r\n                    className=\"px-6 py-2 border-[#FD904B] text-[#FD904B] hover:bg-[#FD904B]/10\"\r\n                    disabled={isLoadingMore}\r\n                  >\r\n                    {isLoadingMore ? 'Loading...' : 'Load More'}\r\n                  </Button>\r\n                </div>\r\n              )}\r\n            </div>\r\n          ) : (\r\n            <p className=\"text-muted-foreground\">\r\n              {filterType === 'ALL'\r\n                ? \"No reviews yet.\"\r\n                : \"You haven't written any reviews yet.\"}\r\n            </p>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ReviewsSection;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AACA;AAOA;AACA;AACA;AAaA;AACA;AACA;AACA;AAQA;AACA;AACA;;;AAtCA;;;;;;;;;;;;;;AAwCA,MAAM,mBAAmB,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAChC,SAAS,uIAAA,CAAA,IAAC,CACP,MAAM,GACN,GAAG,CAAC,IAAI,0CACR,GAAG,CAAC,KAAK;IACZ,QAAQ,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC1B,SAAS,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC7B;AAUA,MAAM,iBAAgD,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,cAAc,EAAE;;IAC1F,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IACxE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,qBAAqB,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD;YAE1C,MAAM;gEAAsB;oBAC1B,qBAAqB,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD;gBAC5C;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC;4CAAO;oBACL,OAAO,mBAAmB,CAAC,WAAW;gBACxC;;QACF;mCAAG,EAAE;IAEL,MAAM,OAAO,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAoB;QACrC,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,SAAS;YACT,QAAQ;YACR,SAAS;QACX;IACF;IAEA,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE,OAAO,OAAO,CAAC,EAAE,aAA2B,KAAK;YAChF,IAAI;gBACF,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS,MAAM;gBAE1D,IAAI,eAAe;gBACnB,MAAM,iBAAiB,aAAa,OAAO,CAAC;gBAE5C,IAAI,gBAAgB;oBAClB,IAAI;wBACF,MAAM,cAAc,KAAK,KAAK,CAAC;wBAC/B,IAAI,YAAY,SAAS,IAAI,YAAY,QAAQ,EAAE;4BACjD,eAAe,GAAG,YAAY,SAAS,CAAC,CAAC,EAAE,YAAY,QAAQ,EAAE;wBACnE;oBACF,EAAE,OAAO,GAAG;wBACV,QAAQ,KAAK,CAAC,+BAA+B;oBAC/C;gBACF;gBAEA,IAAI,CAAC,gBAAgB,YAAY,SAAS,SAAS,IAAI,SAAS,QAAQ,EAAE;oBACxE,eAAe,GAAG,SAAS,SAAS,CAAC,CAAC,EAAE,SAAS,QAAQ,EAAE;gBAC7D;gBAEA,MAAM,mBAAmB,SAAS,OAAO,CAAC,GAAG;iFAAC,CAAC;wBAC7C,IAAI,OAAO,WAAW,IAAI,CAAC,OAAO,OAAO,CAAC,UAAU,CAAC,OAAO,WAAW,GAAG;4BACxE,OAAO;gCACL,GAAG,MAAM;gCACT,SAAS,GAAG,OAAO,WAAW,CAAC,EAAE,EAAE,OAAO,OAAO,EAAE;4BACrD;wBACF;wBACA,OAAO;oBACT;;gBAEA,IAAI,SAAS,GAAG;oBACd,IAAI,eAAe,OAAO;wBACxB,mBAAmB;oBACrB,OAAO,IAAI,eAAe,QAAQ,cAAc;wBAC9C,MAAM,WAAW,iBAAiB,MAAM;iFAAC,CAAC;gCACxC,IAAI,OAAO,SAAS,EAAE;oCACpB,MAAM,qBAAqB,KAAK,KAAK,CAAC,kBAAkB;oCACxD,OAAO,OAAO,SAAS,KAAK,mBAAmB,EAAE;gCACnD,OAAO;oCACL,MAAM,SAAS,OAAO,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;oCAC3C,OAAO,WAAW;gCACpB;4BACF;;wBACA,mBAAmB;oBACrB;gBACF,OAAO;oBACL,IAAI,eAAe,OAAO;wBACxB;wEAAmB,CAAA,OAAQ;uCAAI;uCAAS;iCAAiB;;oBAC3D,OAAO,IAAI,eAAe,QAAQ,cAAc;wBAC9C,MAAM,WAAW,iBAAiB,MAAM;iFAAC,CAAC;gCACxC,IAAI,OAAO,SAAS,EAAE;oCACpB,MAAM,qBAAqB,KAAK,KAAK,CAAC,kBAAkB;oCACxD,OAAO,OAAO,SAAS,KAAK,mBAAmB,EAAE;gCACnD,OAAO;oCACL,MAAM,SAAS,OAAO,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;oCAC3C,OAAO,WAAW;gCACpB;4BACF;;wBACA;wEAAmB,CAAA,OAAQ;uCAAI;uCAAS;iCAAS;;oBACnD;gBACF;gBAEA,WAAW,SAAS,OAAO;gBAC3B,eAAe,SAAS,WAAW;gBAEnC,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO;YACT;QACF;mDAAG;QAAC;QAAS;KAAS;IAEtB,MAAM,iBAAiB;QACrB,iBAAiB;QACjB,IAAI;YACF,MAAM,aAAa,cAAc,GAAG;QACtC,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,eAAe;YACf,aAAa,GAAG;QAClB;mCAAG;QAAC;QAAY;QAAS;KAAa;IAEtC,MAAM,mBAAmB,CAAC;QACxB,kBAAkB;QAClB,sBAAsB;IACxB;IAEA,MAAM,0BAA0B;QAC9B,IAAI,CAAC,gBAAgB;YACnB,QAAQ,KAAK,CAAC;YACd;QACF;QAEA,IAAI,CAAC,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD,KAAK;YAC7B,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,sBAAsB;YACtB,kBAAkB;YAClB;QACF;QAEA,IAAI;YACF,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD,EAAE;YACnB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAEd,MAAM,aAAa,GAAG;YAEtB,IAAI,gBAAgB;gBAClB;YACF;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,0BAA0B;YACxC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;QAC/B,SAAU;YACR,sBAAsB;YACtB,kBAAkB;QACpB;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,UAAU;QACV,KAAK,QAAQ,CAAC,UAAU;IAC1B;IAEA,MAAM,WAAW,OAAO;QACtB,gBAAgB;QAEhB,IAAI;YACF,MAAM,iBAAiB,aAAa,OAAO,CAAC;YAC5C,IAAI,cAAc;YAClB,IAAI,YAAY;YAEhB,IAAI,gBAAgB;gBAClB,IAAI;oBACF,MAAM,cAAc,KAAK,KAAK,CAAC;oBAC/B,IAAI,YAAY,SAAS,IAAI,YAAY,QAAQ,EAAE;wBACjD,cAAc,GAAG,YAAY,SAAS,CAAC,CAAC,EAAE,YAAY,QAAQ,EAAE;wBAChE,YAAY,YAAY,EAAE;oBAC5B;gBACF,EAAE,OAAO,GAAG;oBACV,QAAQ,KAAK,CAAC,+BAA+B;gBAC/C;YACF;YAEA,IAAI,CAAC,eAAe,YAAY,SAAS,SAAS,IAAI,SAAS,QAAQ,EAAE;gBACvE,cAAc,GAAG,SAAS,SAAS,CAAC,CAAC,EAAE,SAAS,QAAQ,EAAE;YAC5D;YAEA,IAAI,CAAC,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD,KAAK;gBAC7B,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YAEA,MAAM,aAAa;gBACjB,SAAS,OAAO,OAAO;gBACvB,QAAQ,OAAO,MAAM;gBACrB,SAAS,OAAO,OAAO;gBACvB,aAAa;gBACb,WAAW;YACb;YAEA,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD,EAAE;YAEnB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAEd,aAAa;YACb,KAAK,KAAK,CAAC;gBACT,SAAS;gBACT,QAAQ;gBACR,SAAS;YACX;YACA,UAAU;YAEV,MAAM,aAAa,GAAG;YAEtB,IAAI,gBAAgB;gBAClB;YACF;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;QAC/B,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE;;0BACE,6LAAC,8IAAA,CAAA,cAAW;gBAAC,MAAM;gBAAoB,cAAc;0BACnD,cAAA,6LAAC,8IAAA,CAAA,qBAAkB;oBAAC,WAAU;;sCAC5B,6LAAC,8IAAA,CAAA,oBAAiB;;8CAChB,6LAAC,8IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,6LAAC,8IAAA,CAAA,yBAAsB;8CAAC;;;;;;;;;;;;sCAI1B,6LAAC,8IAAA,CAAA,oBAAiB;;8CAChB,6LAAC,8IAAA,CAAA,oBAAiB;oCAAC,WAAU;8CAA0D;;;;;;8CACvF,6LAAC,8IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAOP,6LAAC;gBAAI,WAAU;;oBACZ,kCACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA8B;;;;;;0CAE5C,6LAAC,mIAAA,CAAA,OAAI;gCAAE,GAAG,IAAI;0CACZ,cAAA,6LAAC;oCAAK,UAAU,KAAK,YAAY,CAAC;oCAAW,WAAU;;sDACrD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAU;8DAAiC;;;;;;8DAClD,6LAAC;oDAAI,WAAU;8DACZ;wDAAC;wDAAG;wDAAG;wDAAG;wDAAG;qDAAE,CAAC,GAAG,CAAC,CAAC,qBACpB,6LAAC;4DAEC,MAAK;4DACL,SAAS,IAAM,gBAAgB;4DAC/B,WAAU;sEAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;gEACH,WAAW,CAAC,QAAQ,EAAE,QAAQ,SAC1B,kCACA,iBACA;;;;;;2DATD;;;;;;;;;;gDAcV,KAAK,SAAS,CAAC,MAAM,CAAC,MAAM,kBAC3B,6LAAC;oDAAE,WAAU;8DAA6B,KAAK,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO;;;;;;;;;;;;sDAIlF,6LAAC,mIAAA,CAAA,YAAS;4CACR,SAAS,KAAK,OAAO;4CACrB,MAAK;4CACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,WAAQ;oDAAC,WAAU;;sEAClB,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAiC;;;;;;sEACtD,6LAAC,mIAAA,CAAA,cAAW;sEACV,cAAA,6LAAC;gEACE,GAAG,KAAK;gEACT,WAAU;gEACV,MAAM;gEACN,aAAY;;;;;;;;;;;sEAGhB,6LAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;sDAKlB,6LAAC;4CACC,MAAK;4CACL,UAAU;4CACV,WAAU;sDAET,eAAe,kBAAkB;;;;;;;;;;;;;;;;;;;;;;6CAM1C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCAAe;;;;;;;;;;;kCAIhC,6LAAC;;0CACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAyB;;;;;;oCACtC,mCACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC,qIAAA,CAAA,SAAM;gDAAC,OAAO;gDAAY,eAAe,CAAC,QAAwB,cAAc;;kEAC/E,6LAAC,qIAAA,CAAA,gBAAa;wDAAC,WAAU;kEACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,6LAAC,qIAAA,CAAA,gBAAa;;0EACZ,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;0EACxB,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAMhC,gBAAgB,MAAM,GAAG,kBACxB,6LAAC;gCAAI,WAAU;;oCACZ,gBAAgB,GAAG,CAAC,CAAC,uBACpB,6LAAC;4CAAoB,WAAU;;8DAC7B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;0EACL,cAAA,6LAAC,qIAAA,CAAA,iBAAc;oEAAC,WAAU;8EACvB,QAAQ,SAAS,aAAa,QAAQ,SAAS,WAC5C,GAAG,OAAO,OAAO,CAAC,SAAS,CAAC,EAAE,GAAG,OAAO,OAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACzE;;;;;;;;;;;;;;;;sEAIV,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAG,WAAU;sFACX,OAAO,WAAW,IAAI,OAAO,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;wEAEpD,qBAAqB,CAAC;4EACrB,IAAI,eAAe;4EACnB,IAAI,SAAS;4EACb,IAAI;gFACF,MAAM,iBAAiB,aAAa,OAAO,CAAC;gFAC5C,IAAI,gBAAgB;oFAClB,MAAM,cAAc,KAAK,KAAK,CAAC;oFAC/B,IAAI,YAAY,SAAS,IAAI,YAAY,QAAQ,EAAE;wFACjD,eAAe,GAAG,YAAY,SAAS,CAAC,CAAC,EAAE,YAAY,QAAQ,EAAE;wFACjE,SAAS,YAAY,EAAE;oFACzB;gFACF;4EACF,EAAE,OAAO,GAAG;gFACV,QAAQ,KAAK,CAAC,+BAA+B;4EAC/C;4EAEA,MAAM,WACJ,AAAC,OAAO,SAAS,IAAI,UAAU,OAAO,SAAS,KAAK,UACnD,gBAAgB,CACf,AAAC,OAAO,WAAW,IAAI,OAAO,WAAW,KAAK,gBAC9C,OAAO,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,KAAK,YACnC;4EAEF,OAAO,yBACL,6LAAC;gFACC,SAAS,IAAM,iBAAiB,OAAO,EAAE;gFACzC,WAAU;0FAEV,cAAA,6LAAC,6MAAA,CAAA,SAAM;oFAAC,WAAU;;;;;;;;;;uFAElB;wEACN,CAAC;;;;;;;8EAEH,6LAAC;oEAAI,WAAU;8EACZ;wEAAC;wEAAG;wEAAG;wEAAG;wEAAG;qEAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBACvB,6LAAC,qMAAA,CAAA,OAAI;4EAEH,WAAW,CAAC,QAAQ,EAAE,IAAI,OAAO,MAAM,GAAG,kCAAkC,iBAAiB;2EADxF;;;;;;;;;;;;;;;;;;;;;;8DAQf,6LAAC;oDAAE,WAAU;8DACV,OAAO,OAAO,CAAC,QAAQ,CAAC,qBACvB;kEACE,cAAA,6LAAC;sEAAM,OAAO,OAAO,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,IAAI;;;;;;wEAExD,OAAO,OAAO;;;;;;8DAGpB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;;4DAAK;4DAAW,IAAI,KAAK,OAAO,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;2CArExD,OAAO,EAAE;;;;;oCA0EpB,yBACC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,SAAQ;4CACR,WAAU;4CACV,UAAU;sDAET,gBAAgB,eAAe;;;;;;;;;;;;;;;;qDAMxC,6LAAC;gCAAE,WAAU;0CACV,eAAe,QACZ,oBACA;;;;;;;;;;;;;;;;;;;;AAOlB;GAhbM;;QAwBS,iKAAA,CAAA,UAAO;;;KAxBhB;uCAkbS", "debugId": null}}, {"offset": {"line": 4218, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/studentWishlistServices.ts"], "sourcesContent": ["import { getStudentAuthToken } from '@/lib/utils';\r\n\r\nconst API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4005/api/v1';\r\n\r\nexport const addToWishlist = async (classId: string) => {\r\n  try {\r\n    const token = getStudentAuthToken();\r\n    if (!token) {\r\n      throw new Error('Authentication required');\r\n    }\r\n\r\n    const response = await fetch(`${API_URL}/student-wishlist`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        Authorization: `Bearer ${token}`\r\n      },\r\n      body: JSON.stringify({ classId }),\r\n      credentials: 'include'\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json();\r\n      throw new Error(errorData.message || 'Failed to add to wishlist');\r\n    }\r\n\r\n    return await response.json();\r\n  } catch (error) {\r\n    console.error('Error adding to wishlist:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const removeFromWishlist = async (wishlistItemId: string) => {\r\n  try {\r\n    const token = getStudentAuthToken();\r\n    if (!token) {\r\n      throw new Error('Authentication required');\r\n    }\r\n\r\n    const response = await fetch(`${API_URL}/student-wishlist/${wishlistItemId}`, {\r\n      method: 'DELETE',\r\n      headers: {\r\n        Authorization: `Bearer ${token}`\r\n      },\r\n      credentials: 'include'\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json();\r\n      throw new Error(errorData.message || 'Failed to remove from wishlist');\r\n    }\r\n\r\n    return await response.json();\r\n  } catch (error) {\r\n    console.error('Error removing from wishlist:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const checkWishlistStatus = async (classId: string) => {\r\n  try {\r\n    const token = getStudentAuthToken();\r\n    if (!token) {\r\n      return { inWishlist: false };\r\n    }\r\n\r\n    const response = await fetch(`${API_URL}/student-wishlist/check/${classId}`, {\r\n      method: 'GET',\r\n      headers: {\r\n        Authorization: `Bearer ${token}`\r\n      },\r\n      credentials: 'include'\r\n    });\r\n\r\n    if (!response.ok) {\r\n      return { inWishlist: false };\r\n    }\r\n\r\n    const data = await response.json();\r\n    return data.data;\r\n  } catch (error) {\r\n    console.error('Error checking wishlist status:', error);\r\n    return { inWishlist: false };\r\n  }\r\n};\r\n\r\nexport const getWishlist = async (page = 1, limit = 10) => {\r\n  try {\r\n    const token = getStudentAuthToken();\r\n    if (!token) {\r\n      throw new Error('Authentication required');\r\n    }\r\n\r\n    const response = await fetch(`${API_URL}/student-wishlist?page=${page}&limit=${limit}`, {\r\n      method: 'GET',\r\n      headers: {\r\n        Authorization: `Bearer ${token}`\r\n      },\r\n      credentials: 'include'\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json();\r\n      throw new Error(errorData.message || 'Failed to fetch wishlist');\r\n    }\r\n\r\n    return await response.json();\r\n  } catch (error) {\r\n    console.error('Error fetching wishlist:', error);\r\n    throw error;\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;;AAEgB;AAFhB;;AAEA,MAAM,UAAU,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;AAE5C,MAAM,gBAAgB,OAAO;IAClC,IAAI;QACF,MAAM,QAAQ,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD;QAChC,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,iBAAiB,CAAC,EAAE;YAC1D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,eAAe,CAAC,OAAO,EAAE,OAAO;YAClC;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAQ;YAC/B,aAAa;QACf;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM;IACR;AACF;AAEO,MAAM,qBAAqB,OAAO;IACvC,IAAI;QACF,MAAM,QAAQ,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD;QAChC,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,kBAAkB,EAAE,gBAAgB,EAAE;YAC5E,QAAQ;YACR,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;YAClC;YACA,aAAa;QACf;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM;IACR;AACF;AAEO,MAAM,sBAAsB,OAAO;IACxC,IAAI;QACF,MAAM,QAAQ,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD;QAChC,IAAI,CAAC,OAAO;YACV,OAAO;gBAAE,YAAY;YAAM;QAC7B;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,wBAAwB,EAAE,SAAS,EAAE;YAC3E,QAAQ;YACR,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;YAClC;YACA,aAAa;QACf;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,OAAO;gBAAE,YAAY;YAAM;QAC7B;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,IAAI;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO;YAAE,YAAY;QAAM;IAC7B;AACF;AAEO,MAAM,cAAc,OAAO,OAAO,CAAC,EAAE,QAAQ,EAAE;IACpD,IAAI;QACF,MAAM,QAAQ,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD;QAChC,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,uBAAuB,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE;YACtF,QAAQ;YACR,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;YAClC;YACA,aAAa;QACf;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 4339, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/dialog.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as DialogPrimitive from '@radix-ui/react-dialog';\r\nimport { XIcon } from 'lucide-react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Dialog({ ...props }: React.ComponentProps<typeof DialogPrimitive.Root>) {\r\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />;\r\n}\r\n\r\nfunction DialogTrigger({ ...props }: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\r\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />;\r\n}\r\n\r\nfunction DialogPortal({ ...props }: React.ComponentProps<typeof DialogPrimitive.Portal>) {\r\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />;\r\n}\r\n\r\nfunction DialogClose({ ...props }: React.ComponentProps<typeof DialogPrimitive.Close>) {\r\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />;\r\n}\r\n\r\nfunction DialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\r\n  return (\r\n    <DialogPrimitive.Overlay\r\n      data-slot=\"dialog-overlay\"\r\n      className={cn(\r\n        'data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DialogContent({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\r\n  return (\r\n    <DialogPortal data-slot=\"dialog-portal\">\r\n      <DialogOverlay />\r\n      <DialogPrimitive.Content\r\n        data-slot=\"dialog-content\"\r\n        className={cn(\r\n          'bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg',\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\">\r\n          <XIcon />\r\n          <span className=\"sr-only\">Close</span>\r\n        </DialogPrimitive.Close>\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>\r\n  );\r\n}\r\n\r\nfunction DialogHeader({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-header\"\r\n      className={cn('flex flex-col gap-2 text-center sm:text-left', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DialogFooter({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-footer\"\r\n      className={cn('flex flex-col-reverse gap-2 sm:flex-row sm:justify-end', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DialogTitle({ className, ...props }: React.ComponentProps<typeof DialogPrimitive.Title>) {\r\n  return (\r\n    <DialogPrimitive.Title\r\n      data-slot=\"dialog-title\"\r\n      className={cn('text-lg leading-none font-semibold', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\r\n  return (\r\n    <DialogPrimitive.Description\r\n      data-slot=\"dialog-description\"\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport {\r\n  Dialog,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogOverlay,\r\n  DialogPortal,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EAAE,GAAG,OAA0D;IAC7E,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAFS;AAIT,SAAS,cAAc,EAAE,GAAG,OAA6D;IACvF,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAFS;AAIT,SAAS,aAAa,EAAE,GAAG,OAA4D;IACrF,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAFS;AAIT,SAAS,YAAY,EAAE,GAAG,OAA2D;IACnF,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAFS;AAIT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,GAAG,OACkD;IACrD,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAxBS;AA0BT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0DAA0D;QACvE,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAA2D;IAC9F,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 4536, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app/classes-details/%5Bid%5D/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState, useEffect } from \"react\";\r\nimport Image from \"next/image\";\r\nimport { useParams } from \"next/navigation\";\r\nimport { FaGoogleScholar, FaRegHeart, FaHeart } from \"react-icons/fa6\";\r\nimport { MdOutlineElectricBolt } from \"react-icons/md\";\r\nimport { RiMessage2Line } from \"react-icons/ri\";\r\nimport { IoShieldCheckmark } from \"react-icons/io5\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport Header from \"@/app-components/Header\";\r\nimport Footer from \"@/app-components/Footer\";\r\nimport { axiosInstance } from \"@/lib/axios\";\r\nimport { parseAndJoinArray } from \"@/lib/helper\";\r\nimport { useSelector } from \"react-redux\";\r\nimport ReviewsSection from \"@/app-components/ReviewsSection\";\r\nimport { getAverageRating, getReviewsByClassId } from \"@/services/reviewsApi\";\r\nimport { isStudentAuthenticated } from \"@/lib/utils\";\r\nimport { addToWishlist, removeFromWishlist, checkWishlistStatus } from \"@/services/studentWishlistServices\";\r\nimport { toast } from \"sonner\";\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from \"@/components/ui/dialog\";\r\nimport { Loader2 } from \"lucide-react\";\r\nimport { useRouter } from \"next/navigation\";\r\n\r\nconst TABS = [\r\n  { key: \"education\", label: \"Education\", icon: <FaGoogleScholar /> },\r\n  { key: \"work\", label: \"Work Experience\", icon: <MdOutlineElectricBolt /> },\r\n  { key: \"certifications\", label: \"Certifications\", icon: <IoShieldCheckmark /> },\r\n  { key: \"tuition\", label: \"Tuition Classes\", icon: <RiMessage2Line /> },\r\n];\r\n\r\nconst InnerPage = () => {\r\n  const [activeTab, setActiveTab] = useState(\"education\");\r\n  const [data, setData] = useState<any>(null);\r\n  const [averageRating, setAverageRating] = useState(0);\r\n  const [reviewCount, setReviewCount] = useState(0);\r\n  const [isInWishlist, setIsInWishlist] = useState(false);\r\n  const [wishlistItemId, setWishlistItemId] = useState<string | null>(null);\r\n  const [isStudentLoggedIn, setIsStudentLoggedIn] = useState(false);\r\n  const [showLoginModal, setShowLoginModal] = useState(false);\r\n  const { id } = useParams();\r\n  const router = useRouter();\r\n\r\n  useEffect(() => {\r\n    const fetchTeacher = async () => {\r\n      try {\r\n        const res = await axiosInstance.get(`classes/details/${id}`);\r\n        setData(res.data);\r\n      } catch (err) {\r\n        console.error(\"Failed to fetch teacher data\", err);\r\n      }\r\n    };\r\n\r\n    const fetchAverageRating = async () => {\r\n      try {\r\n        const averageRating = await getAverageRating(id as string);\r\n        setAverageRating(averageRating);\r\n      } catch (err) {\r\n        console.error(\"Failed to fetch average rating\", err);\r\n      }\r\n    };\r\n\r\n    const fetchReviewCount = async () => {\r\n      try {\r\n        const response = await getReviewsByClassId(id as string, 1, 1);\r\n        setReviewCount(response.total);\r\n      } catch (err) {\r\n        console.error(\"Failed to fetch review count\", err);\r\n      }\r\n    };\r\n    setIsStudentLoggedIn(isStudentAuthenticated());\r\n\r\n    fetchTeacher();\r\n    fetchAverageRating();\r\n    fetchReviewCount();\r\n  }, [id]);\r\n\r\n  useEffect(() => {\r\n    const checkIfInWishlist = async () => {\r\n      if (!isStudentLoggedIn || !id) return;\r\n\r\n      try {\r\n        const response = await checkWishlistStatus(id as string);\r\n        setIsInWishlist(response.inWishlist);\r\n        if (response.wishlistItem) {\r\n          setWishlistItemId(response.wishlistItem.id);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error checking wishlist status:\", error);\r\n      }\r\n    };\r\n\r\n    checkIfInWishlist();\r\n  }, [isStudentLoggedIn, id]);\r\n\r\n  const userData = useSelector((state: any) => state.user.user);\r\n\r\n  const handleReviewUpdate = async () => {\r\n    try {\r\n      const averageRating = await getAverageRating(id as string);\r\n      setAverageRating(averageRating);\r\n\r\n      const response = await getReviewsByClassId(id as string, 1, 1);\r\n      setReviewCount(response.total);\r\n    } catch (err) {\r\n      console.error(\"Failed to update review stats\", err);\r\n    }\r\n  };\r\n\r\n  const toggleWishlist = async () => {\r\n    if (!isStudentLoggedIn) {\r\n      setShowLoginModal(true);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      if (isInWishlist && wishlistItemId) {\r\n        await removeFromWishlist(wishlistItemId);\r\n        setIsInWishlist(false);\r\n        setWishlistItemId(null);\r\n        toast.success(\"Removed from wishlist\");\r\n      } else {\r\n        const response = await addToWishlist(id as string);\r\n        setIsInWishlist(true);\r\n        if (response.data?.id) {\r\n          setWishlistItemId(response.data.id);\r\n        }\r\n        toast.success(\"Added to wishlist\");\r\n      }\r\n    } catch (error: any) {\r\n      toast.error(error.message || \"Failed to update wishlist\");\r\n    }\r\n  };\r\n\r\n  if (!data) {\r\n    return (\r\n       <div className=\"flex justify-center items-center h-screen\">\r\n        <Loader2 className=\"w-8 h-8 animate-spin text-orange-500\" />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const {\r\n    firstName,\r\n    lastName,\r\n    education = [],\r\n    experience = [],\r\n    certificates = [],\r\n    ClassAbout = {},\r\n    tuitionClasses = [],\r\n    status = {},\r\n  } = data;\r\n\r\n  const fullName = `${firstName} ${lastName}`;\r\n  const profileImg = ClassAbout?.profilePhoto\r\n    ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${ClassAbout.profilePhoto}`\r\n    : \"/teacher-profile.jpg\";\r\n\r\n  const logoImg = ClassAbout?.classesLogo\r\n    ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${ClassAbout.classesLogo}`\r\n    : \"/teacher-profile.jpg\";\r\n\r\n  return (\r\n    <>\r\n      <Header />\r\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 space-y-12\">\r\n        <section className=\"grid md:grid-cols-4 gap-8\">\r\n          <div className=\"md:col-span-3 space-y-8\">\r\n            <div className=\"flex flex-col sm:flex-row gap-6 p-6 rounded-2xl shadow-sm border\">\r\n              <div className=\"relative w-full sm:w-64 h-64 rounded-xl overflow-hidden shadow-lg\">\r\n                <Image\r\n                  src={logoImg}\r\n                  alt=\"Teacher\"\r\n                  fill\r\n                  className=\"object-cover\"\r\n                />\r\n              </div>\r\n              <div className=\"flex-1 space-y-4\">\r\n                <h1 className=\"text-3xl font-bold flex items-center gap-2\">\r\n                  {fullName}\r\n                  {status?.status === \"APPROVED\" && (\r\n                    <IoShieldCheckmark className=\"text-green-500\" />\r\n                  )}\r\n                </h1>\r\n                <p className=\"text-lg text-muted-foreground font-medium\">\r\n                  {ClassAbout?.catchyHeadline || \"Professional Educator\"}\r\n                </p>\r\n                <p className=\"text-sm text-muted-foreground\">\r\n                  {ClassAbout?.tutorBio || \"No bio available.\"}\r\n                </p>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"space-y-6\">\r\n              <h2 className=\"text-2xl font-semibold\">Resume</h2>\r\n              <div className=\"flex flex-wrap gap-4 border-b pb-2\">\r\n                {TABS.map(({ key, label, icon }) => (\r\n                  <button\r\n                    key={key}\r\n                    className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200 ${activeTab === key\r\n                      ? \"bg-orange-100 text-orange-600 font-semibold\"\r\n                      : \"text-muted-foreground hover:bg-gray-100\"\r\n                      }`}\r\n                    onClick={() => setActiveTab(key)}\r\n                  >\r\n                    {icon}\r\n                    {label}\r\n                  </button>\r\n                ))}\r\n              </div>\r\n\r\n              <div className=\"space-y-4\">\r\n                {activeTab === \"education\" && (\r\n                  <div className=\"grid gap-4\">\r\n                    {education.length > 0 && education[0].isDegree ? (\r\n                      education.map((edu: any, idx: number) => (\r\n                        <div\r\n                          key={idx}\r\n                          className=\"p-4 rounded-lg shadow-sm border\"\r\n                        >\r\n                          <p className=\"font-semibold text-foreground\">\r\n                            {edu.university}\r\n                          </p>\r\n                          <p className=\"text-sm text-muted-foreground\">\r\n                            {edu.degree} — {edu.degreeType}\r\n                          </p>\r\n                          <p className=\"text-sm text-muted-foreground\">\r\n                            Passout Year: {edu.passoutYear}\r\n                          </p>\r\n                        </div>\r\n                      ))\r\n                    ) : (\r\n                      <p className=\"text-muted-foreground\">\r\n                        No education details available.\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                )}\r\n\r\n                {activeTab === \"work\" && (\r\n                  <div className=\"grid gap-4\">\r\n                    {experience.length && experience[0].isExperience ? (\r\n                      experience.map((exp: any, idx: number) => (\r\n                        <div\r\n                          key={idx}\r\n                          className=\"p-4 rounded-lg shadow-sm border\"\r\n                        >\r\n                          <p className=\"font-semibold text-foreground\">\r\n                            {exp.title}\r\n                          </p>\r\n                          <p className=\"text-sm text-muted-foreground\">\r\n                            From: {new Date(exp.from).toLocaleDateString()} — To:{\" \"}\r\n                            {new Date(exp.to).toLocaleDateString()}\r\n                          </p>\r\n                        </div>\r\n                      ))\r\n                    ) : (\r\n                      <p className=\"text-muted-foreground\">\r\n                        No work experience available.\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                )}\r\n\r\n                {activeTab === \"certifications\" && (\r\n                  <div className=\"grid gap-4\">\r\n                    {certificates.length > 0 && certificates[0].isCertificate ? (\r\n                      certificates.map((cert: any, idx: number) => (\r\n                        <div\r\n                          key={idx}\r\n                          className=\"p-4 rounded-lg shadow-sm border\"\r\n                        >\r\n                          <p className=\"font-semibold text-foreground\">\r\n                            {cert.title}\r\n                          </p>\r\n                        </div>\r\n                      ))\r\n                    ) : (\r\n                      <p className=\"text-muted-foreground\">\r\n                        No certifications available.\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                )}\r\n\r\n                {activeTab === \"tuition\" && (\r\n                  <div className=\"grid gap-4\">\r\n                    {tuitionClasses.length > 0 ? (\r\n                      tuitionClasses.map((tuition: any, idx: number) => (\r\n                        <div\r\n                          key={idx}\r\n                          className=\"p-6 rounded-lg shadow-sm border\"\r\n                        >\r\n                          <p className=\"text-lg font-semibold text-foreground\">\r\n                            Tuition #{idx + 1}\r\n                          </p>\r\n                          <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4 text-sm text-muted-foreground\">\r\n                            <div>\r\n                              <strong>Category:</strong> {tuition.education || \"N/A\"}\r\n                            </div>\r\n                            <div>\r\n                              <strong>Coaching Type:</strong>{\" \"}\r\n                              {parseAndJoinArray(tuition.coachingType)}\r\n                            </div>\r\n                            {tuition.education === \"Education\" ? (\r\n                              <>\r\n                                <div>\r\n                                  <strong>Board:</strong>{\" \"}\r\n                                  {parseAndJoinArray(tuition.boardType)}\r\n                                </div>\r\n                                <div>\r\n                                  <strong>Medium:</strong>{\" \"}\r\n                                  {parseAndJoinArray(tuition.medium)}\r\n                                </div>\r\n                                <div>\r\n                                  <strong>Section:</strong>{\" \"}\r\n                                  {parseAndJoinArray(tuition.section)}\r\n                                </div>\r\n                                <div>\r\n                                  <strong>Subject:</strong>{\" \"}\r\n                                  {parseAndJoinArray(tuition.subject)}\r\n                                </div>\r\n                              </>\r\n                            ) : (\r\n                              <div>\r\n                                <strong>Details:</strong>{\" \"}\r\n                                {parseAndJoinArray(tuition.details)}\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n                          {tuition.timeSlots?.length > 0 && (\r\n                            <div className=\"mt-4\">\r\n                              <p className=\"font-medium\">Time Slots:</p>\r\n                              <ul className=\"list-disc ml-6 mt-1 text-sm text-muted-foreground\">\r\n                                {tuition.timeSlots.map((slot: any, i: number) => (\r\n                                  <li key={i}>\r\n                                    {slot.from} — {slot.to}\r\n                                  </li>\r\n                                ))}\r\n                              </ul>\r\n                            </div>\r\n                          )}\r\n                        </div>\r\n                      ))\r\n                    ) : (\r\n                      <p className=\"text-muted-foreground\">\r\n                        No tuition classes listed yet.\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <aside className=\"sticky top-24 rounded-2xl p-6 shadow-sm border space-y-6\">\r\n            <div className=\"relative w-full h-48 rounded-xl overflow-hidden\">\r\n              <Image\r\n                src={profileImg}\r\n                alt=\"Profile\"\r\n                fill\r\n                className=\"object-cover\"\r\n              />\r\n            </div>\r\n            <div className=\"text-center\">\r\n              <p className=\"text-2xl font-bold text-yellow-500\">★ {averageRating.toFixed(1)}</p>\r\n              <p className=\"text-sm text-muted-foreground\">{reviewCount} reviews</p>\r\n            </div>\r\n            <div className=\"space-y-3\">\r\n              <Button\r\n                variant=\"default\"\r\n                className=\"w-full flex gap-2 bg-orange-500 hover:bg-orange-600 transition-colors\"\r\n                 onClick={() => {\r\n                  if (!isStudentLoggedIn) {\r\n                    setShowLoginModal(true);\r\n                    return;\r\n                  }\r\n                  router.push(`/student/chat?userId=${data.id}`);\r\n                }}\r\n              >\r\n                <RiMessage2Line /> Send Message\r\n              </Button>\r\n              <Button\r\n                variant=\"outline\"\r\n                className={`w-full flex gap-2 hover:bg-orange-50 transition-colors ${isInWishlist ? 'bg-orange-50 text-orange-600' : ''}`}\r\n                onClick={toggleWishlist}\r\n              >\r\n                {isInWishlist ? <FaHeart className=\"text-orange-500\" /> : <FaRegHeart />}\r\n                {isInWishlist ? 'Saved to Wishlist' : 'Save to My List'}\r\n              </Button>\r\n            </div>\r\n          </aside>\r\n        </section>\r\n\r\n        <ReviewsSection\r\n          classId={id as string}\r\n          userData={userData}\r\n          onReviewSubmit={handleReviewUpdate}\r\n        />\r\n      </main>\r\n      <Footer />\r\n\r\n      <Dialog open={showLoginModal} onOpenChange={setShowLoginModal}>\r\n        <DialogContent className=\"sm:max-w-md\">\r\n          <DialogHeader>\r\n            <DialogTitle className=\"text-center\">Login Required</DialogTitle>\r\n          </DialogHeader>\r\n          <div className=\"space-y-4 py-4\">\r\n            <p className=\"text-center text-muted-foreground\">\r\n              Please login as a student to add this class to your wishlist or send a message.\r\n            </p>\r\n          </div>\r\n        </DialogContent>\r\n      </Dialog>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default InnerPage;"], "names": [], "mappings": ";;;AA+JS;;AA9JT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;;;AAzBA;;;;;;;;;;;;;;;;;;;;;;AA4BA,MAAM,OAAO;IACX;QAAE,KAAK;QAAa,OAAO;QAAa,oBAAM,6LAAC,kJAAA,CAAA,kBAAe;;;;;IAAI;IAClE;QAAE,KAAK;QAAQ,OAAO;QAAmB,oBAAM,6LAAC,iJAAA,CAAA,wBAAqB;;;;;IAAI;IACzE;QAAE,KAAK;QAAkB,OAAO;QAAkB,oBAAM,6LAAC,kJAAA,CAAA,oBAAiB;;;;;IAAI;IAC9E;QAAE,KAAK;QAAW,OAAO;QAAmB,oBAAM,6LAAC,iJAAA,CAAA,iBAAc;;;;;IAAI;CACtE;AAED,MAAM,YAAY;;IAChB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACtC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,EAAE,EAAE,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM;oDAAe;oBACnB,IAAI;wBACF,MAAM,MAAM,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,gBAAgB,EAAE,IAAI;wBAC3D,QAAQ,IAAI,IAAI;oBAClB,EAAE,OAAO,KAAK;wBACZ,QAAQ,KAAK,CAAC,gCAAgC;oBAChD;gBACF;;YAEA,MAAM;0DAAqB;oBACzB,IAAI;wBACF,MAAM,gBAAgB,MAAM,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD,EAAE;wBAC7C,iBAAiB;oBACnB,EAAE,OAAO,KAAK;wBACZ,QAAQ,KAAK,CAAC,kCAAkC;oBAClD;gBACF;;YAEA,MAAM;wDAAmB;oBACvB,IAAI;wBACF,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,sBAAmB,AAAD,EAAE,IAAc,GAAG;wBAC5D,eAAe,SAAS,KAAK;oBAC/B,EAAE,OAAO,KAAK;wBACZ,QAAQ,KAAK,CAAC,gCAAgC;oBAChD;gBACF;;YACA,qBAAqB,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD;YAE1C;YACA;YACA;QACF;8BAAG;QAAC;KAAG;IAEP,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM;yDAAoB;oBACxB,IAAI,CAAC,qBAAqB,CAAC,IAAI;oBAE/B,IAAI;wBACF,MAAM,WAAW,MAAM,CAAA,GAAA,6IAAA,CAAA,sBAAmB,AAAD,EAAE;wBAC3C,gBAAgB,SAAS,UAAU;wBACnC,IAAI,SAAS,YAAY,EAAE;4BACzB,kBAAkB,SAAS,YAAY,CAAC,EAAE;wBAC5C;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,mCAAmC;oBACnD;gBACF;;YAEA;QACF;8BAAG;QAAC;QAAmB;KAAG;IAE1B,MAAM,WAAW,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;2CAAE,CAAC,QAAe,MAAM,IAAI,CAAC,IAAI;;IAE5D,MAAM,qBAAqB;QACzB,IAAI;YACF,MAAM,gBAAgB,MAAM,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD,EAAE;YAC7C,iBAAiB;YAEjB,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,sBAAmB,AAAD,EAAE,IAAc,GAAG;YAC5D,eAAe,SAAS,KAAK;QAC/B,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,iCAAiC;QACjD;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,mBAAmB;YACtB,kBAAkB;YAClB;QACF;QAEA,IAAI;YACF,IAAI,gBAAgB,gBAAgB;gBAClC,MAAM,CAAA,GAAA,6IAAA,CAAA,qBAAkB,AAAD,EAAE;gBACzB,gBAAgB;gBAChB,kBAAkB;gBAClB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,MAAM,WAAW,MAAM,CAAA,GAAA,6IAAA,CAAA,gBAAa,AAAD,EAAE;gBACrC,gBAAgB;gBAChB,IAAI,SAAS,IAAI,EAAE,IAAI;oBACrB,kBAAkB,SAAS,IAAI,CAAC,EAAE;gBACpC;gBACA,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;QACF,EAAE,OAAO,OAAY;YACnB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;QAC/B;IACF;IAEA,IAAI,CAAC,MAAM;QACT,qBACG,6LAAC;YAAI,WAAU;sBACd,cAAA,6LAAC,oNAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;;;;;;IAGzB;IAEA,MAAM,EACJ,SAAS,EACT,QAAQ,EACR,YAAY,EAAE,EACd,aAAa,EAAE,EACf,eAAe,EAAE,EACjB,aAAa,CAAC,CAAC,EACf,iBAAiB,EAAE,EACnB,SAAS,CAAC,CAAC,EACZ,GAAG;IAEJ,MAAM,WAAW,GAAG,UAAU,CAAC,EAAE,UAAU;IAC3C,MAAM,aAAa,YAAY,eAC3B,8DAA0C,WAAW,YAAY,EAAE,GACnE;IAEJ,MAAM,UAAU,YAAY,cACxB,8DAA0C,WAAW,WAAW,EAAE,GAClE;IAEJ,qBACE;;0BACE,6LAAC,sIAAA,CAAA,UAAM;;;;;0BACP,6LAAC;gBAAK,WAAU;;kCACd,6LAAC;wBAAQ,WAAU;;0CACjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oDACJ,KAAK;oDACL,KAAI;oDACJ,IAAI;oDACJ,WAAU;;;;;;;;;;;0DAGd,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;;4DACX;4DACA,QAAQ,WAAW,4BAClB,6LAAC,kJAAA,CAAA,oBAAiB;gEAAC,WAAU;;;;;;;;;;;;kEAGjC,6LAAC;wDAAE,WAAU;kEACV,YAAY,kBAAkB;;;;;;kEAEjC,6LAAC;wDAAE,WAAU;kEACV,YAAY,YAAY;;;;;;;;;;;;;;;;;;kDAK/B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAyB;;;;;;0DACvC,6LAAC;gDAAI,WAAU;0DACZ,KAAK,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,iBAC7B,6LAAC;wDAEC,WAAW,CAAC,yEAAyE,EAAE,cAAc,MACjG,gDACA,2CACA;wDACJ,SAAS,IAAM,aAAa;;4DAE3B;4DACA;;uDARI;;;;;;;;;;0DAaX,6LAAC;gDAAI,WAAU;;oDACZ,cAAc,6BACb,6LAAC;wDAAI,WAAU;kEACZ,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,CAAC,QAAQ,GAC5C,UAAU,GAAG,CAAC,CAAC,KAAU,oBACvB,6LAAC;gEAEC,WAAU;;kFAEV,6LAAC;wEAAE,WAAU;kFACV,IAAI,UAAU;;;;;;kFAEjB,6LAAC;wEAAE,WAAU;;4EACV,IAAI,MAAM;4EAAC;4EAAI,IAAI,UAAU;;;;;;;kFAEhC,6LAAC;wEAAE,WAAU;;4EAAgC;4EAC5B,IAAI,WAAW;;;;;;;;+DAV3B;;;;sFAeT,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;oDAO1C,cAAc,wBACb,6LAAC;wDAAI,WAAU;kEACZ,WAAW,MAAM,IAAI,UAAU,CAAC,EAAE,CAAC,YAAY,GAC9C,WAAW,GAAG,CAAC,CAAC,KAAU,oBACxB,6LAAC;gEAEC,WAAU;;kFAEV,6LAAC;wEAAE,WAAU;kFACV,IAAI,KAAK;;;;;;kFAEZ,6LAAC;wEAAE,WAAU;;4EAAgC;4EACpC,IAAI,KAAK,IAAI,IAAI,EAAE,kBAAkB;4EAAG;4EAAO;4EACrD,IAAI,KAAK,IAAI,EAAE,EAAE,kBAAkB;;;;;;;;+DARjC;;;;sFAaT,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;oDAO1C,cAAc,kCACb,6LAAC;wDAAI,WAAU;kEACZ,aAAa,MAAM,GAAG,KAAK,YAAY,CAAC,EAAE,CAAC,aAAa,GACvD,aAAa,GAAG,CAAC,CAAC,MAAW,oBAC3B,6LAAC;gEAEC,WAAU;0EAEV,cAAA,6LAAC;oEAAE,WAAU;8EACV,KAAK,KAAK;;;;;;+DAJR;;;;sFAST,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;oDAO1C,cAAc,2BACb,6LAAC;wDAAI,WAAU;kEACZ,eAAe,MAAM,GAAG,IACvB,eAAe,GAAG,CAAC,CAAC,SAAc,oBAChC,6LAAC;gEAEC,WAAU;;kFAEV,6LAAC;wEAAE,WAAU;;4EAAwC;4EACzC,MAAM;;;;;;;kFAElB,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;;kGACC,6LAAC;kGAAO;;;;;;oFAAkB;oFAAE,QAAQ,SAAS,IAAI;;;;;;;0FAEnD,6LAAC;;kGACC,6LAAC;kGAAO;;;;;;oFAAwB;oFAC/B,CAAA,GAAA,uHAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,YAAY;;;;;;;4EAExC,QAAQ,SAAS,KAAK,4BACrB;;kGACE,6LAAC;;0GACC,6LAAC;0GAAO;;;;;;4FAAgB;4FACvB,CAAA,GAAA,uHAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,SAAS;;;;;;;kGAEtC,6LAAC;;0GACC,6LAAC;0GAAO;;;;;;4FAAiB;4FACxB,CAAA,GAAA,uHAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,MAAM;;;;;;;kGAEnC,6LAAC;;0GACC,6LAAC;0GAAO;;;;;;4FAAkB;4FACzB,CAAA,GAAA,uHAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,OAAO;;;;;;;kGAEpC,6LAAC;;0GACC,6LAAC;0GAAO;;;;;;4FAAkB;4FACzB,CAAA,GAAA,uHAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,OAAO;;;;;;;;6GAItC,6LAAC;;kGACC,6LAAC;kGAAO;;;;;;oFAAkB;oFACzB,CAAA,GAAA,uHAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,OAAO;;;;;;;;;;;;;oEAIvC,QAAQ,SAAS,EAAE,SAAS,mBAC3B,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAE,WAAU;0FAAc;;;;;;0FAC3B,6LAAC;gFAAG,WAAU;0FACX,QAAQ,SAAS,CAAC,GAAG,CAAC,CAAC,MAAW,kBACjC,6LAAC;;4FACE,KAAK,IAAI;4FAAC;4FAAI,KAAK,EAAE;;uFADf;;;;;;;;;;;;;;;;;+DA7CZ;;;;sFAuDT,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAUjD,6LAAC;gCAAM,WAAU;;kDACf,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4CACJ,KAAK;4CACL,KAAI;4CACJ,IAAI;4CACJ,WAAU;;;;;;;;;;;kDAGd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;;oDAAqC;oDAAG,cAAc,OAAO,CAAC;;;;;;;0DAC3E,6LAAC;gDAAE,WAAU;;oDAAiC;oDAAY;;;;;;;;;;;;;kDAE5D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,WAAU;gDACT,SAAS;oDACR,IAAI,CAAC,mBAAmB;wDACtB,kBAAkB;wDAClB;oDACF;oDACA,OAAO,IAAI,CAAC,CAAC,qBAAqB,EAAE,KAAK,EAAE,EAAE;gDAC/C;;kEAEA,6LAAC,iJAAA,CAAA,iBAAc;;;;;oDAAG;;;;;;;0DAEpB,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,WAAW,CAAC,uDAAuD,EAAE,eAAe,iCAAiC,IAAI;gDACzH,SAAS;;oDAER,6BAAe,6LAAC,kJAAA,CAAA,UAAO;wDAAC,WAAU;;;;;6EAAuB,6LAAC,kJAAA,CAAA,aAAU;;;;;oDACpE,eAAe,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;kCAM9C,6LAAC,8IAAA,CAAA,UAAc;wBACb,SAAS;wBACT,UAAU;wBACV,gBAAgB;;;;;;;;;;;;0BAGpB,6LAAC,sIAAA,CAAA,UAAM;;;;;0BAEP,6LAAC,qIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAgB,cAAc;0BAC1C,cAAA,6LAAC,qIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,6LAAC,qIAAA,CAAA,eAAY;sCACX,cAAA,6LAAC,qIAAA,CAAA,cAAW;gCAAC,WAAU;0CAAc;;;;;;;;;;;sCAEvC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAoC;;;;;;;;;;;;;;;;;;;;;;;;AAQ7D;GAjYM;;QASW,qIAAA,CAAA,YAAS;QACT,qIAAA,CAAA,YAAS;QAsDP,4JAAA,CAAA,cAAW;;;KAhExB;uCAmYS", "debugId": null}}]}