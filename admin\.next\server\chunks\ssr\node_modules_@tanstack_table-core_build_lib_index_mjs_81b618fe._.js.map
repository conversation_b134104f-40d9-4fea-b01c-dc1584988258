{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "index.mjs", "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/node_modules/%40tanstack/table-core/src/columnHelper.ts", "file://G%3A/UEST/uest_app/uest-app/admin/node_modules/%40tanstack/table-core/src/utils.ts", "file://G%3A/UEST/uest_app/uest-app/admin/node_modules/%40tanstack/table-core/src/core/cell.ts", "file://G%3A/UEST/uest_app/uest-app/admin/node_modules/%40tanstack/table-core/src/core/column.ts", "file://G%3A/UEST/uest_app/uest-app/admin/node_modules/%40tanstack/table-core/src/core/headers.ts", "file://G%3A/UEST/uest_app/uest-app/admin/node_modules/%40tanstack/table-core/src/core/row.ts", "file://G%3A/UEST/uest_app/uest-app/admin/node_modules/%40tanstack/table-core/src/features/ColumnFaceting.ts", "file://G%3A/UEST/uest_app/uest-app/admin/node_modules/%40tanstack/table-core/src/filterFns.ts", "file://G%3A/UEST/uest_app/uest-app/admin/node_modules/%40tanstack/table-core/src/features/ColumnFiltering.ts", "file://G%3A/UEST/uest_app/uest-app/admin/node_modules/%40tanstack/table-core/src/aggregationFns.ts", "file://G%3A/UEST/uest_app/uest-app/admin/node_modules/%40tanstack/table-core/src/features/ColumnGrouping.ts", "file://G%3A/UEST/uest_app/uest-app/admin/node_modules/%40tanstack/table-core/src/features/ColumnOrdering.ts", "file://G%3A/UEST/uest_app/uest-app/admin/node_modules/%40tanstack/table-core/src/features/ColumnPinning.ts", "file://G%3A/UEST/uest_app/uest-app/admin/node_modules/%40tanstack/table-core/src/features/ColumnSizing.ts", "file://G%3A/UEST/uest_app/uest-app/admin/node_modules/%40tanstack/table-core/src/features/ColumnVisibility.ts", "file://G%3A/UEST/uest_app/uest-app/admin/node_modules/%40tanstack/table-core/src/features/GlobalFaceting.ts", "file://G%3A/UEST/uest_app/uest-app/admin/node_modules/%40tanstack/table-core/src/features/GlobalFiltering.ts", "file://G%3A/UEST/uest_app/uest-app/admin/node_modules/%40tanstack/table-core/src/features/RowExpanding.ts", "file://G%3A/UEST/uest_app/uest-app/admin/node_modules/%40tanstack/table-core/src/features/RowPagination.ts", "file://G%3A/UEST/uest_app/uest-app/admin/node_modules/%40tanstack/table-core/src/features/RowPinning.ts", "file://G%3A/UEST/uest_app/uest-app/admin/node_modules/%40tanstack/table-core/src/features/RowSelection.ts", "file://G%3A/UEST/uest_app/uest-app/admin/node_modules/%40tanstack/table-core/src/sortingFns.ts", "file://G%3A/UEST/uest_app/uest-app/admin/node_modules/%40tanstack/table-core/src/features/RowSorting.ts", "file://G%3A/UEST/uest_app/uest-app/admin/node_modules/%40tanstack/table-core/src/core/table.ts", "file://G%3A/UEST/uest_app/uest-app/admin/node_modules/%40tanstack/table-core/src/utils/getCoreRowModel.ts", "file://G%3A/UEST/uest_app/uest-app/admin/node_modules/%40tanstack/table-core/src/utils/getExpandedRowModel.ts", "file://G%3A/UEST/uest_app/uest-app/admin/node_modules/%40tanstack/table-core/src/utils/getFacetedMinMaxValues.ts", "file://G%3A/UEST/uest_app/uest-app/admin/node_modules/%40tanstack/table-core/src/utils/filterRowsUtils.ts", "file://G%3A/UEST/uest_app/uest-app/admin/node_modules/%40tanstack/table-core/src/utils/getFacetedRowModel.ts", "file://G%3A/UEST/uest_app/uest-app/admin/node_modules/%40tanstack/table-core/src/utils/getFacetedUniqueValues.ts", "file://G%3A/UEST/uest_app/uest-app/admin/node_modules/%40tanstack/table-core/src/utils/getFilteredRowModel.ts", "file://G%3A/UEST/uest_app/uest-app/admin/node_modules/%40tanstack/table-core/src/utils/getGroupedRowModel.ts", "file://G%3A/UEST/uest_app/uest-app/admin/node_modules/%40tanstack/table-core/src/utils/getPaginationRowModel.ts", "file://G%3A/UEST/uest_app/uest-app/admin/node_modules/%40tanstack/table-core/src/utils/getSortedRowModel.ts"], "sourcesContent": ["import {\n  AccessorFn,\n  AccessorFnColumnDef,\n  AccessorKeyColumnDef,\n  DisplayColumnDef,\n  GroupColumnDef,\n  IdentifiedColumnDef,\n  RowData,\n} from './types'\nimport { DeepKeys, DeepValue } from './utils'\n\n// type Person = {\n//   firstName: string\n//   lastName: string\n//   age: number\n//   visits: number\n//   status: string\n//   progress: number\n//   createdAt: Date\n//   nested: {\n//     foo: [\n//       {\n//         bar: 'bar'\n//       }\n//     ]\n//     bar: { subBar: boolean }[]\n//     baz: {\n//       foo: 'foo'\n//       bar: {\n//         baz: 'baz'\n//       }\n//     }\n//   }\n// }\n\n// const test: DeepKeys<Person> = 'nested.foo.0.bar'\n// const test2: DeepKeys<Person> = 'nested.bar'\n\n// const helper = createColumnHelper<Person>()\n\n// helper.accessor('nested.foo', {\n//   cell: info => info.getValue(),\n// })\n\n// helper.accessor('nested.foo.0.bar', {\n//   cell: info => info.getValue(),\n// })\n\n// helper.accessor('nested.bar', {\n//   cell: info => info.getValue(),\n// })\n\nexport type ColumnHelper<TData extends RowData> = {\n  accessor: <\n    TAccessor extends AccessorFn<TData> | DeepKeys<TData>,\n    TValue extends TAccessor extends AccessorFn<TData, infer TReturn>\n      ? TReturn\n      : TAccessor extends DeepKeys<TData>\n        ? DeepValue<TData, TAccessor>\n        : never,\n  >(\n    accessor: TAccessor,\n    column: TAccessor extends AccessorFn<TData>\n      ? DisplayColumnDef<TData, TValue>\n      : IdentifiedColumnDef<TData, TValue>\n  ) => TAccessor extends AccessorFn<TData>\n    ? AccessorFnColumnDef<TData, TValue>\n    : AccessorKeyColumnDef<TData, TValue>\n  display: (column: DisplayColumnDef<TData>) => DisplayColumnDef<TData, unknown>\n  group: (column: GroupColumnDef<TData>) => GroupColumnDef<TData, unknown>\n}\n\nexport function createColumnHelper<\n  TData extends RowData,\n>(): ColumnHelper<TData> {\n  return {\n    accessor: (accessor, column) => {\n      return typeof accessor === 'function'\n        ? ({\n            ...column,\n            accessorFn: accessor,\n          } as any)\n        : {\n            ...column,\n            accessorKey: accessor,\n          }\n    },\n    display: column => column,\n    group: column => column,\n  }\n}\n", "import { TableOptionsResolved, TableState, Updater } from './types'\n\nexport type PartialKeys<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>\nexport type RequiredKeys<T, K extends keyof T> = Omit<T, K> &\n  Required<Pick<T, K>>\nexport type Overwrite<T, U extends { [TKey in keyof T]?: any }> = Omit<\n  T,\n  keyof U\n> &\n  U\n\nexport type UnionToIntersection<T> = (\n  T extends any ? (x: T) => any : never\n) extends (x: infer R) => any\n  ? R\n  : never\n\nexport type IsAny<T, Y, N> = 1 extends 0 & T ? Y : N\nexport type IsKnown<T, Y, N> = unknown extends T ? N : Y\n\ntype ComputeRange<\n  N extends number,\n  Result extends Array<unknown> = [],\n> = Result['length'] extends N\n  ? Result\n  : ComputeRange<N, [...Result, Result['length']]>\ntype Index40 = ComputeRange<40>[number]\n\n// Is this type a tuple?\ntype IsTuple<T> = T extends readonly any[] & { length: infer Length }\n  ? Length extends Index40\n    ? T\n    : never\n  : never\n\n// If this type is a tuple, what indices are allowed?\ntype AllowedIndexes<\n  Tuple extends ReadonlyArray<any>,\n  Keys extends number = never,\n> = Tuple extends readonly []\n  ? Keys\n  : Tuple extends readonly [infer _, ...infer Tail]\n    ? AllowedIndexes<Tail, Keys | Tail['length']>\n    : Keys\n\nexport type DeepKeys<T, TDepth extends any[] = []> = TDepth['length'] extends 5\n  ? never\n  : unknown extends T\n    ? string\n    : T extends readonly any[] & IsTuple<T>\n      ? AllowedIndexes<T> | DeepKeysPrefix<T, AllowedIndexes<T>, TDepth>\n      : T extends any[]\n        ? DeepKeys<T[number], [...TDepth, any]>\n        : T extends Date\n          ? never\n          : T extends object\n            ? (keyof T & string) | DeepKeysPrefix<T, keyof T, TDepth>\n            : never\n\ntype DeepKeysPrefix<\n  T,\n  TPrefix,\n  TDepth extends any[],\n> = TPrefix extends keyof T & (number | string)\n  ? `${TPrefix}.${DeepKeys<T[TPrefix], [...TDepth, any]> & string}`\n  : never\n\nexport type DeepValue<T, TProp> =\n  T extends Record<string | number, any>\n    ? TProp extends `${infer TBranch}.${infer TDeepProp}`\n      ? DeepValue<T[TBranch], TDeepProp>\n      : T[TProp & string]\n    : never\n\nexport type NoInfer<T> = [T][T extends any ? 0 : never]\n\nexport type Getter<TValue> = <TTValue = TValue>() => NoInfer<TTValue>\n\n///\n\nexport function functionalUpdate<T>(updater: Updater<T>, input: T): T {\n  return typeof updater === 'function'\n    ? (updater as (input: T) => T)(input)\n    : updater\n}\n\nexport function noop() {\n  //\n}\n\nexport function makeStateUpdater<K extends keyof TableState>(\n  key: K,\n  instance: unknown\n) {\n  return (updater: Updater<TableState[K]>) => {\n    ;(instance as any).setState(<TTableState>(old: TTableState) => {\n      return {\n        ...old,\n        [key]: functionalUpdate(updater, (old as any)[key]),\n      }\n    })\n  }\n}\n\ntype AnyFunction = (...args: any) => any\n\nexport function isFunction<T extends AnyFunction>(d: any): d is T {\n  return d instanceof Function\n}\n\nexport function isNumberArray(d: any): d is number[] {\n  return Array.isArray(d) && d.every(val => typeof val === 'number')\n}\n\nexport function flattenBy<TNode>(\n  arr: TNode[],\n  getChildren: (item: TNode) => TNode[]\n) {\n  const flat: TNode[] = []\n\n  const recurse = (subArr: TNode[]) => {\n    subArr.forEach(item => {\n      flat.push(item)\n      const children = getChildren(item)\n      if (children?.length) {\n        recurse(children)\n      }\n    })\n  }\n\n  recurse(arr)\n\n  return flat\n}\n\nexport function memo<TDeps extends readonly any[], TDepArgs, TResult>(\n  getDeps: (depArgs?: TDepArgs) => [...TDeps],\n  fn: (...args: NoInfer<[...TDeps]>) => TResult,\n  opts: {\n    key: any\n    debug?: () => any\n    onChange?: (result: TResult) => void\n  }\n): (depArgs?: TDepArgs) => TResult {\n  let deps: any[] = []\n  let result: TResult | undefined\n\n  return depArgs => {\n    let depTime: number\n    if (opts.key && opts.debug) depTime = Date.now()\n\n    const newDeps = getDeps(depArgs)\n\n    const depsChanged =\n      newDeps.length !== deps.length ||\n      newDeps.some((dep: any, index: number) => deps[index] !== dep)\n\n    if (!depsChanged) {\n      return result!\n    }\n\n    deps = newDeps\n\n    let resultTime: number\n    if (opts.key && opts.debug) resultTime = Date.now()\n\n    result = fn(...newDeps)\n    opts?.onChange?.(result)\n\n    if (opts.key && opts.debug) {\n      if (opts?.debug()) {\n        const depEndTime = Math.round((Date.now() - depTime!) * 100) / 100\n        const resultEndTime = Math.round((Date.now() - resultTime!) * 100) / 100\n        const resultFpsPercentage = resultEndTime / 16\n\n        const pad = (str: number | string, num: number) => {\n          str = String(str)\n          while (str.length < num) {\n            str = ' ' + str\n          }\n          return str\n        }\n\n        console.info(\n          `%c⏱ ${pad(resultEndTime, 5)} /${pad(depEndTime, 5)} ms`,\n          `\n            font-size: .6rem;\n            font-weight: bold;\n            color: hsl(${Math.max(\n              0,\n              Math.min(120 - 120 * resultFpsPercentage, 120)\n            )}deg 100% 31%);`,\n          opts?.key\n        )\n      }\n    }\n\n    return result!\n  }\n}\n\nexport function getMemoOptions(\n  tableOptions: Partial<TableOptionsResolved<any>>,\n  debugLevel:\n    | 'debugAll'\n    | 'debugCells'\n    | 'debugTable'\n    | 'debugColumns'\n    | 'debugRows'\n    | 'debugHeaders',\n  key: string,\n  onChange?: (result: any) => void\n) {\n  return {\n    debug: () => tableOptions?.debugAll ?? tableOptions[debugLevel],\n    key: process.env.NODE_ENV === 'development' && key,\n    onChange,\n  }\n}\n", "import { RowData, Cell, Column, Row, Table } from '../types'\nimport { Getter, getMemoOptions, memo } from '../utils'\n\nexport interface CellContext<TData extends RowData, TValue> {\n  cell: Cell<TData, TValue>\n  column: Column<TData, TValue>\n  getValue: Getter<TValue>\n  renderValue: Getter<TValue | null>\n  row: Row<TData>\n  table: Table<TData>\n}\n\nexport interface CoreCell<TData extends RowData, TValue> {\n  /**\n   * The associated Column object for the cell.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/cell#column)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/cells)\n   */\n  column: Column<TData, TValue>\n  /**\n   * Returns the rendering context (or props) for cell-based components like cells and aggregated cells. Use these props with your framework's `flexRender` utility to render these using the template of your choice:\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/cell#getcontext)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/cells)\n   */\n  getContext: () => CellContext<TData, TValue>\n  /**\n   * Returns the value for the cell, accessed via the associated column's accessor key or accessor function.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/cell#getvalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/cells)\n   */\n  getValue: CellContext<TData, TValue>['getValue']\n  /**\n   * The unique ID for the cell across the entire table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/cell#id)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/cells)\n   */\n  id: string\n  /**\n   * Renders the value for a cell the same as `getValue`, but will return the `renderFallbackValue` if no value is found.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/cell#rendervalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/cells)\n   */\n  renderValue: CellContext<TData, TValue>['renderValue']\n  /**\n   * The associated Row object for the cell.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/cell#row)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/cells)\n   */\n  row: Row<TData>\n}\n\nexport function createCell<TData extends RowData, TValue>(\n  table: Table<TData>,\n  row: Row<TData>,\n  column: Column<TData, TValue>,\n  columnId: string\n): Cell<TData, TValue> {\n  const getRenderValue = () =>\n    cell.getValue() ?? table.options.renderFallbackValue\n\n  const cell: CoreCell<TData, TValue> = {\n    id: `${row.id}_${column.id}`,\n    row,\n    column,\n    getValue: () => row.getValue(columnId),\n    renderValue: getRenderValue,\n    getContext: memo(\n      () => [table, column, row, cell],\n      (table, column, row, cell) => ({\n        table,\n        column,\n        row,\n        cell: cell as Cell<TData, TValue>,\n        getValue: cell.getValue,\n        renderValue: cell.renderValue,\n      }),\n      getMemoOptions(table.options, 'debugCells', 'cell.getContext')\n    ),\n  }\n\n  table._features.forEach(feature => {\n    feature.createCell?.(\n      cell as Cell<TData, TValue>,\n      column,\n      row as Row<TData>,\n      table\n    )\n  }, {})\n\n  return cell as Cell<TData, TValue>\n}\n", "import {\n  Column,\n  Table,\n  AccessorFn,\n  ColumnDef,\n  RowData,\n  ColumnDefResolved,\n} from '../types'\nimport { getMemoOptions, memo } from '../utils'\n\nexport interface CoreColumn<TData extends RowData, TValue> {\n  /**\n   * The resolved accessor function to use when extracting the value for the column from each row. Will only be defined if the column def has a valid accessor key or function defined.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#accessorfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  accessorFn?: AccessorFn<TData, TValue>\n  /**\n   * The original column def used to create the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#columndef)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  columnDef: ColumnDef<TData, TValue>\n  /**\n   * The child column (if the column is a group column). Will be an empty array if the column is not a group column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#columns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  columns: Column<TData, TValue>[]\n  /**\n   * The depth of the column (if grouped) relative to the root column def array.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#depth)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  depth: number\n  /**\n   * Returns the flattened array of this column and all child/grand-child columns for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#getflatcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  getFlatColumns: () => Column<TData, TValue>[]\n  /**\n   * Returns an array of all leaf-node columns for this column. If a column has no children, it is considered the only leaf-node column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#getleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  getLeafColumns: () => Column<TData, TValue>[]\n  /**\n   * The resolved unique identifier for the column resolved in this priority:\n      - A manual `id` property from the column def\n      - The accessor key from the column def\n      - The header string from the column def\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#id)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  id: string\n  /**\n   * The parent column for this column. Will be undefined if this is a root column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#parent)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  parent?: Column<TData, TValue>\n}\n\nexport function createColumn<TData extends RowData, TValue>(\n  table: Table<TData>,\n  columnDef: ColumnDef<TData, TValue>,\n  depth: number,\n  parent?: Column<TData, TValue>\n): Column<TData, TValue> {\n  const defaultColumn = table._getDefaultColumnDef()\n\n  const resolvedColumnDef = {\n    ...defaultColumn,\n    ...columnDef,\n  } as ColumnDefResolved<TData>\n\n  const accessorKey = resolvedColumnDef.accessorKey\n\n  let id =\n    resolvedColumnDef.id ??\n    (accessorKey\n      ? typeof String.prototype.replaceAll === 'function'\n        ? accessorKey.replaceAll('.', '_')\n        : accessorKey.replace(/\\./g, '_')\n      : undefined) ??\n    (typeof resolvedColumnDef.header === 'string'\n      ? resolvedColumnDef.header\n      : undefined)\n\n  let accessorFn: AccessorFn<TData> | undefined\n\n  if (resolvedColumnDef.accessorFn) {\n    accessorFn = resolvedColumnDef.accessorFn\n  } else if (accessorKey) {\n    // Support deep accessor keys\n    if (accessorKey.includes('.')) {\n      accessorFn = (originalRow: TData) => {\n        let result = originalRow as Record<string, any>\n\n        for (const key of accessorKey.split('.')) {\n          result = result?.[key]\n          if (process.env.NODE_ENV !== 'production' && result === undefined) {\n            console.warn(\n              `\"${key}\" in deeply nested key \"${accessorKey}\" returned undefined.`\n            )\n          }\n        }\n\n        return result\n      }\n    } else {\n      accessorFn = (originalRow: TData) =>\n        (originalRow as any)[resolvedColumnDef.accessorKey]\n    }\n  }\n\n  if (!id) {\n    if (process.env.NODE_ENV !== 'production') {\n      throw new Error(\n        resolvedColumnDef.accessorFn\n          ? `Columns require an id when using an accessorFn`\n          : `Columns require an id when using a non-string header`\n      )\n    }\n    throw new Error()\n  }\n\n  let column: CoreColumn<TData, any> = {\n    id: `${String(id)}`,\n    accessorFn,\n    parent: parent as any,\n    depth,\n    columnDef: resolvedColumnDef as ColumnDef<TData, any>,\n    columns: [],\n    getFlatColumns: memo(\n      () => [true],\n      () => {\n        return [\n          column as Column<TData, TValue>,\n          ...column.columns?.flatMap(d => d.getFlatColumns()),\n        ]\n      },\n      getMemoOptions(table.options, 'debugColumns', 'column.getFlatColumns')\n    ),\n    getLeafColumns: memo(\n      () => [table._getOrderColumnsFn()],\n      orderColumns => {\n        if (column.columns?.length) {\n          let leafColumns = column.columns.flatMap(column =>\n            column.getLeafColumns()\n          )\n\n          return orderColumns(leafColumns)\n        }\n\n        return [column as Column<TData, TValue>]\n      },\n      getMemoOptions(table.options, 'debugColumns', 'column.getLeafColumns')\n    ),\n  }\n\n  for (const feature of table._features) {\n    feature.createColumn?.(column as Column<TData, TValue>, table)\n  }\n\n  // Yes, we have to convert table to unknown, because we know more than the compiler here.\n  return column as Column<TData, TValue>\n}\n", "import {\n  <PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n  <PERSON><PERSON>,\n  HeaderGroup,\n  Table,\n  TableFeature,\n} from '../types'\nimport { getMemoOptions, memo } from '../utils'\n\nconst debug = 'debugHeaders'\n\nexport interface CoreHeaderGroup<TData extends RowData> {\n  depth: number\n  headers: Header<TData, unknown>[]\n  id: string\n}\n\nexport interface HeaderContext<TData, TValue> {\n  /**\n   * An instance of a column.\n   */\n  column: Column<TData, TValue>\n  /**\n   * An instance of a header.\n   */\n  header: Header<TData, TValue>\n  /**\n   * The table instance.\n   */\n  table: Table<TData>\n}\n\nexport interface CoreHeader<TData extends RowData, TValue> {\n  /**\n   * The col-span for the header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#colspan)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  colSpan: number\n  /**\n   * The header's associated column object.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#column)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  column: Column<TData, TValue>\n  /**\n   * The depth of the header, zero-indexed based.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#depth)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  depth: number\n  /**\n   * Returns the rendering context (or props) for column-based components like headers, footers and filters.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#getcontext)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getContext: () => HeaderContext<TData, TValue>\n  /**\n   * Returns the leaf headers hierarchically nested under this header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#getleafheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeafHeaders: () => Header<TData, unknown>[]\n  /**\n   * The header's associated header group object.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#headergroup)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  headerGroup: HeaderGroup<TData>\n  /**\n   * The unique identifier for the header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#id)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  id: string\n  /**\n   * The index for the header within the header group.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#index)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  index: number\n  /**\n   * A boolean denoting if the header is a placeholder header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#isplaceholder)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  isPlaceholder: boolean\n  /**\n   * If the header is a placeholder header, this will be a unique header ID that does not conflict with any other headers across the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#placeholderid)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  placeholderId?: string\n  /**\n   * The row-span for the header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#rowspan)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  rowSpan: number\n  /**\n   * The header's hierarchical sub/child headers. Will be empty if the header's associated column is a leaf-column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#subheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  subHeaders: Header<TData, TValue>[]\n}\n\nexport interface HeadersInstance<TData extends RowData> {\n  /**\n   * Returns all header groups for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getheadergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getHeaderGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the header groups for the left pinned columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getleftheadergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeftHeaderGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the header groups for columns that are not pinned.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getcenterheadergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getCenterHeaderGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the header groups for the right pinned columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getrightheadergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getRightHeaderGroups: () => HeaderGroup<TData>[]\n\n  /**\n   * Returns the footer groups for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getfootergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getFooterGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the footer groups for the left pinned columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getleftfootergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeftFooterGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the footer groups for columns that are not pinned.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getcenterfootergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getCenterFooterGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the footer groups for the right pinned columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getrightfootergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getRightFooterGroups: () => HeaderGroup<TData>[]\n\n  /**\n   * Returns headers for all columns in the table, including parent headers.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getflatheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getFlatHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all left pinned columns in the table, including parent headers.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getleftflatheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeftFlatHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all columns that are not pinned, including parent headers.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getcenterflatheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getCenterFlatHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all right pinned columns in the table, including parent headers.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getrightflatheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getRightFlatHeaders: () => Header<TData, unknown>[]\n\n  /**\n   * Returns headers for all leaf columns in the table, (not including parent headers).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getleafheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeafHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all left pinned leaf columns in the table, (not including parent headers).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getleftleafheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeftLeafHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all columns that are not pinned, (not including parent headers).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getcenterleafheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getCenterLeafHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all right pinned leaf columns in the table, (not including parent headers).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getrightleafheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getRightLeafHeaders: () => Header<TData, unknown>[]\n}\n\n//\n\nfunction createHeader<TData extends RowData, TValue>(\n  table: Table<TData>,\n  column: Column<TData, TValue>,\n  options: {\n    id?: string\n    isPlaceholder?: boolean\n    placeholderId?: string\n    index: number\n    depth: number\n  }\n): Header<TData, TValue> {\n  const id = options.id ?? column.id\n\n  let header: CoreHeader<TData, TValue> = {\n    id,\n    column,\n    index: options.index,\n    isPlaceholder: !!options.isPlaceholder,\n    placeholderId: options.placeholderId,\n    depth: options.depth,\n    subHeaders: [],\n    colSpan: 0,\n    rowSpan: 0,\n    headerGroup: null!,\n    getLeafHeaders: (): Header<TData, unknown>[] => {\n      const leafHeaders: Header<TData, unknown>[] = []\n\n      const recurseHeader = (h: CoreHeader<TData, any>) => {\n        if (h.subHeaders && h.subHeaders.length) {\n          h.subHeaders.map(recurseHeader)\n        }\n        leafHeaders.push(h as Header<TData, unknown>)\n      }\n\n      recurseHeader(header)\n\n      return leafHeaders\n    },\n    getContext: () => ({\n      table,\n      header: header as Header<TData, TValue>,\n      column,\n    }),\n  }\n\n  table._features.forEach(feature => {\n    feature.createHeader?.(header as Header<TData, TValue>, table)\n  })\n\n  return header as Header<TData, TValue>\n}\n\nexport const Headers: TableFeature = {\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    // Header Groups\n\n    table.getHeaderGroups = memo(\n      () => [\n        table.getAllColumns(),\n        table.getVisibleLeafColumns(),\n        table.getState().columnPinning.left,\n        table.getState().columnPinning.right,\n      ],\n      (allColumns, leafColumns, left, right) => {\n        const leftColumns =\n          left\n            ?.map(columnId => leafColumns.find(d => d.id === columnId)!)\n            .filter(Boolean) ?? []\n\n        const rightColumns =\n          right\n            ?.map(columnId => leafColumns.find(d => d.id === columnId)!)\n            .filter(Boolean) ?? []\n\n        const centerColumns = leafColumns.filter(\n          column => !left?.includes(column.id) && !right?.includes(column.id)\n        )\n\n        const headerGroups = buildHeaderGroups(\n          allColumns,\n          [...leftColumns, ...centerColumns, ...rightColumns],\n          table\n        )\n\n        return headerGroups\n      },\n      getMemoOptions(table.options, debug, 'getHeaderGroups')\n    )\n\n    table.getCenterHeaderGroups = memo(\n      () => [\n        table.getAllColumns(),\n        table.getVisibleLeafColumns(),\n        table.getState().columnPinning.left,\n        table.getState().columnPinning.right,\n      ],\n      (allColumns, leafColumns, left, right) => {\n        leafColumns = leafColumns.filter(\n          column => !left?.includes(column.id) && !right?.includes(column.id)\n        )\n        return buildHeaderGroups(allColumns, leafColumns, table, 'center')\n      },\n      getMemoOptions(table.options, debug, 'getCenterHeaderGroups')\n    )\n\n    table.getLeftHeaderGroups = memo(\n      () => [\n        table.getAllColumns(),\n        table.getVisibleLeafColumns(),\n        table.getState().columnPinning.left,\n      ],\n      (allColumns, leafColumns, left) => {\n        const orderedLeafColumns =\n          left\n            ?.map(columnId => leafColumns.find(d => d.id === columnId)!)\n            .filter(Boolean) ?? []\n\n        return buildHeaderGroups(allColumns, orderedLeafColumns, table, 'left')\n      },\n      getMemoOptions(table.options, debug, 'getLeftHeaderGroups')\n    )\n\n    table.getRightHeaderGroups = memo(\n      () => [\n        table.getAllColumns(),\n        table.getVisibleLeafColumns(),\n        table.getState().columnPinning.right,\n      ],\n      (allColumns, leafColumns, right) => {\n        const orderedLeafColumns =\n          right\n            ?.map(columnId => leafColumns.find(d => d.id === columnId)!)\n            .filter(Boolean) ?? []\n\n        return buildHeaderGroups(allColumns, orderedLeafColumns, table, 'right')\n      },\n      getMemoOptions(table.options, debug, 'getRightHeaderGroups')\n    )\n\n    // Footer Groups\n\n    table.getFooterGroups = memo(\n      () => [table.getHeaderGroups()],\n      headerGroups => {\n        return [...headerGroups].reverse()\n      },\n      getMemoOptions(table.options, debug, 'getFooterGroups')\n    )\n\n    table.getLeftFooterGroups = memo(\n      () => [table.getLeftHeaderGroups()],\n      headerGroups => {\n        return [...headerGroups].reverse()\n      },\n      getMemoOptions(table.options, debug, 'getLeftFooterGroups')\n    )\n\n    table.getCenterFooterGroups = memo(\n      () => [table.getCenterHeaderGroups()],\n      headerGroups => {\n        return [...headerGroups].reverse()\n      },\n      getMemoOptions(table.options, debug, 'getCenterFooterGroups')\n    )\n\n    table.getRightFooterGroups = memo(\n      () => [table.getRightHeaderGroups()],\n      headerGroups => {\n        return [...headerGroups].reverse()\n      },\n      getMemoOptions(table.options, debug, 'getRightFooterGroups')\n    )\n\n    // Flat Headers\n\n    table.getFlatHeaders = memo(\n      () => [table.getHeaderGroups()],\n      headerGroups => {\n        return headerGroups\n          .map(headerGroup => {\n            return headerGroup.headers\n          })\n          .flat()\n      },\n      getMemoOptions(table.options, debug, 'getFlatHeaders')\n    )\n\n    table.getLeftFlatHeaders = memo(\n      () => [table.getLeftHeaderGroups()],\n      left => {\n        return left\n          .map(headerGroup => {\n            return headerGroup.headers\n          })\n          .flat()\n      },\n      getMemoOptions(table.options, debug, 'getLeftFlatHeaders')\n    )\n\n    table.getCenterFlatHeaders = memo(\n      () => [table.getCenterHeaderGroups()],\n      left => {\n        return left\n          .map(headerGroup => {\n            return headerGroup.headers\n          })\n          .flat()\n      },\n      getMemoOptions(table.options, debug, 'getCenterFlatHeaders')\n    )\n\n    table.getRightFlatHeaders = memo(\n      () => [table.getRightHeaderGroups()],\n      left => {\n        return left\n          .map(headerGroup => {\n            return headerGroup.headers\n          })\n          .flat()\n      },\n      getMemoOptions(table.options, debug, 'getRightFlatHeaders')\n    )\n\n    // Leaf Headers\n\n    table.getCenterLeafHeaders = memo(\n      () => [table.getCenterFlatHeaders()],\n      flatHeaders => {\n        return flatHeaders.filter(header => !header.subHeaders?.length)\n      },\n      getMemoOptions(table.options, debug, 'getCenterLeafHeaders')\n    )\n\n    table.getLeftLeafHeaders = memo(\n      () => [table.getLeftFlatHeaders()],\n      flatHeaders => {\n        return flatHeaders.filter(header => !header.subHeaders?.length)\n      },\n      getMemoOptions(table.options, debug, 'getLeftLeafHeaders')\n    )\n\n    table.getRightLeafHeaders = memo(\n      () => [table.getRightFlatHeaders()],\n      flatHeaders => {\n        return flatHeaders.filter(header => !header.subHeaders?.length)\n      },\n      getMemoOptions(table.options, debug, 'getRightLeafHeaders')\n    )\n\n    table.getLeafHeaders = memo(\n      () => [\n        table.getLeftHeaderGroups(),\n        table.getCenterHeaderGroups(),\n        table.getRightHeaderGroups(),\n      ],\n      (left, center, right) => {\n        return [\n          ...(left[0]?.headers ?? []),\n          ...(center[0]?.headers ?? []),\n          ...(right[0]?.headers ?? []),\n        ]\n          .map(header => {\n            return header.getLeafHeaders()\n          })\n          .flat()\n      },\n      getMemoOptions(table.options, debug, 'getLeafHeaders')\n    )\n  },\n}\n\nexport function buildHeaderGroups<TData extends RowData>(\n  allColumns: Column<TData, unknown>[],\n  columnsToGroup: Column<TData, unknown>[],\n  table: Table<TData>,\n  headerFamily?: 'center' | 'left' | 'right'\n) {\n  // Find the max depth of the columns:\n  // build the leaf column row\n  // build each buffer row going up\n  //    placeholder for non-existent level\n  //    real column for existing level\n\n  let maxDepth = 0\n\n  const findMaxDepth = (columns: Column<TData, unknown>[], depth = 1) => {\n    maxDepth = Math.max(maxDepth, depth)\n\n    columns\n      .filter(column => column.getIsVisible())\n      .forEach(column => {\n        if (column.columns?.length) {\n          findMaxDepth(column.columns, depth + 1)\n        }\n      }, 0)\n  }\n\n  findMaxDepth(allColumns)\n\n  let headerGroups: HeaderGroup<TData>[] = []\n\n  const createHeaderGroup = (\n    headersToGroup: Header<TData, unknown>[],\n    depth: number\n  ) => {\n    // The header group we are creating\n    const headerGroup: HeaderGroup<TData> = {\n      depth,\n      id: [headerFamily, `${depth}`].filter(Boolean).join('_'),\n      headers: [],\n    }\n\n    // The parent columns we're going to scan next\n    const pendingParentHeaders: Header<TData, unknown>[] = []\n\n    // Scan each column for parents\n    headersToGroup.forEach(headerToGroup => {\n      // What is the latest (last) parent column?\n\n      const latestPendingParentHeader = [...pendingParentHeaders].reverse()[0]\n\n      const isLeafHeader = headerToGroup.column.depth === headerGroup.depth\n\n      let column: Column<TData, unknown>\n      let isPlaceholder = false\n\n      if (isLeafHeader && headerToGroup.column.parent) {\n        // The parent header is new\n        column = headerToGroup.column.parent\n      } else {\n        // The parent header is repeated\n        column = headerToGroup.column\n        isPlaceholder = true\n      }\n\n      if (\n        latestPendingParentHeader &&\n        latestPendingParentHeader?.column === column\n      ) {\n        // This column is repeated. Add it as a sub header to the next batch\n        latestPendingParentHeader.subHeaders.push(headerToGroup)\n      } else {\n        // This is a new header. Let's create it\n        const header = createHeader(table, column, {\n          id: [headerFamily, depth, column.id, headerToGroup?.id]\n            .filter(Boolean)\n            .join('_'),\n          isPlaceholder,\n          placeholderId: isPlaceholder\n            ? `${pendingParentHeaders.filter(d => d.column === column).length}`\n            : undefined,\n          depth,\n          index: pendingParentHeaders.length,\n        })\n\n        // Add the headerToGroup as a subHeader of the new header\n        header.subHeaders.push(headerToGroup)\n        // Add the new header to the pendingParentHeaders to get grouped\n        // in the next batch\n        pendingParentHeaders.push(header)\n      }\n\n      headerGroup.headers.push(headerToGroup)\n      headerToGroup.headerGroup = headerGroup\n    })\n\n    headerGroups.push(headerGroup)\n\n    if (depth > 0) {\n      createHeaderGroup(pendingParentHeaders, depth - 1)\n    }\n  }\n\n  const bottomHeaders = columnsToGroup.map((column, index) =>\n    createHeader(table, column, {\n      depth: maxDepth,\n      index,\n    })\n  )\n\n  createHeaderGroup(bottomHeaders, maxDepth - 1)\n\n  headerGroups.reverse()\n\n  // headerGroups = headerGroups.filter(headerGroup => {\n  //   return !headerGroup.headers.every(header => header.isPlaceholder)\n  // })\n\n  const recurseHeadersForSpans = (\n    headers: Header<TData, unknown>[]\n  ): { colSpan: number; rowSpan: number }[] => {\n    const filteredHeaders = headers.filter(header =>\n      header.column.getIsVisible()\n    )\n\n    return filteredHeaders.map(header => {\n      let colSpan = 0\n      let rowSpan = 0\n      let childRowSpans = [0]\n\n      if (header.subHeaders && header.subHeaders.length) {\n        childRowSpans = []\n\n        recurseHeadersForSpans(header.subHeaders).forEach(\n          ({ colSpan: childColSpan, rowSpan: childRowSpan }) => {\n            colSpan += childColSpan\n            childRowSpans.push(childRowSpan)\n          }\n        )\n      } else {\n        colSpan = 1\n      }\n\n      const minChildRowSpan = Math.min(...childRowSpans)\n      rowSpan = rowSpan + minChildRowSpan\n\n      header.colSpan = colSpan\n      header.rowSpan = rowSpan\n\n      return { colSpan, rowSpan }\n    })\n  }\n\n  recurseHeadersForSpans(headerGroups[0]?.headers ?? [])\n\n  return headerGroups\n}\n", "import { RowData, Cell, Row, Table } from '../types'\nimport { flattenBy, getMemoOptions, memo } from '../utils'\nimport { createCell } from './cell'\n\nexport interface CoreRow<TData extends RowData> {\n  _getAllCellsByColumnId: () => Record<string, Cell<TData, unknown>>\n  _uniqueValuesCache: Record<string, unknown>\n  _valuesCache: Record<string, unknown>\n  /**\n   * The depth of the row (if nested or grouped) relative to the root row array.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#depth)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  depth: number\n  /**\n   * Returns all of the cells for the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getallcells)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getAllCells: () => Cell<TData, unknown>[]\n  /**\n   * Returns the leaf rows for the row, not including any parent rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getleafrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getLeafRows: () => Row<TData>[]\n  /**\n   * Returns the parent row for the row, if it exists.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getparentrow)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getParentRow: () => Row<TData> | undefined\n  /**\n   * Returns the parent rows for the row, all the way up to a root row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getparentrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getParentRows: () => Row<TData>[]\n  /**\n   * Returns a unique array of values from the row for a given columnId.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getuniquevalues)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getUniqueValues: <TValue>(columnId: string) => TValue[]\n  /**\n   * Returns the value from the row for a given columnId.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getvalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getValue: <TValue>(columnId: string) => TValue\n  /**\n   * The resolved unique identifier for the row resolved via the `options.getRowId` option. Defaults to the row's index (or relative index if it is a subRow).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#id)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  id: string\n  /**\n   * The index of the row within its parent array (or the root data array).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#index)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  index: number\n  /**\n   * The original row object provided to the table. If the row is a grouped row, the original row object will be the first original in the group.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#original)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  original: TData\n  /**\n   * An array of the original subRows as returned by the `options.getSubRows` option.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#originalsubrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  originalSubRows?: TData[]\n  /**\n   * If nested, this row's parent row id.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#parentid)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  parentId?: string\n  /**\n   * Renders the value for the row in a given columnId the same as `getValue`, but will return the `renderFallbackValue` if no value is found.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#rendervalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  renderValue: <TValue>(columnId: string) => TValue\n  /**\n   * An array of subRows for the row as returned and created by the `options.getSubRows` option.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#subrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  subRows: Row<TData>[]\n}\n\nexport const createRow = <TData extends RowData>(\n  table: Table<TData>,\n  id: string,\n  original: TData,\n  rowIndex: number,\n  depth: number,\n  subRows?: Row<TData>[],\n  parentId?: string\n): Row<TData> => {\n  let row: CoreRow<TData> = {\n    id,\n    index: rowIndex,\n    original,\n    depth,\n    parentId,\n    _valuesCache: {},\n    _uniqueValuesCache: {},\n    getValue: columnId => {\n      if (row._valuesCache.hasOwnProperty(columnId)) {\n        return row._valuesCache[columnId]\n      }\n\n      const column = table.getColumn(columnId)\n\n      if (!column?.accessorFn) {\n        return undefined\n      }\n\n      row._valuesCache[columnId] = column.accessorFn(\n        row.original as TData,\n        rowIndex\n      )\n\n      return row._valuesCache[columnId] as any\n    },\n    getUniqueValues: columnId => {\n      if (row._uniqueValuesCache.hasOwnProperty(columnId)) {\n        return row._uniqueValuesCache[columnId]\n      }\n\n      const column = table.getColumn(columnId)\n\n      if (!column?.accessorFn) {\n        return undefined\n      }\n\n      if (!column.columnDef.getUniqueValues) {\n        row._uniqueValuesCache[columnId] = [row.getValue(columnId)]\n        return row._uniqueValuesCache[columnId]\n      }\n\n      row._uniqueValuesCache[columnId] = column.columnDef.getUniqueValues(\n        row.original as TData,\n        rowIndex\n      )\n\n      return row._uniqueValuesCache[columnId] as any\n    },\n    renderValue: columnId =>\n      row.getValue(columnId) ?? table.options.renderFallbackValue,\n    subRows: subRows ?? [],\n    getLeafRows: () => flattenBy(row.subRows, d => d.subRows),\n    getParentRow: () =>\n      row.parentId ? table.getRow(row.parentId, true) : undefined,\n    getParentRows: () => {\n      let parentRows: Row<TData>[] = []\n      let currentRow = row\n      while (true) {\n        const parentRow = currentRow.getParentRow()\n        if (!parentRow) break\n        parentRows.push(parentRow)\n        currentRow = parentRow\n      }\n      return parentRows.reverse()\n    },\n    getAllCells: memo(\n      () => [table.getAllLeafColumns()],\n      leafColumns => {\n        return leafColumns.map(column => {\n          return createCell(table, row as Row<TData>, column, column.id)\n        })\n      },\n      getMemoOptions(table.options, 'debugRows', 'getAllCells')\n    ),\n\n    _getAllCellsByColumnId: memo(\n      () => [row.getAllCells()],\n      allCells => {\n        return allCells.reduce(\n          (acc, cell) => {\n            acc[cell.column.id] = cell\n            return acc\n          },\n          {} as Record<string, Cell<TData, unknown>>\n        )\n      },\n      getMemoOptions(table.options, 'debugRows', 'getAllCellsByColumnId')\n    ),\n  }\n\n  for (let i = 0; i < table._features.length; i++) {\n    const feature = table._features[i]\n    feature?.createRow?.(row as Row<TData>, table)\n  }\n\n  return row as Row<TData>\n}\n", "import { RowModel } from '..'\nimport { Column, RowData, Table, TableFeature } from '../types'\n\nexport interface FacetedColumn<TData extends RowData> {\n  _getFacetedMinMaxValues?: () => undefined | [number, number]\n  _getFacetedRowModel?: () => RowModel<TData>\n  _getFacetedUniqueValues?: () => Map<any, number>\n  /**\n   * A function that **computes and returns** a min/max tuple derived from `column.getFacetedRowModel`. Useful for displaying faceted result values.\n   * > ⚠️ Requires that you pass a valid `getFacetedMinMaxValues` function to `options.getFacetedMinMaxValues`. A default implementation is provided via the exported `getFacetedMinMaxValues` function.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-faceting#getfacetedminmaxvalues)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-faceting)\n   */\n  getFacetedMinMaxValues: () => undefined | [number, number]\n  /**\n   * Returns the row model with all other column filters applied, excluding its own filter. Useful for displaying faceted result counts.\n   * > ⚠️ Requires that you pass a valid `getFacetedRowModel` function to `options.facetedRowModel`. A default implementation is provided via the exported `getFacetedRowModel` function.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-faceting#getfacetedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-faceting)\n   */\n  getFacetedRowModel: () => RowModel<TData>\n  /**\n   * A function that **computes and returns** a `Map` of unique values and their occurrences derived from `column.getFacetedRowModel`. Useful for displaying faceted result values.\n   * > ⚠️ Requires that you pass a valid `getFacetedUniqueValues` function to `options.getFacetedUniqueValues`. A default implementation is provided via the exported `getFacetedUniqueValues` function.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-faceting#getfaceteduniquevalues)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-faceting)\n   */\n  getFacetedUniqueValues: () => Map<any, number>\n}\n\nexport interface FacetedOptions<TData extends RowData> {\n  getFacetedMinMaxValues?: (\n    table: Table<TData>,\n    columnId: string\n  ) => () => undefined | [number, number]\n  getFacetedRowModel?: (\n    table: Table<TData>,\n    columnId: string\n  ) => () => RowModel<TData>\n  getFacetedUniqueValues?: (\n    table: Table<TData>,\n    columnId: string\n  ) => () => Map<any, number>\n}\n\n//\n\nexport const ColumnFaceting: TableFeature = {\n  createColumn: <TData extends RowData>(\n    column: Column<TData, unknown>,\n    table: Table<TData>\n  ): void => {\n    column._getFacetedRowModel =\n      table.options.getFacetedRowModel &&\n      table.options.getFacetedRowModel(table, column.id)\n    column.getFacetedRowModel = () => {\n      if (!column._getFacetedRowModel) {\n        return table.getPreFilteredRowModel()\n      }\n\n      return column._getFacetedRowModel()\n    }\n    column._getFacetedUniqueValues =\n      table.options.getFacetedUniqueValues &&\n      table.options.getFacetedUniqueValues(table, column.id)\n    column.getFacetedUniqueValues = () => {\n      if (!column._getFacetedUniqueValues) {\n        return new Map()\n      }\n\n      return column._getFacetedUniqueValues()\n    }\n    column._getFacetedMinMaxValues =\n      table.options.getFacetedMinMaxValues &&\n      table.options.getFacetedMinMaxValues(table, column.id)\n    column.getFacetedMinMaxValues = () => {\n      if (!column._getFacetedMinMaxValues) {\n        return undefined\n      }\n\n      return column._getFacetedMinMaxValues()\n    }\n  },\n}\n", "import { FilterFn } from './features/ColumnFiltering'\n\nconst includesString: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: string\n) => {\n  const search = filterValue?.toString()?.toLowerCase()\n  return Boolean(\n    row\n      .getValue<string | null>(columnId)\n      ?.toString()\n      ?.toLowerCase()\n      ?.includes(search)\n  )\n}\n\nincludesString.autoRemove = (val: any) => testFalsey(val)\n\nconst includesStringSensitive: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: string\n) => {\n  return Boolean(\n    row.getValue<string | null>(columnId)?.toString()?.includes(filterValue)\n  )\n}\n\nincludesStringSensitive.autoRemove = (val: any) => testFalsey(val)\n\nconst equalsString: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: string\n) => {\n  return (\n    row.getValue<string | null>(columnId)?.toString()?.toLowerCase() ===\n    filterValue?.toLowerCase()\n  )\n}\n\nequalsString.autoRemove = (val: any) => testFalsey(val)\n\nconst arrIncludes: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: unknown\n) => {\n  return row.getValue<unknown[]>(columnId)?.includes(filterValue)\n}\n\narrIncludes.autoRemove = (val: any) => testFalsey(val)\n\nconst arrIncludesAll: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: unknown[]\n) => {\n  return !filterValue.some(\n    val => !row.getValue<unknown[]>(columnId)?.includes(val)\n  )\n}\n\narrIncludesAll.autoRemove = (val: any) => testFalsey(val) || !val?.length\n\nconst arrIncludesSome: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: unknown[]\n) => {\n  return filterValue.some(val =>\n    row.getValue<unknown[]>(columnId)?.includes(val)\n  )\n}\n\narrIncludesSome.autoRemove = (val: any) => testFalsey(val) || !val?.length\n\nconst equals: FilterFn<any> = (row, columnId: string, filterValue: unknown) => {\n  return row.getValue(columnId) === filterValue\n}\n\nequals.autoRemove = (val: any) => testFalsey(val)\n\nconst weakEquals: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: unknown\n) => {\n  return row.getValue(columnId) == filterValue\n}\n\nweakEquals.autoRemove = (val: any) => testFalsey(val)\n\nconst inNumberRange: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: [number, number]\n) => {\n  let [min, max] = filterValue\n\n  const rowValue = row.getValue<number>(columnId)\n  return rowValue >= min && rowValue <= max\n}\n\ninNumberRange.resolveFilterValue = (val: [any, any]) => {\n  let [unsafeMin, unsafeMax] = val\n\n  let parsedMin =\n    typeof unsafeMin !== 'number' ? parseFloat(unsafeMin as string) : unsafeMin\n  let parsedMax =\n    typeof unsafeMax !== 'number' ? parseFloat(unsafeMax as string) : unsafeMax\n\n  let min =\n    unsafeMin === null || Number.isNaN(parsedMin) ? -Infinity : parsedMin\n  let max = unsafeMax === null || Number.isNaN(parsedMax) ? Infinity : parsedMax\n\n  if (min > max) {\n    const temp = min\n    min = max\n    max = temp\n  }\n\n  return [min, max] as const\n}\n\ninNumberRange.autoRemove = (val: any) =>\n  testFalsey(val) || (testFalsey(val[0]) && testFalsey(val[1]))\n\n// Export\n\nexport const filterFns = {\n  includesString,\n  includesStringSensitive,\n  equalsString,\n  arrIncludes,\n  arrIncludesAll,\n  arrIncludesSome,\n  equals,\n  weakEquals,\n  inNumberRange,\n}\n\nexport type BuiltInFilterFn = keyof typeof filterFns\n\n// Utils\n\nfunction testFalsey(val: any) {\n  return val === undefined || val === null || val === ''\n}\n", "import { RowModel } from '..'\nimport { BuiltInFilterFn, filterFns } from '../filterFns'\nimport {\n  Column,\n  FilterFns,\n  FilterMeta,\n  OnChangeFn,\n  Row,\n  RowData,\n  Table,\n  TableFeature,\n  Updater,\n} from '../types'\nimport { functionalUpdate, isFunction, makeStateUpdater } from '../utils'\n\nexport interface ColumnFiltersTableState {\n  columnFilters: ColumnFiltersState\n}\n\nexport type ColumnFiltersState = ColumnFilter[]\n\nexport interface ColumnFilter {\n  id: string\n  value: unknown\n}\n\nexport interface ResolvedColumnFilter<TData extends RowData> {\n  filterFn: FilterFn<TData>\n  id: string\n  resolvedValue: unknown\n}\n\nexport interface FilterFn<TData extends RowData> {\n  (\n    row: Row<TData>,\n    columnId: string,\n    filterValue: any,\n    addMeta: (meta: FilterMeta) => void\n  ): boolean\n  autoRemove?: ColumnFilterAutoRemoveTestFn<TData>\n  resolveFilterValue?: TransformFilterValueFn<TData>\n}\n\nexport type TransformFilterValueFn<TData extends RowData> = (\n  value: any,\n  column?: Column<TData, unknown>\n) => unknown\n\nexport type ColumnFilterAutoRemoveTestFn<TData extends RowData> = (\n  value: any,\n  column?: Column<TData, unknown>\n) => boolean\n\nexport type CustomFilterFns<TData extends RowData> = Record<\n  string,\n  FilterFn<TData>\n>\n\nexport type FilterFnOption<TData extends RowData> =\n  | 'auto'\n  | BuiltInFilterFn\n  | keyof FilterFns\n  | FilterFn<TData>\n\nexport interface ColumnFiltersColumnDef<TData extends RowData> {\n  /**\n   * Enables/disables the **column** filter for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#enablecolumnfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  enableColumnFilter?: boolean\n  /**\n   * The filter function to use with this column. Can be the name of a built-in filter function or a custom filter function.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#filterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  filterFn?: FilterFnOption<TData>\n}\n\nexport interface ColumnFiltersColumn<TData extends RowData> {\n  /**\n   * Returns an automatically calculated filter function for the column based off of the columns first known value.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getautofilterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getAutoFilterFn: () => FilterFn<TData> | undefined\n  /**\n   * Returns whether or not the column can be **column** filtered.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getcanfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getCanFilter: () => boolean\n  /**\n   * Returns the filter function (either user-defined or automatic, depending on configuration) for the columnId specified.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getfilterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getFilterFn: () => FilterFn<TData> | undefined\n  /**\n   * Returns the index (including `-1`) of the column filter in the table's `state.columnFilters` array.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getfilterindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getFilterIndex: () => number\n  /**\n   * Returns the current filter value for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getfiltervalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getFilterValue: () => unknown\n  /**\n   * Returns whether or not the column is currently filtered.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getisfiltered)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getIsFiltered: () => boolean\n  /**\n   * A function that sets the current filter value for the column. You can pass it a value or an updater function for immutability-safe operations on existing values.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#setfiltervalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  setFilterValue: (updater: Updater<any>) => void\n}\n\nexport interface ColumnFiltersRow<TData extends RowData> {\n  /**\n   * The column filters map for the row. This object tracks whether a row is passing/failing specific filters by their column ID.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#columnfilters)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  columnFilters: Record<string, boolean>\n  /**\n   * The column filters meta map for the row. This object tracks any filter meta for a row as optionally provided during the filtering process.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#columnfiltersmeta)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  columnFiltersMeta: Record<string, FilterMeta>\n}\n\ninterface ColumnFiltersOptionsBase<TData extends RowData> {\n  /**\n   * Enables/disables **column** filtering for all columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#enablecolumnfilters)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  enableColumnFilters?: boolean\n  /**\n   * Enables/disables all filtering for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#enablefilters)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  enableFilters?: boolean\n  /**\n   * By default, filtering is done from parent rows down (so if a parent row is filtered out, all of its children will be filtered out as well). Setting this option to `true` will cause filtering to be done from leaf rows up (which means parent rows will be included so long as one of their child or grand-child rows is also included).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#filterfromleafrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  filterFromLeafRows?: boolean\n  /**\n   * If provided, this function is called **once** per table and should return a **new function** which will calculate and return the row model for the table when it's filtered.\n   * - For server-side filtering, this function is unnecessary and can be ignored since the server should already return the filtered row model.\n   * - For client-side filtering, this function is required. A default implementation is provided via any table adapter's `{ getFilteredRowModel }` export.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getfilteredrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getFilteredRowModel?: (table: Table<any>) => () => RowModel<any>\n  /**\n   * Disables the `getFilteredRowModel` from being used to filter data. This may be useful if your table needs to dynamically support both client-side and server-side filtering.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#manualfiltering)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  manualFiltering?: boolean\n  /**\n   * By default, filtering is done for all rows (max depth of 100), no matter if they are root level parent rows or the child leaf rows of a parent row. Setting this option to `0` will cause filtering to only be applied to the root level parent rows, with all sub-rows remaining unfiltered. Similarly, setting this option to `1` will cause filtering to only be applied to child leaf rows 1 level deep, and so on.\n\n   * This is useful for situations where you want a row's entire child hierarchy to be visible regardless of the applied filter.\n    * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#maxleafrowfilterdepth)\n    * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  maxLeafRowFilterDepth?: number\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnFilters` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#oncolumnfilterschange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  onColumnFiltersChange?: OnChangeFn<ColumnFiltersState>\n}\n\ntype ResolvedFilterFns = keyof FilterFns extends never\n  ? {\n      filterFns?: Record<string, FilterFn<any>>\n    }\n  : {\n      filterFns: Record<keyof FilterFns, FilterFn<any>>\n    }\n\nexport interface ColumnFiltersOptions<TData extends RowData>\n  extends ColumnFiltersOptionsBase<TData>,\n    ResolvedFilterFns {}\n\nexport interface ColumnFiltersInstance<TData extends RowData> {\n  _getFilteredRowModel?: () => RowModel<TData>\n  /**\n   * Returns the row model for the table after **column** filtering has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getfilteredrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getFilteredRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model for the table before any **column** filtering has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getprefilteredrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getPreFilteredRowModel: () => RowModel<TData>\n  /**\n   * Resets the **columnFilters** state to `initialState.columnFilters`, or `true` can be passed to force a default blank state reset to `[]`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#resetcolumnfilters)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  resetColumnFilters: (defaultState?: boolean) => void\n  /**\n   * Resets the **globalFilter** state to `initialState.globalFilter`, or `true` can be passed to force a default blank state reset to `undefined`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#resetglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  resetGlobalFilter: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.columnFilters` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#setcolumnfilters)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  setColumnFilters: (updater: Updater<ColumnFiltersState>) => void\n  /**\n   * Sets or updates the `state.globalFilter` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#setglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  setGlobalFilter: (updater: Updater<any>) => void\n}\n\n//\n\nexport const ColumnFiltering: TableFeature = {\n  getDefaultColumnDef: <\n    TData extends RowData,\n  >(): ColumnFiltersColumnDef<TData> => {\n    return {\n      filterFn: 'auto',\n    }\n  },\n\n  getInitialState: (state): ColumnFiltersTableState => {\n    return {\n      columnFilters: [],\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ColumnFiltersOptions<TData> => {\n    return {\n      onColumnFiltersChange: makeStateUpdater('columnFilters', table),\n      filterFromLeafRows: false,\n      maxLeafRowFilterDepth: 100,\n    } as ColumnFiltersOptions<TData>\n  },\n\n  createColumn: <TData extends RowData>(\n    column: Column<TData, unknown>,\n    table: Table<TData>\n  ): void => {\n    column.getAutoFilterFn = () => {\n      const firstRow = table.getCoreRowModel().flatRows[0]\n\n      const value = firstRow?.getValue(column.id)\n\n      if (typeof value === 'string') {\n        return filterFns.includesString\n      }\n\n      if (typeof value === 'number') {\n        return filterFns.inNumberRange\n      }\n\n      if (typeof value === 'boolean') {\n        return filterFns.equals\n      }\n\n      if (value !== null && typeof value === 'object') {\n        return filterFns.equals\n      }\n\n      if (Array.isArray(value)) {\n        return filterFns.arrIncludes\n      }\n\n      return filterFns.weakEquals\n    }\n    column.getFilterFn = () => {\n      return isFunction(column.columnDef.filterFn)\n        ? column.columnDef.filterFn\n        : column.columnDef.filterFn === 'auto'\n          ? column.getAutoFilterFn()\n          : // @ts-ignore\n            table.options.filterFns?.[column.columnDef.filterFn as string] ??\n            filterFns[column.columnDef.filterFn as BuiltInFilterFn]\n    }\n    column.getCanFilter = () => {\n      return (\n        (column.columnDef.enableColumnFilter ?? true) &&\n        (table.options.enableColumnFilters ?? true) &&\n        (table.options.enableFilters ?? true) &&\n        !!column.accessorFn\n      )\n    }\n\n    column.getIsFiltered = () => column.getFilterIndex() > -1\n\n    column.getFilterValue = () =>\n      table.getState().columnFilters?.find(d => d.id === column.id)?.value\n\n    column.getFilterIndex = () =>\n      table.getState().columnFilters?.findIndex(d => d.id === column.id) ?? -1\n\n    column.setFilterValue = value => {\n      table.setColumnFilters(old => {\n        const filterFn = column.getFilterFn()\n        const previousFilter = old?.find(d => d.id === column.id)\n\n        const newFilter = functionalUpdate(\n          value,\n          previousFilter ? previousFilter.value : undefined\n        )\n\n        //\n        if (\n          shouldAutoRemoveFilter(filterFn as FilterFn<TData>, newFilter, column)\n        ) {\n          return old?.filter(d => d.id !== column.id) ?? []\n        }\n\n        const newFilterObj = { id: column.id, value: newFilter }\n\n        if (previousFilter) {\n          return (\n            old?.map(d => {\n              if (d.id === column.id) {\n                return newFilterObj\n              }\n              return d\n            }) ?? []\n          )\n        }\n\n        if (old?.length) {\n          return [...old, newFilterObj]\n        }\n\n        return [newFilterObj]\n      })\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    _table: Table<TData>\n  ): void => {\n    row.columnFilters = {}\n    row.columnFiltersMeta = {}\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setColumnFilters = (updater: Updater<ColumnFiltersState>) => {\n      const leafColumns = table.getAllLeafColumns()\n\n      const updateFn = (old: ColumnFiltersState) => {\n        return functionalUpdate(updater, old)?.filter(filter => {\n          const column = leafColumns.find(d => d.id === filter.id)\n\n          if (column) {\n            const filterFn = column.getFilterFn()\n\n            if (shouldAutoRemoveFilter(filterFn, filter.value, column)) {\n              return false\n            }\n          }\n\n          return true\n        })\n      }\n\n      table.options.onColumnFiltersChange?.(updateFn)\n    }\n\n    table.resetColumnFilters = defaultState => {\n      table.setColumnFilters(\n        defaultState ? [] : table.initialState?.columnFilters ?? []\n      )\n    }\n\n    table.getPreFilteredRowModel = () => table.getCoreRowModel()\n    table.getFilteredRowModel = () => {\n      if (!table._getFilteredRowModel && table.options.getFilteredRowModel) {\n        table._getFilteredRowModel = table.options.getFilteredRowModel(table)\n      }\n\n      if (table.options.manualFiltering || !table._getFilteredRowModel) {\n        return table.getPreFilteredRowModel()\n      }\n\n      return table._getFilteredRowModel()\n    }\n  },\n}\n\nexport function shouldAutoRemoveFilter<TData extends RowData>(\n  filterFn?: FilterFn<TData>,\n  value?: any,\n  column?: Column<TData, unknown>\n) {\n  return (\n    (filterFn && filterFn.autoRemove\n      ? filterFn.autoRemove(value, column)\n      : false) ||\n    typeof value === 'undefined' ||\n    (typeof value === 'string' && !value)\n  )\n}\n", "import { AggregationFn } from './features/ColumnGrouping'\nimport { isNumberArray } from './utils'\n\nconst sum: AggregationFn<any> = (columnId, _leafRows, childRows) => {\n  // It's faster to just add the aggregations together instead of\n  // process leaf nodes individually\n  return childRows.reduce((sum, next) => {\n    const nextValue = next.getValue(columnId)\n    return sum + (typeof nextValue === 'number' ? nextValue : 0)\n  }, 0)\n}\n\nconst min: AggregationFn<any> = (columnId, _leafRows, childRows) => {\n  let min: number | undefined\n\n  childRows.forEach(row => {\n    const value = row.getValue<number>(columnId)\n\n    if (\n      value != null &&\n      (min! > value || (min === undefined && value >= value))\n    ) {\n      min = value\n    }\n  })\n\n  return min\n}\n\nconst max: AggregationFn<any> = (columnId, _leafRows, childRows) => {\n  let max: number | undefined\n\n  childRows.forEach(row => {\n    const value = row.getValue<number>(columnId)\n    if (\n      value != null &&\n      (max! < value || (max === undefined && value >= value))\n    ) {\n      max = value\n    }\n  })\n\n  return max\n}\n\nconst extent: AggregationFn<any> = (columnId, _leafRows, childRows) => {\n  let min: number | undefined\n  let max: number | undefined\n\n  childRows.forEach(row => {\n    const value = row.getValue<number>(columnId)\n    if (value != null) {\n      if (min === undefined) {\n        if (value >= value) min = max = value\n      } else {\n        if (min > value) min = value\n        if (max! < value) max = value\n      }\n    }\n  })\n\n  return [min, max]\n}\n\nconst mean: AggregationFn<any> = (columnId, leafRows) => {\n  let count = 0\n  let sum = 0\n\n  leafRows.forEach(row => {\n    let value = row.getValue<number>(columnId)\n    if (value != null && (value = +value) >= value) {\n      ++count, (sum += value)\n    }\n  })\n\n  if (count) return sum / count\n\n  return\n}\n\nconst median: AggregationFn<any> = (columnId, leafRows) => {\n  if (!leafRows.length) {\n    return\n  }\n\n  const values = leafRows.map(row => row.getValue(columnId))\n  if (!isNumberArray(values)) {\n    return\n  }\n  if (values.length === 1) {\n    return values[0]\n  }\n\n  const mid = Math.floor(values.length / 2)\n  const nums = values.sort((a, b) => a - b)\n  return values.length % 2 !== 0 ? nums[mid] : (nums[mid - 1]! + nums[mid]!) / 2\n}\n\nconst unique: AggregationFn<any> = (columnId, leafRows) => {\n  return Array.from(new Set(leafRows.map(d => d.getValue(columnId))).values())\n}\n\nconst uniqueCount: AggregationFn<any> = (columnId, leafRows) => {\n  return new Set(leafRows.map(d => d.getValue(columnId))).size\n}\n\nconst count: AggregationFn<any> = (_columnId, leafRows) => {\n  return leafRows.length\n}\n\nexport const aggregationFns = {\n  sum,\n  min,\n  max,\n  extent,\n  mean,\n  median,\n  unique,\n  uniqueCount,\n  count,\n}\n\nexport type BuiltInAggregationFn = keyof typeof aggregationFns\n", "import { RowModel } from '..'\nimport { BuiltInAggregationFn, aggregationFns } from '../aggregationFns'\nimport {\n  AggregationFns,\n  Cell,\n  Column,\n  ColumnDefTemplate,\n  OnChangeFn,\n  Row,\n  RowData,\n  Table,\n  TableFeature,\n  Updater,\n} from '../types'\nimport { isFunction, makeStateUpdater } from '../utils'\n\nexport type GroupingState = string[]\n\nexport interface GroupingTableState {\n  grouping: GroupingState\n}\n\nexport type AggregationFn<TData extends RowData> = (\n  columnId: string,\n  leafRows: Row<TData>[],\n  childRows: Row<TData>[]\n) => any\n\nexport type CustomAggregationFns = Record<string, AggregationFn<any>>\n\nexport type AggregationFnOption<TData extends RowData> =\n  | 'auto'\n  | keyof AggregationFns\n  | BuiltInAggregationFn\n  | AggregationFn<TData>\n\nexport interface GroupingColumnDef<TData extends RowData, TValue> {\n  /**\n   * The cell to display each row for the column if the cell is an aggregate. If a function is passed, it will be passed a props object with the context of the cell and should return the property type for your adapter (the exact type depends on the adapter being used).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#aggregatedcell)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  aggregatedCell?: ColumnDefTemplate<\n    ReturnType<Cell<TData, TValue>['getContext']>\n  >\n  /**\n   * The resolved aggregation function for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#aggregationfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  aggregationFn?: AggregationFnOption<TData>\n  /**\n   * Enables/disables grouping for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#enablegrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  enableGrouping?: boolean\n  /**\n   * Specify a value to be used for grouping rows on this column. If this option is not specified, the value derived from `accessorKey` / `accessorFn` will be used instead.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getgroupingvalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getGroupingValue?: (row: TData) => any\n}\n\nexport interface GroupingColumn<TData extends RowData> {\n  /**\n   * Returns the aggregation function for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getaggregationfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getAggregationFn: () => AggregationFn<TData> | undefined\n  /**\n   * Returns the automatically inferred aggregation function for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getautoaggregationfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getAutoAggregationFn: () => AggregationFn<TData> | undefined\n  /**\n   * Returns whether or not the column can be grouped.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getcangroup)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getCanGroup: () => boolean\n  /**\n   * Returns the index of the column in the grouping state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getgroupedindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getGroupedIndex: () => number\n  /**\n   * Returns whether or not the column is currently grouped.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getisgrouped)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getIsGrouped: () => boolean\n  /**\n   * Returns a function that toggles the grouping state of the column. This is useful for passing to the `onClick` prop of a button.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#gettogglegroupinghandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getToggleGroupingHandler: () => () => void\n  /**\n   * Toggles the grouping state of the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#togglegrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  toggleGrouping: () => void\n}\n\nexport interface GroupingRow {\n  _groupingValuesCache: Record<string, any>\n  /**\n   * Returns the grouping value for any row and column (including leaf rows).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getgroupingvalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getGroupingValue: (columnId: string) => unknown\n  /**\n   * Returns whether or not the row is currently grouped.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getisgrouped)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getIsGrouped: () => boolean\n  /**\n   * If this row is grouped, this is the id of the column that this row is grouped by.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#groupingcolumnid)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  groupingColumnId?: string\n  /**\n   * If this row is grouped, this is the unique/shared value for the `groupingColumnId` for all of the rows in this group.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#groupingvalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  groupingValue?: unknown\n}\n\nexport interface GroupingCell {\n  /**\n   * Returns whether or not the cell is currently aggregated.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getisaggregated)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getIsAggregated: () => boolean\n  /**\n   * Returns whether or not the cell is currently grouped.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getisgrouped)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getIsGrouped: () => boolean\n  /**\n   * Returns whether or not the cell is currently a placeholder cell.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getisplaceholder)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getIsPlaceholder: () => boolean\n}\n\nexport interface ColumnDefaultOptions {\n  enableGrouping: boolean\n  onGroupingChange: OnChangeFn<GroupingState>\n}\n\ninterface GroupingOptionsBase {\n  /**\n   * Enables/disables grouping for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#enablegrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  enableGrouping?: boolean\n  /**\n   * Returns the row model after grouping has taken place, but no further.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getgroupedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getGroupedRowModel?: (table: Table<any>) => () => RowModel<any>\n  /**\n   * Grouping columns are automatically reordered by default to the start of the columns list. If you would rather remove them or leave them as-is, set the appropriate mode here.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#groupedcolumnmode)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  groupedColumnMode?: false | 'reorder' | 'remove'\n  /**\n   * Enables manual grouping. If this option is set to `true`, the table will not automatically group rows using `getGroupedRowModel()` and instead will expect you to manually group the rows before passing them to the table. This is useful if you are doing server-side grouping and aggregation.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#manualgrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  manualGrouping?: boolean\n  /**\n   * If this function is provided, it will be called when the grouping state changes and you will be expected to manage the state yourself. You can pass the managed state back to the table via the `tableOptions.state.grouping` option.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#ongroupingchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  onGroupingChange?: OnChangeFn<GroupingState>\n}\n\ntype ResolvedAggregationFns = keyof AggregationFns extends never\n  ? {\n      aggregationFns?: Record<string, AggregationFn<any>>\n    }\n  : {\n      aggregationFns: Record<keyof AggregationFns, AggregationFn<any>>\n    }\n\nexport interface GroupingOptions\n  extends GroupingOptionsBase,\n    ResolvedAggregationFns {}\n\nexport type GroupingColumnMode = false | 'reorder' | 'remove'\n\nexport interface GroupingInstance<TData extends RowData> {\n  _getGroupedRowModel?: () => RowModel<TData>\n  /**\n   * Returns the row model for the table after grouping has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getgroupedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getGroupedRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model for the table before any grouping has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getpregroupedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getPreGroupedRowModel: () => RowModel<TData>\n  /**\n   * Resets the **grouping** state to `initialState.grouping`, or `true` can be passed to force a default blank state reset to `[]`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#resetgrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  resetGrouping: (defaultState?: boolean) => void\n  /**\n   * Updates the grouping state of the table via an update function or value.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#setgrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  setGrouping: (updater: Updater<GroupingState>) => void\n}\n\n//\n\nexport const ColumnGrouping: TableFeature = {\n  getDefaultColumnDef: <TData extends RowData>(): GroupingColumnDef<\n    TData,\n    unknown\n  > => {\n    return {\n      aggregatedCell: props => (props.getValue() as any)?.toString?.() ?? null,\n      aggregationFn: 'auto',\n    }\n  },\n\n  getInitialState: (state): GroupingTableState => {\n    return {\n      grouping: [],\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): GroupingOptions => {\n    return {\n      onGroupingChange: makeStateUpdater('grouping', table),\n      groupedColumnMode: 'reorder',\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    column.toggleGrouping = () => {\n      table.setGrouping(old => {\n        // Find any existing grouping for this column\n        if (old?.includes(column.id)) {\n          return old.filter(d => d !== column.id)\n        }\n\n        return [...(old ?? []), column.id]\n      })\n    }\n\n    column.getCanGroup = () => {\n      return (\n        (column.columnDef.enableGrouping ?? true) &&\n        (table.options.enableGrouping ?? true) &&\n        (!!column.accessorFn || !!column.columnDef.getGroupingValue)\n      )\n    }\n\n    column.getIsGrouped = () => {\n      return table.getState().grouping?.includes(column.id)\n    }\n\n    column.getGroupedIndex = () => table.getState().grouping?.indexOf(column.id)\n\n    column.getToggleGroupingHandler = () => {\n      const canGroup = column.getCanGroup()\n\n      return () => {\n        if (!canGroup) return\n        column.toggleGrouping()\n      }\n    }\n    column.getAutoAggregationFn = () => {\n      const firstRow = table.getCoreRowModel().flatRows[0]\n\n      const value = firstRow?.getValue(column.id)\n\n      if (typeof value === 'number') {\n        return aggregationFns.sum\n      }\n\n      if (Object.prototype.toString.call(value) === '[object Date]') {\n        return aggregationFns.extent\n      }\n    }\n    column.getAggregationFn = () => {\n      if (!column) {\n        throw new Error()\n      }\n\n      return isFunction(column.columnDef.aggregationFn)\n        ? column.columnDef.aggregationFn\n        : column.columnDef.aggregationFn === 'auto'\n          ? column.getAutoAggregationFn()\n          : table.options.aggregationFns?.[\n              column.columnDef.aggregationFn as string\n            ] ??\n            aggregationFns[\n              column.columnDef.aggregationFn as BuiltInAggregationFn\n            ]\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setGrouping = updater => table.options.onGroupingChange?.(updater)\n\n    table.resetGrouping = defaultState => {\n      table.setGrouping(defaultState ? [] : table.initialState?.grouping ?? [])\n    }\n\n    table.getPreGroupedRowModel = () => table.getFilteredRowModel()\n    table.getGroupedRowModel = () => {\n      if (!table._getGroupedRowModel && table.options.getGroupedRowModel) {\n        table._getGroupedRowModel = table.options.getGroupedRowModel(table)\n      }\n\n      if (table.options.manualGrouping || !table._getGroupedRowModel) {\n        return table.getPreGroupedRowModel()\n      }\n\n      return table._getGroupedRowModel()\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row.getIsGrouped = () => !!row.groupingColumnId\n    row.getGroupingValue = columnId => {\n      if (row._groupingValuesCache.hasOwnProperty(columnId)) {\n        return row._groupingValuesCache[columnId]\n      }\n\n      const column = table.getColumn(columnId)\n\n      if (!column?.columnDef.getGroupingValue) {\n        return row.getValue(columnId)\n      }\n\n      row._groupingValuesCache[columnId] = column.columnDef.getGroupingValue(\n        row.original\n      )\n\n      return row._groupingValuesCache[columnId]\n    }\n    row._groupingValuesCache = {}\n  },\n\n  createCell: <TData extends RowData, TValue>(\n    cell: Cell<TData, TValue>,\n    column: Column<TData, TValue>,\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    const getRenderValue = () =>\n      cell.getValue() ?? table.options.renderFallbackValue\n\n    cell.getIsGrouped = () =>\n      column.getIsGrouped() && column.id === row.groupingColumnId\n    cell.getIsPlaceholder = () => !cell.getIsGrouped() && column.getIsGrouped()\n    cell.getIsAggregated = () =>\n      !cell.getIsGrouped() && !cell.getIsPlaceholder() && !!row.subRows?.length\n  },\n}\n\nexport function orderColumns<TData extends RowData>(\n  leafColumns: Column<TData, unknown>[],\n  grouping: string[],\n  groupedColumnMode?: GroupingColumnMode\n) {\n  if (!grouping?.length || !groupedColumnMode) {\n    return leafColumns\n  }\n\n  const nonGroupingColumns = leafColumns.filter(\n    col => !grouping.includes(col.id)\n  )\n\n  if (groupedColumnMode === 'remove') {\n    return nonGroupingColumns\n  }\n\n  const groupingColumns = grouping\n    .map(g => leafColumns.find(col => col.id === g)!)\n    .filter(Boolean)\n\n  return [...groupingColumns, ...nonGroupingColumns]\n}\n", "import { getMemoOptions, makeStateUpdater, memo } from '../utils'\n\nimport {\n  Column,\n  OnChangeFn,\n  RowData,\n  Table,\n  TableFeature,\n  Updater,\n} from '../types'\n\nimport { orderColumns } from './ColumnGrouping'\nimport { ColumnPinningPosition, _getVisibleLeafColumns } from '..'\n\nexport interface ColumnOrderTableState {\n  columnOrder: ColumnOrderState\n}\n\nexport type ColumnOrderState = string[]\n\nexport interface ColumnOrderOptions {\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnOrder` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-ordering#oncolumnorderchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-ordering)\n   */\n  onColumnOrderChange?: OnChangeFn<ColumnOrderState>\n}\n\nexport interface ColumnOrderColumn {\n  /**\n   * Returns the index of the column in the order of the visible columns. Optionally pass a `position` parameter to get the index of the column in a sub-section of the table\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-ordering#getindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-ordering)\n   */\n  getIndex: (position?: ColumnPinningPosition | 'center') => number\n  /**\n   * Returns `true` if the column is the first column in the order of the visible columns. Optionally pass a `position` parameter to check if the column is the first in a sub-section of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-ordering#getisfirstcolumn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-ordering)\n   */\n  getIsFirstColumn: (position?: ColumnPinningPosition | 'center') => boolean\n  /**\n   * Returns `true` if the column is the last column in the order of the visible columns. Optionally pass a `position` parameter to check if the column is the last in a sub-section of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-ordering#getislastcolumn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-ordering)\n   */\n  getIsLastColumn: (position?: ColumnPinningPosition | 'center') => boolean\n}\n\nexport interface ColumnOrderDefaultOptions {\n  onColumnOrderChange: OnChangeFn<ColumnOrderState>\n}\n\nexport interface ColumnOrderInstance<TData extends RowData> {\n  _getOrderColumnsFn: () => (\n    columns: Column<TData, unknown>[]\n  ) => Column<TData, unknown>[]\n  /**\n   * Resets the **columnOrder** state to `initialState.columnOrder`, or `true` can be passed to force a default blank state reset to `[]`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-ordering#resetcolumnorder)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-ordering)\n   */\n  resetColumnOrder: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.columnOrder` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-ordering#setcolumnorder)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-ordering)\n   */\n  setColumnOrder: (updater: Updater<ColumnOrderState>) => void\n}\n\n//\n\nexport const ColumnOrdering: TableFeature = {\n  getInitialState: (state): ColumnOrderTableState => {\n    return {\n      columnOrder: [],\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ColumnOrderDefaultOptions => {\n    return {\n      onColumnOrderChange: makeStateUpdater('columnOrder', table),\n    }\n  },\n\n  createColumn: <TData extends RowData>(\n    column: Column<TData, unknown>,\n    table: Table<TData>\n  ): void => {\n    column.getIndex = memo(\n      position => [_getVisibleLeafColumns(table, position)],\n      columns => columns.findIndex(d => d.id === column.id),\n      getMemoOptions(table.options, 'debugColumns', 'getIndex')\n    )\n    column.getIsFirstColumn = position => {\n      const columns = _getVisibleLeafColumns(table, position)\n      return columns[0]?.id === column.id\n    }\n    column.getIsLastColumn = position => {\n      const columns = _getVisibleLeafColumns(table, position)\n      return columns[columns.length - 1]?.id === column.id\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setColumnOrder = updater =>\n      table.options.onColumnOrderChange?.(updater)\n    table.resetColumnOrder = defaultState => {\n      table.setColumnOrder(\n        defaultState ? [] : table.initialState.columnOrder ?? []\n      )\n    }\n    table._getOrderColumnsFn = memo(\n      () => [\n        table.getState().columnOrder,\n        table.getState().grouping,\n        table.options.groupedColumnMode,\n      ],\n      (columnOrder, grouping, groupedColumnMode) =>\n        (columns: Column<TData, unknown>[]) => {\n          // Sort grouped columns to the start of the column list\n          // before the headers are built\n          let orderedColumns: Column<TData, unknown>[] = []\n\n          // If there is no order, return the normal columns\n          if (!columnOrder?.length) {\n            orderedColumns = columns\n          } else {\n            const columnOrderCopy = [...columnOrder]\n\n            // If there is an order, make a copy of the columns\n            const columnsCopy = [...columns]\n\n            // And make a new ordered array of the columns\n\n            // Loop over the columns and place them in order into the new array\n            while (columnsCopy.length && columnOrderCopy.length) {\n              const targetColumnId = columnOrderCopy.shift()\n              const foundIndex = columnsCopy.findIndex(\n                d => d.id === targetColumnId\n              )\n              if (foundIndex > -1) {\n                orderedColumns.push(columnsCopy.splice(foundIndex, 1)[0]!)\n              }\n            }\n\n            // If there are any columns left, add them to the end\n            orderedColumns = [...orderedColumns, ...columnsCopy]\n          }\n\n          return orderColumns(orderedColumns, grouping, groupedColumnMode)\n        },\n      getMemoOptions(table.options, 'debugTable', '_getOrderColumnsFn')\n    )\n  },\n}\n", "import {\n  OnChangeFn,\n  Updater,\n  Table,\n  Column,\n  Row,\n  Cell,\n  RowData,\n  TableFeature,\n} from '../types'\nimport { getMemoOptions, makeStateUpdater, memo } from '../utils'\n\nexport type ColumnPinningPosition = false | 'left' | 'right'\n\nexport interface ColumnPinningState {\n  left?: string[]\n  right?: string[]\n}\n\nexport interface ColumnPinningTableState {\n  columnPinning: ColumnPinningState\n}\n\nexport interface ColumnPinningOptions {\n  /**\n   * Enables/disables column pinning for the table. Defaults to `true`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#enablecolumnpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  enableColumnPinning?: boolean\n  /**\n   * @deprecated Use `enableColumnPinning` or `enableRowPinning` instead.\n   * Enables/disables all pinning for the table. Defaults to `true`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#enablepinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  enablePinning?: boolean\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnPinning` changes. This overrides the default internal state management, so you will also need to supply `state.columnPinning` from your own managed state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#oncolumnpinningchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/oncolumnpinningchange)\n   */\n  onColumnPinningChange?: OnChangeFn<ColumnPinningState>\n}\n\nexport interface ColumnPinningDefaultOptions {\n  onColumnPinningChange: OnChangeFn<ColumnPinningState>\n}\n\nexport interface ColumnPinningColumnDef {\n  /**\n   * Enables/disables column pinning for this column. Defaults to `true`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#enablepinning-1)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  enablePinning?: boolean\n}\n\nexport interface ColumnPinningColumn {\n  /**\n   * Returns whether or not the column can be pinned.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getcanpin)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getCanPin: () => boolean\n  /**\n   * Returns the pinned position of the column. (`'left'`, `'right'` or `false`)\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getispinned)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getIsPinned: () => ColumnPinningPosition\n  /**\n   * Returns the numeric pinned index of the column within a pinned column group.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getpinnedindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getPinnedIndex: () => number\n  /**\n   * Pins a column to the `'left'` or `'right'`, or unpins the column to the center if `false` is passed.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#pin)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  pin: (position: ColumnPinningPosition) => void\n}\n\nexport interface ColumnPinningRow<TData extends RowData> {\n  /**\n   * Returns all center pinned (unpinned) leaf cells in the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getcentervisiblecells)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getCenterVisibleCells: () => Cell<TData, unknown>[]\n  /**\n   * Returns all left pinned leaf cells in the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getleftvisiblecells)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getLeftVisibleCells: () => Cell<TData, unknown>[]\n  /**\n   * Returns all right pinned leaf cells in the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getrightvisiblecells)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getRightVisibleCells: () => Cell<TData, unknown>[]\n}\n\nexport interface ColumnPinningInstance<TData extends RowData> {\n  /**\n   * Returns all center pinned (unpinned) leaf columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getcenterleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getCenterLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns whether or not any columns are pinned. Optionally specify to only check for pinned columns in either the `left` or `right` position.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getissomecolumnspinned)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getIsSomeColumnsPinned: (position?: ColumnPinningPosition) => boolean\n  /**\n   * Returns all left pinned leaf columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getleftleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getLeftLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns all right pinned leaf columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getrightleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getRightLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Resets the **columnPinning** state to `initialState.columnPinning`, or `true` can be passed to force a default blank state reset to `{ left: [], right: [], }`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#resetcolumnpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  resetColumnPinning: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.columnPinning` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#setcolumnpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  setColumnPinning: (updater: Updater<ColumnPinningState>) => void\n}\n\n//\n\nconst getDefaultColumnPinningState = (): ColumnPinningState => ({\n  left: [],\n  right: [],\n})\n\nexport const ColumnPinning: TableFeature = {\n  getInitialState: (state): ColumnPinningTableState => {\n    return {\n      columnPinning: getDefaultColumnPinningState(),\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ColumnPinningDefaultOptions => {\n    return {\n      onColumnPinningChange: makeStateUpdater('columnPinning', table),\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    column.pin = position => {\n      const columnIds = column\n        .getLeafColumns()\n        .map(d => d.id)\n        .filter(Boolean) as string[]\n\n      table.setColumnPinning(old => {\n        if (position === 'right') {\n          return {\n            left: (old?.left ?? []).filter(d => !columnIds?.includes(d)),\n            right: [\n              ...(old?.right ?? []).filter(d => !columnIds?.includes(d)),\n              ...columnIds,\n            ],\n          }\n        }\n\n        if (position === 'left') {\n          return {\n            left: [\n              ...(old?.left ?? []).filter(d => !columnIds?.includes(d)),\n              ...columnIds,\n            ],\n            right: (old?.right ?? []).filter(d => !columnIds?.includes(d)),\n          }\n        }\n\n        return {\n          left: (old?.left ?? []).filter(d => !columnIds?.includes(d)),\n          right: (old?.right ?? []).filter(d => !columnIds?.includes(d)),\n        }\n      })\n    }\n\n    column.getCanPin = () => {\n      const leafColumns = column.getLeafColumns()\n\n      return leafColumns.some(\n        d =>\n          (d.columnDef.enablePinning ?? true) &&\n          (table.options.enableColumnPinning ??\n            table.options.enablePinning ??\n            true)\n      )\n    }\n\n    column.getIsPinned = () => {\n      const leafColumnIds = column.getLeafColumns().map(d => d.id)\n\n      const { left, right } = table.getState().columnPinning\n\n      const isLeft = leafColumnIds.some(d => left?.includes(d))\n      const isRight = leafColumnIds.some(d => right?.includes(d))\n\n      return isLeft ? 'left' : isRight ? 'right' : false\n    }\n\n    column.getPinnedIndex = () => {\n      const position = column.getIsPinned()\n\n      return position\n        ? table.getState().columnPinning?.[position]?.indexOf(column.id) ?? -1\n        : 0\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row.getCenterVisibleCells = memo(\n      () => [\n        row._getAllVisibleCells(),\n        table.getState().columnPinning.left,\n        table.getState().columnPinning.right,\n      ],\n      (allCells, left, right) => {\n        const leftAndRight: string[] = [...(left ?? []), ...(right ?? [])]\n\n        return allCells.filter(d => !leftAndRight.includes(d.column.id))\n      },\n      getMemoOptions(table.options, 'debugRows', 'getCenterVisibleCells')\n    )\n    row.getLeftVisibleCells = memo(\n      () => [row._getAllVisibleCells(), table.getState().columnPinning.left],\n      (allCells, left) => {\n        const cells = (left ?? [])\n          .map(columnId => allCells.find(cell => cell.column.id === columnId)!)\n          .filter(Boolean)\n          .map(d => ({ ...d, position: 'left' }) as Cell<TData, unknown>)\n\n        return cells\n      },\n      getMemoOptions(table.options, 'debugRows', 'getLeftVisibleCells')\n    )\n    row.getRightVisibleCells = memo(\n      () => [row._getAllVisibleCells(), table.getState().columnPinning.right],\n      (allCells, right) => {\n        const cells = (right ?? [])\n          .map(columnId => allCells.find(cell => cell.column.id === columnId)!)\n          .filter(Boolean)\n          .map(d => ({ ...d, position: 'right' }) as Cell<TData, unknown>)\n\n        return cells\n      },\n      getMemoOptions(table.options, 'debugRows', 'getRightVisibleCells')\n    )\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setColumnPinning = updater =>\n      table.options.onColumnPinningChange?.(updater)\n\n    table.resetColumnPinning = defaultState =>\n      table.setColumnPinning(\n        defaultState\n          ? getDefaultColumnPinningState()\n          : table.initialState?.columnPinning ?? getDefaultColumnPinningState()\n      )\n\n    table.getIsSomeColumnsPinned = position => {\n      const pinningState = table.getState().columnPinning\n\n      if (!position) {\n        return Boolean(pinningState.left?.length || pinningState.right?.length)\n      }\n      return Boolean(pinningState[position]?.length)\n    }\n\n    table.getLeftLeafColumns = memo(\n      () => [table.getAllLeafColumns(), table.getState().columnPinning.left],\n      (allColumns, left) => {\n        return (left ?? [])\n          .map(columnId => allColumns.find(column => column.id === columnId)!)\n          .filter(Boolean)\n      },\n      getMemoOptions(table.options, 'debugColumns', 'getLeftLeafColumns')\n    )\n\n    table.getRightLeafColumns = memo(\n      () => [table.getAllLeafColumns(), table.getState().columnPinning.right],\n      (allColumns, right) => {\n        return (right ?? [])\n          .map(columnId => allColumns.find(column => column.id === columnId)!)\n          .filter(Boolean)\n      },\n      getMemoOptions(table.options, 'debugColumns', 'getRightLeafColumns')\n    )\n\n    table.getCenterLeafColumns = memo(\n      () => [\n        table.getAllLeafColumns(),\n        table.getState().columnPinning.left,\n        table.getState().columnPinning.right,\n      ],\n      (allColumns, left, right) => {\n        const leftAndRight: string[] = [...(left ?? []), ...(right ?? [])]\n\n        return allColumns.filter(d => !leftAndRight.includes(d.id))\n      },\n      getMemoOptions(table.options, 'debugColumns', 'getCenterLeafColumns')\n    )\n  },\n}\n", "import { _getVisibleLeafColumns } from '..'\nimport {\n  <PERSON><PERSON><PERSON>,\n  <PERSON>um<PERSON>,\n  Header,\n  OnChangeFn,\n  Table,\n  Updater,\n  TableFeature,\n} from '../types'\nimport { getMemoOptions, makeStateUpdater, memo } from '../utils'\nimport { ColumnPinningPosition } from './ColumnPinning'\n\n//\n\nexport interface ColumnSizingTableState {\n  columnSizing: ColumnSizingState\n  columnSizingInfo: ColumnSizingInfoState\n}\n\nexport type ColumnSizingState = Record<string, number>\n\nexport interface ColumnSizingInfoState {\n  columnSizingStart: [string, number][]\n  deltaOffset: null | number\n  deltaPercentage: null | number\n  isResizingColumn: false | string\n  startOffset: null | number\n  startSize: null | number\n}\n\nexport type ColumnResizeMode = 'onChange' | 'onEnd'\n\nexport type ColumnResizeDirection = 'ltr' | 'rtl'\n\nexport interface ColumnSizingOptions {\n  /**\n   * Determines when the columnSizing state is updated. `onChange` updates the state when the user is dragging the resize handle. `onEnd` updates the state when the user releases the resize handle.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#columnresizemode)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  columnResizeMode?: ColumnResizeMode\n  /**\n   * Enables or disables column resizing for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#enablecolumnresizing)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  enableColumnResizing?: boolean\n  /**\n   * Enables or disables right-to-left support for resizing the column. defaults to 'ltr'.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#columnResizeDirection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  columnResizeDirection?: ColumnResizeDirection\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnSizing` changes. This overrides the default internal state management, so you will also need to supply `state.columnSizing` from your own managed state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#oncolumnsizingchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  onColumnSizingChange?: OnChangeFn<ColumnSizingState>\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnSizingInfo` changes. This overrides the default internal state management, so you will also need to supply `state.columnSizingInfo` from your own managed state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#oncolumnsizinginfochange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  onColumnSizingInfoChange?: OnChangeFn<ColumnSizingInfoState>\n}\n\nexport type ColumnSizingDefaultOptions = Pick<\n  ColumnSizingOptions,\n  | 'columnResizeMode'\n  | 'onColumnSizingChange'\n  | 'onColumnSizingInfoChange'\n  | 'columnResizeDirection'\n>\n\nexport interface ColumnSizingInstance {\n  /**\n   * If pinning, returns the total size of the center portion of the table by calculating the sum of the sizes of all unpinned/center leaf-columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getcentertotalsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getCenterTotalSize: () => number\n  /**\n   * Returns the total size of the left portion of the table by calculating the sum of the sizes of all left leaf-columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getlefttotalsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getLeftTotalSize: () => number\n  /**\n   * Returns the total size of the right portion of the table by calculating the sum of the sizes of all right leaf-columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getrighttotalsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getRightTotalSize: () => number\n  /**\n   * Returns the total size of the table by calculating the sum of the sizes of all leaf-columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#gettotalsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getTotalSize: () => number\n  /**\n   * Resets column sizing to its initial state. If `defaultState` is `true`, the default state for the table will be used instead of the initialValue provided to the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#resetcolumnsizing)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  resetColumnSizing: (defaultState?: boolean) => void\n  /**\n   * Resets column sizing info to its initial state. If `defaultState` is `true`, the default state for the table will be used instead of the initialValue provided to the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#resetheadersizeinfo)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  resetHeaderSizeInfo: (defaultState?: boolean) => void\n  /**\n   * Sets the column sizing state using an updater function or a value. This will trigger the underlying `onColumnSizingChange` function if one is passed to the table options, otherwise the state will be managed automatically by the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#setcolumnsizing)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  setColumnSizing: (updater: Updater<ColumnSizingState>) => void\n  /**\n   * Sets the column sizing info state using an updater function or a value. This will trigger the underlying `onColumnSizingInfoChange` function if one is passed to the table options, otherwise the state will be managed automatically by the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#setcolumnsizinginfo)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  setColumnSizingInfo: (updater: Updater<ColumnSizingInfoState>) => void\n}\n\nexport interface ColumnSizingColumnDef {\n  /**\n   * Enables or disables column resizing for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#enableresizing)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  enableResizing?: boolean\n  /**\n   * The maximum allowed size for the column\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#maxsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  maxSize?: number\n  /**\n   * The minimum allowed size for the column\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#minsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  minSize?: number\n  /**\n   * The desired size for the column\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#size)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  size?: number\n}\n\nexport interface ColumnSizingColumn {\n  /**\n   * Returns `true` if the column can be resized.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getcanresize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getCanResize: () => boolean\n  /**\n   * Returns `true` if the column is currently being resized.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getisresizing)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getIsResizing: () => boolean\n  /**\n   * Returns the current size of the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getSize: () => number\n  /**\n   * Returns the offset measurement along the row-axis (usually the x-axis for standard tables) for the header. This is effectively a sum of the offset measurements of all preceding (left) headers in relation to the current column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getstart)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getStart: (position?: ColumnPinningPosition | 'center') => number\n  /**\n   * Returns the offset measurement along the row-axis (usually the x-axis for standard tables) for the header. This is effectively a sum of the offset measurements of all succeeding (right) headers in relation to the current column.\n   */\n  getAfter: (position?: ColumnPinningPosition | 'center') => number\n  /**\n   * Resets the column to its initial size.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#resetsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  resetSize: () => void\n}\n\nexport interface ColumnSizingHeader {\n  /**\n   * Returns an event handler function that can be used to resize the header. It can be used as an:\n   * - `onMouseDown` handler\n   * - `onTouchStart` handler\n   *\n   * The dragging and release events are automatically handled for you.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getresizehandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getResizeHandler: (context?: Document) => (event: unknown) => void\n  /**\n   * Returns the current size of the header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getSize: () => number\n  /**\n   * Returns the offset measurement along the row-axis (usually the x-axis for standard tables) for the header. This is effectively a sum of the offset measurements of all preceding headers.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getstart)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getStart: (position?: ColumnPinningPosition) => number\n}\n\n//\n\nexport const defaultColumnSizing = {\n  size: 150,\n  minSize: 20,\n  maxSize: Number.MAX_SAFE_INTEGER,\n}\n\nconst getDefaultColumnSizingInfoState = (): ColumnSizingInfoState => ({\n  startOffset: null,\n  startSize: null,\n  deltaOffset: null,\n  deltaPercentage: null,\n  isResizingColumn: false,\n  columnSizingStart: [],\n})\n\nexport const ColumnSizing: TableFeature = {\n  getDefaultColumnDef: (): ColumnSizingColumnDef => {\n    return defaultColumnSizing\n  },\n  getInitialState: (state): ColumnSizingTableState => {\n    return {\n      columnSizing: {},\n      columnSizingInfo: getDefaultColumnSizingInfoState(),\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ColumnSizingDefaultOptions => {\n    return {\n      columnResizeMode: 'onEnd',\n      columnResizeDirection: 'ltr',\n      onColumnSizingChange: makeStateUpdater('columnSizing', table),\n      onColumnSizingInfoChange: makeStateUpdater('columnSizingInfo', table),\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    column.getSize = () => {\n      const columnSize = table.getState().columnSizing[column.id]\n\n      return Math.min(\n        Math.max(\n          column.columnDef.minSize ?? defaultColumnSizing.minSize,\n          columnSize ?? column.columnDef.size ?? defaultColumnSizing.size\n        ),\n        column.columnDef.maxSize ?? defaultColumnSizing.maxSize\n      )\n    }\n\n    column.getStart = memo(\n      position => [\n        position,\n        _getVisibleLeafColumns(table, position),\n        table.getState().columnSizing,\n      ],\n      (position, columns) =>\n        columns\n          .slice(0, column.getIndex(position))\n          .reduce((sum, column) => sum + column.getSize(), 0),\n      getMemoOptions(table.options, 'debugColumns', 'getStart')\n    )\n\n    column.getAfter = memo(\n      position => [\n        position,\n        _getVisibleLeafColumns(table, position),\n        table.getState().columnSizing,\n      ],\n      (position, columns) =>\n        columns\n          .slice(column.getIndex(position) + 1)\n          .reduce((sum, column) => sum + column.getSize(), 0),\n      getMemoOptions(table.options, 'debugColumns', 'getAfter')\n    )\n\n    column.resetSize = () => {\n      table.setColumnSizing(({ [column.id]: _, ...rest }) => {\n        return rest\n      })\n    }\n    column.getCanResize = () => {\n      return (\n        (column.columnDef.enableResizing ?? true) &&\n        (table.options.enableColumnResizing ?? true)\n      )\n    }\n    column.getIsResizing = () => {\n      return table.getState().columnSizingInfo.isResizingColumn === column.id\n    }\n  },\n\n  createHeader: <TData extends RowData, TValue>(\n    header: Header<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    header.getSize = () => {\n      let sum = 0\n\n      const recurse = (header: Header<TData, TValue>) => {\n        if (header.subHeaders.length) {\n          header.subHeaders.forEach(recurse)\n        } else {\n          sum += header.column.getSize() ?? 0\n        }\n      }\n\n      recurse(header)\n\n      return sum\n    }\n    header.getStart = () => {\n      if (header.index > 0) {\n        const prevSiblingHeader = header.headerGroup.headers[header.index - 1]!\n        return prevSiblingHeader.getStart() + prevSiblingHeader.getSize()\n      }\n\n      return 0\n    }\n    header.getResizeHandler = _contextDocument => {\n      const column = table.getColumn(header.column.id)\n      const canResize = column?.getCanResize()\n\n      return (e: unknown) => {\n        if (!column || !canResize) {\n          return\n        }\n\n        ;(e as any).persist?.()\n\n        if (isTouchStartEvent(e)) {\n          // lets not respond to multiple touches (e.g. 2 or 3 fingers)\n          if (e.touches && e.touches.length > 1) {\n            return\n          }\n        }\n\n        const startSize = header.getSize()\n\n        const columnSizingStart: [string, number][] = header\n          ? header.getLeafHeaders().map(d => [d.column.id, d.column.getSize()])\n          : [[column.id, column.getSize()]]\n\n        const clientX = isTouchStartEvent(e)\n          ? Math.round(e.touches[0]!.clientX)\n          : (e as MouseEvent).clientX\n\n        const newColumnSizing: ColumnSizingState = {}\n\n        const updateOffset = (\n          eventType: 'move' | 'end',\n          clientXPos?: number\n        ) => {\n          if (typeof clientXPos !== 'number') {\n            return\n          }\n\n          table.setColumnSizingInfo(old => {\n            const deltaDirection =\n              table.options.columnResizeDirection === 'rtl' ? -1 : 1\n            const deltaOffset =\n              (clientXPos - (old?.startOffset ?? 0)) * deltaDirection\n            const deltaPercentage = Math.max(\n              deltaOffset / (old?.startSize ?? 0),\n              -0.999999\n            )\n\n            old.columnSizingStart.forEach(([columnId, headerSize]) => {\n              newColumnSizing[columnId] =\n                Math.round(\n                  Math.max(headerSize + headerSize * deltaPercentage, 0) * 100\n                ) / 100\n            })\n\n            return {\n              ...old,\n              deltaOffset,\n              deltaPercentage,\n            }\n          })\n\n          if (\n            table.options.columnResizeMode === 'onChange' ||\n            eventType === 'end'\n          ) {\n            table.setColumnSizing(old => ({\n              ...old,\n              ...newColumnSizing,\n            }))\n          }\n        }\n\n        const onMove = (clientXPos?: number) => updateOffset('move', clientXPos)\n\n        const onEnd = (clientXPos?: number) => {\n          updateOffset('end', clientXPos)\n\n          table.setColumnSizingInfo(old => ({\n            ...old,\n            isResizingColumn: false,\n            startOffset: null,\n            startSize: null,\n            deltaOffset: null,\n            deltaPercentage: null,\n            columnSizingStart: [],\n          }))\n        }\n\n        const contextDocument =\n          _contextDocument || typeof document !== 'undefined' ? document : null\n\n        const mouseEvents = {\n          moveHandler: (e: MouseEvent) => onMove(e.clientX),\n          upHandler: (e: MouseEvent) => {\n            contextDocument?.removeEventListener(\n              'mousemove',\n              mouseEvents.moveHandler\n            )\n            contextDocument?.removeEventListener(\n              'mouseup',\n              mouseEvents.upHandler\n            )\n            onEnd(e.clientX)\n          },\n        }\n\n        const touchEvents = {\n          moveHandler: (e: TouchEvent) => {\n            if (e.cancelable) {\n              e.preventDefault()\n              e.stopPropagation()\n            }\n            onMove(e.touches[0]!.clientX)\n            return false\n          },\n          upHandler: (e: TouchEvent) => {\n            contextDocument?.removeEventListener(\n              'touchmove',\n              touchEvents.moveHandler\n            )\n            contextDocument?.removeEventListener(\n              'touchend',\n              touchEvents.upHandler\n            )\n            if (e.cancelable) {\n              e.preventDefault()\n              e.stopPropagation()\n            }\n            onEnd(e.touches[0]?.clientX)\n          },\n        }\n\n        const passiveIfSupported = passiveEventSupported()\n          ? { passive: false }\n          : false\n\n        if (isTouchStartEvent(e)) {\n          contextDocument?.addEventListener(\n            'touchmove',\n            touchEvents.moveHandler,\n            passiveIfSupported\n          )\n          contextDocument?.addEventListener(\n            'touchend',\n            touchEvents.upHandler,\n            passiveIfSupported\n          )\n        } else {\n          contextDocument?.addEventListener(\n            'mousemove',\n            mouseEvents.moveHandler,\n            passiveIfSupported\n          )\n          contextDocument?.addEventListener(\n            'mouseup',\n            mouseEvents.upHandler,\n            passiveIfSupported\n          )\n        }\n\n        table.setColumnSizingInfo(old => ({\n          ...old,\n          startOffset: clientX,\n          startSize,\n          deltaOffset: 0,\n          deltaPercentage: 0,\n          columnSizingStart,\n          isResizingColumn: column.id,\n        }))\n      }\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setColumnSizing = updater =>\n      table.options.onColumnSizingChange?.(updater)\n    table.setColumnSizingInfo = updater =>\n      table.options.onColumnSizingInfoChange?.(updater)\n    table.resetColumnSizing = defaultState => {\n      table.setColumnSizing(\n        defaultState ? {} : table.initialState.columnSizing ?? {}\n      )\n    }\n    table.resetHeaderSizeInfo = defaultState => {\n      table.setColumnSizingInfo(\n        defaultState\n          ? getDefaultColumnSizingInfoState()\n          : table.initialState.columnSizingInfo ??\n              getDefaultColumnSizingInfoState()\n      )\n    }\n    table.getTotalSize = () =>\n      table.getHeaderGroups()[0]?.headers.reduce((sum, header) => {\n        return sum + header.getSize()\n      }, 0) ?? 0\n    table.getLeftTotalSize = () =>\n      table.getLeftHeaderGroups()[0]?.headers.reduce((sum, header) => {\n        return sum + header.getSize()\n      }, 0) ?? 0\n    table.getCenterTotalSize = () =>\n      table.getCenterHeaderGroups()[0]?.headers.reduce((sum, header) => {\n        return sum + header.getSize()\n      }, 0) ?? 0\n    table.getRightTotalSize = () =>\n      table.getRightHeaderGroups()[0]?.headers.reduce((sum, header) => {\n        return sum + header.getSize()\n      }, 0) ?? 0\n  },\n}\n\nlet passiveSupported: boolean | null = null\nexport function passiveEventSupported() {\n  if (typeof passiveSupported === 'boolean') return passiveSupported\n\n  let supported = false\n  try {\n    const options = {\n      get passive() {\n        supported = true\n        return false\n      },\n    }\n\n    const noop = () => {}\n\n    window.addEventListener('test', noop, options)\n    window.removeEventListener('test', noop)\n  } catch (err) {\n    supported = false\n  }\n  passiveSupported = supported\n  return passiveSupported\n}\n\nfunction isTouchStartEvent(e: unknown): e is TouchEvent {\n  return (e as TouchEvent).type === 'touchstart'\n}\n", "import { ColumnPinningPosition } from '..'\nimport {\n  Cell,\n  Column,\n  OnChangeFn,\n  Table,\n  Updater,\n  Row,\n  RowData,\n  TableFeature,\n} from '../types'\nimport { getMemoOptions, makeStateUpdater, memo } from '../utils'\n\nexport type VisibilityState = Record<string, boolean>\n\nexport interface VisibilityTableState {\n  columnVisibility: VisibilityState\n}\n\nexport interface VisibilityOptions {\n  /**\n   * Whether to enable column hiding. Defaults to `true`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#enablehiding)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  enableHiding?: boolean\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnVisibility` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#oncolumnvisibilitychange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  onColumnVisibilityChange?: OnChangeFn<VisibilityState>\n}\n\nexport type VisibilityDefaultOptions = Pick<\n  VisibilityOptions,\n  'onColumnVisibilityChange'\n>\n\nexport interface VisibilityInstance<TData extends RowData> {\n  /**\n   * If column pinning, returns a flat array of leaf-node columns that are visible in the unpinned/center portion of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getcentervisibleleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getCenterVisibleLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns whether all columns are visible\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getisallcolumnsvisible)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getIsAllColumnsVisible: () => boolean\n  /**\n   * Returns whether any columns are visible\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getissomecolumnsvisible)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getIsSomeColumnsVisible: () => boolean\n  /**\n   * If column pinning, returns a flat array of leaf-node columns that are visible in the left portion of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getleftvisibleleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getLeftVisibleLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * If column pinning, returns a flat array of leaf-node columns that are visible in the right portion of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getrightvisibleleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getRightVisibleLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns a handler for toggling the visibility of all columns, meant to be bound to a `input[type=checkbox]` element.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#gettoggleallcolumnsvisibilityhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getToggleAllColumnsVisibilityHandler: () => (event: unknown) => void\n  /**\n   * Returns a flat array of columns that are visible, including parent columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getvisibleflatcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getVisibleFlatColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns a flat array of leaf-node columns that are visible.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getvisibleleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getVisibleLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Resets the column visibility state to the initial state. If `defaultState` is provided, the state will be reset to `{}`\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#resetcolumnvisibility)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  resetColumnVisibility: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.columnVisibility` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#setcolumnvisibility)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  setColumnVisibility: (updater: Updater<VisibilityState>) => void\n  /**\n   * Toggles the visibility of all columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#toggleallcolumnsvisible)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  toggleAllColumnsVisible: (value?: boolean) => void\n}\n\nexport interface VisibilityColumnDef {\n  enableHiding?: boolean\n}\n\nexport interface VisibilityRow<TData extends RowData> {\n  _getAllVisibleCells: () => Cell<TData, unknown>[]\n  /**\n   * Returns an array of cells that account for column visibility for the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getvisiblecells)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getVisibleCells: () => Cell<TData, unknown>[]\n}\n\nexport interface VisibilityColumn {\n  /**\n   * Returns whether the column can be hidden\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getcanhide)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getCanHide: () => boolean\n  /**\n   * Returns whether the column is visible\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getisvisible)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getIsVisible: () => boolean\n  /**\n   * Returns a function that can be used to toggle the column visibility. This function can be used to bind to an event handler to a checkbox.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#gettogglevisibilityhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getToggleVisibilityHandler: () => (event: unknown) => void\n  /**\n   * Toggles the visibility of the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#togglevisibility)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  toggleVisibility: (value?: boolean) => void\n}\n\n//\n\nexport const ColumnVisibility: TableFeature = {\n  getInitialState: (state): VisibilityTableState => {\n    return {\n      columnVisibility: {},\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): VisibilityDefaultOptions => {\n    return {\n      onColumnVisibilityChange: makeStateUpdater('columnVisibility', table),\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    column.toggleVisibility = value => {\n      if (column.getCanHide()) {\n        table.setColumnVisibility(old => ({\n          ...old,\n          [column.id]: value ?? !column.getIsVisible(),\n        }))\n      }\n    }\n    column.getIsVisible = () => {\n      const childColumns = column.columns\n      return (\n        (childColumns.length\n          ? childColumns.some(c => c.getIsVisible())\n          : table.getState().columnVisibility?.[column.id]) ?? true\n      )\n    }\n\n    column.getCanHide = () => {\n      return (\n        (column.columnDef.enableHiding ?? true) &&\n        (table.options.enableHiding ?? true)\n      )\n    }\n    column.getToggleVisibilityHandler = () => {\n      return (e: unknown) => {\n        column.toggleVisibility?.(\n          ((e as MouseEvent).target as HTMLInputElement).checked\n        )\n      }\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row._getAllVisibleCells = memo(\n      () => [row.getAllCells(), table.getState().columnVisibility],\n      cells => {\n        return cells.filter(cell => cell.column.getIsVisible())\n      },\n      getMemoOptions(table.options, 'debugRows', '_getAllVisibleCells')\n    )\n    row.getVisibleCells = memo(\n      () => [\n        row.getLeftVisibleCells(),\n        row.getCenterVisibleCells(),\n        row.getRightVisibleCells(),\n      ],\n      (left, center, right) => [...left, ...center, ...right],\n      getMemoOptions(table.options, 'debugRows', 'getVisibleCells')\n    )\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    const makeVisibleColumnsMethod = (\n      key: string,\n      getColumns: () => Column<TData, unknown>[]\n    ): (() => Column<TData, unknown>[]) => {\n      return memo(\n        () => [\n          getColumns(),\n          getColumns()\n            .filter(d => d.getIsVisible())\n            .map(d => d.id)\n            .join('_'),\n        ],\n        columns => {\n          return columns.filter(d => d.getIsVisible?.())\n        },\n        getMemoOptions(table.options, 'debugColumns', key)\n      )\n    }\n\n    table.getVisibleFlatColumns = makeVisibleColumnsMethod(\n      'getVisibleFlatColumns',\n      () => table.getAllFlatColumns()\n    )\n    table.getVisibleLeafColumns = makeVisibleColumnsMethod(\n      'getVisibleLeafColumns',\n      () => table.getAllLeafColumns()\n    )\n    table.getLeftVisibleLeafColumns = makeVisibleColumnsMethod(\n      'getLeftVisibleLeafColumns',\n      () => table.getLeftLeafColumns()\n    )\n    table.getRightVisibleLeafColumns = makeVisibleColumnsMethod(\n      'getRightVisibleLeafColumns',\n      () => table.getRightLeafColumns()\n    )\n    table.getCenterVisibleLeafColumns = makeVisibleColumnsMethod(\n      'getCenterVisibleLeafColumns',\n      () => table.getCenterLeafColumns()\n    )\n\n    table.setColumnVisibility = updater =>\n      table.options.onColumnVisibilityChange?.(updater)\n\n    table.resetColumnVisibility = defaultState => {\n      table.setColumnVisibility(\n        defaultState ? {} : table.initialState.columnVisibility ?? {}\n      )\n    }\n\n    table.toggleAllColumnsVisible = value => {\n      value = value ?? !table.getIsAllColumnsVisible()\n\n      table.setColumnVisibility(\n        table.getAllLeafColumns().reduce(\n          (obj, column) => ({\n            ...obj,\n            [column.id]: !value ? !column.getCanHide?.() : value,\n          }),\n          {}\n        )\n      )\n    }\n\n    table.getIsAllColumnsVisible = () =>\n      !table.getAllLeafColumns().some(column => !column.getIsVisible?.())\n\n    table.getIsSomeColumnsVisible = () =>\n      table.getAllLeafColumns().some(column => column.getIsVisible?.())\n\n    table.getToggleAllColumnsVisibilityHandler = () => {\n      return (e: unknown) => {\n        table.toggleAllColumnsVisible(\n          ((e as MouseEvent).target as HTMLInputElement)?.checked\n        )\n      }\n    }\n  },\n}\n\nexport function _getVisibleLeafColumns<TData extends RowData>(\n  table: Table<TData>,\n  position?: ColumnPinningPosition | 'center'\n) {\n  return !position\n    ? table.getVisibleLeafColumns()\n    : position === 'center'\n      ? table.getCenterVisibleLeafColumns()\n      : position === 'left'\n        ? table.getLeftVisibleLeafColumns()\n        : table.getRightVisibleLeafColumns()\n}\n", "import { RowModel } from '..'\nimport { Table, RowData, TableFeature } from '../types'\n\nexport interface GlobalFacetingInstance<TData extends RowData> {\n  _getGlobalFacetedMinMaxValues?: () => undefined | [number, number]\n  _getGlobalFacetedRowModel?: () => RowModel<TData>\n  _getGlobalFacetedUniqueValues?: () => Map<any, number>\n  /**\n   * Currently, this function returns the built-in `includesString` filter function. In future releases, it may return more dynamic filter functions based on the nature of the data provided.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-faceting#getglobalautofilterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-faceting)\n   */\n  getGlobalFacetedMinMaxValues: () => undefined | [number, number]\n  /**\n   * Returns the row model for the table after **global** filtering has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-faceting#getglobalfacetedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-faceting)\n   */\n  getGlobalFacetedRowModel: () => RowModel<TData>\n  /**\n   * Returns the faceted unique values for the global filter.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-faceting#getglobalfaceteduniquevalues)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-faceting)\n   */\n  getGlobalFacetedUniqueValues: () => Map<any, number>\n}\n\n//\n\nexport const GlobalFaceting: TableFeature = {\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table._getGlobalFacetedRowModel =\n      table.options.getFacetedRowModel &&\n      table.options.getFacetedRowModel(table, '__global__')\n\n    table.getGlobalFacetedRowModel = () => {\n      if (table.options.manualFiltering || !table._getGlobalFacetedRowModel) {\n        return table.getPreFilteredRowModel()\n      }\n\n      return table._getGlobalFacetedRowModel()\n    }\n\n    table._getGlobalFacetedUniqueValues =\n      table.options.getFacetedUniqueValues &&\n      table.options.getFacetedUniqueValues(table, '__global__')\n    table.getGlobalFacetedUniqueValues = () => {\n      if (!table._getGlobalFacetedUniqueValues) {\n        return new Map()\n      }\n\n      return table._getGlobalFacetedUniqueValues()\n    }\n\n    table._getGlobalFacetedMinMaxValues =\n      table.options.getFacetedMinMaxValues &&\n      table.options.getFacetedMinMaxValues(table, '__global__')\n    table.getGlobalFacetedMinMaxValues = () => {\n      if (!table._getGlobalFacetedMinMaxValues) {\n        return\n      }\n\n      return table._getGlobalFacetedMinMaxValues()\n    }\n  },\n}\n", "import { FilterFn, FilterFnOption } from '..'\nimport { BuiltInFilterFn, filterFns } from '../filterFns'\nimport {\n  Column,\n  OnChangeFn,\n  Table,\n  Updater,\n  RowData,\n  TableFeature,\n} from '../types'\nimport { isFunction, makeStateUpdater } from '../utils'\n\nexport interface GlobalFilterTableState {\n  globalFilter: any\n}\n\nexport interface GlobalFilterColumnDef {\n  /**\n   * Enables/disables the **global** filter for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#enableglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  enableGlobalFilter?: boolean\n}\n\nexport interface GlobalFilterColumn {\n  /**\n   * Returns whether or not the column can be **globally** filtered. Set to `false` to disable a column from being scanned during global filtering.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#getcanglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  getCanGlobalFilter: () => boolean\n}\n\nexport interface GlobalFilterOptions<TData extends RowData> {\n  /**\n   * Enables/disables **global** filtering for all columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#enableglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  enableGlobalFilter?: boolean\n  /**\n   * If provided, this function will be called with the column and should return `true` or `false` to indicate whether this column should be used for global filtering.\n   *\n   * This is useful if the column can contain data that is not `string` or `number` (i.e. `undefined`).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#getcolumncanglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  getColumnCanGlobalFilter?: (column: Column<TData, unknown>) => boolean\n  /**\n   * The filter function to use for global filtering.\n   * - A `string` referencing a built-in filter function\n   * - A `string` that references a custom filter functions provided via the `tableOptions.filterFns` option\n   * - A custom filter function\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#globalfilterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  globalFilterFn?: FilterFnOption<TData>\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.globalFilter` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#onglobalfilterchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  onGlobalFilterChange?: OnChangeFn<any>\n}\n\nexport interface GlobalFilterInstance<TData extends RowData> {\n  /**\n   * Currently, this function returns the built-in `includesString` filter function. In future releases, it may return more dynamic filter functions based on the nature of the data provided.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#getglobalautofilterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  getGlobalAutoFilterFn: () => FilterFn<TData> | undefined\n  /**\n   * Returns the filter function (either user-defined or automatic, depending on configuration) for the global filter.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#getglobalfilterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  getGlobalFilterFn: () => FilterFn<TData> | undefined\n  /**\n   * Resets the **globalFilter** state to `initialState.globalFilter`, or `true` can be passed to force a default blank state reset to `undefined`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#resetglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  resetGlobalFilter: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.globalFilter` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#setglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  setGlobalFilter: (updater: Updater<any>) => void\n}\n\n//\n\nexport const GlobalFiltering: TableFeature = {\n  getInitialState: (state): GlobalFilterTableState => {\n    return {\n      globalFilter: undefined,\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): GlobalFilterOptions<TData> => {\n    return {\n      onGlobalFilterChange: makeStateUpdater('globalFilter', table),\n      globalFilterFn: 'auto',\n      getColumnCanGlobalFilter: column => {\n        const value = table\n          .getCoreRowModel()\n          .flatRows[0]?._getAllCellsByColumnId()\n          [column.id]?.getValue()\n\n        return typeof value === 'string' || typeof value === 'number'\n      },\n    } as GlobalFilterOptions<TData>\n  },\n\n  createColumn: <TData extends RowData>(\n    column: Column<TData, unknown>,\n    table: Table<TData>\n  ): void => {\n    column.getCanGlobalFilter = () => {\n      return (\n        (column.columnDef.enableGlobalFilter ?? true) &&\n        (table.options.enableGlobalFilter ?? true) &&\n        (table.options.enableFilters ?? true) &&\n        (table.options.getColumnCanGlobalFilter?.(column) ?? true) &&\n        !!column.accessorFn\n      )\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.getGlobalAutoFilterFn = () => {\n      return filterFns.includesString\n    }\n\n    table.getGlobalFilterFn = () => {\n      const { globalFilterFn: globalFilterFn } = table.options\n\n      return isFunction(globalFilterFn)\n        ? globalFilterFn\n        : globalFilterFn === 'auto'\n          ? table.getGlobalAutoFilterFn()\n          : table.options.filterFns?.[globalFilterFn as string] ??\n            filterFns[globalFilterFn as BuiltInFilterFn]\n    }\n\n    table.setGlobalFilter = updater => {\n      table.options.onGlobalFilterChange?.(updater)\n    }\n\n    table.resetGlobalFilter = defaultState => {\n      table.setGlobalFilter(\n        defaultState ? undefined : table.initialState.globalFilter\n      )\n    }\n  },\n}\n", "import { RowModel } from '..'\nimport {\n  OnChangeFn,\n  Table,\n  Row,\n  Updater,\n  RowData,\n  TableFeature,\n} from '../types'\nimport { makeStateUpdater } from '../utils'\n\nexport type ExpandedStateList = Record<string, boolean>\nexport type ExpandedState = true | Record<string, boolean>\nexport interface ExpandedTableState {\n  expanded: ExpandedState\n}\n\nexport interface ExpandedRow {\n  /**\n   * Returns whether the row can be expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getcanexpand)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getCanExpand: () => boolean\n  /**\n   * Returns whether all parent rows of the row are expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getisallparentsexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getIsAllParentsExpanded: () => boolean\n  /**\n   * Returns whether the row is expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getisexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getIsExpanded: () => boolean\n  /**\n   * Returns a function that can be used to toggle the expanded state of the row. This function can be used to bind to an event handler to a button.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#gettoggleexpandedhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getToggleExpandedHandler: () => () => void\n  /**\n   * Toggles the expanded state (or sets it if `expanded` is provided) for the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#toggleexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  toggleExpanded: (expanded?: boolean) => void\n}\n\nexport interface ExpandedOptions<TData extends RowData> {\n  /**\n   * Enable this setting to automatically reset the expanded state of the table when expanding state changes.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#autoresetexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  autoResetExpanded?: boolean\n  /**\n   * Enable/disable expanding for all rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#enableexpanding)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  enableExpanding?: boolean\n  /**\n   * This function is responsible for returning the expanded row model. If this function is not provided, the table will not expand rows. You can use the default exported `getExpandedRowModel` function to get the expanded row model or implement your own.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getexpandedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getExpandedRowModel?: (table: Table<any>) => () => RowModel<any>\n  /**\n   * If provided, allows you to override the default behavior of determining whether a row is currently expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getisrowexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getIsRowExpanded?: (row: Row<TData>) => boolean\n  /**\n   * If provided, allows you to override the default behavior of determining whether a row can be expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getrowcanexpand)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getRowCanExpand?: (row: Row<TData>) => boolean\n  /**\n   * Enables manual row expansion. If this is set to `true`, `getExpandedRowModel` will not be used to expand rows and you would be expected to perform the expansion in your own data model. This is useful if you are doing server-side expansion.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#manualexpanding)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  manualExpanding?: boolean\n  /**\n   * This function is called when the `expanded` table state changes. If a function is provided, you will be responsible for managing this state on your own. To pass the managed state back to the table, use the `tableOptions.state.expanded` option.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#onexpandedchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  onExpandedChange?: OnChangeFn<ExpandedState>\n  /**\n   * If `true` expanded rows will be paginated along with the rest of the table (which means expanded rows may span multiple pages). If `false` expanded rows will not be considered for pagination (which means expanded rows will always render on their parents page. This also means more rows will be rendered than the set page size)\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#paginateexpandedrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  paginateExpandedRows?: boolean\n}\n\nexport interface ExpandedInstance<TData extends RowData> {\n  _autoResetExpanded: () => void\n  _getExpandedRowModel?: () => RowModel<TData>\n  /**\n   * Returns whether there are any rows that can be expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getcansomerowsexpand)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getCanSomeRowsExpand: () => boolean\n  /**\n   * Returns the maximum depth of the expanded rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getexpandeddepth)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getExpandedDepth: () => number\n  /**\n   * Returns the row model after expansion has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getexpandedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getExpandedRowModel: () => RowModel<TData>\n  /**\n   * Returns whether all rows are currently expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getisallrowsexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getIsAllRowsExpanded: () => boolean\n  /**\n   * Returns whether there are any rows that are currently expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getissomerowsexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getIsSomeRowsExpanded: () => boolean\n  /**\n   * Returns the row model before expansion has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getpreexpandedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getPreExpandedRowModel: () => RowModel<TData>\n  /**\n   * Returns a handler that can be used to toggle the expanded state of all rows. This handler is meant to be used with an `input[type=checkbox]` element.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#gettoggleallrowsexpandedhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getToggleAllRowsExpandedHandler: () => (event: unknown) => void\n  /**\n   * Resets the expanded state of the table to the initial state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#resetexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  resetExpanded: (defaultState?: boolean) => void\n  /**\n   * Updates the expanded state of the table via an update function or value.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#setexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  setExpanded: (updater: Updater<ExpandedState>) => void\n  /**\n   * Toggles the expanded state for all rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#toggleallrowsexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  toggleAllRowsExpanded: (expanded?: boolean) => void\n}\n\n//\n\nexport const RowExpanding: TableFeature = {\n  getInitialState: (state): ExpandedTableState => {\n    return {\n      expanded: {},\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ExpandedOptions<TData> => {\n    return {\n      onExpandedChange: makeStateUpdater('expanded', table),\n      paginateExpandedRows: true,\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    let registered = false\n    let queued = false\n\n    table._autoResetExpanded = () => {\n      if (!registered) {\n        table._queue(() => {\n          registered = true\n        })\n        return\n      }\n\n      if (\n        table.options.autoResetAll ??\n        table.options.autoResetExpanded ??\n        !table.options.manualExpanding\n      ) {\n        if (queued) return\n        queued = true\n        table._queue(() => {\n          table.resetExpanded()\n          queued = false\n        })\n      }\n    }\n    table.setExpanded = updater => table.options.onExpandedChange?.(updater)\n    table.toggleAllRowsExpanded = expanded => {\n      if (expanded ?? !table.getIsAllRowsExpanded()) {\n        table.setExpanded(true)\n      } else {\n        table.setExpanded({})\n      }\n    }\n    table.resetExpanded = defaultState => {\n      table.setExpanded(defaultState ? {} : table.initialState?.expanded ?? {})\n    }\n    table.getCanSomeRowsExpand = () => {\n      return table\n        .getPrePaginationRowModel()\n        .flatRows.some(row => row.getCanExpand())\n    }\n    table.getToggleAllRowsExpandedHandler = () => {\n      return (e: unknown) => {\n        ;(e as any).persist?.()\n        table.toggleAllRowsExpanded()\n      }\n    }\n    table.getIsSomeRowsExpanded = () => {\n      const expanded = table.getState().expanded\n      return expanded === true || Object.values(expanded).some(Boolean)\n    }\n    table.getIsAllRowsExpanded = () => {\n      const expanded = table.getState().expanded\n\n      // If expanded is true, save some cycles and return true\n      if (typeof expanded === 'boolean') {\n        return expanded === true\n      }\n\n      if (!Object.keys(expanded).length) {\n        return false\n      }\n\n      // If any row is not expanded, return false\n      if (table.getRowModel().flatRows.some(row => !row.getIsExpanded())) {\n        return false\n      }\n\n      // They must all be expanded :shrug:\n      return true\n    }\n    table.getExpandedDepth = () => {\n      let maxDepth = 0\n\n      const rowIds =\n        table.getState().expanded === true\n          ? Object.keys(table.getRowModel().rowsById)\n          : Object.keys(table.getState().expanded)\n\n      rowIds.forEach(id => {\n        const splitId = id.split('.')\n        maxDepth = Math.max(maxDepth, splitId.length)\n      })\n\n      return maxDepth\n    }\n    table.getPreExpandedRowModel = () => table.getSortedRowModel()\n    table.getExpandedRowModel = () => {\n      if (!table._getExpandedRowModel && table.options.getExpandedRowModel) {\n        table._getExpandedRowModel = table.options.getExpandedRowModel(table)\n      }\n\n      if (table.options.manualExpanding || !table._getExpandedRowModel) {\n        return table.getPreExpandedRowModel()\n      }\n\n      return table._getExpandedRowModel()\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row.toggleExpanded = expanded => {\n      table.setExpanded(old => {\n        const exists = old === true ? true : !!old?.[row.id]\n\n        let oldExpanded: ExpandedStateList = {}\n\n        if (old === true) {\n          Object.keys(table.getRowModel().rowsById).forEach(rowId => {\n            oldExpanded[rowId] = true\n          })\n        } else {\n          oldExpanded = old\n        }\n\n        expanded = expanded ?? !exists\n\n        if (!exists && expanded) {\n          return {\n            ...oldExpanded,\n            [row.id]: true,\n          }\n        }\n\n        if (exists && !expanded) {\n          const { [row.id]: _, ...rest } = oldExpanded\n          return rest\n        }\n\n        return old\n      })\n    }\n    row.getIsExpanded = () => {\n      const expanded = table.getState().expanded\n\n      return !!(\n        table.options.getIsRowExpanded?.(row) ??\n        (expanded === true || expanded?.[row.id])\n      )\n    }\n    row.getCanExpand = () => {\n      return (\n        table.options.getRowCanExpand?.(row) ??\n        ((table.options.enableExpanding ?? true) && !!row.subRows?.length)\n      )\n    }\n    row.getIsAllParentsExpanded = () => {\n      let isFullyExpanded = true\n      let currentRow = row\n\n      while (isFullyExpanded && currentRow.parentId) {\n        currentRow = table.getRow(currentRow.parentId, true)\n        isFullyExpanded = currentRow.getIsExpanded()\n      }\n\n      return isFullyExpanded\n    }\n    row.getToggleExpandedHandler = () => {\n      const canExpand = row.getCanExpand()\n\n      return () => {\n        if (!canExpand) return\n        row.toggleExpanded()\n      }\n    }\n  },\n}\n", "import {\n  OnChangeFn,\n  Table,\n  RowModel,\n  Updater,\n  <PERSON>Data,\n  TableFeature,\n} from '../types'\nimport {\n  functionalUpdate,\n  getMemoOptions,\n  makeStateUpdater,\n  memo,\n} from '../utils'\n\nexport interface PaginationState {\n  pageIndex: number\n  pageSize: number\n}\n\nexport interface PaginationTableState {\n  pagination: PaginationState\n}\n\nexport interface PaginationInitialTableState {\n  pagination?: Partial<PaginationState>\n}\n\nexport interface PaginationOptions {\n  /**\n   * If set to `true`, pagination will be reset to the first page when page-altering state changes eg. `data` is updated, filters change, grouping changes, etc.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#autoresetpageindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  autoResetPageIndex?: boolean\n  /**\n   * Returns the row model after pagination has taken place, but no further.\n   *\n   * Pagination columns are automatically reordered by default to the start of the columns list. If you would rather remove them or leave them as-is, set the appropriate mode here.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getpaginationrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getPaginationRowModel?: (table: Table<any>) => () => RowModel<any>\n  /**\n   * Enables manual pagination. If this option is set to `true`, the table will not automatically paginate rows using `getPaginationRowModel()` and instead will expect you to manually paginate the rows before passing them to the table. This is useful if you are doing server-side pagination and aggregation.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#manualpagination)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  manualPagination?: boolean\n  /**\n   * If this function is provided, it will be called when the pagination state changes and you will be expected to manage the state yourself. You can pass the managed state back to the table via the `tableOptions.state.pagination` option.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#onpaginationchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  onPaginationChange?: OnChangeFn<PaginationState>\n  /**\n   * When manually controlling pagination, you can supply a total `pageCount` value to the table if you know it (Or supply a `rowCount` and `pageCount` will be calculated). If you do not know how many pages there are, you can set this to `-1`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#pagecount)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  pageCount?: number\n  /**\n   * When manually controlling pagination, you can supply a total `rowCount` value to the table if you know it. The `pageCount` can be calculated from this value and the `pageSize`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#rowcount)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  rowCount?: number\n}\n\nexport interface PaginationDefaultOptions {\n  onPaginationChange: OnChangeFn<PaginationState>\n}\n\nexport interface PaginationInstance<TData extends RowData> {\n  _autoResetPageIndex: () => void\n  _getPaginationRowModel?: () => RowModel<TData>\n  /**\n   * Returns whether the table can go to the next page.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getcannextpage)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getCanNextPage: () => boolean\n  /**\n   * Returns whether the table can go to the previous page.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getcanpreviouspage)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getCanPreviousPage: () => boolean\n  /**\n   * Returns the page count. If manually paginating or controlling the pagination state, this will come directly from the `options.pageCount` table option, otherwise it will be calculated from the table data using the total row count and current page size.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getpagecount)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getPageCount: () => number\n  /**\n   * Returns the row count. If manually paginating or controlling the pagination state, this will come directly from the `options.rowCount` table option, otherwise it will be calculated from the table data.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getrowcount)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getRowCount: () => number\n  /**\n   * Returns an array of page options (zero-index-based) for the current page size.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getpageoptions)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getPageOptions: () => number[]\n  /**\n   * Returns the row model for the table after pagination has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getpaginationrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getPaginationRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model for the table before any pagination has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getprepaginationrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getPrePaginationRowModel: () => RowModel<TData>\n  /**\n   * Increments the page index by one, if possible.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#nextpage)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  nextPage: () => void\n  /**\n   * Decrements the page index by one, if possible.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#previouspage)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  previousPage: () => void\n  /**\n   * Sets the page index to `0`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#firstpage)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  firstPage: () => void\n  /**\n   * Sets the page index to the last page.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#lastpage)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  lastPage: () => void\n  /**\n   * Resets the page index to its initial state. If `defaultState` is `true`, the page index will be reset to `0` regardless of initial state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#resetpageindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  resetPageIndex: (defaultState?: boolean) => void\n  /**\n   * Resets the page size to its initial state. If `defaultState` is `true`, the page size will be reset to `10` regardless of initial state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#resetpagesize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  resetPageSize: (defaultState?: boolean) => void\n  /**\n   * Resets the **pagination** state to `initialState.pagination`, or `true` can be passed to force a default blank state reset to `[]`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#resetpagination)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  resetPagination: (defaultState?: boolean) => void\n  /**\n   * @deprecated The page count no longer exists in the pagination state. Just pass as a table option instead.\n   */\n  setPageCount: (updater: Updater<number>) => void\n  /**\n   * Updates the page index using the provided function or value in the `state.pagination.pageIndex` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#setpageindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  setPageIndex: (updater: Updater<number>) => void\n  /**\n   * Updates the page size using the provided function or value in the `state.pagination.pageSize` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#setpagesize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  setPageSize: (updater: Updater<number>) => void\n  /**\n   * Sets or updates the `state.pagination` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#setpagination)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  setPagination: (updater: Updater<PaginationState>) => void\n}\n\n//\n\nconst defaultPageIndex = 0\nconst defaultPageSize = 10\n\nconst getDefaultPaginationState = (): PaginationState => ({\n  pageIndex: defaultPageIndex,\n  pageSize: defaultPageSize,\n})\n\nexport const RowPagination: TableFeature = {\n  getInitialState: (state): PaginationTableState => {\n    return {\n      ...state,\n      pagination: {\n        ...getDefaultPaginationState(),\n        ...state?.pagination,\n      },\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): PaginationDefaultOptions => {\n    return {\n      onPaginationChange: makeStateUpdater('pagination', table),\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    let registered = false\n    let queued = false\n\n    table._autoResetPageIndex = () => {\n      if (!registered) {\n        table._queue(() => {\n          registered = true\n        })\n        return\n      }\n\n      if (\n        table.options.autoResetAll ??\n        table.options.autoResetPageIndex ??\n        !table.options.manualPagination\n      ) {\n        if (queued) return\n        queued = true\n        table._queue(() => {\n          table.resetPageIndex()\n          queued = false\n        })\n      }\n    }\n    table.setPagination = updater => {\n      const safeUpdater: Updater<PaginationState> = old => {\n        let newState = functionalUpdate(updater, old)\n\n        return newState\n      }\n\n      return table.options.onPaginationChange?.(safeUpdater)\n    }\n    table.resetPagination = defaultState => {\n      table.setPagination(\n        defaultState\n          ? getDefaultPaginationState()\n          : table.initialState.pagination ?? getDefaultPaginationState()\n      )\n    }\n    table.setPageIndex = updater => {\n      table.setPagination(old => {\n        let pageIndex = functionalUpdate(updater, old.pageIndex)\n\n        const maxPageIndex =\n          typeof table.options.pageCount === 'undefined' ||\n          table.options.pageCount === -1\n            ? Number.MAX_SAFE_INTEGER\n            : table.options.pageCount - 1\n\n        pageIndex = Math.max(0, Math.min(pageIndex, maxPageIndex))\n\n        return {\n          ...old,\n          pageIndex,\n        }\n      })\n    }\n    table.resetPageIndex = defaultState => {\n      table.setPageIndex(\n        defaultState\n          ? defaultPageIndex\n          : table.initialState?.pagination?.pageIndex ?? defaultPageIndex\n      )\n    }\n    table.resetPageSize = defaultState => {\n      table.setPageSize(\n        defaultState\n          ? defaultPageSize\n          : table.initialState?.pagination?.pageSize ?? defaultPageSize\n      )\n    }\n    table.setPageSize = updater => {\n      table.setPagination(old => {\n        const pageSize = Math.max(1, functionalUpdate(updater, old.pageSize))\n        const topRowIndex = old.pageSize * old.pageIndex!\n        const pageIndex = Math.floor(topRowIndex / pageSize)\n\n        return {\n          ...old,\n          pageIndex,\n          pageSize,\n        }\n      })\n    }\n    //deprecated\n    table.setPageCount = updater =>\n      table.setPagination(old => {\n        let newPageCount = functionalUpdate(\n          updater,\n          table.options.pageCount ?? -1\n        )\n\n        if (typeof newPageCount === 'number') {\n          newPageCount = Math.max(-1, newPageCount)\n        }\n\n        return {\n          ...old,\n          pageCount: newPageCount,\n        }\n      })\n\n    table.getPageOptions = memo(\n      () => [table.getPageCount()],\n      pageCount => {\n        let pageOptions: number[] = []\n        if (pageCount && pageCount > 0) {\n          pageOptions = [...new Array(pageCount)].fill(null).map((_, i) => i)\n        }\n        return pageOptions\n      },\n      getMemoOptions(table.options, 'debugTable', 'getPageOptions')\n    )\n\n    table.getCanPreviousPage = () => table.getState().pagination.pageIndex > 0\n\n    table.getCanNextPage = () => {\n      const { pageIndex } = table.getState().pagination\n\n      const pageCount = table.getPageCount()\n\n      if (pageCount === -1) {\n        return true\n      }\n\n      if (pageCount === 0) {\n        return false\n      }\n\n      return pageIndex < pageCount - 1\n    }\n\n    table.previousPage = () => {\n      return table.setPageIndex(old => old - 1)\n    }\n\n    table.nextPage = () => {\n      return table.setPageIndex(old => {\n        return old + 1\n      })\n    }\n\n    table.firstPage = () => {\n      return table.setPageIndex(0)\n    }\n\n    table.lastPage = () => {\n      return table.setPageIndex(table.getPageCount() - 1)\n    }\n\n    table.getPrePaginationRowModel = () => table.getExpandedRowModel()\n    table.getPaginationRowModel = () => {\n      if (\n        !table._getPaginationRowModel &&\n        table.options.getPaginationRowModel\n      ) {\n        table._getPaginationRowModel =\n          table.options.getPaginationRowModel(table)\n      }\n\n      if (table.options.manualPagination || !table._getPaginationRowModel) {\n        return table.getPrePaginationRowModel()\n      }\n\n      return table._getPaginationRowModel()\n    }\n\n    table.getPageCount = () => {\n      return (\n        table.options.pageCount ??\n        Math.ceil(table.getRowCount() / table.getState().pagination.pageSize)\n      )\n    }\n\n    table.getRowCount = () => {\n      return (\n        table.options.rowCount ?? table.getPrePaginationRowModel().rows.length\n      )\n    }\n  },\n}\n", "import {\n  OnChangeFn,\n  Updater,\n  Table,\n  Row,\n  RowData,\n  TableFeature,\n} from '../types'\nimport { getMemoOptions, makeStateUpdater, memo } from '../utils'\n\nexport type RowPinningPosition = false | 'top' | 'bottom'\n\nexport interface RowPinningState {\n  bottom?: string[]\n  top?: string[]\n}\n\nexport interface RowPinningTableState {\n  rowPinning: RowPinningState\n}\n\nexport interface RowPinningOptions<TData extends RowData> {\n  /**\n   * Enables/disables row pinning for the table. Defaults to `true`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#enablerowpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  enableRowPinning?: boolean | ((row: Row<TData>) => boolean)\n  /**\n   * When `false`, pinned rows will not be visible if they are filtered or paginated out of the table. When `true`, pinned rows will always be visible regardless of filtering or pagination. Defaults to `true`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#keeppinnedrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  keepPinnedRows?: boolean\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.rowPinning` changes. This overrides the default internal state management, so you will also need to supply `state.rowPinning` from your own managed state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#onrowpinningchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/onrowpinningchange)\n   */\n  onRowPinningChange?: OnChangeFn<RowPinningState>\n}\n\nexport interface RowPinningDefaultOptions {\n  onRowPinningChange: OnChangeFn<RowPinningState>\n}\n\nexport interface RowPinningRow {\n  /**\n   * Returns whether or not the row can be pinned.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#getcanpin-1)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getCanPin: () => boolean\n  /**\n   * Returns the pinned position of the row. (`'top'`, `'bottom'` or `false`)\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#getispinned-1)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getIsPinned: () => RowPinningPosition\n  /**\n   * Returns the numeric pinned index of the row within a pinned row group.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#getpinnedindex-1)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getPinnedIndex: () => number\n  /**\n   * Pins a row to the `'top'` or `'bottom'`, or unpins the row to the center if `false` is passed.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#pin-1)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  pin: (\n    position: RowPinningPosition,\n    includeLeafRows?: boolean,\n    includeParentRows?: boolean\n  ) => void\n}\n\nexport interface RowPinningInstance<TData extends RowData> {\n  _getPinnedRows: (\n    visiblePinnedRows: Array<Row<TData>>,\n    pinnedRowIds: Array<string> | undefined,\n    position: 'top' | 'bottom'\n  ) => Row<TData>[]\n  /**\n   * Returns all bottom pinned rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#getbottomrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getBottomRows: () => Row<TData>[]\n  /**\n   * Returns all rows that are not pinned to the top or bottom.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#getcenterrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getCenterRows: () => Row<TData>[]\n  /**\n   * Returns whether or not any rows are pinned. Optionally specify to only check for pinned rows in either the `top` or `bottom` position.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#getissomerowspinned)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getIsSomeRowsPinned: (position?: RowPinningPosition) => boolean\n  /**\n   * Returns all top pinned rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#gettoprows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getTopRows: () => Row<TData>[]\n  /**\n   * Resets the **rowPinning** state to `initialState.rowPinning`, or `true` can be passed to force a default blank state reset to `{ top: [], bottom: [], }`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#resetrowpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  resetRowPinning: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.rowPinning` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#setrowpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  setRowPinning: (updater: Updater<RowPinningState>) => void\n}\n\n//\n\nconst getDefaultRowPinningState = (): RowPinningState => ({\n  top: [],\n  bottom: [],\n})\n\nexport const RowPinning: TableFeature = {\n  getInitialState: (state): RowPinningTableState => {\n    return {\n      rowPinning: getDefaultRowPinningState(),\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): RowPinningDefaultOptions => {\n    return {\n      onRowPinningChange: makeStateUpdater('rowPinning', table),\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row.pin = (position, includeLeafRows, includeParentRows) => {\n      const leafRowIds = includeLeafRows\n        ? row.getLeafRows().map(({ id }) => id)\n        : []\n      const parentRowIds = includeParentRows\n        ? row.getParentRows().map(({ id }) => id)\n        : []\n      const rowIds = new Set([...parentRowIds, row.id, ...leafRowIds])\n\n      table.setRowPinning(old => {\n        if (position === 'bottom') {\n          return {\n            top: (old?.top ?? []).filter(d => !rowIds?.has(d)),\n            bottom: [\n              ...(old?.bottom ?? []).filter(d => !rowIds?.has(d)),\n              ...Array.from(rowIds),\n            ],\n          }\n        }\n\n        if (position === 'top') {\n          return {\n            top: [\n              ...(old?.top ?? []).filter(d => !rowIds?.has(d)),\n              ...Array.from(rowIds),\n            ],\n            bottom: (old?.bottom ?? []).filter(d => !rowIds?.has(d)),\n          }\n        }\n\n        return {\n          top: (old?.top ?? []).filter(d => !rowIds?.has(d)),\n          bottom: (old?.bottom ?? []).filter(d => !rowIds?.has(d)),\n        }\n      })\n    }\n    row.getCanPin = () => {\n      const { enableRowPinning, enablePinning } = table.options\n      if (typeof enableRowPinning === 'function') {\n        return enableRowPinning(row)\n      }\n      return enableRowPinning ?? enablePinning ?? true\n    }\n    row.getIsPinned = () => {\n      const rowIds = [row.id]\n\n      const { top, bottom } = table.getState().rowPinning\n\n      const isTop = rowIds.some(d => top?.includes(d))\n      const isBottom = rowIds.some(d => bottom?.includes(d))\n\n      return isTop ? 'top' : isBottom ? 'bottom' : false\n    }\n    row.getPinnedIndex = () => {\n      const position = row.getIsPinned()\n      if (!position) return -1\n\n      const visiblePinnedRowIds = (\n        position === 'top' ? table.getTopRows() : table.getBottomRows()\n      )?.map(({ id }) => id)\n\n      return visiblePinnedRowIds?.indexOf(row.id) ?? -1\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setRowPinning = updater => table.options.onRowPinningChange?.(updater)\n\n    table.resetRowPinning = defaultState =>\n      table.setRowPinning(\n        defaultState\n          ? getDefaultRowPinningState()\n          : table.initialState?.rowPinning ?? getDefaultRowPinningState()\n      )\n\n    table.getIsSomeRowsPinned = position => {\n      const pinningState = table.getState().rowPinning\n\n      if (!position) {\n        return Boolean(pinningState.top?.length || pinningState.bottom?.length)\n      }\n      return Boolean(pinningState[position]?.length)\n    }\n\n    table._getPinnedRows = (visibleRows, pinnedRowIds, position) => {\n      const rows =\n        table.options.keepPinnedRows ?? true\n          ? //get all rows that are pinned even if they would not be otherwise visible\n            //account for expanded parent rows, but not pagination or filtering\n            (pinnedRowIds ?? []).map(rowId => {\n              const row = table.getRow(rowId, true)\n              return row.getIsAllParentsExpanded() ? row : null\n            })\n          : //else get only visible rows that are pinned\n            (pinnedRowIds ?? []).map(\n              rowId => visibleRows.find(row => row.id === rowId)!\n            )\n\n      return rows.filter(Boolean).map(d => ({ ...d, position })) as Row<TData>[]\n    }\n\n    table.getTopRows = memo(\n      () => [table.getRowModel().rows, table.getState().rowPinning.top],\n      (allRows, topPinnedRowIds) =>\n        table._getPinnedRows(allRows, topPinnedRowIds, 'top'),\n      getMemoOptions(table.options, 'debugRows', 'getTopRows')\n    )\n\n    table.getBottomRows = memo(\n      () => [table.getRowModel().rows, table.getState().rowPinning.bottom],\n      (allRows, bottomPinnedRowIds) =>\n        table._getPinnedRows(allRows, bottomPinnedRowIds, 'bottom'),\n      getMemoOptions(table.options, 'debugRows', 'getBottomRows')\n    )\n\n    table.getCenterRows = memo(\n      () => [\n        table.getRowModel().rows,\n        table.getState().rowPinning.top,\n        table.getState().rowPinning.bottom,\n      ],\n      (allRows, top, bottom) => {\n        const topAndBottom = new Set([...(top ?? []), ...(bottom ?? [])])\n        return allRows.filter(d => !topAndBottom.has(d.id))\n      },\n      getMemoOptions(table.options, 'debugRows', 'getCenterRows')\n    )\n  },\n}\n", "import {\n  OnChangeFn,\n  Table,\n  Row,\n  <PERSON>Model,\n  Updater,\n  RowData,\n  TableFeature,\n} from '../types'\nimport { getMemoOptions, makeStateUpdater, memo } from '../utils'\n\nexport type RowSelectionState = Record<string, boolean>\n\nexport interface RowSelectionTableState {\n  rowSelection: RowSelectionState\n}\n\nexport interface RowSelectionOptions<TData extends RowData> {\n  /**\n   * - Enables/disables multiple row selection for all rows in the table OR\n   * - A function that given a row, returns whether to enable/disable multiple row selection for that row's children/grandchildren\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#enablemultirowselection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  enableMultiRowSelection?: boolean | ((row: Row<TData>) => boolean)\n  /**\n   * - Enables/disables row selection for all rows in the table OR\n   * - A function that given a row, returns whether to enable/disable row selection for that row\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#enablerowselection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  enableRowSelection?: boolean | ((row: Row<TData>) => boolean)\n  /**\n   * Enables/disables automatic sub-row selection when a parent row is selected, or a function that enables/disables automatic sub-row selection for each row.\n   * (Use in combination with expanding or grouping features)\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#enablesubrowselection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  enableSubRowSelection?: boolean | ((row: Row<TData>) => boolean)\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.rowSelection` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#onrowselectionchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  onRowSelectionChange?: OnChangeFn<RowSelectionState>\n  // enableGroupingRowSelection?:\n  //   | boolean\n  //   | ((\n  //       row: Row<TData>\n  //     ) => boolean)\n  // isAdditiveSelectEvent?: (e: unknown) => boolean\n  // isInclusiveSelectEvent?: (e: unknown) => boolean\n  // selectRowsFn?: (\n  //   table: Table<TData>,\n  //   rowModel: RowModel<TData>\n  // ) => RowModel<TData>\n}\n\nexport interface RowSelectionRow {\n  /**\n   * Returns whether or not the row can multi-select.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getcanmultiselect)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getCanMultiSelect: () => boolean\n  /**\n   * Returns whether or not the row can be selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getcanselect)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getCanSelect: () => boolean\n  /**\n   * Returns whether or not the row can select sub rows automatically when the parent row is selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getcanselectsubrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getCanSelectSubRows: () => boolean\n  /**\n   * Returns whether or not all of the row's sub rows are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getisallsubrowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsAllSubRowsSelected: () => boolean\n  /**\n   * Returns whether or not the row is selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getisselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsSelected: () => boolean\n  /**\n   * Returns whether or not some of the row's sub rows are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getissomeselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsSomeSelected: () => boolean\n  /**\n   * Returns a handler that can be used to toggle the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#gettoggleselectedhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getToggleSelectedHandler: () => (event: unknown) => void\n  /**\n   * Selects/deselects the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#toggleselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  toggleSelected: (value?: boolean, opts?: { selectChildren?: boolean }) => void\n}\n\nexport interface RowSelectionInstance<TData extends RowData> {\n  /**\n   * Returns the row model of all rows that are selected after filtering has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getfilteredselectedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getFilteredSelectedRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model of all rows that are selected after grouping has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getgroupedselectedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getGroupedSelectedRowModel: () => RowModel<TData>\n  /**\n   * Returns whether or not all rows on the current page are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getisallpagerowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsAllPageRowsSelected: () => boolean\n  /**\n   * Returns whether or not all rows in the table are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getisallrowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsAllRowsSelected: () => boolean\n  /**\n   * Returns whether or not any rows on the current page are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getissomepagerowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsSomePageRowsSelected: () => boolean\n  /**\n   * Returns whether or not any rows in the table are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getissomerowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsSomeRowsSelected: () => boolean\n  /**\n   * Returns the core row model of all rows before row selection has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getpreselectedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getPreSelectedRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model of all rows that are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getselectedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getSelectedRowModel: () => RowModel<TData>\n  /**\n   * Returns a handler that can be used to toggle all rows on the current page.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#gettoggleallpagerowsselectedhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getToggleAllPageRowsSelectedHandler: () => (event: unknown) => void\n  /**\n   * Returns a handler that can be used to toggle all rows in the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#gettoggleallrowsselectedhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getToggleAllRowsSelectedHandler: () => (event: unknown) => void\n  /**\n   * Resets the **rowSelection** state to the `initialState.rowSelection`, or `true` can be passed to force a default blank state reset to `{}`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#resetrowselection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  resetRowSelection: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.rowSelection` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#setrowselection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  setRowSelection: (updater: Updater<RowSelectionState>) => void\n  /**\n   * Selects/deselects all rows on the current page.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#toggleallpagerowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  toggleAllPageRowsSelected: (value?: boolean) => void\n  /**\n   * Selects/deselects all rows in the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#toggleallrowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  toggleAllRowsSelected: (value?: boolean) => void\n}\n\n//\n\nexport const RowSelection: TableFeature = {\n  getInitialState: (state): RowSelectionTableState => {\n    return {\n      rowSelection: {},\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): RowSelectionOptions<TData> => {\n    return {\n      onRowSelectionChange: makeStateUpdater('rowSelection', table),\n      enableRowSelection: true,\n      enableMultiRowSelection: true,\n      enableSubRowSelection: true,\n      // enableGroupingRowSelection: false,\n      // isAdditiveSelectEvent: (e: unknown) => !!e.metaKey,\n      // isInclusiveSelectEvent: (e: unknown) => !!e.shiftKey,\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setRowSelection = updater =>\n      table.options.onRowSelectionChange?.(updater)\n    table.resetRowSelection = defaultState =>\n      table.setRowSelection(\n        defaultState ? {} : table.initialState.rowSelection ?? {}\n      )\n    table.toggleAllRowsSelected = value => {\n      table.setRowSelection(old => {\n        value =\n          typeof value !== 'undefined' ? value : !table.getIsAllRowsSelected()\n\n        const rowSelection = { ...old }\n\n        const preGroupedFlatRows = table.getPreGroupedRowModel().flatRows\n\n        // We don't use `mutateRowIsSelected` here for performance reasons.\n        // All of the rows are flat already, so it wouldn't be worth it\n        if (value) {\n          preGroupedFlatRows.forEach(row => {\n            if (!row.getCanSelect()) {\n              return\n            }\n            rowSelection[row.id] = true\n          })\n        } else {\n          preGroupedFlatRows.forEach(row => {\n            delete rowSelection[row.id]\n          })\n        }\n\n        return rowSelection\n      })\n    }\n    table.toggleAllPageRowsSelected = value =>\n      table.setRowSelection(old => {\n        const resolvedValue =\n          typeof value !== 'undefined'\n            ? value\n            : !table.getIsAllPageRowsSelected()\n\n        const rowSelection: RowSelectionState = { ...old }\n\n        table.getRowModel().rows.forEach(row => {\n          mutateRowIsSelected(rowSelection, row.id, resolvedValue, true, table)\n        })\n\n        return rowSelection\n      })\n\n    // addRowSelectionRange: rowId => {\n    //   const {\n    //     rows,\n    //     rowsById,\n    //     options: { selectGroupingRows, selectSubRows },\n    //   } = table\n\n    //   const findSelectedRow = (rows: Row[]) => {\n    //     let found\n    //     rows.find(d => {\n    //       if (d.getIsSelected()) {\n    //         found = d\n    //         return true\n    //       }\n    //       const subFound = findSelectedRow(d.subRows || [])\n    //       if (subFound) {\n    //         found = subFound\n    //         return true\n    //       }\n    //       return false\n    //     })\n    //     return found\n    //   }\n\n    //   const firstRow = findSelectedRow(rows) || rows[0]\n    //   const lastRow = rowsById[rowId]\n\n    //   let include = false\n    //   const selectedRowIds = {}\n\n    //   const addRow = (row: Row) => {\n    //     mutateRowIsSelected(selectedRowIds, row.id, true, {\n    //       rowsById,\n    //       selectGroupingRows: selectGroupingRows!,\n    //       selectSubRows: selectSubRows!,\n    //     })\n    //   }\n\n    //   table.rows.forEach(row => {\n    //     const isFirstRow = row.id === firstRow.id\n    //     const isLastRow = row.id === lastRow.id\n\n    //     if (isFirstRow || isLastRow) {\n    //       if (!include) {\n    //         include = true\n    //       } else if (include) {\n    //         addRow(row)\n    //         include = false\n    //       }\n    //     }\n\n    //     if (include) {\n    //       addRow(row)\n    //     }\n    //   })\n\n    //   table.setRowSelection(selectedRowIds)\n    // },\n    table.getPreSelectedRowModel = () => table.getCoreRowModel()\n    table.getSelectedRowModel = memo(\n      () => [table.getState().rowSelection, table.getCoreRowModel()],\n      (rowSelection, rowModel) => {\n        if (!Object.keys(rowSelection).length) {\n          return {\n            rows: [],\n            flatRows: [],\n            rowsById: {},\n          }\n        }\n\n        return selectRowsFn(table, rowModel)\n      },\n      getMemoOptions(table.options, 'debugTable', 'getSelectedRowModel')\n    )\n\n    table.getFilteredSelectedRowModel = memo(\n      () => [table.getState().rowSelection, table.getFilteredRowModel()],\n      (rowSelection, rowModel) => {\n        if (!Object.keys(rowSelection).length) {\n          return {\n            rows: [],\n            flatRows: [],\n            rowsById: {},\n          }\n        }\n\n        return selectRowsFn(table, rowModel)\n      },\n      getMemoOptions(table.options, 'debugTable', 'getFilteredSelectedRowModel')\n    )\n\n    table.getGroupedSelectedRowModel = memo(\n      () => [table.getState().rowSelection, table.getSortedRowModel()],\n      (rowSelection, rowModel) => {\n        if (!Object.keys(rowSelection).length) {\n          return {\n            rows: [],\n            flatRows: [],\n            rowsById: {},\n          }\n        }\n\n        return selectRowsFn(table, rowModel)\n      },\n      getMemoOptions(table.options, 'debugTable', 'getGroupedSelectedRowModel')\n    )\n\n    ///\n\n    // getGroupingRowCanSelect: rowId => {\n    //   const row = table.getRow(rowId)\n\n    //   if (!row) {\n    //     throw new Error()\n    //   }\n\n    //   if (typeof table.options.enableGroupingRowSelection === 'function') {\n    //     return table.options.enableGroupingRowSelection(row)\n    //   }\n\n    //   return table.options.enableGroupingRowSelection ?? false\n    // },\n\n    table.getIsAllRowsSelected = () => {\n      const preGroupedFlatRows = table.getFilteredRowModel().flatRows\n      const { rowSelection } = table.getState()\n\n      let isAllRowsSelected = Boolean(\n        preGroupedFlatRows.length && Object.keys(rowSelection).length\n      )\n\n      if (isAllRowsSelected) {\n        if (\n          preGroupedFlatRows.some(\n            row => row.getCanSelect() && !rowSelection[row.id]\n          )\n        ) {\n          isAllRowsSelected = false\n        }\n      }\n\n      return isAllRowsSelected\n    }\n\n    table.getIsAllPageRowsSelected = () => {\n      const paginationFlatRows = table\n        .getPaginationRowModel()\n        .flatRows.filter(row => row.getCanSelect())\n      const { rowSelection } = table.getState()\n\n      let isAllPageRowsSelected = !!paginationFlatRows.length\n\n      if (\n        isAllPageRowsSelected &&\n        paginationFlatRows.some(row => !rowSelection[row.id])\n      ) {\n        isAllPageRowsSelected = false\n      }\n\n      return isAllPageRowsSelected\n    }\n\n    table.getIsSomeRowsSelected = () => {\n      const totalSelected = Object.keys(\n        table.getState().rowSelection ?? {}\n      ).length\n      return (\n        totalSelected > 0 &&\n        totalSelected < table.getFilteredRowModel().flatRows.length\n      )\n    }\n\n    table.getIsSomePageRowsSelected = () => {\n      const paginationFlatRows = table.getPaginationRowModel().flatRows\n      return table.getIsAllPageRowsSelected()\n        ? false\n        : paginationFlatRows\n            .filter(row => row.getCanSelect())\n            .some(d => d.getIsSelected() || d.getIsSomeSelected())\n    }\n\n    table.getToggleAllRowsSelectedHandler = () => {\n      return (e: unknown) => {\n        table.toggleAllRowsSelected(\n          ((e as MouseEvent).target as HTMLInputElement).checked\n        )\n      }\n    }\n\n    table.getToggleAllPageRowsSelectedHandler = () => {\n      return (e: unknown) => {\n        table.toggleAllPageRowsSelected(\n          ((e as MouseEvent).target as HTMLInputElement).checked\n        )\n      }\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row.toggleSelected = (value, opts) => {\n      const isSelected = row.getIsSelected()\n\n      table.setRowSelection(old => {\n        value = typeof value !== 'undefined' ? value : !isSelected\n\n        if (row.getCanSelect() && isSelected === value) {\n          return old\n        }\n\n        const selectedRowIds = { ...old }\n\n        mutateRowIsSelected(\n          selectedRowIds,\n          row.id,\n          value,\n          opts?.selectChildren ?? true,\n          table\n        )\n\n        return selectedRowIds\n      })\n    }\n    row.getIsSelected = () => {\n      const { rowSelection } = table.getState()\n      return isRowSelected(row, rowSelection)\n    }\n\n    row.getIsSomeSelected = () => {\n      const { rowSelection } = table.getState()\n      return isSubRowSelected(row, rowSelection, table) === 'some'\n    }\n\n    row.getIsAllSubRowsSelected = () => {\n      const { rowSelection } = table.getState()\n      return isSubRowSelected(row, rowSelection, table) === 'all'\n    }\n\n    row.getCanSelect = () => {\n      if (typeof table.options.enableRowSelection === 'function') {\n        return table.options.enableRowSelection(row)\n      }\n\n      return table.options.enableRowSelection ?? true\n    }\n\n    row.getCanSelectSubRows = () => {\n      if (typeof table.options.enableSubRowSelection === 'function') {\n        return table.options.enableSubRowSelection(row)\n      }\n\n      return table.options.enableSubRowSelection ?? true\n    }\n\n    row.getCanMultiSelect = () => {\n      if (typeof table.options.enableMultiRowSelection === 'function') {\n        return table.options.enableMultiRowSelection(row)\n      }\n\n      return table.options.enableMultiRowSelection ?? true\n    }\n    row.getToggleSelectedHandler = () => {\n      const canSelect = row.getCanSelect()\n\n      return (e: unknown) => {\n        if (!canSelect) return\n        row.toggleSelected(\n          ((e as MouseEvent).target as HTMLInputElement)?.checked\n        )\n      }\n    }\n  },\n}\n\nconst mutateRowIsSelected = <TData extends RowData>(\n  selectedRowIds: Record<string, boolean>,\n  id: string,\n  value: boolean,\n  includeChildren: boolean,\n  table: Table<TData>\n) => {\n  const row = table.getRow(id, true)\n\n  // const isGrouped = row.getIsGrouped()\n\n  // if ( // TODO: enforce grouping row selection rules\n  //   !isGrouped ||\n  //   (isGrouped && table.options.enableGroupingRowSelection)\n  // ) {\n  if (value) {\n    if (!row.getCanMultiSelect()) {\n      Object.keys(selectedRowIds).forEach(key => delete selectedRowIds[key])\n    }\n    if (row.getCanSelect()) {\n      selectedRowIds[id] = true\n    }\n  } else {\n    delete selectedRowIds[id]\n  }\n  // }\n\n  if (includeChildren && row.subRows?.length && row.getCanSelectSubRows()) {\n    row.subRows.forEach(row =>\n      mutateRowIsSelected(selectedRowIds, row.id, value, includeChildren, table)\n    )\n  }\n}\n\nexport function selectRowsFn<TData extends RowData>(\n  table: Table<TData>,\n  rowModel: RowModel<TData>\n): RowModel<TData> {\n  const rowSelection = table.getState().rowSelection\n\n  const newSelectedFlatRows: Row<TData>[] = []\n  const newSelectedRowsById: Record<string, Row<TData>> = {}\n\n  // Filters top level and nested rows\n  const recurseRows = (rows: Row<TData>[], depth = 0): Row<TData>[] => {\n    return rows\n      .map(row => {\n        const isSelected = isRowSelected(row, rowSelection)\n\n        if (isSelected) {\n          newSelectedFlatRows.push(row)\n          newSelectedRowsById[row.id] = row\n        }\n\n        if (row.subRows?.length) {\n          row = {\n            ...row,\n            subRows: recurseRows(row.subRows, depth + 1),\n          }\n        }\n\n        if (isSelected) {\n          return row\n        }\n      })\n      .filter(Boolean) as Row<TData>[]\n  }\n\n  return {\n    rows: recurseRows(rowModel.rows),\n    flatRows: newSelectedFlatRows,\n    rowsById: newSelectedRowsById,\n  }\n}\n\nexport function isRowSelected<TData extends RowData>(\n  row: Row<TData>,\n  selection: Record<string, boolean>\n): boolean {\n  return selection[row.id] ?? false\n}\n\nexport function isSubRowSelected<TData extends RowData>(\n  row: Row<TData>,\n  selection: Record<string, boolean>,\n  table: Table<TData>\n): boolean | 'some' | 'all' {\n  if (!row.subRows?.length) return false\n\n  let allChildrenSelected = true\n  let someSelected = false\n\n  row.subRows.forEach(subRow => {\n    // Bail out early if we know both of these\n    if (someSelected && !allChildrenSelected) {\n      return\n    }\n\n    if (subRow.getCanSelect()) {\n      if (isRowSelected(subRow, selection)) {\n        someSelected = true\n      } else {\n        allChildrenSelected = false\n      }\n    }\n\n    // Check row selection of nested subrows\n    if (subRow.subRows && subRow.subRows.length) {\n      const subRowChildrenSelected = isSubRowSelected(subRow, selection, table)\n      if (subRowChildrenSelected === 'all') {\n        someSelected = true\n      } else if (subRowChildrenSelected === 'some') {\n        someSelected = true\n        allChildrenSelected = false\n      } else {\n        allChildrenSelected = false\n      }\n    }\n  })\n\n  return allChildrenSelected ? 'all' : someSelected ? 'some' : false\n}\n", "import { SortingFn } from './features/RowSorting'\n\nexport const reSplitAlphaNumeric = /([0-9]+)/gm\n\nconst alphanumeric: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareAlphanumeric(\n    toString(rowA.getValue(columnId)).toLowerCase(),\n    toString(rowB.getValue(columnId)).toLowerCase()\n  )\n}\n\nconst alphanumericCaseSensitive: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareAlphanumeric(\n    toString(rowA.getValue(columnId)),\n    toString(rowB.getValue(columnId))\n  )\n}\n\n// The text filter is more basic (less numeric support)\n// but is much faster\nconst text: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareBasic(\n    toString(rowA.getValue(columnId)).toLowerCase(),\n    toString(rowB.getValue(columnId)).toLowerCase()\n  )\n}\n\n// The text filter is more basic (less numeric support)\n// but is much faster\nconst textCaseSensitive: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareBasic(\n    toString(rowA.getValue(columnId)),\n    toString(rowB.getValue(columnId))\n  )\n}\n\nconst datetime: SortingFn<any> = (rowA, rowB, columnId) => {\n  const a = rowA.getValue<Date>(columnId)\n  const b = rowB.getValue<Date>(columnId)\n\n  // Can handle nullish values\n  // Use > and < because == (and ===) doesn't work with\n  // Date objects (would require calling getTime()).\n  return a > b ? 1 : a < b ? -1 : 0\n}\n\nconst basic: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareBasic(rowA.getValue(columnId), rowB.getValue(columnId))\n}\n\n// Utils\n\nfunction compareBasic(a: any, b: any) {\n  return a === b ? 0 : a > b ? 1 : -1\n}\n\nfunction toString(a: any) {\n  if (typeof a === 'number') {\n    if (isNaN(a) || a === Infinity || a === -Infinity) {\n      return ''\n    }\n    return String(a)\n  }\n  if (typeof a === 'string') {\n    return a\n  }\n  return ''\n}\n\n// Mixed sorting is slow, but very inclusive of many edge cases.\n// It handles numbers, mixed alphanumeric combinations, and even\n// null, undefined, and Infinity\nfunction compareAlphanumeric(aStr: string, bStr: string) {\n  // Split on number groups, but keep the delimiter\n  // Then remove falsey split values\n  const a = aStr.split(reSplitAlphaNumeric).filter(Boolean)\n  const b = bStr.split(reSplitAlphaNumeric).filter(Boolean)\n\n  // While\n  while (a.length && b.length) {\n    const aa = a.shift()!\n    const bb = b.shift()!\n\n    const an = parseInt(aa, 10)\n    const bn = parseInt(bb, 10)\n\n    const combo = [an, bn].sort()\n\n    // Both are string\n    if (isNaN(combo[0]!)) {\n      if (aa > bb) {\n        return 1\n      }\n      if (bb > aa) {\n        return -1\n      }\n      continue\n    }\n\n    // One is a string, one is a number\n    if (isNaN(combo[1]!)) {\n      return isNaN(an) ? -1 : 1\n    }\n\n    // Both are numbers\n    if (an > bn) {\n      return 1\n    }\n    if (bn > an) {\n      return -1\n    }\n  }\n\n  return a.length - b.length\n}\n\n// Exports\n\nexport const sortingFns = {\n  alphanumeric,\n  alphanumericCaseSensitive,\n  text,\n  textCaseSensitive,\n  datetime,\n  basic,\n}\n\nexport type BuiltInSortingFn = keyof typeof sortingFns\n", "import { RowModel } from '..'\nimport {\n  BuiltInSortingFn,\n  reSplitAlphaNumeric,\n  sortingFns,\n} from '../sortingFns'\n\nimport {\n  Column,\n  OnChangeFn,\n  Table,\n  Row,\n  Updater,\n  RowData,\n  SortingFns,\n  TableFeature,\n} from '../types'\n\nimport { isFunction, makeStateUpdater } from '../utils'\n\nexport type SortDirection = 'asc' | 'desc'\n\nexport interface ColumnSort {\n  desc: boolean\n  id: string\n}\n\nexport type SortingState = ColumnSort[]\n\nexport interface SortingTableState {\n  sorting: SortingState\n}\n\nexport interface SortingFn<TData extends RowData> {\n  (rowA: Row<TData>, rowB: Row<TData>, columnId: string): number\n}\n\nexport type CustomSortingFns<TData extends RowData> = Record<\n  string,\n  SortingFn<TData>\n>\n\nexport type SortingFnOption<TData extends RowData> =\n  | 'auto'\n  | keyof SortingFns\n  | BuiltInSortingFn\n  | SortingFn<TData>\n\nexport interface SortingColumnDef<TData extends RowData> {\n  /**\n   * Enables/Disables multi-sorting for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablemultisort)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableMultiSort?: boolean\n  /**\n   * Enables/Disables sorting for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablesorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableSorting?: boolean\n  /**\n   * Inverts the order of the sorting for this column. This is useful for values that have an inverted best/worst scale where lower numbers are better, eg. a ranking (1st, 2nd, 3rd) or golf-like scoring\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#invertsorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  invertSorting?: boolean\n  /**\n   * Set to `true` for sorting toggles on this column to start in the descending direction.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#sortdescfirst)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  sortDescFirst?: boolean\n  /**\n   * The sorting function to use with this column.\n   * - A `string` referencing a built-in sorting function\n   * - A custom sorting function\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#sortingfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  sortingFn?: SortingFnOption<TData>\n  /**\n   * The priority of undefined values when sorting this column.\n   * - `false`\n   *   - Undefined values will be considered tied and need to be sorted by the next column filter or original index (whichever applies)\n   * - `-1`\n   *   - Undefined values will be sorted with higher priority (ascending) (if ascending, undefined will appear on the beginning of the list)\n   * - `1`\n   *   - Undefined values will be sorted with lower priority (descending) (if ascending, undefined will appear on the end of the list)\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#sortundefined)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  sortUndefined?: false | -1 | 1 | 'first' | 'last'\n}\n\nexport interface SortingColumn<TData extends RowData> {\n  /**\n   * Removes this column from the table's sorting state\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#clearsorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  clearSorting: () => void\n  /**\n   * Returns a sort direction automatically inferred based on the columns values.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getautosortdir)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getAutoSortDir: () => SortDirection\n  /**\n   * Returns a sorting function automatically inferred based on the columns values.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getautosortingfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getAutoSortingFn: () => SortingFn<TData>\n  /**\n   * Returns whether this column can be multi-sorted.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getcanmultisort)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getCanMultiSort: () => boolean\n  /**\n   * Returns whether this column can be sorted.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getcansort)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getCanSort: () => boolean\n  /**\n   * Returns the first direction that should be used when sorting this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getfirstsortdir)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getFirstSortDir: () => SortDirection\n  /**\n   * Returns the current sort direction of this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getissorted)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getIsSorted: () => false | SortDirection\n  /**\n   * Returns the next sorting order.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getnextsortingorder)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getNextSortingOrder: () => SortDirection | false\n  /**\n   * Returns the index position of this column's sorting within the sorting state\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getsortindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getSortIndex: () => number\n  /**\n   * Returns the resolved sorting function to be used for this column\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getsortingfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getSortingFn: () => SortingFn<TData>\n  /**\n   * Returns a function that can be used to toggle this column's sorting state. This is useful for attaching a click handler to the column header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#gettogglesortinghandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getToggleSortingHandler: () => undefined | ((event: unknown) => void)\n  /**\n   * Toggles this columns sorting state. If `desc` is provided, it will force the sort direction to that value. If `isMulti` is provided, it will additivity multi-sort the column (or toggle it if it is already sorted).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#togglesorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  toggleSorting: (desc?: boolean, isMulti?: boolean) => void\n}\n\ninterface SortingOptionsBase {\n  /**\n   * Enables/disables the ability to remove multi-sorts\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablemultiremove)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableMultiRemove?: boolean\n  /**\n   * Enables/Disables multi-sorting for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablemultisort)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableMultiSort?: boolean\n  /**\n   * Enables/Disables sorting for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablesorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableSorting?: boolean\n  /**\n   * Enables/Disables the ability to remove sorting for the table.\n   * - If `true` then changing sort order will circle like: 'none' -> 'desc' -> 'asc' -> 'none' -> ...\n   * - If `false` then changing sort order will circle like: 'none' -> 'desc' -> 'asc' -> 'desc' -> 'asc' -> ...\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablesortingremoval)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableSortingRemoval?: boolean\n  /**\n   * This function is used to retrieve the sorted row model. If using server-side sorting, this function is not required. To use client-side sorting, pass the exported `getSortedRowModel()` from your adapter to your table or implement your own.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getsortedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getSortedRowModel?: (table: Table<any>) => () => RowModel<any>\n  /**\n   * Pass a custom function that will be used to determine if a multi-sort event should be triggered. It is passed the event from the sort toggle handler and should return `true` if the event should trigger a multi-sort.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#ismultisortevent)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  isMultiSortEvent?: (e: unknown) => boolean\n  /**\n   * Enables manual sorting for the table. If this is `true`, you will be expected to sort your data before it is passed to the table. This is useful if you are doing server-side sorting.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#manualsorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  manualSorting?: boolean\n  /**\n   * Set a maximum number of columns that can be multi-sorted.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#maxmultisortcolcount)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  maxMultiSortColCount?: number\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.sorting` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#onsortingchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  onSortingChange?: OnChangeFn<SortingState>\n  /**\n   * If `true`, all sorts will default to descending as their first toggle state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#sortdescfirst)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  sortDescFirst?: boolean\n}\n\ntype ResolvedSortingFns = keyof SortingFns extends never\n  ? {\n      sortingFns?: Record<string, SortingFn<any>>\n    }\n  : {\n      sortingFns: Record<keyof SortingFns, SortingFn<any>>\n    }\n\nexport interface SortingOptions<TData extends RowData>\n  extends SortingOptionsBase,\n    ResolvedSortingFns {}\n\nexport interface SortingInstance<TData extends RowData> {\n  _getSortedRowModel?: () => RowModel<TData>\n  /**\n   * Returns the row model for the table before any sorting has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getpresortedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getPreSortedRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model for the table after sorting has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getsortedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getSortedRowModel: () => RowModel<TData>\n  /**\n   * Resets the **sorting** state to `initialState.sorting`, or `true` can be passed to force a default blank state reset to `[]`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#resetsorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  resetSorting: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.sorting` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#setsorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  setSorting: (updater: Updater<SortingState>) => void\n}\n\n//\n\nexport const RowSorting: TableFeature = {\n  getInitialState: (state): SortingTableState => {\n    return {\n      sorting: [],\n      ...state,\n    }\n  },\n\n  getDefaultColumnDef: <TData extends RowData>(): SortingColumnDef<TData> => {\n    return {\n      sortingFn: 'auto',\n      sortUndefined: 1,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): SortingOptions<TData> => {\n    return {\n      onSortingChange: makeStateUpdater('sorting', table),\n      isMultiSortEvent: (e: unknown) => {\n        return (e as MouseEvent).shiftKey\n      },\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    column.getAutoSortingFn = () => {\n      const firstRows = table.getFilteredRowModel().flatRows.slice(10)\n\n      let isString = false\n\n      for (const row of firstRows) {\n        const value = row?.getValue(column.id)\n\n        if (Object.prototype.toString.call(value) === '[object Date]') {\n          return sortingFns.datetime\n        }\n\n        if (typeof value === 'string') {\n          isString = true\n\n          if (value.split(reSplitAlphaNumeric).length > 1) {\n            return sortingFns.alphanumeric\n          }\n        }\n      }\n\n      if (isString) {\n        return sortingFns.text\n      }\n\n      return sortingFns.basic\n    }\n    column.getAutoSortDir = () => {\n      const firstRow = table.getFilteredRowModel().flatRows[0]\n\n      const value = firstRow?.getValue(column.id)\n\n      if (typeof value === 'string') {\n        return 'asc'\n      }\n\n      return 'desc'\n    }\n    column.getSortingFn = () => {\n      if (!column) {\n        throw new Error()\n      }\n\n      return isFunction(column.columnDef.sortingFn)\n        ? column.columnDef.sortingFn\n        : column.columnDef.sortingFn === 'auto'\n          ? column.getAutoSortingFn()\n          : table.options.sortingFns?.[column.columnDef.sortingFn as string] ??\n            sortingFns[column.columnDef.sortingFn as BuiltInSortingFn]\n    }\n    column.toggleSorting = (desc, multi) => {\n      // if (column.columns.length) {\n      //   column.columns.forEach((c, i) => {\n      //     if (c.id) {\n      //       table.toggleColumnSorting(c.id, undefined, multi || !!i)\n      //     }\n      //   })\n      //   return\n      // }\n\n      // this needs to be outside of table.setSorting to be in sync with rerender\n      const nextSortingOrder = column.getNextSortingOrder()\n      const hasManualValue = typeof desc !== 'undefined' && desc !== null\n\n      table.setSorting(old => {\n        // Find any existing sorting for this column\n        const existingSorting = old?.find(d => d.id === column.id)\n        const existingIndex = old?.findIndex(d => d.id === column.id)\n\n        let newSorting: SortingState = []\n\n        // What should we do with this sort action?\n        let sortAction: 'add' | 'remove' | 'toggle' | 'replace'\n        let nextDesc = hasManualValue ? desc : nextSortingOrder === 'desc'\n\n        // Multi-mode\n        if (old?.length && column.getCanMultiSort() && multi) {\n          if (existingSorting) {\n            sortAction = 'toggle'\n          } else {\n            sortAction = 'add'\n          }\n        } else {\n          // Normal mode\n          if (old?.length && existingIndex !== old.length - 1) {\n            sortAction = 'replace'\n          } else if (existingSorting) {\n            sortAction = 'toggle'\n          } else {\n            sortAction = 'replace'\n          }\n        }\n\n        // Handle toggle states that will remove the sorting\n        if (sortAction === 'toggle') {\n          // If we are \"actually\" toggling (not a manual set value), should we remove the sorting?\n          if (!hasManualValue) {\n            // Is our intention to remove?\n            if (!nextSortingOrder) {\n              sortAction = 'remove'\n            }\n          }\n        }\n\n        if (sortAction === 'add') {\n          newSorting = [\n            ...old,\n            {\n              id: column.id,\n              desc: nextDesc,\n            },\n          ]\n          // Take latest n columns\n          newSorting.splice(\n            0,\n            newSorting.length -\n              (table.options.maxMultiSortColCount ?? Number.MAX_SAFE_INTEGER)\n          )\n        } else if (sortAction === 'toggle') {\n          // This flips (or sets) the\n          newSorting = old.map(d => {\n            if (d.id === column.id) {\n              return {\n                ...d,\n                desc: nextDesc,\n              }\n            }\n            return d\n          })\n        } else if (sortAction === 'remove') {\n          newSorting = old.filter(d => d.id !== column.id)\n        } else {\n          newSorting = [\n            {\n              id: column.id,\n              desc: nextDesc,\n            },\n          ]\n        }\n\n        return newSorting\n      })\n    }\n\n    column.getFirstSortDir = () => {\n      const sortDescFirst =\n        column.columnDef.sortDescFirst ??\n        table.options.sortDescFirst ??\n        column.getAutoSortDir() === 'desc'\n      return sortDescFirst ? 'desc' : 'asc'\n    }\n\n    column.getNextSortingOrder = (multi?: boolean) => {\n      const firstSortDirection = column.getFirstSortDir()\n      const isSorted = column.getIsSorted()\n\n      if (!isSorted) {\n        return firstSortDirection\n      }\n\n      if (\n        isSorted !== firstSortDirection &&\n        (table.options.enableSortingRemoval ?? true) && // If enableSortRemove, enable in general\n        (multi ? table.options.enableMultiRemove ?? true : true) // If multi, don't allow if enableMultiRemove))\n      ) {\n        return false\n      }\n      return isSorted === 'desc' ? 'asc' : 'desc'\n    }\n\n    column.getCanSort = () => {\n      return (\n        (column.columnDef.enableSorting ?? true) &&\n        (table.options.enableSorting ?? true) &&\n        !!column.accessorFn\n      )\n    }\n\n    column.getCanMultiSort = () => {\n      return (\n        column.columnDef.enableMultiSort ??\n        table.options.enableMultiSort ??\n        !!column.accessorFn\n      )\n    }\n\n    column.getIsSorted = () => {\n      const columnSort = table.getState().sorting?.find(d => d.id === column.id)\n\n      return !columnSort ? false : columnSort.desc ? 'desc' : 'asc'\n    }\n\n    column.getSortIndex = () =>\n      table.getState().sorting?.findIndex(d => d.id === column.id) ?? -1\n\n    column.clearSorting = () => {\n      //clear sorting for just 1 column\n      table.setSorting(old =>\n        old?.length ? old.filter(d => d.id !== column.id) : []\n      )\n    }\n\n    column.getToggleSortingHandler = () => {\n      const canSort = column.getCanSort()\n\n      return (e: unknown) => {\n        if (!canSort) return\n        ;(e as any).persist?.()\n        column.toggleSorting?.(\n          undefined,\n          column.getCanMultiSort() ? table.options.isMultiSortEvent?.(e) : false\n        )\n      }\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setSorting = updater => table.options.onSortingChange?.(updater)\n    table.resetSorting = defaultState => {\n      table.setSorting(defaultState ? [] : table.initialState?.sorting ?? [])\n    }\n    table.getPreSortedRowModel = () => table.getGroupedRowModel()\n    table.getSortedRowModel = () => {\n      if (!table._getSortedRowModel && table.options.getSortedRowModel) {\n        table._getSortedRowModel = table.options.getSortedRowModel(table)\n      }\n\n      if (table.options.manualSorting || !table._getSortedRowModel) {\n        return table.getPreSortedRowModel()\n      }\n\n      return table._getSortedRowModel()\n    }\n  },\n}\n", "import { functionalUpdate, getMemoOptions, memo, RequiredKeys } from '../utils'\n\nimport {\n  Updater,\n  TableOptionsResolved,\n  TableState,\n  Table,\n  InitialTableState,\n  Row,\n  Column,\n  RowModel,\n  ColumnDef,\n  TableOptions,\n  RowData,\n  TableMeta,\n  ColumnDefResolved,\n  GroupColumnDef,\n  TableFeature,\n} from '../types'\n\n//\nimport { createColumn } from './column'\nimport { Headers } from './headers'\n//\n\nimport { ColumnFaceting } from '../features/ColumnFaceting'\nimport { ColumnFiltering } from '../features/ColumnFiltering'\nimport { ColumnGrouping } from '../features/ColumnGrouping'\nimport { ColumnOrdering } from '../features/ColumnOrdering'\nimport { ColumnPinning } from '../features/ColumnPinning'\nimport { ColumnSizing } from '../features/ColumnSizing'\nimport { ColumnVisibility } from '../features/ColumnVisibility'\nimport { GlobalFaceting } from '../features/GlobalFaceting'\nimport { GlobalFiltering } from '../features/GlobalFiltering'\nimport { RowExpanding } from '../features/RowExpanding'\nimport { RowPagination } from '../features/RowPagination'\nimport { RowPinning } from '../features/RowPinning'\nimport { RowSelection } from '../features/RowSelection'\nimport { RowSorting } from '../features/RowSorting'\n\nconst builtInFeatures = [\n  Headers,\n  ColumnVisibility,\n  ColumnOrdering,\n  ColumnPinning,\n  ColumnFaceting,\n  ColumnFiltering,\n  GlobalFaceting, //depends on ColumnFaceting\n  GlobalFiltering, //depends on ColumnFiltering\n  RowSorting,\n  ColumnGrouping, //depends on RowSorting\n  RowExpanding,\n  RowPagination,\n  RowPinning,\n  RowSelection,\n  ColumnSizing,\n] as const\n\n//\n\nexport interface CoreTableState {}\n\nexport interface CoreOptions<TData extends RowData> {\n  /**\n   * An array of extra features that you can add to the table instance.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#_features)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  _features?: TableFeature[]\n  /**\n   * Set this option to override any of the `autoReset...` feature options.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#autoresetall)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  autoResetAll?: boolean\n  /**\n   * The array of column defs to use for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#columns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  columns: ColumnDef<TData, any>[]\n  /**\n   * The data for the table to display. This array should match the type you provided to `table.setRowType<...>`. Columns can access this data via string/index or a functional accessor. When the `data` option changes reference, the table will reprocess the data.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#data)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  data: TData[]\n  /**\n   * Set this option to `true` to output all debugging information to the console.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#debugall)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  debugAll?: boolean\n  /**\n   * Set this option to `true` to output cell debugging information to the console.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#debugcells]\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  debugCells?: boolean\n  /**\n   * Set this option to `true` to output column debugging information to the console.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#debugcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  debugColumns?: boolean\n  /**\n   * Set this option to `true` to output header debugging information to the console.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#debugheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  debugHeaders?: boolean\n  /**\n   * Set this option to `true` to output row debugging information to the console.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#debugrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  debugRows?: boolean\n  /**\n   * Set this option to `true` to output table debugging information to the console.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#debugtable)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  debugTable?: boolean\n  /**\n   * Default column options to use for all column defs supplied to the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#defaultcolumn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  defaultColumn?: Partial<ColumnDef<TData, unknown>>\n  /**\n   * This required option is a factory for a function that computes and returns the core row model for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getcorerowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getCoreRowModel: (table: Table<any>) => () => RowModel<any>\n  /**\n   * This optional function is used to derive a unique ID for any given row. If not provided the rows index is used (nested rows join together with `.` using their grandparents' index eg. `index.index.index`). If you need to identify individual rows that are originating from any server-side operations, it's suggested you use this function to return an ID that makes sense regardless of network IO/ambiguity eg. a userId, taskId, database ID field, etc.\n   * @example getRowId: row => row.userId\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getrowid)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getRowId?: (originalRow: TData, index: number, parent?: Row<TData>) => string\n  /**\n   * This optional function is used to access the sub rows for any given row. If you are using nested rows, you will need to use this function to return the sub rows object (or undefined) from the row.\n   * @example getSubRows: row => row.subRows\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getsubrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getSubRows?: (originalRow: TData, index: number) => undefined | TData[]\n  /**\n   * Use this option to optionally pass initial state to the table. This state will be used when resetting various table states either automatically by the table (eg. `options.autoResetPageIndex`) or via functions like `table.resetRowSelection()`. Most reset function allow you optionally pass a flag to reset to a blank/default state instead of the initial state.\n   *\n   * Table state will not be reset when this object changes, which also means that the initial state object does not need to be stable.\n   *\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#initialstate)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  initialState?: InitialTableState\n  /**\n   * This option is used to optionally implement the merging of table options.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#mergeoptions)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  mergeOptions?: (\n    defaultOptions: TableOptions<TData>,\n    options: Partial<TableOptions<TData>>\n  ) => TableOptions<TData>\n  /**\n   * You can pass any object to `options.meta` and access it anywhere the `table` is available via `table.options.meta`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#meta)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  meta?: TableMeta<TData>\n  /**\n   * The `onStateChange` option can be used to optionally listen to state changes within the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#onstatechange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  onStateChange: (updater: Updater<TableState>) => void\n  /**\n   * Value used when the desired value is not found in the data.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#renderfallbackvalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  renderFallbackValue: any\n  /**\n   * The `state` option can be used to optionally _control_ part or all of the table state. The state you pass here will merge with and overwrite the internal automatically-managed state to produce the final state for the table. You can also listen to state changes via the `onStateChange` option.\n   * > Note: Any state passed in here will override both the internal state and any other `initialState` you provide.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#state)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  state: Partial<TableState>\n}\n\nexport interface CoreInstance<TData extends RowData> {\n  _features: readonly TableFeature[]\n  _getAllFlatColumnsById: () => Record<string, Column<TData, unknown>>\n  _getColumnDefs: () => ColumnDef<TData, unknown>[]\n  _getCoreRowModel?: () => RowModel<TData>\n  _getDefaultColumnDef: () => Partial<ColumnDef<TData, unknown>>\n  _getRowId: (_: TData, index: number, parent?: Row<TData>) => string\n  _queue: (cb: () => void) => void\n  /**\n   * Returns all columns in the table in their normalized and nested hierarchy.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getallcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getAllColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns all columns in the table flattened to a single level.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getallflatcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getAllFlatColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns all leaf-node columns in the table flattened to a single level. This does not include parent columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getallleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getAllLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns a single column by its ID.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getcolumn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getColumn: (columnId: string) => Column<TData, unknown> | undefined\n  /**\n   * Returns the core row model before any processing has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getcorerowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getCoreRowModel: () => RowModel<TData>\n  /**\n   * Returns the row with the given ID.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getrow)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getRow: (id: string, searchAll?: boolean) => Row<TData>\n  /**\n   * Returns the final model after all processing from other used features has been applied. This is the row model that is most commonly used for rendering.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getRowModel: () => RowModel<TData>\n  /**\n   * Call this function to get the table's current state. It's recommended to use this function and its state, especially when managing the table state manually. It is the exact same state used internally by the table for every feature and function it provides.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getstate)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getState: () => TableState\n  /**\n   * This is the resolved initial state of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#initialstate)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  initialState: TableState\n  /**\n   * A read-only reference to the table's current options.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#options)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  options: RequiredKeys<TableOptionsResolved<TData>, 'state'>\n  /**\n   * Call this function to reset the table state to the initial state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#reset)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  reset: () => void\n  /**\n   * This function can be used to update the table options.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#setoptions)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  setOptions: (newOptions: Updater<TableOptionsResolved<TData>>) => void\n  /**\n   * Call this function to update the table state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#setstate)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  setState: (updater: Updater<TableState>) => void\n}\n\nexport function createTable<TData extends RowData>(\n  options: TableOptionsResolved<TData>\n): Table<TData> {\n  if (\n    process.env.NODE_ENV !== 'production' &&\n    (options.debugAll || options.debugTable)\n  ) {\n    console.info('Creating Table Instance...')\n  }\n\n  const _features = [...builtInFeatures, ...(options._features ?? [])]\n\n  let table = { _features } as unknown as Table<TData>\n\n  const defaultOptions = table._features.reduce((obj, feature) => {\n    return Object.assign(obj, feature.getDefaultOptions?.(table))\n  }, {}) as TableOptionsResolved<TData>\n\n  const mergeOptions = (options: TableOptionsResolved<TData>) => {\n    if (table.options.mergeOptions) {\n      return table.options.mergeOptions(defaultOptions, options)\n    }\n\n    return {\n      ...defaultOptions,\n      ...options,\n    }\n  }\n\n  const coreInitialState: CoreTableState = {}\n\n  let initialState = {\n    ...coreInitialState,\n    ...(options.initialState ?? {}),\n  } as TableState\n\n  table._features.forEach(feature => {\n    initialState = (feature.getInitialState?.(initialState) ??\n      initialState) as TableState\n  })\n\n  const queued: (() => void)[] = []\n  let queuedTimeout = false\n\n  const coreInstance: CoreInstance<TData> = {\n    _features,\n    options: {\n      ...defaultOptions,\n      ...options,\n    },\n    initialState,\n    _queue: cb => {\n      queued.push(cb)\n\n      if (!queuedTimeout) {\n        queuedTimeout = true\n\n        // Schedule a microtask to run the queued callbacks after\n        // the current call stack (render, etc) has finished.\n        Promise.resolve()\n          .then(() => {\n            while (queued.length) {\n              queued.shift()!()\n            }\n            queuedTimeout = false\n          })\n          .catch(error =>\n            setTimeout(() => {\n              throw error\n            })\n          )\n      }\n    },\n    reset: () => {\n      table.setState(table.initialState)\n    },\n    setOptions: updater => {\n      const newOptions = functionalUpdate(updater, table.options)\n      table.options = mergeOptions(newOptions) as RequiredKeys<\n        TableOptionsResolved<TData>,\n        'state'\n      >\n    },\n\n    getState: () => {\n      return table.options.state as TableState\n    },\n\n    setState: (updater: Updater<TableState>) => {\n      table.options.onStateChange?.(updater)\n    },\n\n    _getRowId: (row: TData, index: number, parent?: Row<TData>) =>\n      table.options.getRowId?.(row, index, parent) ??\n      `${parent ? [parent.id, index].join('.') : index}`,\n\n    getCoreRowModel: () => {\n      if (!table._getCoreRowModel) {\n        table._getCoreRowModel = table.options.getCoreRowModel(table)\n      }\n\n      return table._getCoreRowModel!()\n    },\n\n    // The final calls start at the bottom of the model,\n    // expanded rows, which then work their way up\n\n    getRowModel: () => {\n      return table.getPaginationRowModel()\n    },\n    //in next version, we should just pass in the row model as the optional 2nd arg\n    getRow: (id: string, searchAll?: boolean) => {\n      let row = (\n        searchAll ? table.getPrePaginationRowModel() : table.getRowModel()\n      ).rowsById[id]\n\n      if (!row) {\n        row = table.getCoreRowModel().rowsById[id]\n        if (!row) {\n          if (process.env.NODE_ENV !== 'production') {\n            throw new Error(`getRow could not find row with ID: ${id}`)\n          }\n          throw new Error()\n        }\n      }\n\n      return row\n    },\n    _getDefaultColumnDef: memo(\n      () => [table.options.defaultColumn],\n      defaultColumn => {\n        defaultColumn = (defaultColumn ?? {}) as Partial<\n          ColumnDef<TData, unknown>\n        >\n\n        return {\n          header: props => {\n            const resolvedColumnDef = props.header.column\n              .columnDef as ColumnDefResolved<TData>\n\n            if (resolvedColumnDef.accessorKey) {\n              return resolvedColumnDef.accessorKey\n            }\n\n            if (resolvedColumnDef.accessorFn) {\n              return resolvedColumnDef.id\n            }\n\n            return null\n          },\n          // footer: props => props.header.column.id,\n          cell: props => props.renderValue<any>()?.toString?.() ?? null,\n          ...table._features.reduce((obj, feature) => {\n            return Object.assign(obj, feature.getDefaultColumnDef?.())\n          }, {}),\n          ...defaultColumn,\n        } as Partial<ColumnDef<TData, unknown>>\n      },\n      getMemoOptions(options, 'debugColumns', '_getDefaultColumnDef')\n    ),\n\n    _getColumnDefs: () => table.options.columns,\n\n    getAllColumns: memo(\n      () => [table._getColumnDefs()],\n      columnDefs => {\n        const recurseColumns = (\n          columnDefs: ColumnDef<TData, unknown>[],\n          parent?: Column<TData, unknown>,\n          depth = 0\n        ): Column<TData, unknown>[] => {\n          return columnDefs.map(columnDef => {\n            const column = createColumn(table, columnDef, depth, parent)\n\n            const groupingColumnDef = columnDef as GroupColumnDef<\n              TData,\n              unknown\n            >\n\n            column.columns = groupingColumnDef.columns\n              ? recurseColumns(groupingColumnDef.columns, column, depth + 1)\n              : []\n\n            return column\n          })\n        }\n\n        return recurseColumns(columnDefs)\n      },\n      getMemoOptions(options, 'debugColumns', 'getAllColumns')\n    ),\n\n    getAllFlatColumns: memo(\n      () => [table.getAllColumns()],\n      allColumns => {\n        return allColumns.flatMap(column => {\n          return column.getFlatColumns()\n        })\n      },\n      getMemoOptions(options, 'debugColumns', 'getAllFlatColumns')\n    ),\n\n    _getAllFlatColumnsById: memo(\n      () => [table.getAllFlatColumns()],\n      flatColumns => {\n        return flatColumns.reduce(\n          (acc, column) => {\n            acc[column.id] = column\n            return acc\n          },\n          {} as Record<string, Column<TData, unknown>>\n        )\n      },\n      getMemoOptions(options, 'debugColumns', 'getAllFlatColumnsById')\n    ),\n\n    getAllLeafColumns: memo(\n      () => [table.getAllColumns(), table._getOrderColumnsFn()],\n      (allColumns, orderColumns) => {\n        let leafColumns = allColumns.flatMap(column => column.getLeafColumns())\n        return orderColumns(leafColumns)\n      },\n      getMemoOptions(options, 'debugColumns', 'getAllLeafColumns')\n    ),\n\n    getColumn: columnId => {\n      const column = table._getAllFlatColumnsById()[columnId]\n\n      if (process.env.NODE_ENV !== 'production' && !column) {\n        console.error(`[Table] Column with id '${columnId}' does not exist.`)\n      }\n\n      return column\n    },\n  }\n\n  Object.assign(table, coreInstance)\n\n  for (let index = 0; index < table._features.length; index++) {\n    const feature = table._features[index]\n    feature?.createTable?.(table)\n  }\n\n  return table\n}\n", "import { createRow } from '../core/row'\nimport { Table, Row, RowModel, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\n\nexport function getCoreRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [table.options.data],\n      (\n        data\n      ): {\n        rows: Row<TData>[]\n        flatRows: Row<TData>[]\n        rowsById: Record<string, Row<TData>>\n      } => {\n        const rowModel: RowModel<TData> = {\n          rows: [],\n          flatRows: [],\n          rowsById: {},\n        }\n\n        const accessRows = (\n          originalRows: TData[],\n          depth = 0,\n          parentRow?: Row<TData>\n        ): Row<TData>[] => {\n          const rows = [] as Row<TData>[]\n\n          for (let i = 0; i < originalRows.length; i++) {\n            // This could be an expensive check at scale, so we should move it somewhere else, but where?\n            // if (!id) {\n            //   if (process.env.NODE_ENV !== 'production') {\n            //     throw new Error(`getRowId expected an ID, but got ${id}`)\n            //   }\n            // }\n\n            // Make the row\n            const row = createRow(\n              table,\n              table._getRowId(originalRows[i]!, i, parentRow),\n              originalRows[i]!,\n              i,\n              depth,\n              undefined,\n              parentRow?.id\n            )\n\n            // Keep track of every row in a flat array\n            rowModel.flatRows.push(row)\n            // Also keep track of every row by its ID\n            rowModel.rowsById[row.id] = row\n            // Push table row into parent\n            rows.push(row)\n\n            // Get the original subrows\n            if (table.options.getSubRows) {\n              row.originalSubRows = table.options.getSubRows(\n                originalRows[i]!,\n                i\n              )\n\n              // Then recursively access them\n              if (row.originalSubRows?.length) {\n                row.subRows = accessRows(row.originalSubRows, depth + 1, row)\n              }\n            }\n          }\n\n          return rows\n        }\n\n        rowModel.rows = accessRows(data)\n\n        return rowModel\n      },\n      getMemoOptions(table.options, 'debugTable', 'getRowModel', () =>\n        table._autoResetPageIndex()\n      )\n    )\n}\n", "import { Table, Row, RowModel, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\n\nexport function getExpandedRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [\n        table.getState().expanded,\n        table.getPreExpandedRowModel(),\n        table.options.paginateExpandedRows,\n      ],\n      (expanded, rowModel, paginateExpandedRows) => {\n        if (\n          !rowModel.rows.length ||\n          (expanded !== true && !Object.keys(expanded ?? {}).length)\n        ) {\n          return rowModel\n        }\n\n        if (!paginateExpandedRows) {\n          // Only expand rows at this point if they are being paginated\n          return rowModel\n        }\n\n        return expandRows(rowModel)\n      },\n      getMemoOptions(table.options, 'debugTable', 'getExpandedRowModel')\n    )\n}\n\nexport function expandRows<TData extends RowData>(rowModel: RowModel<TData>) {\n  const expandedRows: Row<TData>[] = []\n\n  const handleRow = (row: Row<TData>) => {\n    expandedRows.push(row)\n\n    if (row.subRows?.length && row.getIsExpanded()) {\n      row.subRows.forEach(handleRow)\n    }\n  }\n\n  rowModel.rows.forEach(handleRow)\n\n  return {\n    rows: expandedRows,\n    flatRows: rowModel.flatRows,\n    rowsById: rowModel.rowsById,\n  }\n}\n", "import { Table, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\n\nexport function getFacetedMinMaxValues<TData extends RowData>(): (\n  table: Table<TData>,\n  columnId: string\n) => () => undefined | [number, number] {\n  return (table, columnId) =>\n    memo(\n      () => [table.getColumn(columnId)?.getFacetedRowModel()],\n      facetedRowModel => {\n        if (!facetedRowModel) return undefined\n\n        const uniqueValues = facetedRowModel.flatRows\n          .flatMap(flatRow => flatRow.getUniqueValues(columnId) ?? [])\n          .map(Number)\n          .filter(value => !Number.isNaN(value))\n\n        if (!uniqueValues.length) return\n\n        let facetedMinValue = uniqueValues[0]!\n        let facetedMaxValue = uniqueValues[uniqueValues.length - 1]!\n\n        for (const value of uniqueValues) {\n          if (value < facetedMinValue) facetedMinValue = value\n          else if (value > facetedMaxValue) facetedMaxValue = value\n        }\n\n        return [facetedMinValue, facetedMaxValue]\n      },\n      getMemoOptions(table.options, 'debugTable', 'getFacetedMinMaxValues')\n    )\n}\n", "import { createRow } from '../core/row'\nimport { Row, RowModel, Table, RowData } from '../types'\n\nexport function filterRows<TData extends RowData>(\n  rows: Row<TData>[],\n  filterRowImpl: (row: Row<TData>) => any,\n  table: Table<TData>\n) {\n  if (table.options.filterFromLeafRows) {\n    return filterRowModelFromLeafs(rows, filterRowImpl, table)\n  }\n\n  return filterRowModelFromRoot(rows, filterRowImpl, table)\n}\n\nfunction filterRowModelFromLeafs<TData extends RowData>(\n  rowsToFilter: Row<TData>[],\n  filterRow: (row: Row<TData>) => Row<TData>[],\n  table: Table<TData>\n): RowModel<TData> {\n  const newFilteredFlatRows: Row<TData>[] = []\n  const newFilteredRowsById: Record<string, Row<TData>> = {}\n  const maxDepth = table.options.maxLeafRowFilterDepth ?? 100\n\n  const recurseFilterRows = (rowsToFilter: Row<TData>[], depth = 0) => {\n    const rows: Row<TData>[] = []\n\n    // Filter from children up first\n    for (let i = 0; i < rowsToFilter.length; i++) {\n      let row = rowsToFilter[i]!\n\n      const newRow = createRow(\n        table,\n        row.id,\n        row.original,\n        row.index,\n        row.depth,\n        undefined,\n        row.parentId\n      )\n      newRow.columnFilters = row.columnFilters\n\n      if (row.subRows?.length && depth < maxDepth) {\n        newRow.subRows = recurseFilterRows(row.subRows, depth + 1)\n        row = newRow\n\n        if (filterRow(row) && !newRow.subRows.length) {\n          rows.push(row)\n          newFilteredRowsById[row.id] = row\n          newFilteredFlatRows.push(row)\n          continue\n        }\n\n        if (filterRow(row) || newRow.subRows.length) {\n          rows.push(row)\n          newFilteredRowsById[row.id] = row\n          newFilteredFlatRows.push(row)\n          continue\n        }\n      } else {\n        row = newRow\n        if (filterRow(row)) {\n          rows.push(row)\n          newFilteredRowsById[row.id] = row\n          newFilteredFlatRows.push(row)\n        }\n      }\n    }\n\n    return rows\n  }\n\n  return {\n    rows: recurseFilterRows(rowsToFilter),\n    flatRows: newFilteredFlatRows,\n    rowsById: newFilteredRowsById,\n  }\n}\n\nfunction filterRowModelFromRoot<TData extends RowData>(\n  rowsToFilter: Row<TData>[],\n  filterRow: (row: Row<TData>) => any,\n  table: Table<TData>\n): RowModel<TData> {\n  const newFilteredFlatRows: Row<TData>[] = []\n  const newFilteredRowsById: Record<string, Row<TData>> = {}\n  const maxDepth = table.options.maxLeafRowFilterDepth ?? 100\n\n  // Filters top level and nested rows\n  const recurseFilterRows = (rowsToFilter: Row<TData>[], depth = 0) => {\n    // Filter from parents downward first\n\n    const rows: Row<TData>[] = []\n\n    // Apply the filter to any subRows\n    for (let i = 0; i < rowsToFilter.length; i++) {\n      let row = rowsToFilter[i]!\n\n      const pass = filterRow(row)\n\n      if (pass) {\n        if (row.subRows?.length && depth < maxDepth) {\n          const newRow = createRow(\n            table,\n            row.id,\n            row.original,\n            row.index,\n            row.depth,\n            undefined,\n            row.parentId\n          )\n          newRow.subRows = recurseFilterRows(row.subRows, depth + 1)\n          row = newRow\n        }\n\n        rows.push(row)\n        newFilteredFlatRows.push(row)\n        newFilteredRowsById[row.id] = row\n      }\n    }\n\n    return rows\n  }\n\n  return {\n    rows: recurseFilterRows(rowsToFilter),\n    flatRows: newFilteredFlatRows,\n    rowsById: newFilteredRowsById,\n  }\n}\n", "import { Table, RowModel, Row, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\nimport { filterRows } from './filterRowsUtils'\n\nexport function getFacetedRowModel<TData extends RowData>(): (\n  table: Table<TData>,\n  columnId: string\n) => () => RowModel<TData> {\n  return (table, columnId) =>\n    memo(\n      () => [\n        table.getPreFilteredRowModel(),\n        table.getState().columnFilters,\n        table.getState().globalFilter,\n        table.getFilteredRowModel(),\n      ],\n      (preRowModel, columnFilters, globalFilter) => {\n        if (\n          !preRowModel.rows.length ||\n          (!columnFilters?.length && !globalFilter)\n        ) {\n          return preRowModel\n        }\n\n        const filterableIds = [\n          ...columnFilters.map(d => d.id).filter(d => d !== columnId),\n          globalFilter ? '__global__' : undefined,\n        ].filter(Boolean) as string[]\n\n        const filterRowsImpl = (row: Row<TData>) => {\n          // Horizontally filter rows through each column\n          for (let i = 0; i < filterableIds.length; i++) {\n            if (row.columnFilters[filterableIds[i]!] === false) {\n              return false\n            }\n          }\n          return true\n        }\n\n        return filterRows(preRowModel.rows, filterRowsImpl, table)\n      },\n      getMemoOptions(table.options, 'debugTable', 'getFacetedRowModel')\n    )\n}\n", "import { Table, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\n\nexport function getFacetedUniqueValues<TData extends RowData>(): (\n  table: Table<TData>,\n  columnId: string\n) => () => Map<any, number> {\n  return (table, columnId) =>\n    memo(\n      () => [table.getColumn(columnId)?.getFacetedRowModel()],\n      facetedRowModel => {\n        if (!facetedRowModel) return new Map()\n\n        let facetedUniqueValues = new Map<any, number>()\n\n        for (let i = 0; i < facetedRowModel.flatRows.length; i++) {\n          const values =\n            facetedRowModel.flatRows[i]!.getUniqueValues<number>(columnId)\n\n          for (let j = 0; j < values.length; j++) {\n            const value = values[j]!\n\n            if (facetedUniqueValues.has(value)) {\n              facetedUniqueValues.set(\n                value,\n                (facetedUniqueValues.get(value) ?? 0) + 1\n              )\n            } else {\n              facetedUniqueValues.set(value, 1)\n            }\n          }\n        }\n\n        return facetedUniqueValues\n      },\n      getMemoOptions(\n        table.options,\n        'debugTable',\n        `getFacetedUniqueValues_${columnId}`\n      )\n    )\n}\n", "import { ResolvedColumnFilter } from '../features/ColumnFiltering'\nimport { Table, RowModel, Row, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\nimport { filterRows } from './filterRowsUtils'\n\nexport function getFilteredRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [\n        table.getPreFilteredRowModel(),\n        table.getState().columnFilters,\n        table.getState().globalFilter,\n      ],\n      (rowModel, columnFilters, globalFilter) => {\n        if (\n          !rowModel.rows.length ||\n          (!columnFilters?.length && !globalFilter)\n        ) {\n          for (let i = 0; i < rowModel.flatRows.length; i++) {\n            rowModel.flatRows[i]!.columnFilters = {}\n            rowModel.flatRows[i]!.columnFiltersMeta = {}\n          }\n          return rowModel\n        }\n\n        const resolvedColumnFilters: ResolvedColumnFilter<TData>[] = []\n        const resolvedGlobalFilters: ResolvedColumnFilter<TData>[] = []\n\n        ;(columnFilters ?? []).forEach(d => {\n          const column = table.getColumn(d.id)\n\n          if (!column) {\n            return\n          }\n\n          const filterFn = column.getFilterFn()\n\n          if (!filterFn) {\n            if (process.env.NODE_ENV !== 'production') {\n              console.warn(\n                `Could not find a valid 'column.filterFn' for column with the ID: ${column.id}.`\n              )\n            }\n            return\n          }\n\n          resolvedColumnFilters.push({\n            id: d.id,\n            filterFn,\n            resolvedValue: filterFn.resolveFilterValue?.(d.value) ?? d.value,\n          })\n        })\n\n        const filterableIds = (columnFilters ?? []).map(d => d.id)\n\n        const globalFilterFn = table.getGlobalFilterFn()\n\n        const globallyFilterableColumns = table\n          .getAllLeafColumns()\n          .filter(column => column.getCanGlobalFilter())\n\n        if (\n          globalFilter &&\n          globalFilterFn &&\n          globallyFilterableColumns.length\n        ) {\n          filterableIds.push('__global__')\n\n          globallyFilterableColumns.forEach(column => {\n            resolvedGlobalFilters.push({\n              id: column.id,\n              filterFn: globalFilterFn,\n              resolvedValue:\n                globalFilterFn.resolveFilterValue?.(globalFilter) ??\n                globalFilter,\n            })\n          })\n        }\n\n        let currentColumnFilter\n        let currentGlobalFilter\n\n        // Flag the prefiltered row model with each filter state\n        for (let j = 0; j < rowModel.flatRows.length; j++) {\n          const row = rowModel.flatRows[j]!\n\n          row.columnFilters = {}\n\n          if (resolvedColumnFilters.length) {\n            for (let i = 0; i < resolvedColumnFilters.length; i++) {\n              currentColumnFilter = resolvedColumnFilters[i]!\n              const id = currentColumnFilter.id\n\n              // Tag the row with the column filter state\n              row.columnFilters[id] = currentColumnFilter.filterFn(\n                row,\n                id,\n                currentColumnFilter.resolvedValue,\n                filterMeta => {\n                  row.columnFiltersMeta[id] = filterMeta\n                }\n              )\n            }\n          }\n\n          if (resolvedGlobalFilters.length) {\n            for (let i = 0; i < resolvedGlobalFilters.length; i++) {\n              currentGlobalFilter = resolvedGlobalFilters[i]!\n              const id = currentGlobalFilter.id\n              // Tag the row with the first truthy global filter state\n              if (\n                currentGlobalFilter.filterFn(\n                  row,\n                  id,\n                  currentGlobalFilter.resolvedValue,\n                  filterMeta => {\n                    row.columnFiltersMeta[id] = filterMeta\n                  }\n                )\n              ) {\n                row.columnFilters.__global__ = true\n                break\n              }\n            }\n\n            if (row.columnFilters.__global__ !== true) {\n              row.columnFilters.__global__ = false\n            }\n          }\n        }\n\n        const filterRowsImpl = (row: Row<TData>) => {\n          // Horizontally filter rows through each column\n          for (let i = 0; i < filterableIds.length; i++) {\n            if (row.columnFilters[filterableIds[i]!] === false) {\n              return false\n            }\n          }\n          return true\n        }\n\n        // Filter final rows using all of the active filters\n        return filterRows(rowModel.rows, filterRowsImpl, table)\n      },\n      getMemoOptions(table.options, 'debugTable', 'getFilteredRowModel', () =>\n        table._autoResetPageIndex()\n      )\n    )\n}\n", "import { createRow } from '../core/row'\nimport { Row, RowData, RowModel, Table } from '../types'\nimport { flattenBy, getMemoOptions, memo } from '../utils'\nimport { GroupingState } from '../features/ColumnGrouping'\n\nexport function getGroupedRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [table.getState().grouping, table.getPreGroupedRowModel()],\n      (grouping, rowModel) => {\n        if (!rowModel.rows.length || !grouping.length) {\n          rowModel.rows.forEach(row => {\n            row.depth = 0\n            row.parentId = undefined\n          })\n          return rowModel\n        }\n\n        // Filter the grouping list down to columns that exist\n        const existingGrouping = grouping.filter(columnId =>\n          table.getColumn(columnId)\n        )\n\n        const groupedFlatRows: Row<TData>[] = []\n        const groupedRowsById: Record<string, Row<TData>> = {}\n        // const onlyGroupedFlatRows: Row[] = [];\n        // const onlyGroupedRowsById: Record<RowId, Row> = {};\n        // const nonGroupedFlatRows: Row[] = [];\n        // const nonGroupedRowsById: Record<RowId, Row> = {};\n\n        // Recursively group the data\n        const groupUpRecursively = (\n          rows: Row<TData>[],\n          depth = 0,\n          parentId?: string\n        ) => {\n          // Grouping depth has been been met\n          // Stop grouping and simply rewrite thd depth and row relationships\n          if (depth >= existingGrouping.length) {\n            return rows.map(row => {\n              row.depth = depth\n\n              groupedFlatRows.push(row)\n              groupedRowsById[row.id] = row\n\n              if (row.subRows) {\n                row.subRows = groupUpRecursively(row.subRows, depth + 1, row.id)\n              }\n\n              return row\n            })\n          }\n\n          const columnId: string = existingGrouping[depth]!\n\n          // Group the rows together for this level\n          const rowGroupsMap = groupBy(rows, columnId)\n\n          // Perform aggregations for each group\n          const aggregatedGroupedRows = Array.from(rowGroupsMap.entries()).map(\n            ([groupingValue, groupedRows], index) => {\n              let id = `${columnId}:${groupingValue}`\n              id = parentId ? `${parentId}>${id}` : id\n\n              // First, Recurse to group sub rows before aggregation\n              const subRows = groupUpRecursively(groupedRows, depth + 1, id)\n\n              subRows.forEach(subRow => {\n                subRow.parentId = id\n              })\n\n              // Flatten the leaf rows of the rows in this group\n              const leafRows = depth\n                ? flattenBy(groupedRows, row => row.subRows)\n                : groupedRows\n\n              const row = createRow(\n                table,\n                id,\n                leafRows[0]!.original,\n                index,\n                depth,\n                undefined,\n                parentId\n              )\n\n              Object.assign(row, {\n                groupingColumnId: columnId,\n                groupingValue,\n                subRows,\n                leafRows,\n                getValue: (columnId: string) => {\n                  // Don't aggregate columns that are in the grouping\n                  if (existingGrouping.includes(columnId)) {\n                    if (row._valuesCache.hasOwnProperty(columnId)) {\n                      return row._valuesCache[columnId]\n                    }\n\n                    if (groupedRows[0]) {\n                      row._valuesCache[columnId] =\n                        groupedRows[0].getValue(columnId) ?? undefined\n                    }\n\n                    return row._valuesCache[columnId]\n                  }\n\n                  if (row._groupingValuesCache.hasOwnProperty(columnId)) {\n                    return row._groupingValuesCache[columnId]\n                  }\n\n                  // Aggregate the values\n                  const column = table.getColumn(columnId)\n                  const aggregateFn = column?.getAggregationFn()\n\n                  if (aggregateFn) {\n                    row._groupingValuesCache[columnId] = aggregateFn(\n                      columnId,\n                      leafRows,\n                      groupedRows\n                    )\n\n                    return row._groupingValuesCache[columnId]\n                  }\n                },\n              })\n\n              subRows.forEach(subRow => {\n                groupedFlatRows.push(subRow)\n                groupedRowsById[subRow.id] = subRow\n                // if (subRow.getIsGrouped?.()) {\n                //   onlyGroupedFlatRows.push(subRow);\n                //   onlyGroupedRowsById[subRow.id] = subRow;\n                // } else {\n                //   nonGroupedFlatRows.push(subRow);\n                //   nonGroupedRowsById[subRow.id] = subRow;\n                // }\n              })\n\n              return row\n            }\n          )\n\n          return aggregatedGroupedRows\n        }\n\n        const groupedRows = groupUpRecursively(rowModel.rows, 0)\n\n        groupedRows.forEach(subRow => {\n          groupedFlatRows.push(subRow)\n          groupedRowsById[subRow.id] = subRow\n          // if (subRow.getIsGrouped?.()) {\n          //   onlyGroupedFlatRows.push(subRow);\n          //   onlyGroupedRowsById[subRow.id] = subRow;\n          // } else {\n          //   nonGroupedFlatRows.push(subRow);\n          //   nonGroupedRowsById[subRow.id] = subRow;\n          // }\n        })\n\n        return {\n          rows: groupedRows,\n          flatRows: groupedFlatRows,\n          rowsById: groupedRowsById,\n        }\n      },\n      getMemoOptions(table.options, 'debugTable', 'getGroupedRowModel', () => {\n        table._queue(() => {\n          table._autoResetExpanded()\n          table._autoResetPageIndex()\n        })\n      })\n    )\n}\n\nfunction groupBy<TData extends RowData>(rows: Row<TData>[], columnId: string) {\n  const groupMap = new Map<any, Row<TData>[]>()\n\n  return rows.reduce((map, row) => {\n    const resKey = `${row.getGroupingValue(columnId)}`\n    const previous = map.get(resKey)\n    if (!previous) {\n      map.set(resKey, [row])\n    } else {\n      previous.push(row)\n    }\n    return map\n  }, groupMap)\n}\n", "import { Table, RowModel, Row, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\nimport { expandRows } from './getExpandedRowModel'\n\nexport function getPaginationRowModel<TData extends RowData>(opts?: {\n  initialSync: boolean\n}): (table: Table<TData>) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [\n        table.getState().pagination,\n        table.getPrePaginationRowModel(),\n        table.options.paginateExpandedRows\n          ? undefined\n          : table.getState().expanded,\n      ],\n      (pagination, rowModel) => {\n        if (!rowModel.rows.length) {\n          return rowModel\n        }\n\n        const { pageSize, pageIndex } = pagination\n        let { rows, flatRows, rowsById } = rowModel\n        const pageStart = pageSize * pageIndex\n        const pageEnd = pageStart + pageSize\n\n        rows = rows.slice(pageStart, pageEnd)\n\n        let paginatedRowModel: RowModel<TData>\n\n        if (!table.options.paginateExpandedRows) {\n          paginatedRowModel = expandRows({\n            rows,\n            flatRows,\n            rowsById,\n          })\n        } else {\n          paginatedRowModel = {\n            rows,\n            flatRows,\n            rowsById,\n          }\n        }\n\n        paginatedRowModel.flatRows = []\n\n        const handleRow = (row: Row<TData>) => {\n          paginatedRowModel.flatRows.push(row)\n          if (row.subRows.length) {\n            row.subRows.forEach(handleRow)\n          }\n        }\n\n        paginatedRowModel.rows.forEach(handleRow)\n\n        return paginatedRowModel\n      },\n      getMemoOptions(table.options, 'debugTable', 'getPaginationRowModel')\n    )\n}\n", "import { Table, Row, RowModel, RowData } from '../types'\nimport { SortingFn } from '../features/RowSorting'\nimport { getMemoOptions, memo } from '../utils'\n\nexport function getSortedRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [table.getState().sorting, table.getPreSortedRowModel()],\n      (sorting, rowModel) => {\n        if (!rowModel.rows.length || !sorting?.length) {\n          return rowModel\n        }\n\n        const sortingState = table.getState().sorting\n\n        const sortedFlatRows: Row<TData>[] = []\n\n        // Filter out sortings that correspond to non existing columns\n        const availableSorting = sortingState.filter(sort =>\n          table.getColumn(sort.id)?.getCanSort()\n        )\n\n        const columnInfoById: Record<\n          string,\n          {\n            sortUndefined?: false | -1 | 1 | 'first' | 'last'\n            invertSorting?: boolean\n            sortingFn: SortingFn<TData>\n          }\n        > = {}\n\n        availableSorting.forEach(sortEntry => {\n          const column = table.getColumn(sortEntry.id)\n          if (!column) return\n\n          columnInfoById[sortEntry.id] = {\n            sortUndefined: column.columnDef.sortUndefined,\n            invertSorting: column.columnDef.invertSorting,\n            sortingFn: column.getSortingFn(),\n          }\n        })\n\n        const sortData = (rows: Row<TData>[]) => {\n          // This will also perform a stable sorting using the row index\n          // if needed.\n          const sortedData = rows.map(row => ({ ...row }))\n\n          sortedData.sort((rowA, rowB) => {\n            for (let i = 0; i < availableSorting.length; i += 1) {\n              const sortEntry = availableSorting[i]!\n              const columnInfo = columnInfoById[sortEntry.id]!\n              const sortUndefined = columnInfo.sortUndefined\n              const isDesc = sortEntry?.desc ?? false\n\n              let sortInt = 0\n\n              // All sorting ints should always return in ascending order\n              if (sortUndefined) {\n                const aValue = rowA.getValue(sortEntry.id)\n                const bValue = rowB.getValue(sortEntry.id)\n\n                const aUndefined = aValue === undefined\n                const bUndefined = bValue === undefined\n\n                if (aUndefined || bUndefined) {\n                  if (sortUndefined === 'first') return aUndefined ? -1 : 1\n                  if (sortUndefined === 'last') return aUndefined ? 1 : -1\n                  sortInt =\n                    aUndefined && bUndefined\n                      ? 0\n                      : aUndefined\n                        ? sortUndefined\n                        : -sortUndefined\n                }\n              }\n\n              if (sortInt === 0) {\n                sortInt = columnInfo.sortingFn(rowA, rowB, sortEntry.id)\n              }\n\n              // If sorting is non-zero, take care of desc and inversion\n              if (sortInt !== 0) {\n                if (isDesc) {\n                  sortInt *= -1\n                }\n\n                if (columnInfo.invertSorting) {\n                  sortInt *= -1\n                }\n\n                return sortInt\n              }\n            }\n\n            return rowA.index - rowB.index\n          })\n\n          // If there are sub-rows, sort them\n          sortedData.forEach(row => {\n            sortedFlatRows.push(row)\n            if (row.subRows?.length) {\n              row.subRows = sortData(row.subRows)\n            }\n          })\n\n          return sortedData\n        }\n\n        return {\n          rows: sortData(rowModel.rows),\n          flatRows: sortedFlatRows,\n          rowsById: rowModel.rowsById,\n        }\n      },\n      getMemoOptions(table.options, 'debugTable', 'getSortedRowModel', () =>\n        table._autoResetPageIndex()\n      )\n    )\n}\n"], "names": ["createColumnHelper", "accessor", "column", "accessorFn", "accessorKey", "display", "group", "functionalUpdate", "updater", "input", "noop", "makeStateUpdater", "key", "instance", "setState", "old", "isFunction", "d", "Function", "isNumberArray", "Array", "isArray", "every", "val", "flattenBy", "arr", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flat", "recurse", "subArr", "for<PERSON>ach", "item", "push", "children", "length", "memo", "getDeps", "fn", "opts", "deps", "result", "depArgs", "depTime", "debug", "Date", "now", "newDeps", "depsChanged", "some", "dep", "index", "resultTime", "onChange", "depEndTime", "Math", "round", "resultEndTime", "resultFpsPercentage", "pad", "str", "num", "String", "console", "info", "max", "min", "getMemoOptions", "tableOptions", "debugLevel", "_tableOptions$debugAl", "debugAll", "process", "env", "NODE_ENV", "createCell", "table", "row", "columnId", "getRenderValue", "_cell$getValue", "cell", "getValue", "options", "renderFallbackValue", "id", "renderValue", "getContext", "_features", "feature", "createColumn", "columnDef", "depth", "parent", "_ref", "_resolvedColumnDef$id", "defaultColumn", "_getDefaultColumnDef", "resolvedColumnDef", "prototype", "replaceAll", "replace", "undefined", "header", "includes", "originalRow", "split", "_result", "warn", "Error", "columns", "getFlatColumns", "_column$columns", "flatMap", "getLeafColumns", "_getOrderColumnsFn", "orderColumns", "_column$columns2", "leafColumns", "createHeader", "_options$id", "isPlaceholder", "placeholderId", "subHeaders", "colSpan", "rowSpan", "headerGroup", "getLeafHeaders", "leafHeaders", "recurse<PERSON><PERSON><PERSON>", "h", "map", "Headers", "createTable", "getHeaderGroups", "getAllColumns", "getVisibleLeafColumns", "getState", "columnPinning", "left", "right", "allColumns", "_left$map$filter", "_right$map$filter", "leftColumns", "find", "filter", "Boolean", "rightColumns", "centerColumns", "headerGroups", "buildHeaderGroups", "getCenterHeaderGroups", "getLeftHeaderGroups", "_left$map$filter2", "orderedLeafColumns", "getRightHeaderGroups", "_right$map$filter2", "getFooterGroups", "reverse", "getLeftFooterGroups", "getCenterFooterGroups", "getRightFooterGroups", "getFlatHeaders", "headers", "getLeftFlatHeaders", "getCenterFlatHeaders", "getRightFlatHeaders", "getCenterLeafHeaders", "flatHeaders", "_header$subHeaders", "getLeftLeafHeaders", "_header$subHeaders2", "getRightLeafHeaders", "_header$subHeaders3", "center", "_left$0$headers", "_left$", "_center$0$headers", "_center$", "_right$0$headers", "_right$", "columnsToGroup", "headerFamily", "_headerGroups$0$heade", "_headerGroups$", "max<PERSON><PERSON><PERSON>", "findMaxDepth", "getIsVisible", "createHeaderGroup", "headersToGroup", "join", "pendingParentHeaders", "headerToGroup", "latestPendingParentHeader", "isLeafHeader", "bottomHeaders", "recurseHeadersForSpans", "filteredHeaders", "childRowSpans", "childColSpan", "childRowSpan", "minChildRowSpan", "createRow", "original", "rowIndex", "subRows", "parentId", "_valuesCache", "_uniqueValuesCache", "hasOwnProperty", "getColumn", "getUniqueValues", "_row$getValue", "getLeafRows", "getParentRow", "getRow", "getParentRows", "parentRows", "currentRow", "parentRow", "getAllCells", "getAllLeafColumns", "_getAllCellsByColumnId", "allCells", "reduce", "acc", "i", "ColumnFaceting", "_getFacetedRowModel", "getFacetedRowModel", "getPreFilteredRowModel", "_getFacetedUniqueValues", "getFacetedUniqueValues", "Map", "_getFacetedMinMaxValues", "getFacetedMinMaxValues", "includesString", "filterValue", "_filterValue$toString", "search", "toString", "toLowerCase", "autoRemove", "<PERSON><PERSON><PERSON><PERSON>", "includesStringSensitive", "_row$getValue2", "equalsString", "_row$getValue3", "arrIncludes", "_row$getValue4", "arrIncludesAll", "_row$getValue5", "arrIncludesSome", "_row$getValue6", "equals", "weakEquals", "inNumberRange", "rowValue", "resolveFilterValue", "unsafeMin", "unsafeMax", "parsedMin", "parseFloat", "parsedMax", "Number", "isNaN", "Infinity", "temp", "filterFns", "ColumnFiltering", "getDefaultColumnDef", "filterFn", "getInitialState", "state", "columnFilters", "getDefaultOptions", "onColumnFiltersChange", "filterFromLeafRows", "maxLeafRowFilterDepth", "getAutoFilterFn", "firstRow", "getCoreRowModel", "flatRows", "value", "getFilterFn", "_table$options$filter", "_table$options$filter2", "getCanFilter", "_column$columnDef$ena", "_table$options$enable", "_table$options$enable2", "enableColumnFilter", "enableColumnFilters", "enableFilters", "getIsFiltered", "getFilterIndex", "getFilterValue", "_table$getState$colum", "_table$getState$colum2", "_table$getState$colum3", "findIndex", "setFilterValue", "setColumnFilters", "previousFilter", "newFilter", "shouldAutoRemoveFilter", "_old$filter", "newFilterObj", "_old$map", "_table", "columnFiltersMeta", "updateFn", "_functionalUpdate", "resetColumnFilters", "defaultState", "_table$initialState$c", "_table$initialState", "initialState", "getFilteredRowModel", "_getFilteredRowModel", "manualFiltering", "sum", "_leafRows", "childRows", "next", "nextValue", "extent", "mean", "leafRows", "count", "median", "values", "mid", "floor", "nums", "sort", "a", "b", "unique", "from", "Set", "uniqueCount", "size", "_columnId", "aggregationFns", "ColumnGrouping", "aggregatedCell", "props", "_toString", "_props$getValue", "aggregationFn", "grouping", "onGroupingChange", "groupedColumnMode", "toggleGrouping", "setGrouping", "getCanGroup", "enableGrouping", "getGroupingValue", "getIsGrouped", "_table$getState$group", "getGroupedIndex", "_table$getState$group2", "indexOf", "getToggleGroupingHandler", "canGroup", "getAutoAggregationFn", "Object", "call", "getAggregationFn", "_table$options$aggreg", "_table$options$aggreg2", "resetGrouping", "_table$initialState$g", "getPreGroupedRowModel", "getGroupedRowModel", "_getGroupedRowModel", "manualGrouping", "groupingColumnId", "_groupingValuesCache", "getIsPlaceholder", "getIsAggregated", "_row$subRows", "nonGroupingColumns", "col", "groupingColumns", "g", "ColumnOrdering", "columnOrder", "onColumnOrderChange", "getIndex", "position", "_getVisibleLeafColumns", "getIsFirstColumn", "_columns$", "getIsLastColumn", "_columns", "setColumnOrder", "resetColumnOrder", "orderedColumns", "columnOrderCopy", "columnsCopy", "targetColumnId", "shift", "foundIndex", "splice", "getDefaultColumnPinningState", "ColumnPinning", "onColumnPinningChange", "pin", "columnIds", "setColumnPinning", "_old$left3", "_old$right3", "_old$left", "_old$right", "_old$left2", "_old$right2", "getCanPin", "_d$columnDef$enablePi", "enablePinning", "enableColumnPinning", "getIsPinned", "leafColumnIds", "isLeft", "isRight", "getPinnedIndex", "getCenterVisibleCells", "_getAllVisibleCells", "leftAndRight", "getLeftVisibleCells", "cells", "getRightVisibleCells", "resetColumnPinning", "getIsSomeColumnsPinned", "_pinningState$positio", "pinningState", "_pinningState$left", "_pinningState$right", "getLeftLeafColumns", "getRightLeafColumns", "getCenterLeafColumns", "defaultColumnSizing", "minSize", "maxSize", "MAX_SAFE_INTEGER", "getDefaultColumnSizingInfoState", "startOffset", "startSize", "deltaOffset", "deltaPercentage", "isResizingColumn", "columnSizingStart", "ColumnSizing", "columnSizing", "columnSizingInfo", "columnResizeMode", "columnResizeDirection", "onColumnSizingChange", "onColumnSizingInfoChange", "getSize", "_column$columnDef$min", "_column$columnDef$max", "columnSize", "getStart", "slice", "getAfter", "resetSize", "setColumnSizing", "_ref2", "_", "rest", "getCanResize", "enableResizing", "enableColumnResizing", "getIsResizing", "_header$column$getSiz", "prevSiblingHeader", "getResizeHandler", "_contextDocument", "canResize", "e", "persist", "isTouchStartEvent", "touches", "clientX", "newColumnSizing", "updateOffset", "eventType", "clientXPos", "setColumnSizingInfo", "_old$startOffset", "_old$startSize", "deltaDirection", "_ref3", "headerSize", "onMove", "onEnd", "contextDocument", "document", "mouseEvents", "<PERSON><PERSON><PERSON><PERSON>", "up<PERSON><PERSON><PERSON>", "removeEventListener", "touchEvents", "cancelable", "preventDefault", "stopPropagation", "_e$touches$", "passiveIfSupported", "passiveEventSupported", "passive", "addEventListener", "resetColumnSizing", "resetHeaderSizeInfo", "_table$initialState$c2", "getTotalSize", "_table$getHeaderGroup", "_table$getHeaderGroup2", "getLeftTotalSize", "_table$getLeftHeaderG", "_table$getLeftHeaderG2", "getCenterTotalSize", "_table$getCenterHeade", "_table$getCenterHeade2", "getRightTotalSize", "_table$getRightHeader", "_table$getRightHeader2", "passiveSupported", "supported", "window", "err", "type", "ColumnVisibility", "columnVisibility", "onColumnVisibilityChange", "toggleVisibility", "getCanHide", "setColumnVisibility", "childColumns", "c", "enableHiding", "getToggleVisibilityHandler", "target", "checked", "getVisibleCells", "makeVisibleColumnsMethod", "getColumns", "getVisibleFlatColumns", "getAllFlatColumns", "getLeftVisibleLeafColumns", "getRightVisibleLeafColumns", "getCenterVisibleLeafColumns", "resetColumnVisibility", "toggleAllColumnsVisible", "_value", "getIsAllColumnsVisible", "obj", "getIsSomeColumnsVisible", "getToggleAllColumnsVisibilityHandler", "_target", "GlobalFaceting", "_getGlobalFacetedRowModel", "getGlobalFacetedRowModel", "_getGlobalFacetedUniqueValues", "getGlobalFacetedUniqueValues", "_getGlobalFacetedMinMaxValues", "getGlobalFacetedMinMaxValues", "GlobalFiltering", "globalFilter", "onGlobalFilterChange", "globalFilterFn", "getColumnCanGlobalFilter", "_table$getCoreRowMode", "getCanGlobalFilter", "_table$options$getCol", "enableGlobalFilter", "getGlobalAutoFilterFn", "getGlobalFilterFn", "setGlobalFilter", "resetGlobalFilter", "RowExpanding", "expanded", "onExpandedChange", "paginateExpandedRows", "registered", "queued", "_autoResetExpanded", "_table$options$autoRe", "_queue", "autoResetAll", "autoResetExpanded", "manualExpanding", "resetExpanded", "setExpanded", "toggleAllRowsExpanded", "getIsAllRowsExpanded", "_table$initialState$e", "getCanSomeRowsExpand", "getPrePaginationRowModel", "getCanExpand", "getToggleAllRowsExpandedHandler", "getIsSomeRowsExpanded", "keys", "getRowModel", "getIsExpanded", "getExpandedDepth", "rowIds", "rowsById", "splitId", "getPreExpandedRowModel", "getSortedRowModel", "getExpandedRowModel", "_getExpandedRowModel", "toggleExpanded", "_expanded", "exists", "oldExpanded", "rowId", "_table$options$getIsR", "getIsRowExpanded", "_table$options$getRow", "getRowCanExpand", "enableExpanding", "getIsAllParentsExpanded", "isFullyExpanded", "getToggleExpandedHandler", "canExpand", "defaultPageIndex", "defaultPageSize", "getDefaultPaginationState", "pageIndex", "pageSize", "RowPagination", "pagination", "onPaginationChange", "_autoResetPageIndex", "autoResetPageIndex", "manualPagination", "resetPageIndex", "setPagination", "safeUpdater", "newState", "resetPagination", "_table$initialState$p", "setPageIndex", "maxPageIndex", "pageCount", "_table$initialState$p2", "resetPageSize", "_table$initialState$p3", "_table$initialState2", "setPageSize", "topRowIndex", "setPageCount", "_table$options$pageCo", "newPageCount", "getPageOptions", "getPageCount", "pageOptions", "fill", "getCanPreviousPage", "getCanNextPage", "previousPage", "nextPage", "firstPage", "lastPage", "getPaginationRowModel", "_getPaginationRowModel", "_table$options$pageCo2", "ceil", "getRowCount", "_table$options$rowCou", "rowCount", "rows", "getDefaultRowPinningState", "top", "bottom", "RowPinning", "rowPinning", "onRowPinningChange", "includeLeafRows", "includeParentRows", "leafRowIds", "parentRowIds", "setRowPinning", "_old$top3", "_old$bottom3", "_old$top", "_old$bottom", "has", "_old$top2", "_old$bottom2", "enableRowPinning", "isTop", "isBottom", "_ref4", "_visiblePinnedRowIds$", "visiblePinnedRowIds", "getTopRows", "getBottomRows", "_ref5", "resetRowPinning", "_table$initialState$r", "getIsSomeRowsPinned", "_pinningState$top", "_pinningState$bottom", "_getPinnedRows", "visibleRows", "pinnedRowIds", "_table$options$keepPi", "keepPinnedRows", "allRows", "topPinnedRowIds", "bottomPinnedRowIds", "getCenterRows", "topAndBottom", "RowSelection", "rowSelection", "onRowSelectionChange", "enableRowSelection", "enableMultiRowSelection", "enableSubRowSelection", "setRowSelection", "resetRowSelection", "toggleAllRowsSelected", "getIsAllRowsSelected", "preGroupedFlatRows", "getCanSelect", "toggleAllPageRowsSelected", "resolvedValue", "getIsAllPageRowsSelected", "mutateRowIsSelected", "getPreSelectedRowModel", "getSelectedRowModel", "rowModel", "selectRowsFn", "getFilteredSelectedRowModel", "getGroupedSelectedRowModel", "isAllRowsSelected", "paginationFlatRows", "isAllPageRowsSelected", "getIsSomeRowsSelected", "_table$getState$rowSe", "totalSelected", "getIsSomePageRowsSelected", "getIsSelected", "getIsSomeSelected", "getToggleAllRowsSelectedHandler", "getToggleAllPageRowsSelectedHandler", "toggleSelected", "isSelected", "_opts$selectChildren", "selectedRowIds", "select<PERSON><PERSON><PERSON><PERSON>", "isRowSelected", "isSubRowSelected", "getIsAllSubRowsSelected", "getCanSelectSubRows", "getCanMultiSelect", "_table$options$enable3", "getToggleSelectedHandler", "canSelect", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "newSelectedFlatRows", "newSelectedRowsById", "recurseRows", "_row$subRows2", "selection", "_selection$row$id", "_row$subRows3", "allChildrenSelected", "someSelected", "subRow", "subRowChildrenSelected", "reSplitAlphaNumeric", "alphanumeric", "rowA", "rowB", "compareAlphanumeric", "alphanumericCaseSensitive", "text", "compareBasic", "textCaseSensitive", "datetime", "basic", "aStr", "bStr", "aa", "bb", "an", "parseInt", "bn", "combo", "sortingFns", "RowSorting", "sorting", "sortingFn", "sortUndefined", "onSortingChange", "isMultiSortEvent", "shift<PERSON>ey", "getAutoSortingFn", "firstRows", "isString", "getAutoSortDir", "getSortingFn", "_table$options$sortin", "_table$options$sortin2", "toggleSorting", "desc", "multi", "nextSortingOrder", "getNextSortingOrder", "hasManual<PERSON><PERSON>ue", "setSorting", "existingSorting", "existingIndex", "newSorting", "sortAction", "nextDesc", "getCanMultiSort", "_table$options$maxMul", "maxMultiSortColCount", "getFirstSortDir", "_column$columnDef$sor", "sortDescFirst", "firstSortDirection", "isSorted", "getIsSorted", "enableSortingRemoval", "enableMultiRemove", "getCanSort", "enableSorting", "_column$columnDef$ena2", "enableMultiSort", "_table$getState$sorti", "columnSort", "getSortIndex", "_table$getState$sorti2", "_table$getState$sorti3", "clearSorting", "getToggleSortingHandler", "canSort", "resetSorting", "_table$initialState$s", "getPreSortedRowModel", "_getSortedRowModel", "manualSorting", "builtInFeatures", "_options$_features", "_options$initialState", "debugTable", "defaultOptions", "assign", "mergeOptions", "coreInitialState", "_feature$getInitialSt", "queuedTimeout", "coreInstance", "cb", "Promise", "resolve", "then", "catch", "error", "setTimeout", "reset", "setOptions", "newOptions", "onStateChange", "_getRowId", "getRowId", "_getCoreRowModel", "searchAll", "_defaultColumn", "_props$renderValue$to", "_props$renderValue", "_getColumnDefs", "columnDefs", "recurseColumns", "groupingColumnDef", "_getAllFlatColumnsById", "flatColumns", "data", "accessRows", "originalRows", "getSubRows", "_row$originalSubRows", "originalSubRows", "expandRows", "expandedRows", "handleRow", "_table$getColumn", "facetedRowModel", "uniqueValues", "flatRow", "_flatRow$getUniqueVal", "facetedMinValue", "facetedMaxValue", "filterRows", "filterRowImpl", "filterRowModelFromLeafs", "filterRowModelFromRoot", "rowsToFilter", "filterRow", "_table$options$maxLea", "newFilteredFlatRows", "newFilteredRowsById", "recurseFilterRows", "newRow", "_table$options$maxLea2", "pass", "preRowModel", "filterableIds", "filterRowsImpl", "facetedUniqueValues", "j", "_facetedUniqueValues$", "set", "get", "resolvedColumnFilters", "resolvedGlobalFilters", "_filterFn$resolveFilt", "globallyFilterableColumns", "_globalFilterFn$resol", "currentColumnFilter", "currentGlobalFilter", "filterMeta", "__global__", "existingGrouping", "groupedFlatRows", "groupedRowsById", "groupUpRecursively", "rowGroupsMap", "groupBy", "aggregatedGroupedRows", "entries", "groupingValue", "groupedRows", "_groupedRows$0$getVal", "aggregateFn", "groupMap", "res<PERSON>ey", "previous", "pageStart", "pageEnd", "paginatedRowModel", "sortingState", "sortedFlatRows", "availableSorting", "columnInfoById", "sortEntry", "invertSorting", "sortData", "sortedData", "_sortEntry$desc", "columnInfo", "isDesc", "sortInt", "aValue", "bValue", "aUndefined", "bUndefined"], "mappings": ";;;;;;;;;MAWA,kBAAA;AACA,sBAAA;AACA,qBAAA;AACA,gBAAA;AACA,mBAAA;AACA,mBAAA;AACA,qBAAA;AACA,oBAAA;AACA,cAAA;AACA,aAAA;AACA,UAAA;AACA,qBAAA;AACA,UAAA;AACA,QAAA;AACA,iCAAA;AACA,aAAA;AACA,mBAAA;AACA,eAAA;AACA,qBAAA;AACA,UAAA;AACA,QAAA;AACA,MAAA;AACA,IAAA;AAEA,oDAAA;AACA,+CAAA;AAEA,8CAAA;AAEA,kCAAA;AACA,mCAAA;AACA,KAAA;AAEA,wCAAA;AACA,mCAAA;AACA,KAAA;AAEA,kCAAA;AACA,mCAAA;AACA,KAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsBO,SAASA,kBAAkBA,GAET;IACvB,OAAO;QACLC,QAAQ,EAAEA,CAACA,QAAQ,EAAEC,MAAM,KAAK;YAC9B,OAAO,OAAOD,QAAQ,KAAK,UAAU,GAChC;gBACC,GAAGC,MAAM;gBACTC,UAAU,EAAEF,QAAAA;YACd,CAAC,GACD;gBACE,GAAGC,MAAM;gBACTE,WAAW,EAAEH,QAAAA;aACd,CAAA;SACN;QACDI,OAAO,GAAEH,MAAM,GAAIA,MAAM;QACzBI,KAAK,GAAEJ,MAAM,GAAIA,MAAAA;KAClB,CAAA;AACH;AC9DA,wBAAA;AAOA,qDAAA;AA2CA,GAAA;AAEO,SAASK,gBAAgBA,CAAIC,OAAmB,EAAEC,KAAQ,EAAK;IACpE,OAAO,OAAOD,OAAO,KAAK,UAAU,GAC/BA,OAAO,CAAqBC,KAAK,CAAC,GACnCD,OAAO,CAAA;AACb,CAAA;AAEO,SAASE,IAAIA,GAAG;AACrB,EAAA;AAAA,CAAA;AAGK,SAASC,gBAAgBA,CAC9BC,GAAM,EACNC,QAAiB,EACjB;IACA,QAAQL,OAA+B,IAAK;QACxCK,QAAQ,CAASC,QAAQ,EAAeC,GAAgB,IAAK;YAC7D,OAAO;gBACL,GAAGA,GAAG;gBACN,CAACH,GAAG,CAAA,EAAGL,gBAAgB,CAACC,OAAO,EAAGO,GAAG,CAASH,GAAG,CAAC,CAAA;aACnD,CAAA;QACH,CAAC,CAAC,CAAA;KACH,CAAA;AACH,CAAA;AAIO,SAASI,UAAUA,CAAwBC,CAAM,EAAU;IAChE,OAAOA,CAAC,YAAYC,QAAQ,CAAA;AAC9B,CAAA;AAEO,SAASC,aAAaA,CAACF,CAAM,EAAiB;IACnD,OAAOG,KAAK,CAACC,OAAO,CAACJ,CAAC,CAAC,IAAIA,CAAC,CAACK,KAAK,EAACC,GAAG,GAAI,OAAOA,GAAG,KAAK,QAAQ,CAAC,CAAA;AACpE,CAAA;AAEO,SAASC,SAASA,CACvBC,GAAY,EACZC,WAAqC,EACrC;IACA,MAAMC,IAAa,GAAG,EAAE,CAAA;IAExB,MAAMC,OAAO,IAAIC,MAAe,IAAK;QACnCA,MAAM,CAACC,OAAO,EAACC,IAAI,IAAI;YACrBJ,IAAI,CAACK,IAAI,CAACD,IAAI,CAAC,CAAA;YACf,MAAME,QAAQ,GAAGP,WAAW,CAACK,IAAI,CAAC,CAAA;YAClC,IAAIE,QAAQ,IAAA,IAAA,IAARA,QAAQ,CAAEC,MAAM,EAAE;gBACpBN,OAAO,CAACK,QAAQ,CAAC,CAAA;YACnB,CAAA;QACF,CAAC,CAAC,CAAA;KACH,CAAA;IAEDL,OAAO,CAACH,GAAG,CAAC,CAAA;IAEZ,OAAOE,IAAI,CAAA;AACb,CAAA;AAEO,SAASQ,IAAIA,CAClBC,OAA2C,EAC3CC,EAA6C,EAC7CC,IAIC,EACgC;IACjC,IAAIC,IAAW,GAAG,EAAE,CAAA;IACpB,IAAIC,MAA2B,CAAA;IAE/B,QAAOC,OAAO,IAAI;QAChB,IAAIC,OAAe,CAAA;QACnB,IAAIJ,IAAI,CAAC1B,GAAG,IAAI0B,IAAI,CAACK,KAAK,EAAED,OAAO,GAAGE,IAAI,CAACC,GAAG,EAAE,CAAA;QAEhD,MAAMC,OAAO,GAAGV,OAAO,CAACK,OAAO,CAAC,CAAA;QAEhC,MAAMM,WAAW,GACfD,OAAO,CAACZ,MAAM,KAAKK,IAAI,CAACL,MAAM,IAC9BY,OAAO,CAACE,IAAI,CAAC,CAACC,GAAQ,EAAEC,KAAa,GAAKX,IAAI,CAACW,KAAK,CAAC,KAAKD,GAAG,CAAC,CAAA;QAEhE,IAAI,CAACF,WAAW,EAAE;YAChB,OAAOP,MAAM,CAAA;QACf,CAAA;QAEAD,IAAI,GAAGO,OAAO,CAAA;QAEd,IAAIK,UAAkB,CAAA;QACtB,IAAIb,IAAI,CAAC1B,GAAG,IAAI0B,IAAI,CAACK,KAAK,EAAEQ,UAAU,GAAGP,IAAI,CAACC,GAAG,EAAE,CAAA;QAEnDL,MAAM,GAAGH,EAAE,CAAC,GAAGS,OAAO,CAAC,CAAA;QACvBR,IAAI,IAAA,IAAA,IAAJA,IAAI,CAAEc,QAAQ,IAAA,IAAA,IAAdd,IAAI,CAAEc,QAAQ,CAAGZ,MAAM,CAAC,CAAA;QAExB,IAAIF,IAAI,CAAC1B,GAAG,IAAI0B,IAAI,CAACK,KAAK,EAAE;YAC1B,IAAIL,IAAI,IAAJA,IAAAA,IAAAA,IAAI,CAAEK,KAAK,EAAE,EAAE;gBACjB,MAAMU,UAAU,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACX,IAAI,CAACC,GAAG,EAAE,GAAGH,OAAQ,IAAI,GAAG,CAAC,GAAG,GAAG,CAAA;gBAClE,MAAMc,aAAa,GAAGF,IAAI,CAACC,KAAK,CAAC,CAACX,IAAI,CAACC,GAAG,EAAE,GAAGM,UAAW,IAAI,GAAG,CAAC,GAAG,GAAG,CAAA;gBACxE,MAAMM,mBAAmB,GAAGD,aAAa,GAAG,EAAE,CAAA;gBAE9C,MAAME,GAAG,GAAGA,CAACC,GAAoB,EAAEC,GAAW,KAAK;oBACjDD,GAAG,GAAGE,MAAM,CAACF,GAAG,CAAC,CAAA;oBACjB,MAAOA,GAAG,CAACzB,MAAM,GAAG0B,GAAG,CAAE;wBACvBD,GAAG,GAAG,GAAG,GAAGA,GAAG,CAAA;oBACjB,CAAA;oBACA,OAAOA,GAAG,CAAA;iBACX,CAAA;gBAEDG,OAAO,CAACC,IAAI,CACV,CAAA,IAAA,EAAOL,GAAG,CAACF,aAAa,EAAE,CAAC,CAAC,CAAA,EAAA,EAAKE,GAAG,CAACL,UAAU,EAAE,CAAC,CAAC,CAAA,GAAA,CAAK,EACxD,CAAA;;;uBAGeC,EAAAA,IAAI,CAACU,GAAG,CACnB,CAAC,EACDV,IAAI,CAACW,GAAG,CAAC,GAAG,GAAG,GAAG,GAAGR,mBAAmB,EAAE,GAAG,CAC/C,CAAC,CAAA,cAAA,CAAgB,EACnBnB,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAE1B,GACR,CAAC,CAAA;YACH,CAAA;QACF,CAAA;QAEA,OAAO4B,MAAM,CAAA;KACd,CAAA;AACH,CAAA;AAEO,SAAS0B,cAAcA,CAC5BC,YAAgD,EAChDC,UAMkB,EAClBxD,GAAW,EACXwC,QAAgC,EAChC;IACA,OAAO;QACLT,KAAK,EAAEA,MAAA;YAAA,IAAA0B,qBAAA,CAAA;YAAA,OAAA,CAAAA,qBAAA,GAAMF,YAAY,IAAA,IAAA,GAAA,KAAA,CAAA,GAAZA,YAAY,CAAEG,QAAQ,KAAA,IAAA,GAAAD,qBAAA,GAAIF,YAAY,CAACC,UAAU,CAAC,CAAA;QAAA,CAAA;QAC/DxD,GAAG,EAAE2D,OAAO,CAACC,GAAG,CAACC,QAAQ,gCAAK,aAAa,IAAI7D,GAAG;QAClDwC,QAAAA;KACD,CAAA;AACH;ACvKO,SAASsB,UAAUA,CACxBC,KAAmB,EACnBC,GAAe,EACf1E,MAA6B,EAC7B2E,QAAgB,EACK;IACrB,MAAMC,cAAc,GAAGA,MAAA;QAAA,IAAAC,cAAA,CAAA;QAAA,OAAA,CAAAA,cAAA,GACrBC,IAAI,CAACC,QAAQ,EAAE,KAAAF,IAAAA,GAAAA,cAAA,GAAIJ,KAAK,CAACO,OAAO,CAACC,mBAAmB,CAAA;IAAA,CAAA,CAAA;IAEtD,MAAMH,IAA6B,GAAG;QACpCI,EAAE,EAAE,GAAGR,GAAG,CAACQ,EAAE,CAAIlF,CAAAA,EAAAA,MAAM,CAACkF,EAAE,CAAE,CAAA;QAC5BR,GAAG;QACH1E,MAAM;QACN+E,QAAQ,EAAEA,IAAML,GAAG,CAACK,QAAQ,CAACJ,QAAQ,CAAC;QACtCQ,WAAW,EAAEP,cAAc;QAC3BQ,UAAU,EAAEnD,IAAI,CACd,IAAM;gBAACwC,KAAK;gBAAEzE,MAAM;gBAAE0E,GAAG;gBAAEI,IAAI;aAAC,EAChC,CAACL,KAAK,EAAEzE,MAAM,EAAE0E,GAAG,EAAEI,IAAI,GAAA,CAAM;gBAC7BL,KAAK;gBACLzE,MAAM;gBACN0E,GAAG;gBACHI,IAAI,EAAEA,IAA2B;gBACjCC,QAAQ,EAAED,IAAI,CAACC,QAAQ;gBACvBI,WAAW,EAAEL,IAAI,CAACK,WAAAA;aACnB,CAAC,EACFnB,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,iBAAiB,CAC/D,CAAA;KACD,CAAA;IAEDP,KAAK,CAACY,SAAS,CAACzD,OAAO,EAAC0D,OAAO,IAAI;QACjCA,OAAO,CAACd,UAAU,IAAlBc,IAAAA,IAAAA,OAAO,CAACd,UAAU,CAChBM,IAAI,EACJ9E,MAAM,EACN0E,GAAG,EACHD,KACF,CAAC,CAAA;KACF,EAAE,CAAA,CAAE,CAAC,CAAA;IAEN,OAAOK,IAAI,CAAA;AACb;AC1BO,SAASS,YAAYA,CAC1Bd,KAAmB,EACnBe,SAAmC,EACnCC,KAAa,EACbC,MAA8B,EACP;IAAA,IAAAC,IAAA,EAAAC,qBAAA,CAAA;IACvB,MAAMC,aAAa,GAAGpB,KAAK,CAACqB,oBAAoB,EAAE,CAAA;IAElD,MAAMC,iBAAiB,GAAG;QACxB,GAAGF,aAAa;QAChB,GAAGL,SAAAA;KACwB,CAAA;IAE7B,MAAMtF,WAAW,GAAG6F,iBAAiB,CAAC7F,WAAW,CAAA;IAEjD,IAAIgF,EAAE,GAAAS,CAAAA,IAAA,GAAAC,CAAAA,qBAAA,GACJG,iBAAiB,CAACb,EAAE,KAAAU,IAAAA,GAAAA,qBAAA,GACnB1F,WAAW,GACR,OAAOyD,MAAM,CAACqC,SAAS,CAACC,UAAU,KAAK,UAAU,GAC/C/F,WAAW,CAAC+F,UAAU,CAAC,GAAG,EAAE,GAAG,CAAC,GAChC/F,WAAW,CAACgG,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,GACjCC,SAAS,KAAA,IAAA,GAAAR,IAAA,GACZ,OAAOI,iBAAiB,CAACK,MAAM,KAAK,QAAQ,GACzCL,iBAAiB,CAACK,MAAM,GACxBD,SAAU,CAAA;IAEhB,IAAIlG,UAAyC,CAAA;IAE7C,IAAI8F,iBAAiB,CAAC9F,UAAU,EAAE;QAChCA,UAAU,GAAG8F,iBAAiB,CAAC9F,UAAU,CAAA;KAC1C,MAAM,IAAIC,WAAW,EAAE;QACtB,6BAAA;QACA,IAAIA,WAAW,CAACmG,QAAQ,CAAC,GAAG,CAAC,EAAE;YAC7BpG,UAAU,IAAIqG,WAAkB,IAAK;gBACnC,IAAIhE,MAAM,GAAGgE,WAAkC,CAAA;gBAE/C,KAAK,MAAM5F,GAAG,IAAIR,WAAW,CAACqG,KAAK,CAAC,GAAG,CAAC,CAAE;oBAAA,IAAAC,OAAA,CAAA;oBACxClE,MAAM,GAAA,CAAAkE,OAAA,GAAGlE,MAAM,KAAA,OAAA,KAAA,IAANkE,OAAA,CAAS9F,GAAG,CAAC,CAAA;oBACtB,IAAI2D,OAAO,CAACC,GAAG,CAACC,QAAQ,gCAAK,YAAY,IAAIjC,MAAM,KAAK6D,SAAS,EAAE;wBACjEvC,OAAO,CAAC6C,IAAI,CACV,CAAA,CAAA,EAAI/F,GAAG,CAA2BR,wBAAAA,EAAAA,WAAW,CAAA,qBAAA,CAC/C,CAAC,CAAA;oBACH,CAAA;gBACF,CAAA;gBAEA,OAAOoC,MAAM,CAAA;aACd,CAAA;QACH,CAAC,MAAM;YACLrC,UAAU,IAAIqG,WAAkB,GAC7BA,WAAW,CAASP,iBAAiB,CAAC7F,WAAW,CAAC,CAAA;QACvD,CAAA;IACF,CAAA;IAEA,IAAI,CAACgF,EAAE,EAAE;QACP,IAAIb,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,WAAc,CAAF;YACvC,MAAM,IAAImC,KAAK,CACbX,iBAAiB,CAAC9F,UAAU,GACxB,CAAA,8CAAA,CAAgD,GAChD,CAAA,oDAAA,CACN,CAAC,CAAA;QACH,CAAA;QACA,MAAM,IAAIyG,KAAK,EAAE,CAAA;IACnB,CAAA;IAEA,IAAI1G,MAA8B,GAAG;QACnCkF,EAAE,EAAE,CAAGvB,EAAAA,MAAM,CAACuB,EAAE,CAAC,CAAE,CAAA;QACnBjF,UAAU;QACVyF,MAAM,EAAEA,MAAa;QACrBD,KAAK;QACLD,SAAS,EAAEO,iBAA0C;QACrDY,OAAO,EAAE,EAAE;QACXC,cAAc,EAAE3E,IAAI,CAClB,IAAM;gBAAC,IAAI;aAAC,EACZ,MAAM;YAAA,IAAA4E,eAAA,CAAA;YACJ,OAAO;gBACL7G,MAAM,EACN;mBAAA,CAAA6G,eAAA,GAAG7G,MAAM,CAAC2G,OAAO,KAAdE,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,eAAA,CAAgBC,OAAO,EAAC/F,CAAC,GAAIA,CAAC,CAAC6F,cAAc,EAAE,CAAC;aACpD,CAAA;SACF,EACD5C,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,cAAc,EAAE,uBAAuB,CACvE,CAAC;QACD+B,cAAc,EAAE9E,IAAI,CAClB,IAAM;gBAACwC,KAAK,CAACuC,kBAAkB,EAAE;aAAC,GAClCC,YAAY,IAAI;YAAA,IAAAC,gBAAA,CAAA;YACd,IAAAA,CAAAA,gBAAA,GAAIlH,MAAM,CAAC2G,OAAO,KAAdO,IAAAA,IAAAA,gBAAA,CAAgBlF,MAAM,EAAE;gBAC1B,IAAImF,WAAW,GAAGnH,MAAM,CAAC2G,OAAO,CAACG,OAAO,EAAC9G,MAAM,GAC7CA,MAAM,CAAC+G,cAAc,EACvB,CAAC,CAAA;gBAED,OAAOE,YAAY,CAACE,WAAW,CAAC,CAAA;YAClC,CAAA;YAEA,OAAO;gBAACnH,MAAM;aAA0B,CAAA;SACzC,EACDgE,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,cAAc,EAAE,uBAAuB,CACvE,CAAA;KACD,CAAA;IAED,KAAK,MAAMM,OAAO,IAAIb,KAAK,CAACY,SAAS,CAAE;QACrCC,OAAO,CAACC,YAAY,IAAA,IAAA,IAApBD,OAAO,CAACC,YAAY,CAAGvF,MAAM,EAA2ByE,KAAK,CAAC,CAAA;IAChE,CAAA;IAEA,yFAAA;IACA,OAAOzE,MAAM,CAAA;AACf;AC9JA,MAAMyC,KAAK,GAAG,cAAc,CAAA;AAwM5B,EAAA;AAEA,SAAS2E,YAAYA,CACnB3C,KAAmB,EACnBzE,MAA6B,EAC7BgF,OAMC,EACsB;IAAA,IAAAqC,WAAA,CAAA;IACvB,MAAMnC,EAAE,GAAA,CAAAmC,WAAA,GAAGrC,OAAO,CAACE,EAAE,KAAA,IAAA,GAAAmC,WAAA,GAAIrH,MAAM,CAACkF,EAAE,CAAA;IAElC,IAAIkB,MAAiC,GAAG;QACtClB,EAAE;QACFlF,MAAM;QACNgD,KAAK,EAAEgC,OAAO,CAAChC,KAAK;QACpBsE,aAAa,EAAE,CAAC,CAACtC,OAAO,CAACsC,aAAa;QACtCC,aAAa,EAAEvC,OAAO,CAACuC,aAAa;QACpC9B,KAAK,EAAET,OAAO,CAACS,KAAK;QACpB+B,UAAU,EAAE,EAAE;QACdC,OAAO,EAAE,CAAC;QACVC,OAAO,EAAE,CAAC;QACVC,WAAW,EAAE,IAAK;QAClBC,cAAc,EAAEA,MAAgC;YAC9C,MAAMC,WAAqC,GAAG,EAAE,CAAA;YAEhD,MAAMC,aAAa,IAAIC,CAAyB,IAAK;gBACnD,IAAIA,CAAC,CAACP,UAAU,IAAIO,CAAC,CAACP,UAAU,CAACxF,MAAM,EAAE;oBACvC+F,CAAC,CAACP,UAAU,CAACQ,GAAG,CAACF,aAAa,CAAC,CAAA;gBACjC,CAAA;gBACAD,WAAW,CAAC/F,IAAI,CAACiG,CAA2B,CAAC,CAAA;aAC9C,CAAA;YAEDD,aAAa,CAAC1B,MAAM,CAAC,CAAA;YAErB,OAAOyB,WAAW,CAAA;SACnB;QACDzC,UAAU,EAAEA,IAAAA,CAAO;gBACjBX,KAAK;gBACL2B,MAAM,EAAEA,MAA+B;gBACvCpG,MAAAA;aACD,CAAA;KACF,CAAA;IAEDyE,KAAK,CAACY,SAAS,CAACzD,OAAO,EAAC0D,OAAO,IAAI;QACjCA,OAAO,CAAC8B,YAAY,IAAA,IAAA,IAApB9B,OAAO,CAAC8B,YAAY,CAAGhB,MAAM,EAA2B3B,KAAK,CAAC,CAAA;IAChE,CAAC,CAAC,CAAA;IAEF,OAAO2B,MAAM,CAAA;AACf,CAAA;AAEO,MAAM6B,OAAqB,GAAG;IACnCC,WAAW,GAA0BzD,KAAmB,IAAW;QACjE,gBAAA;QAEAA,KAAK,CAAC0D,eAAe,GAAGlG,IAAI,CAC1B,IAAM;gBACJwC,KAAK,CAAC2D,aAAa,EAAE;gBACrB3D,KAAK,CAAC4D,qBAAqB,EAAE;gBAC7B5D,KAAK,CAAC6D,QAAQ,EAAE,CAACC,aAAa,CAACC,IAAI;gBACnC/D,KAAK,CAAC6D,QAAQ,EAAE,CAACC,aAAa,CAACE,KAAK;aACrC,EACD,CAACC,UAAU,EAAEvB,WAAW,EAAEqB,IAAI,EAAEC,KAAK,KAAK;YAAA,IAAAE,gBAAA,EAAAC,iBAAA,CAAA;YACxC,MAAMC,WAAW,GAAA,CAAAF,gBAAA,GACfH,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CACAR,GAAG,EAACrD,QAAQ,GAAIwC,WAAW,CAAC2B,IAAI,EAAC/H,CAAC,GAAIA,CAAC,CAACmE,EAAE,KAAKP,QAAQ,CAAE,CAAC,CAC3DoE,MAAM,CAACC,OAAO,CAAC,KAAAL,IAAAA,GAAAA,gBAAA,GAAI,EAAE,CAAA;YAE1B,MAAMM,YAAY,GAAA,CAAAL,iBAAA,GAChBH,KAAK,IAALA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,KAAK,CACDT,GAAG,EAACrD,QAAQ,GAAIwC,WAAW,CAAC2B,IAAI,EAAC/H,CAAC,GAAIA,CAAC,CAACmE,EAAE,KAAKP,QAAQ,CAAE,CAAC,CAC3DoE,MAAM,CAACC,OAAO,CAAC,KAAAJ,IAAAA,GAAAA,iBAAA,GAAI,EAAE,CAAA;YAE1B,MAAMM,aAAa,GAAG/B,WAAW,CAAC4B,MAAM,EACtC/I,MAAM,GAAI,CAAA,CAACwI,IAAI,IAAA,IAAA,IAAJA,IAAI,CAAEnC,QAAQ,CAACrG,MAAM,CAACkF,EAAE,CAAC,CAAA,IAAI,CAAA,CAACuD,KAAK,IAAA,QAALA,KAAK,CAAEpC,QAAQ,CAACrG,MAAM,CAACkF,EAAE,CAAC,CACrE,CAAC,CAAA;YAED,MAAMiE,YAAY,GAAGC,iBAAiB,CACpCV,UAAU,EACV,CAAC;mBAAGG,WAAW,EAAE;mBAAGK,aAAa,EAAE;mBAAGD,YAAY;aAAC,EACnDxE,KACF,CAAC,CAAA;YAED,OAAO0E,YAAY,CAAA;SACpB,EACDnF,cAAc,CAACS,KAAK,CAACO,OAAO,EAAEvC,KAAK,EAAE,iBAAiB,CACxD,CAAC,CAAA;QAEDgC,KAAK,CAAC4E,qBAAqB,GAAGpH,IAAI,CAChC,IAAM;gBACJwC,KAAK,CAAC2D,aAAa,EAAE;gBACrB3D,KAAK,CAAC4D,qBAAqB,EAAE;gBAC7B5D,KAAK,CAAC6D,QAAQ,EAAE,CAACC,aAAa,CAACC,IAAI;gBACnC/D,KAAK,CAAC6D,QAAQ,EAAE,CAACC,aAAa,CAACE,KAAK;aACrC,EACD,CAACC,UAAU,EAAEvB,WAAW,EAAEqB,IAAI,EAAEC,KAAK,KAAK;YACxCtB,WAAW,GAAGA,WAAW,CAAC4B,MAAM,EAC9B/I,MAAM,GAAI,CAAA,CAACwI,IAAI,IAAA,IAAA,IAAJA,IAAI,CAAEnC,QAAQ,CAACrG,MAAM,CAACkF,EAAE,CAAC,CAAA,IAAI,CAAA,CAACuD,KAAK,IAAA,QAALA,KAAK,CAAEpC,QAAQ,CAACrG,MAAM,CAACkF,EAAE,CAAC,CACrE,CAAC,CAAA;YACD,OAAOkE,iBAAiB,CAACV,UAAU,EAAEvB,WAAW,EAAE1C,KAAK,EAAE,QAAQ,CAAC,CAAA;SACnE,EACDT,cAAc,CAACS,KAAK,CAACO,OAAO,EAAEvC,KAAK,EAAE,uBAAuB,CAC9D,CAAC,CAAA;QAEDgC,KAAK,CAAC6E,mBAAmB,GAAGrH,IAAI,CAC9B,IAAM;gBACJwC,KAAK,CAAC2D,aAAa,EAAE;gBACrB3D,KAAK,CAAC4D,qBAAqB,EAAE;gBAC7B5D,KAAK,CAAC6D,QAAQ,EAAE,CAACC,aAAa,CAACC,IAAI;aACpC,EACD,CAACE,UAAU,EAAEvB,WAAW,EAAEqB,IAAI,KAAK;YAAA,IAAAe,iBAAA,CAAA;YACjC,MAAMC,kBAAkB,GAAA,CAAAD,iBAAA,GACtBf,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CACAR,GAAG,EAACrD,QAAQ,GAAIwC,WAAW,CAAC2B,IAAI,EAAC/H,CAAC,GAAIA,CAAC,CAACmE,EAAE,KAAKP,QAAQ,CAAE,CAAC,CAC3DoE,MAAM,CAACC,OAAO,CAAC,KAAAO,IAAAA,GAAAA,iBAAA,GAAI,EAAE,CAAA;YAE1B,OAAOH,iBAAiB,CAACV,UAAU,EAAEc,kBAAkB,EAAE/E,KAAK,EAAE,MAAM,CAAC,CAAA;SACxE,EACDT,cAAc,CAACS,KAAK,CAACO,OAAO,EAAEvC,KAAK,EAAE,qBAAqB,CAC5D,CAAC,CAAA;QAEDgC,KAAK,CAACgF,oBAAoB,GAAGxH,IAAI,CAC/B,IAAM;gBACJwC,KAAK,CAAC2D,aAAa,EAAE;gBACrB3D,KAAK,CAAC4D,qBAAqB,EAAE;gBAC7B5D,KAAK,CAAC6D,QAAQ,EAAE,CAACC,aAAa,CAACE,KAAK;aACrC,EACD,CAACC,UAAU,EAAEvB,WAAW,EAAEsB,KAAK,KAAK;YAAA,IAAAiB,kBAAA,CAAA;YAClC,MAAMF,kBAAkB,GAAA,CAAAE,kBAAA,GACtBjB,KAAK,IAALA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,KAAK,CACDT,GAAG,EAACrD,QAAQ,GAAIwC,WAAW,CAAC2B,IAAI,EAAC/H,CAAC,GAAIA,CAAC,CAACmE,EAAE,KAAKP,QAAQ,CAAE,CAAC,CAC3DoE,MAAM,CAACC,OAAO,CAAC,KAAAU,IAAAA,GAAAA,kBAAA,GAAI,EAAE,CAAA;YAE1B,OAAON,iBAAiB,CAACV,UAAU,EAAEc,kBAAkB,EAAE/E,KAAK,EAAE,OAAO,CAAC,CAAA;SACzE,EACDT,cAAc,CAACS,KAAK,CAACO,OAAO,EAAEvC,KAAK,EAAE,sBAAsB,CAC7D,CAAC,CAAA;QAED,gBAAA;QAEAgC,KAAK,CAACkF,eAAe,GAAG1H,IAAI,CAC1B,IAAM;gBAACwC,KAAK,CAAC0D,eAAe,EAAE;aAAC,GAC/BgB,YAAY,IAAI;YACd,OAAO,CAAC;mBAAGA,YAAY;aAAC,CAACS,OAAO,EAAE,CAAA;SACnC,EACD5F,cAAc,CAACS,KAAK,CAACO,OAAO,EAAEvC,KAAK,EAAE,iBAAiB,CACxD,CAAC,CAAA;QAEDgC,KAAK,CAACoF,mBAAmB,GAAG5H,IAAI,CAC9B,IAAM;gBAACwC,KAAK,CAAC6E,mBAAmB,EAAE;aAAC,GACnCH,YAAY,IAAI;YACd,OAAO,CAAC;mBAAGA,YAAY;aAAC,CAACS,OAAO,EAAE,CAAA;SACnC,EACD5F,cAAc,CAACS,KAAK,CAACO,OAAO,EAAEvC,KAAK,EAAE,qBAAqB,CAC5D,CAAC,CAAA;QAEDgC,KAAK,CAACqF,qBAAqB,GAAG7H,IAAI,CAChC,IAAM;gBAACwC,KAAK,CAAC4E,qBAAqB,EAAE;aAAC,GACrCF,YAAY,IAAI;YACd,OAAO,CAAC;mBAAGA,YAAY;aAAC,CAACS,OAAO,EAAE,CAAA;SACnC,EACD5F,cAAc,CAACS,KAAK,CAACO,OAAO,EAAEvC,KAAK,EAAE,uBAAuB,CAC9D,CAAC,CAAA;QAEDgC,KAAK,CAACsF,oBAAoB,GAAG9H,IAAI,CAC/B,IAAM;gBAACwC,KAAK,CAACgF,oBAAoB,EAAE;aAAC,GACpCN,YAAY,IAAI;YACd,OAAO,CAAC;mBAAGA,YAAY;aAAC,CAACS,OAAO,EAAE,CAAA;SACnC,EACD5F,cAAc,CAACS,KAAK,CAACO,OAAO,EAAEvC,KAAK,EAAE,sBAAsB,CAC7D,CAAC,CAAA;QAED,eAAA;QAEAgC,KAAK,CAACuF,cAAc,GAAG/H,IAAI,CACzB,IAAM;gBAACwC,KAAK,CAAC0D,eAAe,EAAE;aAAC,GAC/BgB,YAAY,IAAI;YACd,OAAOA,YAAY,CAChBnB,GAAG,EAACL,WAAW,IAAI;gBAClB,OAAOA,WAAW,CAACsC,OAAO,CAAA;YAC5B,CAAC,CAAC,CACDxI,IAAI,EAAE,CAAA;SACV,EACDuC,cAAc,CAACS,KAAK,CAACO,OAAO,EAAEvC,KAAK,EAAE,gBAAgB,CACvD,CAAC,CAAA;QAEDgC,KAAK,CAACyF,kBAAkB,GAAGjI,IAAI,CAC7B,IAAM;gBAACwC,KAAK,CAAC6E,mBAAmB,EAAE;aAAC,GACnCd,IAAI,IAAI;YACN,OAAOA,IAAI,CACRR,GAAG,EAACL,WAAW,IAAI;gBAClB,OAAOA,WAAW,CAACsC,OAAO,CAAA;YAC5B,CAAC,CAAC,CACDxI,IAAI,EAAE,CAAA;SACV,EACDuC,cAAc,CAACS,KAAK,CAACO,OAAO,EAAEvC,KAAK,EAAE,oBAAoB,CAC3D,CAAC,CAAA;QAEDgC,KAAK,CAAC0F,oBAAoB,GAAGlI,IAAI,CAC/B,IAAM;gBAACwC,KAAK,CAAC4E,qBAAqB,EAAE;aAAC,GACrCb,IAAI,IAAI;YACN,OAAOA,IAAI,CACRR,GAAG,EAACL,WAAW,IAAI;gBAClB,OAAOA,WAAW,CAACsC,OAAO,CAAA;YAC5B,CAAC,CAAC,CACDxI,IAAI,EAAE,CAAA;SACV,EACDuC,cAAc,CAACS,KAAK,CAACO,OAAO,EAAEvC,KAAK,EAAE,sBAAsB,CAC7D,CAAC,CAAA;QAEDgC,KAAK,CAAC2F,mBAAmB,GAAGnI,IAAI,CAC9B,IAAM;gBAACwC,KAAK,CAACgF,oBAAoB,EAAE;aAAC,GACpCjB,IAAI,IAAI;YACN,OAAOA,IAAI,CACRR,GAAG,EAACL,WAAW,IAAI;gBAClB,OAAOA,WAAW,CAACsC,OAAO,CAAA;YAC5B,CAAC,CAAC,CACDxI,IAAI,EAAE,CAAA;SACV,EACDuC,cAAc,CAACS,KAAK,CAACO,OAAO,EAAEvC,KAAK,EAAE,qBAAqB,CAC5D,CAAC,CAAA;QAED,eAAA;QAEAgC,KAAK,CAAC4F,oBAAoB,GAAGpI,IAAI,CAC/B,IAAM;gBAACwC,KAAK,CAAC0F,oBAAoB,EAAE;aAAC,GACpCG,WAAW,IAAI;YACb,OAAOA,WAAW,CAACvB,MAAM,EAAC3C,MAAM,IAAA;gBAAA,IAAAmE,kBAAA,CAAA;gBAAA,OAAI,CAAA,CAAA,CAAAA,kBAAA,GAACnE,MAAM,CAACoB,UAAU,KAAA,IAAA,IAAjB+C,kBAAA,CAAmBvI,MAAM,CAAA,CAAA;aAAC,CAAA,CAAA;SAChE,EACDgC,cAAc,CAACS,KAAK,CAACO,OAAO,EAAEvC,KAAK,EAAE,sBAAsB,CAC7D,CAAC,CAAA;QAEDgC,KAAK,CAAC+F,kBAAkB,GAAGvI,IAAI,CAC7B,IAAM;gBAACwC,KAAK,CAACyF,kBAAkB,EAAE;aAAC,GAClCI,WAAW,IAAI;YACb,OAAOA,WAAW,CAACvB,MAAM,EAAC3C,MAAM,IAAA;gBAAA,IAAAqE,mBAAA,CAAA;gBAAA,OAAI,CAAA,CAAA,CAAAA,mBAAA,GAACrE,MAAM,CAACoB,UAAU,KAAA,IAAA,IAAjBiD,mBAAA,CAAmBzI,MAAM,CAAA,CAAA;aAAC,CAAA,CAAA;SAChE,EACDgC,cAAc,CAACS,KAAK,CAACO,OAAO,EAAEvC,KAAK,EAAE,oBAAoB,CAC3D,CAAC,CAAA;QAEDgC,KAAK,CAACiG,mBAAmB,GAAGzI,IAAI,CAC9B,IAAM;gBAACwC,KAAK,CAAC2F,mBAAmB,EAAE;aAAC,GACnCE,WAAW,IAAI;YACb,OAAOA,WAAW,CAACvB,MAAM,EAAC3C,MAAM,IAAA;gBAAA,IAAAuE,mBAAA,CAAA;gBAAA,OAAI,CAAA,CAAA,CAAAA,mBAAA,GAACvE,MAAM,CAACoB,UAAU,KAAA,IAAA,IAAjBmD,mBAAA,CAAmB3I,MAAM,CAAA,CAAA;aAAC,CAAA,CAAA;SAChE,EACDgC,cAAc,CAACS,KAAK,CAACO,OAAO,EAAEvC,KAAK,EAAE,qBAAqB,CAC5D,CAAC,CAAA;QAEDgC,KAAK,CAACmD,cAAc,GAAG3F,IAAI,CACzB,IAAM;gBACJwC,KAAK,CAAC6E,mBAAmB,EAAE;gBAC3B7E,KAAK,CAAC4E,qBAAqB,EAAE;gBAC7B5E,KAAK,CAACgF,oBAAoB,EAAE;aAC7B,EACD,CAACjB,IAAI,EAAEoC,MAAM,EAAEnC,KAAK,KAAK;YAAA,IAAAoC,eAAA,EAAAC,MAAA,EAAAC,iBAAA,EAAAC,QAAA,EAAAC,gBAAA,EAAAC,OAAA,CAAA;YACvB,OAAO,CACL;mBAAA,CAAAL,eAAA,GAAA,CAAAC,MAAA,GAAItC,IAAI,CAAC,CAAC,CAAC,KAAPsC,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,MAAA,CAASb,OAAO,KAAAY,IAAAA,GAAAA,eAAA,GAAI,EAAE,GAC1B;mBAAAE,CAAAA,iBAAA,GAAAC,CAAAA,QAAA,GAAIJ,MAAM,CAAC,CAAC,CAAC,KAATI,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,QAAA,CAAWf,OAAO,KAAAc,IAAAA,GAAAA,iBAAA,GAAI,EAAE,GAC5B;mBAAAE,CAAAA,gBAAA,GAAAC,CAAAA,OAAA,GAAIzC,KAAK,CAAC,CAAC,CAAC,KAAA,IAAA,GAAA,KAAA,CAAA,GAARyC,OAAA,CAAUjB,OAAO,KAAA,IAAA,GAAAgB,gBAAA,GAAI,EAAE;aAC5B,CACEjD,GAAG,EAAC5B,MAAM,IAAI;gBACb,OAAOA,MAAM,CAACwB,cAAc,EAAE,CAAA;YAChC,CAAC,CAAC,CACDnG,IAAI,EAAE,CAAA;SACV,EACDuC,cAAc,CAACS,KAAK,CAACO,OAAO,EAAEvC,KAAK,EAAE,gBAAgB,CACvD,CAAC,CAAA;IACH,CAAA;AACF,EAAC;AAEM,SAAS2G,iBAAiBA,CAC/BV,UAAoC,EACpCyC,cAAwC,EACxC1G,KAAmB,EACnB2G,YAA0C,EAC1C;IAAA,IAAAC,qBAAA,EAAAC,cAAA,CAAA;IACA,qCAAA;IACA,4BAAA;IACA,iCAAA;IACA,wCAAA;IACA,oCAAA;IAEA,IAAIC,QAAQ,GAAG,CAAC,CAAA;IAEhB,MAAMC,YAAY,GAAG,SAAC7E,OAAiC,EAAElB,KAAK,EAAS;QAAA,IAAdA,KAAK,KAAA,KAAA,CAAA,EAAA;YAALA,KAAK,GAAG,CAAC,CAAA;QAAA,CAAA;QAChE8F,QAAQ,GAAGnI,IAAI,CAACU,GAAG,CAACyH,QAAQ,EAAE9F,KAAK,CAAC,CAAA;QAEpCkB,OAAO,CACJoC,MAAM,EAAC/I,MAAM,GAAIA,MAAM,CAACyL,YAAY,EAAE,CAAC,CACvC7J,OAAO,EAAC5B,MAAM,IAAI;YAAA,IAAA6G,eAAA,CAAA;YACjB,IAAAA,CAAAA,eAAA,GAAI7G,MAAM,CAAC2G,OAAO,KAAdE,IAAAA,IAAAA,eAAA,CAAgB7E,MAAM,EAAE;gBAC1BwJ,YAAY,CAACxL,MAAM,CAAC2G,OAAO,EAAElB,KAAK,GAAG,CAAC,CAAC,CAAA;YACzC,CAAA;SACD,EAAE,CAAC,CAAC,CAAA;KACR,CAAA;IAED+F,YAAY,CAAC9C,UAAU,CAAC,CAAA;IAExB,IAAIS,YAAkC,GAAG,EAAE,CAAA;IAE3C,MAAMuC,iBAAiB,GAAGA,CACxBC,cAAwC,EACxClG,KAAa,KACV;QACH,mCAAA;QACA,MAAMkC,WAA+B,GAAG;YACtClC,KAAK;YACLP,EAAE,EAAE;gBAACkG,YAAY;gBAAE,CAAA,EAAG3F,KAAK,CAAE,CAAA;aAAC,CAACsD,MAAM,CAACC,OAAO,CAAC,CAAC4C,IAAI,CAAC,GAAG,CAAC;YACxD3B,OAAO,EAAE,EAAA;SACV,CAAA;QAED,8CAAA;QACA,MAAM4B,oBAA8C,GAAG,EAAE,CAAA;QAEzD,+BAAA;QACAF,cAAc,CAAC/J,OAAO,EAACkK,aAAa,IAAI;YACtC,2CAAA;YAEA,MAAMC,yBAAyB,GAAG,CAAC;mBAAGF,oBAAoB;aAAC,CAACjC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAA;YAExE,MAAMoC,YAAY,GAAGF,aAAa,CAAC9L,MAAM,CAACyF,KAAK,KAAKkC,WAAW,CAAClC,KAAK,CAAA;YAErE,IAAIzF,MAA8B,CAAA;YAClC,IAAIsH,aAAa,GAAG,KAAK,CAAA;YAEzB,IAAI0E,YAAY,IAAIF,aAAa,CAAC9L,MAAM,CAAC0F,MAAM,EAAE;gBAC/C,2BAAA;gBACA1F,MAAM,GAAG8L,aAAa,CAAC9L,MAAM,CAAC0F,MAAM,CAAA;YACtC,CAAC,MAAM;gBACL,gCAAA;gBACA1F,MAAM,GAAG8L,aAAa,CAAC9L,MAAM,CAAA;gBAC7BsH,aAAa,GAAG,IAAI,CAAA;YACtB,CAAA;YAEA,IACEyE,yBAAyB,IACzB,CAAAA,yBAAyB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAzBA,yBAAyB,CAAE/L,MAAM,MAAKA,MAAM,EAC5C;gBACA,oEAAA;gBACA+L,yBAAyB,CAACvE,UAAU,CAAC1F,IAAI,CAACgK,aAAa,CAAC,CAAA;YAC1D,CAAC,MAAM;gBACL,wCAAA;gBACA,MAAM1F,MAAM,GAAGgB,YAAY,CAAC3C,KAAK,EAAEzE,MAAM,EAAE;oBACzCkF,EAAE,EAAE;wBAACkG,YAAY;wBAAE3F,KAAK;wBAAEzF,MAAM,CAACkF,EAAE;wBAAE4G,aAAa,IAAA,IAAA,GAAA,KAAA,CAAA,GAAbA,aAAa,CAAE5G,EAAE;qBAAC,CACpD6D,MAAM,CAACC,OAAO,CAAC,CACf4C,IAAI,CAAC,GAAG,CAAC;oBACZtE,aAAa;oBACbC,aAAa,EAAED,aAAa,GACxB,CAAA,EAAGuE,oBAAoB,CAAC9C,MAAM,EAAChI,CAAC,GAAIA,CAAC,CAACf,MAAM,KAAKA,MAAM,CAAC,CAACgC,MAAM,CAAA,CAAE,GACjEmE,SAAS;oBACbV,KAAK;oBACLzC,KAAK,EAAE6I,oBAAoB,CAAC7J,MAAAA;gBAC9B,CAAC,CAAC,CAAA;gBAEF,yDAAA;gBACAoE,MAAM,CAACoB,UAAU,CAAC1F,IAAI,CAACgK,aAAa,CAAC,CAAA;gBACrC,gEAAA;gBACA,oBAAA;gBACAD,oBAAoB,CAAC/J,IAAI,CAACsE,MAAM,CAAC,CAAA;YACnC,CAAA;YAEAuB,WAAW,CAACsC,OAAO,CAACnI,IAAI,CAACgK,aAAa,CAAC,CAAA;YACvCA,aAAa,CAACnE,WAAW,GAAGA,WAAW,CAAA;QACzC,CAAC,CAAC,CAAA;QAEFwB,YAAY,CAACrH,IAAI,CAAC6F,WAAW,CAAC,CAAA;QAE9B,IAAIlC,KAAK,GAAG,CAAC,EAAE;YACbiG,iBAAiB,CAACG,oBAAoB,EAAEpG,KAAK,GAAG,CAAC,CAAC,CAAA;QACpD,CAAA;KACD,CAAA;IAED,MAAMwG,aAAa,GAAGd,cAAc,CAACnD,GAAG,CAAC,CAAChI,MAAM,EAAEgD,KAAK,GACrDoE,YAAY,CAAC3C,KAAK,EAAEzE,MAAM,EAAE;YAC1ByF,KAAK,EAAE8F,QAAQ;YACfvI,KAAAA;QACF,CAAC,CACH,CAAC,CAAA;IAED0I,iBAAiB,CAACO,aAAa,EAAEV,QAAQ,GAAG,CAAC,CAAC,CAAA;IAE9CpC,YAAY,CAACS,OAAO,EAAE,CAAA;IAEtB,sDAAA;IACA,sEAAA;IACA,KAAA;IAEA,MAAMsC,sBAAsB,IAC1BjC,OAAiC,IACU;QAC3C,MAAMkC,eAAe,GAAGlC,OAAO,CAAClB,MAAM,EAAC3C,MAAM,GAC3CA,MAAM,CAACpG,MAAM,CAACyL,YAAY,EAC5B,CAAC,CAAA;QAED,OAAOU,eAAe,CAACnE,GAAG,EAAC5B,MAAM,IAAI;YACnC,IAAIqB,OAAO,GAAG,CAAC,CAAA;YACf,IAAIC,OAAO,GAAG,CAAC,CAAA;YACf,IAAI0E,aAAa,GAAG;gBAAC,CAAC;aAAC,CAAA;YAEvB,IAAIhG,MAAM,CAACoB,UAAU,IAAIpB,MAAM,CAACoB,UAAU,CAACxF,MAAM,EAAE;gBACjDoK,aAAa,GAAG,EAAE,CAAA;gBAElBF,sBAAsB,CAAC9F,MAAM,CAACoB,UAAU,CAAC,CAAC5F,OAAO,EAC/C+D,IAAA,IAAsD;oBAAA,IAArD,EAAE8B,OAAO,EAAE4E,YAAY,EAAE3E,OAAO,EAAE4E,YAAAA,EAAc,GAAA3G,IAAA,CAAA;oBAC/C8B,OAAO,IAAI4E,YAAY,CAAA;oBACvBD,aAAa,CAACtK,IAAI,CAACwK,YAAY,CAAC,CAAA;gBAClC,CACF,CAAC,CAAA;YACH,CAAC,MAAM;gBACL7E,OAAO,GAAG,CAAC,CAAA;YACb,CAAA;YAEA,MAAM8E,eAAe,GAAGnJ,IAAI,CAACW,GAAG,CAAC,GAAGqI,aAAa,CAAC,CAAA;YAClD1E,OAAO,GAAGA,OAAO,GAAG6E,eAAe,CAAA;YAEnCnG,MAAM,CAACqB,OAAO,GAAGA,OAAO,CAAA;YACxBrB,MAAM,CAACsB,OAAO,GAAGA,OAAO,CAAA;YAExB,OAAO;gBAAED,OAAO;gBAAEC,OAAAA;aAAS,CAAA;QAC7B,CAAC,CAAC,CAAA;KACH,CAAA;IAEDwE,sBAAsB,CAAA,CAAAb,qBAAA,GAAA,CAAAC,cAAA,GAACnC,YAAY,CAAC,CAAC,CAAC,KAAA,IAAA,GAAA,KAAA,CAAA,GAAfmC,cAAA,CAAiBrB,OAAO,KAAA,OAAAoB,qBAAA,GAAI,EAAE,CAAC,CAAA;IAEtD,OAAOlC,YAAY,CAAA;AACrB;MChiBaqD,SAAS,GAAGA,CACvB/H,KAAmB,EACnBS,EAAU,EACVuH,QAAe,EACfC,QAAgB,EAChBjH,KAAa,EACbkH,OAAsB,EACtBC,QAAiB,KACF;IACf,IAAIlI,GAAmB,GAAG;QACxBQ,EAAE;QACFlC,KAAK,EAAE0J,QAAQ;QACfD,QAAQ;QACRhH,KAAK;QACLmH,QAAQ;QACRC,YAAY,EAAE,CAAA,CAAE;QAChBC,kBAAkB,EAAE,CAAA,CAAE;QACtB/H,QAAQ,GAAEJ,QAAQ,IAAI;YACpB,IAAID,GAAG,CAACmI,YAAY,CAACE,cAAc,CAACpI,QAAQ,CAAC,EAAE;gBAC7C,OAAOD,GAAG,CAACmI,YAAY,CAAClI,QAAQ,CAAC,CAAA;YACnC,CAAA;YAEA,MAAM3E,MAAM,GAAGyE,KAAK,CAACuI,SAAS,CAACrI,QAAQ,CAAC,CAAA;YAExC,IAAI,CAAA,CAAC3E,MAAM,IAAA,IAAA,IAANA,MAAM,CAAEC,UAAU,CAAE,EAAA;gBACvB,OAAOkG,SAAS,CAAA;YAClB,CAAA;YAEAzB,GAAG,CAACmI,YAAY,CAAClI,QAAQ,CAAC,GAAG3E,MAAM,CAACC,UAAU,CAC5CyE,GAAG,CAAC+H,QAAQ,EACZC,QACF,CAAC,CAAA;YAED,OAAOhI,GAAG,CAACmI,YAAY,CAAClI,QAAQ,CAAC,CAAA;SAClC;QACDsI,eAAe,GAAEtI,QAAQ,IAAI;YAC3B,IAAID,GAAG,CAACoI,kBAAkB,CAACC,cAAc,CAACpI,QAAQ,CAAC,EAAE;gBACnD,OAAOD,GAAG,CAACoI,kBAAkB,CAACnI,QAAQ,CAAC,CAAA;YACzC,CAAA;YAEA,MAAM3E,MAAM,GAAGyE,KAAK,CAACuI,SAAS,CAACrI,QAAQ,CAAC,CAAA;YAExC,IAAI,CAAA,CAAC3E,MAAM,IAAA,IAAA,IAANA,MAAM,CAAEC,UAAU,CAAE,EAAA;gBACvB,OAAOkG,SAAS,CAAA;YAClB,CAAA;YAEA,IAAI,CAACnG,MAAM,CAACwF,SAAS,CAACyH,eAAe,EAAE;gBACrCvI,GAAG,CAACoI,kBAAkB,CAACnI,QAAQ,CAAC,GAAG;oBAACD,GAAG,CAACK,QAAQ,CAACJ,QAAQ,CAAC;iBAAC,CAAA;gBAC3D,OAAOD,GAAG,CAACoI,kBAAkB,CAACnI,QAAQ,CAAC,CAAA;YACzC,CAAA;YAEAD,GAAG,CAACoI,kBAAkB,CAACnI,QAAQ,CAAC,GAAG3E,MAAM,CAACwF,SAAS,CAACyH,eAAe,CACjEvI,GAAG,CAAC+H,QAAQ,EACZC,QACF,CAAC,CAAA;YAED,OAAOhI,GAAG,CAACoI,kBAAkB,CAACnI,QAAQ,CAAC,CAAA;SACxC;QACDQ,WAAW,GAAER,QAAQ,IAAA;YAAA,IAAAuI,aAAA,CAAA;YAAA,OAAA,CAAAA,aAAA,GACnBxI,GAAG,CAACK,QAAQ,CAACJ,QAAQ,CAAC,KAAA,IAAA,GAAAuI,aAAA,GAAIzI,KAAK,CAACO,OAAO,CAACC,mBAAmB,CAAA;QAAA,CAAA;QAC7D0H,OAAO,EAAEA,OAAO,IAAPA,IAAAA,GAAAA,OAAO,GAAI,EAAE;QACtBQ,WAAW,EAAEA,IAAM7L,SAAS,CAACoD,GAAG,CAACiI,OAAO,GAAE5L,CAAC,GAAIA,CAAC,CAAC4L,OAAO,CAAC;QACzDS,YAAY,EAAEA,IACZ1I,GAAG,CAACkI,QAAQ,GAAGnI,KAAK,CAAC4I,MAAM,CAAC3I,GAAG,CAACkI,QAAQ,EAAE,IAAI,CAAC,GAAGzG,SAAS;QAC7DmH,aAAa,EAAEA,MAAM;YACnB,IAAIC,UAAwB,GAAG,EAAE,CAAA;YACjC,IAAIC,UAAU,GAAG9I,GAAG,CAAA;YACpB,MAAO,IAAI,CAAE;gBACX,MAAM+I,SAAS,GAAGD,UAAU,CAACJ,YAAY,EAAE,CAAA;gBAC3C,IAAI,CAACK,SAAS,EAAE,MAAA;gBAChBF,UAAU,CAACzL,IAAI,CAAC2L,SAAS,CAAC,CAAA;gBAC1BD,UAAU,GAAGC,SAAS,CAAA;YACxB,CAAA;YACA,OAAOF,UAAU,CAAC3D,OAAO,EAAE,CAAA;SAC5B;QACD8D,WAAW,EAAEzL,IAAI,CACf,IAAM;gBAACwC,KAAK,CAACkJ,iBAAiB,EAAE;aAAC,GACjCxG,WAAW,IAAI;YACb,OAAOA,WAAW,CAACa,GAAG,EAAChI,MAAM,IAAI;gBAC/B,OAAOwE,UAAU,CAACC,KAAK,EAAEC,GAAG,EAAgB1E,MAAM,EAAEA,MAAM,CAACkF,EAAE,CAAC,CAAA;YAChE,CAAC,CAAC,CAAA;SACH,EACDlB,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,WAAW,EAAE,aAAa,CAC1D,CAAC;QAED4I,sBAAsB,EAAE3L,IAAI,CAC1B,IAAM;gBAACyC,GAAG,CAACgJ,WAAW,EAAE;aAAC,GACzBG,QAAQ,IAAI;YACV,OAAOA,QAAQ,CAACC,MAAM,CACpB,CAACC,GAAG,EAAEjJ,IAAI,KAAK;gBACbiJ,GAAG,CAACjJ,IAAI,CAAC9E,MAAM,CAACkF,EAAE,CAAC,GAAGJ,IAAI,CAAA;gBAC1B,OAAOiJ,GAAG,CAAA;aACX,EACD,CAAA,CACF,CAAC,CAAA;SACF,EACD/J,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,WAAW,EAAE,uBAAuB,CACpE,CAAA;KACD,CAAA;IAED,IAAK,IAAIgJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvJ,KAAK,CAACY,SAAS,CAACrD,MAAM,EAAEgM,CAAC,EAAE,CAAE;QAC/C,MAAM1I,OAAO,GAAGb,KAAK,CAACY,SAAS,CAAC2I,CAAC,CAAC,CAAA;QAClC1I,OAAO,IAAPA,IAAAA,IAAAA,OAAO,CAAEkH,SAAS,IAAlBlH,IAAAA,IAAAA,OAAO,CAAEkH,SAAS,CAAG9H,GAAG,EAAgBD,KAAK,CAAC,CAAA;IAChD,CAAA;IAEA,OAAOC,GAAG,CAAA;AACZ;AC3JA,EAAA;AAEO,MAAMuJ,cAA4B,GAAG;IAC1C1I,YAAY,EAAEA,CACZvF,MAA8B,EAC9ByE,KAAmB,KACV;QACTzE,MAAM,CAACkO,mBAAmB,GACxBzJ,KAAK,CAACO,OAAO,CAACmJ,kBAAkB,IAChC1J,KAAK,CAACO,OAAO,CAACmJ,kBAAkB,CAAC1J,KAAK,EAAEzE,MAAM,CAACkF,EAAE,CAAC,CAAA;QACpDlF,MAAM,CAACmO,kBAAkB,GAAG,MAAM;YAChC,IAAI,CAACnO,MAAM,CAACkO,mBAAmB,EAAE;gBAC/B,OAAOzJ,KAAK,CAAC2J,sBAAsB,EAAE,CAAA;YACvC,CAAA;YAEA,OAAOpO,MAAM,CAACkO,mBAAmB,EAAE,CAAA;SACpC,CAAA;QACDlO,MAAM,CAACqO,uBAAuB,GAC5B5J,KAAK,CAACO,OAAO,CAACsJ,sBAAsB,IACpC7J,KAAK,CAACO,OAAO,CAACsJ,sBAAsB,CAAC7J,KAAK,EAAEzE,MAAM,CAACkF,EAAE,CAAC,CAAA;QACxDlF,MAAM,CAACsO,sBAAsB,GAAG,MAAM;YACpC,IAAI,CAACtO,MAAM,CAACqO,uBAAuB,EAAE;gBACnC,OAAO,IAAIE,GAAG,EAAE,CAAA;YAClB,CAAA;YAEA,OAAOvO,MAAM,CAACqO,uBAAuB,EAAE,CAAA;SACxC,CAAA;QACDrO,MAAM,CAACwO,uBAAuB,GAC5B/J,KAAK,CAACO,OAAO,CAACyJ,sBAAsB,IACpChK,KAAK,CAACO,OAAO,CAACyJ,sBAAsB,CAAChK,KAAK,EAAEzE,MAAM,CAACkF,EAAE,CAAC,CAAA;QACxDlF,MAAM,CAACyO,sBAAsB,GAAG,MAAM;YACpC,IAAI,CAACzO,MAAM,CAACwO,uBAAuB,EAAE;gBACnC,OAAOrI,SAAS,CAAA;YAClB,CAAA;YAEA,OAAOnG,MAAM,CAACwO,uBAAuB,EAAE,CAAA;SACxC,CAAA;IACH,CAAA;AACF;ACjFA,MAAME,cAA6B,GAAGA,CACpChK,GAAG,EACHC,QAAgB,EAChBgK,WAAmB,KAChB;IAAA,IAAAC,qBAAA,EAAA1B,aAAA,CAAA;IACH,MAAM2B,MAAM,GAAGF,WAAW,IAAAC,IAAAA,IAAAA,CAAAA,qBAAA,GAAXD,WAAW,CAAEG,QAAQ,EAAE,KAAvBF,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,qBAAA,CAAyBG,WAAW,EAAE,CAAA;IACrD,OAAO/F,OAAO,CAAA,CAAAkE,aAAA,GACZxI,GAAG,CACAK,QAAQ,CAAgBJ,QAAQ,CAAC,KAAA,IAAA,IAAA,CAAAuI,aAAA,GADpCA,aAAA,CAEI4B,QAAQ,EAAE,KAAA5B,IAAAA,IAAAA,CAAAA,aAAA,GAFdA,aAAA,CAGI6B,WAAW,EAAE,KAAA,IAAA,GAAA,KAAA,CAAA,GAHjB7B,aAAA,CAII7G,QAAQ,CAACwI,MAAM,CACrB,CAAC,CAAA;AACH,CAAC,CAAA;AAEDH,cAAc,CAACM,UAAU,IAAI3N,GAAQ,GAAK4N,UAAU,CAAC5N,GAAG,CAAC,CAAA;AAEzD,MAAM6N,uBAAsC,GAAGA,CAC7CxK,GAAG,EACHC,QAAgB,EAChBgK,WAAmB,KAChB;IAAA,IAAAQ,cAAA,CAAA;IACH,OAAOnG,OAAO,CAAAmG,CAAAA,cAAA,GACZzK,GAAG,CAACK,QAAQ,CAAgBJ,QAAQ,CAAC,KAAAwK,IAAAA,IAAAA,CAAAA,cAAA,GAArCA,cAAA,CAAuCL,QAAQ,EAAE,KAAA,IAAA,GAAA,KAAA,CAAA,GAAjDK,cAAA,CAAmD9I,QAAQ,CAACsI,WAAW,CACzE,CAAC,CAAA;AACH,CAAC,CAAA;AAEDO,uBAAuB,CAACF,UAAU,IAAI3N,GAAQ,GAAK4N,UAAU,CAAC5N,GAAG,CAAC,CAAA;AAElE,MAAM+N,YAA2B,GAAGA,CAClC1K,GAAG,EACHC,QAAgB,EAChBgK,WAAmB,KAChB;IAAA,IAAAU,cAAA,CAAA;IACH,OACE,CAAAA,CAAAA,cAAA,GAAA3K,GAAG,CAACK,QAAQ,CAAgBJ,QAAQ,CAAC,KAAA,IAAA,IAAA,CAAA0K,cAAA,GAArCA,cAAA,CAAuCP,QAAQ,EAAE,KAAjDO,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,cAAA,CAAmDN,WAAW,EAAE,MAAA,CAChEJ,WAAW,IAAXA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,WAAW,CAAEI,WAAW,EAAE,CAAA,CAAA;AAE9B,CAAC,CAAA;AAEDK,YAAY,CAACJ,UAAU,IAAI3N,GAAQ,GAAK4N,UAAU,CAAC5N,GAAG,CAAC,CAAA;AAEvD,MAAMiO,WAA0B,GAAGA,CACjC5K,GAAG,EACHC,QAAgB,EAChBgK,WAAoB,KACjB;IAAA,IAAAY,cAAA,CAAA;IACH,OAAA,CAAAA,cAAA,GAAO7K,GAAG,CAACK,QAAQ,CAAYJ,QAAQ,CAAC,KAAA,IAAA,GAAA,KAAA,CAAA,GAAjC4K,cAAA,CAAmClJ,QAAQ,CAACsI,WAAW,CAAC,CAAA;AACjE,CAAC,CAAA;AAEDW,WAAW,CAACN,UAAU,IAAI3N,GAAQ,GAAK4N,UAAU,CAAC5N,GAAG,CAAC,CAAA;AAEtD,MAAMmO,cAA6B,GAAGA,CACpC9K,GAAG,EACHC,QAAgB,EAChBgK,WAAsB,KACnB;IACH,OAAO,CAACA,WAAW,CAAC7L,IAAI,EACtBzB,GAAG,IAAA;QAAA,IAAAoO,cAAA,CAAA;QAAA,OAAI,CAAA,CAAAA,CAAAA,cAAA,GAAC/K,GAAG,CAACK,QAAQ,CAAYJ,QAAQ,CAAC,KAAA,QAAjC8K,cAAA,CAAmCpJ,QAAQ,CAAChF,GAAG,CAAC,CAAA,CAAA;IAAA,CAC1D,CAAC,CAAA;AACH,CAAC,CAAA;AAEDmO,cAAc,CAACR,UAAU,IAAI3N,GAAQ,GAAK4N,UAAU,CAAC5N,GAAG,CAAC,IAAI,CAAA,CAACA,GAAG,IAAHA,IAAAA,IAAAA,GAAG,CAAEW,MAAM,CAAA,CAAA;AAEzE,MAAM0N,eAA8B,GAAGA,CACrChL,GAAG,EACHC,QAAgB,EAChBgK,WAAsB,KACnB;IACH,OAAOA,WAAW,CAAC7L,IAAI,EAACzB,GAAG,IAAA;QAAA,IAAAsO,cAAA,CAAA;QAAA,OAAA,CAAAA,cAAA,GACzBjL,GAAG,CAACK,QAAQ,CAAYJ,QAAQ,CAAC,KAAA,IAAA,GAAA,KAAA,CAAA,GAAjCgL,cAAA,CAAmCtJ,QAAQ,CAAChF,GAAG,CAAC,CAAA;IAAA,CAClD,CAAC,CAAA;AACH,CAAC,CAAA;AAEDqO,eAAe,CAACV,UAAU,IAAI3N,GAAQ,GAAK4N,UAAU,CAAC5N,GAAG,CAAC,IAAI,CAAA,CAACA,GAAG,IAAHA,IAAAA,IAAAA,GAAG,CAAEW,MAAM,CAAA,CAAA;AAE1E,MAAM4N,MAAqB,GAAGA,CAAClL,GAAG,EAAEC,QAAgB,EAAEgK,WAAoB,KAAK;IAC7E,OAAOjK,GAAG,CAACK,QAAQ,CAACJ,QAAQ,CAAC,KAAKgK,WAAW,CAAA;AAC/C,CAAC,CAAA;AAEDiB,MAAM,CAACZ,UAAU,IAAI3N,GAAQ,GAAK4N,UAAU,CAAC5N,GAAG,CAAC,CAAA;AAEjD,MAAMwO,UAAyB,GAAGA,CAChCnL,GAAG,EACHC,QAAgB,EAChBgK,WAAoB,KACjB;IACH,OAAOjK,GAAG,CAACK,QAAQ,CAACJ,QAAQ,CAAC,IAAIgK,WAAW,CAAA;AAC9C,CAAC,CAAA;AAEDkB,UAAU,CAACb,UAAU,IAAI3N,GAAQ,GAAK4N,UAAU,CAAC5N,GAAG,CAAC,CAAA;AAErD,MAAMyO,aAA4B,GAAGA,CACnCpL,GAAG,EACHC,QAAgB,EAChBgK,WAA6B,KAC1B;IACH,IAAI,CAAC5K,GAAG,EAAED,GAAG,CAAC,GAAG6K,WAAW,CAAA;IAE5B,MAAMoB,QAAQ,GAAGrL,GAAG,CAACK,QAAQ,CAASJ,QAAQ,CAAC,CAAA;IAC/C,OAAOoL,QAAQ,IAAIhM,GAAG,IAAIgM,QAAQ,IAAIjM,GAAG,CAAA;AAC3C,CAAC,CAAA;AAEDgM,aAAa,CAACE,kBAAkB,IAAI3O,GAAe,IAAK;IACtD,IAAI,CAAC4O,SAAS,EAAEC,SAAS,CAAC,GAAG7O,GAAG,CAAA;IAEhC,IAAI8O,SAAS,GACX,OAAOF,SAAS,KAAK,QAAQ,GAAGG,UAAU,CAACH,SAAmB,CAAC,GAAGA,SAAS,CAAA;IAC7E,IAAII,SAAS,GACX,OAAOH,SAAS,KAAK,QAAQ,GAAGE,UAAU,CAACF,SAAmB,CAAC,GAAGA,SAAS,CAAA;IAE7E,IAAInM,GAAG,GACLkM,SAAS,KAAK,IAAI,IAAIK,MAAM,CAACC,KAAK,CAACJ,SAAS,CAAC,GAAG,CAACK,QAAQ,GAAGL,SAAS,CAAA;IACvE,IAAIrM,GAAG,GAAGoM,SAAS,KAAK,IAAI,IAAII,MAAM,CAACC,KAAK,CAACF,SAAS,CAAC,GAAGG,QAAQ,GAAGH,SAAS,CAAA;IAE9E,IAAItM,GAAG,GAAGD,GAAG,EAAE;QACb,MAAM2M,IAAI,GAAG1M,GAAG,CAAA;QAChBA,GAAG,GAAGD,GAAG,CAAA;QACTA,GAAG,GAAG2M,IAAI,CAAA;IACZ,CAAA;IAEA,OAAO;QAAC1M,GAAG;QAAED,GAAG;KAAC,CAAA;AACnB,CAAC,CAAA;AAEDgM,aAAa,CAACd,UAAU,IAAI3N,GAAQ,GAClC4N,UAAU,CAAC5N,GAAG,CAAC,IAAK4N,UAAU,CAAC5N,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI4N,UAAU,CAAC5N,GAAG,CAAC,CAAC,CAAC,CAAE,CAAA;AAE/D,SAAA;AAEO,MAAMqP,SAAS,GAAG;IACvBhC,cAAc;IACdQ,uBAAuB;IACvBE,YAAY;IACZE,WAAW;IACXE,cAAc;IACdE,eAAe;IACfE,MAAM;IACNC,UAAU;IACVC,aAAAA;AACF,EAAC;AAID,QAAA;AAEA,SAASb,UAAUA,CAAC5N,GAAQ,EAAE;IAC5B,OAAOA,GAAG,KAAK8E,SAAS,IAAI9E,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,EAAE,CAAA;AACxD;AC2FA,EAAA;AAEO,MAAMsP,eAA6B,GAAG;IAC3CC,mBAAmB,EAAEA,MAEiB;QACpC,OAAO;YACLC,QAAQ,EAAE,MAAA;SACX,CAAA;KACF;IAEDC,eAAe,GAAGC,KAAK,IAA8B;QACnD,OAAO;YACLC,aAAa,EAAE,EAAE;YACjB,GAAGD,KAAAA;SACJ,CAAA;KACF;IAEDE,iBAAiB,GACfxM,KAAmB,IACa;QAChC,OAAO;YACLyM,qBAAqB,EAAEzQ,gBAAgB,CAAC,eAAe,EAAEgE,KAAK,CAAC;YAC/D0M,kBAAkB,EAAE,KAAK;YACzBC,qBAAqB,EAAE,GAAA;SACxB,CAAA;KACF;IAED7L,YAAY,EAAEA,CACZvF,MAA8B,EAC9ByE,KAAmB,KACV;QACTzE,MAAM,CAACqR,eAAe,GAAG,MAAM;YAC7B,MAAMC,QAAQ,GAAG7M,KAAK,CAAC8M,eAAe,EAAE,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAA;YAEpD,MAAMC,KAAK,GAAGH,QAAQ,IAARA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,QAAQ,CAAEvM,QAAQ,CAAC/E,MAAM,CAACkF,EAAE,CAAC,CAAA;YAE3C,IAAI,OAAOuM,KAAK,KAAK,QAAQ,EAAE;gBAC7B,OAAOf,SAAS,CAAChC,cAAc,CAAA;YACjC,CAAA;YAEA,IAAI,OAAO+C,KAAK,KAAK,QAAQ,EAAE;gBAC7B,OAAOf,SAAS,CAACZ,aAAa,CAAA;YAChC,CAAA;YAEA,IAAI,OAAO2B,KAAK,KAAK,SAAS,EAAE;gBAC9B,OAAOf,SAAS,CAACd,MAAM,CAAA;YACzB,CAAA;YAEA,IAAI6B,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;gBAC/C,OAAOf,SAAS,CAACd,MAAM,CAAA;YACzB,CAAA;YAEA,IAAI1O,KAAK,CAACC,OAAO,CAACsQ,KAAK,CAAC,EAAE;gBACxB,OAAOf,SAAS,CAACpB,WAAW,CAAA;YAC9B,CAAA;YAEA,OAAOoB,SAAS,CAACb,UAAU,CAAA;SAC5B,CAAA;QACD7P,MAAM,CAAC0R,WAAW,GAAG,MAAM;YAAA,IAAAC,qBAAA,EAAAC,sBAAA,CAAA;YACzB,OAAO9Q,UAAU,CAACd,MAAM,CAACwF,SAAS,CAACqL,QAAQ,CAAC,GACxC7Q,MAAM,CAACwF,SAAS,CAACqL,QAAQ,GACzB7Q,MAAM,CAACwF,SAAS,CAACqL,QAAQ,KAAK,MAAM,GAClC7Q,MAAM,CAACqR,eAAe,EAAE,GACxB,CAAAM,qBAAA,GAAA,CAAAC,sBAAA,GACAnN,KAAK,CAACO,OAAO,CAAC0L,SAAS,KAAA,IAAA,GAAA,KAAA,CAAA,GAAvBkB,sBAAA,CAA0B5R,MAAM,CAACwF,SAAS,CAACqL,QAAQ,CAAW,KAAAc,IAAAA,GAAAA,qBAAA,GAC9DjB,SAAS,CAAC1Q,MAAM,CAACwF,SAAS,CAACqL,QAAQ,CAAoB,CAAA;SAC9D,CAAA;QACD7Q,MAAM,CAAC6R,YAAY,GAAG,MAAM;YAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,CAAA;YAC1B,OACE,CAAA,CAAAF,qBAAA,GAAC9R,MAAM,CAACwF,SAAS,CAACyM,kBAAkB,KAAA,IAAA,GAAAH,qBAAA,GAAI,IAAI,KAAA,CAAA,CAAAC,qBAAA,GAC3CtN,KAAK,CAACO,OAAO,CAACkN,mBAAmB,KAAA,IAAA,GAAAH,qBAAA,GAAI,IAAI,CAAC,IAAA,CAAAC,CAAAA,sBAAA,GAC1CvN,KAAK,CAACO,OAAO,CAACmN,aAAa,KAAA,OAAAH,sBAAA,GAAI,IAAI,CAAC,IACrC,CAAC,CAAChS,MAAM,CAACC,UAAU,CAAA;SAEtB,CAAA;QAEDD,MAAM,CAACoS,aAAa,GAAG,IAAMpS,MAAM,CAACqS,cAAc,EAAE,GAAG,CAAC,CAAC,CAAA;QAEzDrS,MAAM,CAACsS,cAAc,GAAG,MAAA;YAAA,IAAAC,qBAAA,CAAA;YAAA,OAAA,CAAAA,qBAAA,GACtB9N,KAAK,CAAC6D,QAAQ,EAAE,CAAC0I,aAAa,KAAA,IAAA,IAAA,CAAAuB,qBAAA,GAA9BA,qBAAA,CAAgCzJ,IAAI,EAAC/H,CAAC,GAAIA,CAAC,CAACmE,EAAE,KAAKlF,MAAM,CAACkF,EAAE,CAAC,KAA7DqN,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,qBAAA,CAA+Dd,KAAK,CAAA;QAAA,CAAA,CAAA;QAEtEzR,MAAM,CAACqS,cAAc,GAAG,MAAA;YAAA,IAAAG,sBAAA,EAAAC,sBAAA,CAAA;YAAA,OAAA,CAAAD,sBAAA,GAAA,CAAAC,sBAAA,GACtBhO,KAAK,CAAC6D,QAAQ,EAAE,CAAC0I,aAAa,KAAA,IAAA,GAAA,KAAA,CAAA,GAA9ByB,sBAAA,CAAgCC,SAAS,EAAC3R,CAAC,GAAIA,CAAC,CAACmE,EAAE,KAAKlF,MAAM,CAACkF,EAAE,CAAC,KAAA,IAAA,GAAAsN,sBAAA,GAAI,CAAC,CAAC,CAAA;QAAA,CAAA,CAAA;QAE1ExS,MAAM,CAAC2S,cAAc,IAAGlB,KAAK,IAAI;YAC/BhN,KAAK,CAACmO,gBAAgB,EAAC/R,GAAG,IAAI;gBAC5B,MAAMgQ,QAAQ,GAAG7Q,MAAM,CAAC0R,WAAW,EAAE,CAAA;gBACrC,MAAMmB,cAAc,GAAGhS,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAEiI,IAAI,EAAC/H,CAAC,GAAIA,CAAC,CAACmE,EAAE,KAAKlF,MAAM,CAACkF,EAAE,CAAC,CAAA;gBAEzD,MAAM4N,SAAS,GAAGzS,gBAAgB,CAChCoR,KAAK,EACLoB,cAAc,GAAGA,cAAc,CAACpB,KAAK,GAAGtL,SAC1C,CAAC,CAAA;gBAED,EAAA;gBACA,IACE4M,sBAAsB,CAAClC,QAAQ,EAAqBiC,SAAS,EAAE9S,MAAM,CAAC,EACtE;oBAAA,IAAAgT,WAAA,CAAA;oBACA,OAAAA,CAAAA,WAAA,GAAOnS,GAAG,IAAA,IAAA,GAAA,KAAA,CAAA,GAAHA,GAAG,CAAEkI,MAAM,EAAChI,CAAC,GAAIA,CAAC,CAACmE,EAAE,KAAKlF,MAAM,CAACkF,EAAE,CAAC,KAAA,IAAA,GAAA8N,WAAA,GAAI,EAAE,CAAA;gBACnD,CAAA;gBAEA,MAAMC,YAAY,GAAG;oBAAE/N,EAAE,EAAElF,MAAM,CAACkF,EAAE;oBAAEuM,KAAK,EAAEqB,SAAAA;iBAAW,CAAA;gBAExD,IAAID,cAAc,EAAE;oBAAA,IAAAK,QAAA,CAAA;oBAClB,OAAAA,CAAAA,QAAA,GACErS,GAAG,IAAA,IAAA,GAAA,KAAA,CAAA,GAAHA,GAAG,CAAEmH,GAAG,EAACjH,CAAC,IAAI;wBACZ,IAAIA,CAAC,CAACmE,EAAE,KAAKlF,MAAM,CAACkF,EAAE,EAAE;4BACtB,OAAO+N,YAAY,CAAA;wBACrB,CAAA;wBACA,OAAOlS,CAAC,CAAA;oBACV,CAAC,CAAC,KAAA,IAAA,GAAAmS,QAAA,GAAI,EAAE,CAAA;gBAEZ,CAAA;gBAEA,IAAIrS,GAAG,IAAA,IAAA,IAAHA,GAAG,CAAEmB,MAAM,EAAE;oBACf,OAAO,CAAC;2BAAGnB,GAAG;wBAAEoS,YAAY;qBAAC,CAAA;gBAC/B,CAAA;gBAEA,OAAO;oBAACA,YAAY;iBAAC,CAAA;YACvB,CAAC,CAAC,CAAA;SACH,CAAA;KACF;IAEDzG,SAAS,EAAEA,CACT9H,GAAe,EACfyO,MAAoB,KACX;QACTzO,GAAG,CAACsM,aAAa,GAAG,CAAA,CAAE,CAAA;QACtBtM,GAAG,CAAC0O,iBAAiB,GAAG,CAAA,CAAE,CAAA;KAC3B;IAEDlL,WAAW,GAA0BzD,KAAmB,IAAW;QACjEA,KAAK,CAACmO,gBAAgB,IAAItS,OAAoC,IAAK;YACjE,MAAM6G,WAAW,GAAG1C,KAAK,CAACkJ,iBAAiB,EAAE,CAAA;YAE7C,MAAM0F,QAAQ,IAAIxS,GAAuB,IAAK;gBAAA,IAAAyS,iBAAA,CAAA;gBAC5C,OAAA,CAAAA,iBAAA,GAAOjT,gBAAgB,CAACC,OAAO,EAAEO,GAAG,CAAC,KAAA,IAAA,GAAA,KAAA,CAAA,GAA9ByS,iBAAA,CAAgCvK,MAAM,EAACA,MAAM,IAAI;oBACtD,MAAM/I,MAAM,GAAGmH,WAAW,CAAC2B,IAAI,EAAC/H,CAAC,GAAIA,CAAC,CAACmE,EAAE,KAAK6D,MAAM,CAAC7D,EAAE,CAAC,CAAA;oBAExD,IAAIlF,MAAM,EAAE;wBACV,MAAM6Q,QAAQ,GAAG7Q,MAAM,CAAC0R,WAAW,EAAE,CAAA;wBAErC,IAAIqB,sBAAsB,CAAClC,QAAQ,EAAE9H,MAAM,CAAC0I,KAAK,EAAEzR,MAAM,CAAC,EAAE;4BAC1D,OAAO,KAAK,CAAA;wBACd,CAAA;oBACF,CAAA;oBAEA,OAAO,IAAI,CAAA;gBACb,CAAC,CAAC,CAAA;aACH,CAAA;YAEDyE,KAAK,CAACO,OAAO,CAACkM,qBAAqB,IAAnCzM,IAAAA,IAAAA,KAAK,CAACO,OAAO,CAACkM,qBAAqB,CAAGmC,QAAQ,CAAC,CAAA;SAChD,CAAA;QAED5O,KAAK,CAAC8O,kBAAkB,IAAGC,YAAY,IAAI;YAAA,IAAAC,qBAAA,EAAAC,mBAAA,CAAA;YACzCjP,KAAK,CAACmO,gBAAgB,CACpBY,YAAY,GAAG,EAAE,GAAA,CAAAC,qBAAA,GAAA,CAAAC,mBAAA,GAAGjP,KAAK,CAACkP,YAAY,KAAA,OAAA,KAAA,IAAlBD,mBAAA,CAAoB1C,aAAa,KAAAyC,IAAAA,GAAAA,qBAAA,GAAI,EAC3D,CAAC,CAAA;SACF,CAAA;QAEDhP,KAAK,CAAC2J,sBAAsB,GAAG,IAAM3J,KAAK,CAAC8M,eAAe,EAAE,CAAA;QAC5D9M,KAAK,CAACmP,mBAAmB,GAAG,MAAM;YAChC,IAAI,CAACnP,KAAK,CAACoP,oBAAoB,IAAIpP,KAAK,CAACO,OAAO,CAAC4O,mBAAmB,EAAE;gBACpEnP,KAAK,CAACoP,oBAAoB,GAAGpP,KAAK,CAACO,OAAO,CAAC4O,mBAAmB,CAACnP,KAAK,CAAC,CAAA;YACvE,CAAA;YAEA,IAAIA,KAAK,CAACO,OAAO,CAAC8O,eAAe,IAAI,CAACrP,KAAK,CAACoP,oBAAoB,EAAE;gBAChE,OAAOpP,KAAK,CAAC2J,sBAAsB,EAAE,CAAA;YACvC,CAAA;YAEA,OAAO3J,KAAK,CAACoP,oBAAoB,EAAE,CAAA;SACpC,CAAA;IACH,CAAA;AACF,EAAC;AAEM,SAASd,sBAAsBA,CACpClC,QAA0B,EAC1BY,KAAW,EACXzR,MAA+B,EAC/B;IACA,OACE,CAAC6Q,QAAQ,IAAIA,QAAQ,CAAC7B,UAAU,GAC5B6B,QAAQ,CAAC7B,UAAU,CAACyC,KAAK,EAAEzR,MAAM,CAAC,GAClC,KAAK,KACT,OAAOyR,KAAK,KAAK,WAAW,IAC3B,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAACA,KAAM,CAAA;AAEzC;ACzaA,MAAMsC,GAAuB,GAAGA,CAACpP,QAAQ,EAAEqP,SAAS,EAAEC,SAAS,KAAK;IAClE,+DAAA;IACA,kCAAA;IACA,OAAOA,SAAS,CAACnG,MAAM,CAAC,CAACiG,GAAG,EAAEG,IAAI,KAAK;QACrC,MAAMC,SAAS,GAAGD,IAAI,CAACnP,QAAQ,CAACJ,QAAQ,CAAC,CAAA;QACzC,OAAOoP,GAAG,GAAA,CAAI,OAAOI,SAAS,KAAK,QAAQ,GAAGA,SAAS,GAAG,CAAC,CAAC,CAAA;KAC7D,EAAE,CAAC,CAAC,CAAA;AACP,CAAC,CAAA;AAED,MAAMpQ,GAAuB,GAAGA,CAACY,QAAQ,EAAEqP,SAAS,EAAEC,SAAS,KAAK;IAClE,IAAIlQ,GAAuB,CAAA;IAE3BkQ,SAAS,CAACrS,OAAO,EAAC8C,GAAG,IAAI;QACvB,MAAM+M,KAAK,GAAG/M,GAAG,CAACK,QAAQ,CAASJ,QAAQ,CAAC,CAAA;QAE5C,IACE8M,KAAK,IAAI,IAAI,IAAA,CACZ1N,GAAG,GAAI0N,KAAK,IAAK1N,GAAG,KAAKoC,SAAS,IAAIsL,KAAK,IAAIA,KAAM,CAAC,EACvD;YACA1N,GAAG,GAAG0N,KAAK,CAAA;QACb,CAAA;IACF,CAAC,CAAC,CAAA;IAEF,OAAO1N,GAAG,CAAA;AACZ,CAAC,CAAA;AAED,MAAMD,GAAuB,GAAGA,CAACa,QAAQ,EAAEqP,SAAS,EAAEC,SAAS,KAAK;IAClE,IAAInQ,GAAuB,CAAA;IAE3BmQ,SAAS,CAACrS,OAAO,EAAC8C,GAAG,IAAI;QACvB,MAAM+M,KAAK,GAAG/M,GAAG,CAACK,QAAQ,CAASJ,QAAQ,CAAC,CAAA;QAC5C,IACE8M,KAAK,IAAI,IAAI,IAAA,CACZ3N,GAAG,GAAI2N,KAAK,IAAK3N,GAAG,KAAKqC,SAAS,IAAIsL,KAAK,IAAIA,KAAM,CAAC,EACvD;YACA3N,GAAG,GAAG2N,KAAK,CAAA;QACb,CAAA;IACF,CAAC,CAAC,CAAA;IAEF,OAAO3N,GAAG,CAAA;AACZ,CAAC,CAAA;AAED,MAAMsQ,MAA0B,GAAGA,CAACzP,QAAQ,EAAEqP,SAAS,EAAEC,SAAS,KAAK;IACrE,IAAIlQ,GAAuB,CAAA;IAC3B,IAAID,GAAuB,CAAA;IAE3BmQ,SAAS,CAACrS,OAAO,EAAC8C,GAAG,IAAI;QACvB,MAAM+M,KAAK,GAAG/M,GAAG,CAACK,QAAQ,CAASJ,QAAQ,CAAC,CAAA;QAC5C,IAAI8M,KAAK,IAAI,IAAI,EAAE;YACjB,IAAI1N,GAAG,KAAKoC,SAAS,EAAE;gBACrB,IAAIsL,KAAK,IAAIA,KAAK,EAAE1N,GAAG,GAAGD,GAAG,GAAG2N,KAAK,CAAA;YACvC,CAAC,MAAM;gBACL,IAAI1N,GAAG,GAAG0N,KAAK,EAAE1N,GAAG,GAAG0N,KAAK,CAAA;gBAC5B,IAAI3N,GAAG,GAAI2N,KAAK,EAAE3N,GAAG,GAAG2N,KAAK,CAAA;YAC/B,CAAA;QACF,CAAA;IACF,CAAC,CAAC,CAAA;IAEF,OAAO;QAAC1N,GAAG;QAAED,GAAG;KAAC,CAAA;AACnB,CAAC,CAAA;AAED,MAAMuQ,IAAwB,GAAGA,CAAC1P,QAAQ,EAAE2P,QAAQ,KAAK;IACvD,IAAIC,KAAK,GAAG,CAAC,CAAA;IACb,IAAIR,GAAG,GAAG,CAAC,CAAA;IAEXO,QAAQ,CAAC1S,OAAO,EAAC8C,GAAG,IAAI;QACtB,IAAI+M,KAAK,GAAG/M,GAAG,CAACK,QAAQ,CAASJ,QAAQ,CAAC,CAAA;QAC1C,IAAI8M,KAAK,IAAI,IAAI,IAAI,CAACA,KAAK,GAAG,CAACA,KAAK,KAAKA,KAAK,EAAE;YAC9C,EAAE8C,KAAK,EAAGR,GAAG,IAAItC,KAAM,CAAA;QACzB,CAAA;IACF,CAAC,CAAC,CAAA;IAEF,IAAI8C,KAAK,EAAE,OAAOR,GAAG,GAAGQ,KAAK,CAAA;IAE7B,OAAA;AACF,CAAC,CAAA;AAED,MAAMC,MAA0B,GAAGA,CAAC7P,QAAQ,EAAE2P,QAAQ,KAAK;IACzD,IAAI,CAACA,QAAQ,CAACtS,MAAM,EAAE;QACpB,OAAA;IACF,CAAA;IAEA,MAAMyS,MAAM,GAAGH,QAAQ,CAACtM,GAAG,EAACtD,GAAG,GAAIA,GAAG,CAACK,QAAQ,CAACJ,QAAQ,CAAC,CAAC,CAAA;IAC1D,IAAI,CAAC1D,aAAa,CAACwT,MAAM,CAAC,EAAE;QAC1B,OAAA;IACF,CAAA;IACA,IAAIA,MAAM,CAACzS,MAAM,KAAK,CAAC,EAAE;QACvB,OAAOyS,MAAM,CAAC,CAAC,CAAC,CAAA;IAClB,CAAA;IAEA,MAAMC,GAAG,GAAGtR,IAAI,CAACuR,KAAK,CAACF,MAAM,CAACzS,MAAM,GAAG,CAAC,CAAC,CAAA;IACzC,MAAM4S,IAAI,GAAGH,MAAM,CAACI,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,GAAKD,CAAC,GAAGC,CAAC,CAAC,CAAA;IACzC,OAAON,MAAM,CAACzS,MAAM,GAAG,CAAC,KAAK,CAAC,GAAG4S,IAAI,CAACF,GAAG,CAAC,GAAG,CAACE,IAAI,CAACF,GAAG,GAAG,CAAC,CAAC,GAAIE,IAAI,CAACF,GAAG,CAAE,IAAI,CAAC,CAAA;AAChF,CAAC,CAAA;AAED,MAAMM,MAA0B,GAAGA,CAACrQ,QAAQ,EAAE2P,QAAQ,KAAK;IACzD,OAAOpT,KAAK,CAAC+T,IAAI,CAAC,IAAIC,GAAG,CAACZ,QAAQ,CAACtM,GAAG,EAACjH,CAAC,GAAIA,CAAC,CAACgE,QAAQ,CAACJ,QAAQ,CAAC,CAAC,CAAC,CAAC8P,MAAM,EAAE,CAAC,CAAA;AAC9E,CAAC,CAAA;AAED,MAAMU,WAA+B,GAAGA,CAACxQ,QAAQ,EAAE2P,QAAQ,KAAK;IAC9D,OAAO,IAAIY,GAAG,CAACZ,QAAQ,CAACtM,GAAG,EAACjH,CAAC,GAAIA,CAAC,CAACgE,QAAQ,CAACJ,QAAQ,CAAC,CAAC,CAAC,CAACyQ,IAAI,CAAA;AAC9D,CAAC,CAAA;AAED,MAAMb,KAAyB,GAAGA,CAACc,SAAS,EAAEf,QAAQ,KAAK;IACzD,OAAOA,QAAQ,CAACtS,MAAM,CAAA;AACxB,CAAC,CAAA;AAEM,MAAMsT,cAAc,GAAG;IAC5BvB,GAAG;IACHhQ,GAAG;IACHD,GAAG;IACHsQ,MAAM;IACNC,IAAI;IACJG,MAAM;IACNQ,MAAM;IACNG,WAAW;IACXZ,KAAAA;AACF;ACuHA,EAAA;AAEO,MAAMgB,cAA4B,GAAG;IAC1C3E,mBAAmB,EAAEA,MAGhB;QACH,OAAO;YACL4E,cAAc,GAAEC,KAAK,IAAA;gBAAA,IAAAC,SAAA,EAAAC,eAAA,CAAA;gBAAA,OAAAD,CAAAA,SAAA,GAAA,CAAAC,eAAA,GAAKF,KAAK,CAAC1Q,QAAQ,EAAE,KAAjB4Q,IAAAA,IAAAA,eAAA,CAA2B7G,QAAQ,IAAA,IAAA,GAAA,KAAA,CAAA,GAAnC6G,eAAA,CAA2B7G,QAAQ,EAAI,KAAA,IAAA,GAAA4G,SAAA,GAAI,IAAI,CAAA;YAAA,CAAA;YACxEE,aAAa,EAAE,MAAA;SAChB,CAAA;KACF;IAED9E,eAAe,GAAGC,KAAK,IAAyB;QAC9C,OAAO;YACL8E,QAAQ,EAAE,EAAE;YACZ,GAAG9E,KAAAA;SACJ,CAAA;KACF;IAEDE,iBAAiB,GACfxM,KAAmB,IACC;QACpB,OAAO;YACLqR,gBAAgB,EAAErV,gBAAgB,CAAC,UAAU,EAAEgE,KAAK,CAAC;YACrDsR,iBAAiB,EAAE,SAAA;SACpB,CAAA;KACF;IAEDxQ,YAAY,EAAEA,CACZvF,MAA6B,EAC7ByE,KAAmB,KACV;QACTzE,MAAM,CAACgW,cAAc,GAAG,MAAM;YAC5BvR,KAAK,CAACwR,WAAW,EAACpV,GAAG,IAAI;gBACvB,6CAAA;gBACA,IAAIA,GAAG,IAAHA,IAAAA,IAAAA,GAAG,CAAEwF,QAAQ,CAACrG,MAAM,CAACkF,EAAE,CAAC,EAAE;oBAC5B,OAAOrE,GAAG,CAACkI,MAAM,EAAChI,CAAC,GAAIA,CAAC,KAAKf,MAAM,CAACkF,EAAE,CAAC,CAAA;gBACzC,CAAA;gBAEA,OAAO,CAAC;uBAAIrE,GAAG,IAAA,IAAA,GAAHA,GAAG,GAAI,EAAE;oBAAGb,MAAM,CAACkF,EAAE;iBAAC,CAAA;YACpC,CAAC,CAAC,CAAA;SACH,CAAA;QAEDlF,MAAM,CAACkW,WAAW,GAAG,MAAM;YAAA,IAAApE,qBAAA,EAAAC,qBAAA,CAAA;YACzB,OACE,CAAA,CAAAD,qBAAA,GAAC9R,MAAM,CAACwF,SAAS,CAAC2Q,cAAc,KAAArE,IAAAA,GAAAA,qBAAA,GAAI,IAAI,KAAA,CAAA,CAAAC,qBAAA,GACvCtN,KAAK,CAACO,OAAO,CAACmR,cAAc,KAAA,IAAA,GAAApE,qBAAA,GAAI,IAAI,CAAC,IAAA,CACrC,CAAC,CAAC/R,MAAM,CAACC,UAAU,IAAI,CAAC,CAACD,MAAM,CAACwF,SAAS,CAAC4Q,gBAAgB,CAAC,CAAA;SAE/D,CAAA;QAEDpW,MAAM,CAACqW,YAAY,GAAG,MAAM;YAAA,IAAAC,qBAAA,CAAA;YAC1B,OAAA,CAAAA,qBAAA,GAAO7R,KAAK,CAAC6D,QAAQ,EAAE,CAACuN,QAAQ,KAAA,IAAA,GAAA,KAAA,CAAA,GAAzBS,qBAAA,CAA2BjQ,QAAQ,CAACrG,MAAM,CAACkF,EAAE,CAAC,CAAA;SACtD,CAAA;QAEDlF,MAAM,CAACuW,eAAe,GAAG,MAAA;YAAA,IAAAC,sBAAA,CAAA;YAAA,OAAA,CAAAA,sBAAA,GAAM/R,KAAK,CAAC6D,QAAQ,EAAE,CAACuN,QAAQ,KAAA,IAAA,GAAA,KAAA,CAAA,GAAzBW,sBAAA,CAA2BC,OAAO,CAACzW,MAAM,CAACkF,EAAE,CAAC,CAAA;QAAA,CAAA,CAAA;QAE5ElF,MAAM,CAAC0W,wBAAwB,GAAG,MAAM;YACtC,MAAMC,QAAQ,GAAG3W,MAAM,CAACkW,WAAW,EAAE,CAAA;YAErC,OAAO,MAAM;gBACX,IAAI,CAACS,QAAQ,EAAE,OAAA;gBACf3W,MAAM,CAACgW,cAAc,EAAE,CAAA;aACxB,CAAA;SACF,CAAA;QACDhW,MAAM,CAAC4W,oBAAoB,GAAG,MAAM;YAClC,MAAMtF,QAAQ,GAAG7M,KAAK,CAAC8M,eAAe,EAAE,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAA;YAEpD,MAAMC,KAAK,GAAGH,QAAQ,IAARA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,QAAQ,CAAEvM,QAAQ,CAAC/E,MAAM,CAACkF,EAAE,CAAC,CAAA;YAE3C,IAAI,OAAOuM,KAAK,KAAK,QAAQ,EAAE;gBAC7B,OAAO6D,cAAc,CAACvB,GAAG,CAAA;YAC3B,CAAA;YAEA,IAAI8C,MAAM,CAAC7Q,SAAS,CAAC8I,QAAQ,CAACgI,IAAI,CAACrF,KAAK,CAAC,KAAK,eAAe,EAAE;gBAC7D,OAAO6D,cAAc,CAAClB,MAAM,CAAA;YAC9B,CAAA;SACD,CAAA;QACDpU,MAAM,CAAC+W,gBAAgB,GAAG,MAAM;YAAA,IAAAC,qBAAA,EAAAC,sBAAA,CAAA;YAC9B,IAAI,CAACjX,MAAM,EAAE;gBACX,MAAM,IAAI0G,KAAK,EAAE,CAAA;YACnB,CAAA;YAEA,OAAO5F,UAAU,CAACd,MAAM,CAACwF,SAAS,CAACoQ,aAAa,CAAC,GAC7C5V,MAAM,CAACwF,SAAS,CAACoQ,aAAa,GAC9B5V,MAAM,CAACwF,SAAS,CAACoQ,aAAa,KAAK,MAAM,GACvC5V,MAAM,CAAC4W,oBAAoB,EAAE,GAAA,CAAAI,qBAAA,GAAA,CAAAC,sBAAA,GAC7BxS,KAAK,CAACO,OAAO,CAACsQ,cAAc,KAAA,IAAA,GAAA,KAAA,CAAA,GAA5B2B,sBAAA,CACEjX,MAAM,CAACwF,SAAS,CAACoQ,aAAa,CAC/B,KAAAoB,IAAAA,GAAAA,qBAAA,GACD1B,cAAc,CACZtV,MAAM,CAACwF,SAAS,CAACoQ,aAAa,CAC/B,CAAA;SACR,CAAA;KACF;IAED1N,WAAW,GAA0BzD,KAAmB,IAAW;QACjEA,KAAK,CAACwR,WAAW,IAAG3V,OAAO,GAAImE,KAAK,CAACO,OAAO,CAAC8Q,gBAAgB,IAAA,IAAA,GAAA,KAAA,CAAA,GAA9BrR,KAAK,CAACO,OAAO,CAAC8Q,gBAAgB,CAAGxV,OAAO,CAAC,CAAA;QAExEmE,KAAK,CAACyS,aAAa,IAAG1D,YAAY,IAAI;YAAA,IAAA2D,qBAAA,EAAAzD,mBAAA,CAAA;YACpCjP,KAAK,CAACwR,WAAW,CAACzC,YAAY,GAAG,EAAE,GAAA,CAAA2D,qBAAA,GAAA,CAAAzD,mBAAA,GAAGjP,KAAK,CAACkP,YAAY,KAAA,OAAA,KAAA,IAAlBD,mBAAA,CAAoBmC,QAAQ,KAAAsB,IAAAA,GAAAA,qBAAA,GAAI,EAAE,CAAC,CAAA;SAC1E,CAAA;QAED1S,KAAK,CAAC2S,qBAAqB,GAAG,IAAM3S,KAAK,CAACmP,mBAAmB,EAAE,CAAA;QAC/DnP,KAAK,CAAC4S,kBAAkB,GAAG,MAAM;YAC/B,IAAI,CAAC5S,KAAK,CAAC6S,mBAAmB,IAAI7S,KAAK,CAACO,OAAO,CAACqS,kBAAkB,EAAE;gBAClE5S,KAAK,CAAC6S,mBAAmB,GAAG7S,KAAK,CAACO,OAAO,CAACqS,kBAAkB,CAAC5S,KAAK,CAAC,CAAA;YACrE,CAAA;YAEA,IAAIA,KAAK,CAACO,OAAO,CAACuS,cAAc,IAAI,CAAC9S,KAAK,CAAC6S,mBAAmB,EAAE;gBAC9D,OAAO7S,KAAK,CAAC2S,qBAAqB,EAAE,CAAA;YACtC,CAAA;YAEA,OAAO3S,KAAK,CAAC6S,mBAAmB,EAAE,CAAA;SACnC,CAAA;KACF;IAED9K,SAAS,EAAEA,CACT9H,GAAe,EACfD,KAAmB,KACV;QACTC,GAAG,CAAC2R,YAAY,GAAG,IAAM,CAAC,CAAC3R,GAAG,CAAC8S,gBAAgB,CAAA;QAC/C9S,GAAG,CAAC0R,gBAAgB,IAAGzR,QAAQ,IAAI;YACjC,IAAID,GAAG,CAAC+S,oBAAoB,CAAC1K,cAAc,CAACpI,QAAQ,CAAC,EAAE;gBACrD,OAAOD,GAAG,CAAC+S,oBAAoB,CAAC9S,QAAQ,CAAC,CAAA;YAC3C,CAAA;YAEA,MAAM3E,MAAM,GAAGyE,KAAK,CAACuI,SAAS,CAACrI,QAAQ,CAAC,CAAA;YAExC,IAAI,CAAA,CAAC3E,MAAM,IAANA,IAAAA,IAAAA,MAAM,CAAEwF,SAAS,CAAC4Q,gBAAgB,CAAE,EAAA;gBACvC,OAAO1R,GAAG,CAACK,QAAQ,CAACJ,QAAQ,CAAC,CAAA;YAC/B,CAAA;YAEAD,GAAG,CAAC+S,oBAAoB,CAAC9S,QAAQ,CAAC,GAAG3E,MAAM,CAACwF,SAAS,CAAC4Q,gBAAgB,CACpE1R,GAAG,CAAC+H,QACN,CAAC,CAAA;YAED,OAAO/H,GAAG,CAAC+S,oBAAoB,CAAC9S,QAAQ,CAAC,CAAA;SAC1C,CAAA;QACDD,GAAG,CAAC+S,oBAAoB,GAAG,CAAA,CAAE,CAAA;KAC9B;IAEDjT,UAAU,EAAEA,CACVM,IAAyB,EACzB9E,MAA6B,EAC7B0E,GAAe,EACfD,KAAmB,KACV;QAITK,IAAI,CAACuR,YAAY,GAAG,IAClBrW,MAAM,CAACqW,YAAY,EAAE,IAAIrW,MAAM,CAACkF,EAAE,KAAKR,GAAG,CAAC8S,gBAAgB,CAAA;QAC7D1S,IAAI,CAAC4S,gBAAgB,GAAG,IAAM,CAAC5S,IAAI,CAACuR,YAAY,EAAE,IAAIrW,MAAM,CAACqW,YAAY,EAAE,CAAA;QAC3EvR,IAAI,CAAC6S,eAAe,GAAG,MAAA;YAAA,IAAAC,YAAA,CAAA;YAAA,OACrB,CAAC9S,IAAI,CAACuR,YAAY,EAAE,IAAI,CAACvR,IAAI,CAAC4S,gBAAgB,EAAE,IAAI,CAAC,CAAA,CAAAE,CAAAA,YAAA,GAAClT,GAAG,CAACiI,OAAO,KAAA,IAAA,IAAXiL,YAAA,CAAa5V,MAAM,CAAA,CAAA;QAAA,CAAA,CAAA;IAC7E,CAAA;AACF,EAAC;AAEM,SAASiF,YAAYA,CAC1BE,WAAqC,EACrC0O,QAAkB,EAClBE,iBAAsC,EACtC;IACA,IAAI,CAAA,CAACF,QAAQ,IAARA,IAAAA,IAAAA,QAAQ,CAAE7T,MAAM,CAAA,IAAI,CAAC+T,iBAAiB,EAAE;QAC3C,OAAO5O,WAAW,CAAA;IACpB,CAAA;IAEA,MAAM0Q,kBAAkB,GAAG1Q,WAAW,CAAC4B,MAAM,EAC3C+O,GAAG,GAAI,CAACjC,QAAQ,CAACxP,QAAQ,CAACyR,GAAG,CAAC5S,EAAE,CAClC,CAAC,CAAA;IAED,IAAI6Q,iBAAiB,KAAK,QAAQ,EAAE;QAClC,OAAO8B,kBAAkB,CAAA;IAC3B,CAAA;IAEA,MAAME,eAAe,GAAGlC,QAAQ,CAC7B7N,GAAG,EAACgQ,CAAC,GAAI7Q,WAAW,CAAC2B,IAAI,EAACgP,GAAG,GAAIA,GAAG,CAAC5S,EAAE,KAAK8S,CAAC,CAAE,CAAC,CAChDjP,MAAM,CAACC,OAAO,CAAC,CAAA;IAElB,OAAO,CAAC;WAAG+O,eAAe,EAAE;WAAGF,kBAAkB;KAAC,CAAA;AACpD;AC7VA,EAAA;AAEO,MAAMI,cAA4B,GAAG;IAC1CnH,eAAe,GAAGC,KAAK,IAA4B;QACjD,OAAO;YACLmH,WAAW,EAAE,EAAE;YACf,GAAGnH,KAAAA;SACJ,CAAA;KACF;IAEDE,iBAAiB,GACfxM,KAAmB,IACW;QAC9B,OAAO;YACL0T,mBAAmB,EAAE1X,gBAAgB,CAAC,aAAa,EAAEgE,KAAK,CAAA;SAC3D,CAAA;KACF;IAEDc,YAAY,EAAEA,CACZvF,MAA8B,EAC9ByE,KAAmB,KACV;QACTzE,MAAM,CAACoY,QAAQ,GAAGnW,IAAI,EACpBoW,QAAQ,GAAI;gBAACC,sBAAsB,CAAC7T,KAAK,EAAE4T,QAAQ,CAAC;aAAC,GACrD1R,OAAO,GAAIA,OAAO,CAAC+L,SAAS,EAAC3R,CAAC,GAAIA,CAAC,CAACmE,EAAE,KAAKlF,MAAM,CAACkF,EAAE,CAAC,EACrDlB,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,cAAc,EAAE,UAAU,CAC1D,CAAC,CAAA;QACDhF,MAAM,CAACuY,gBAAgB,IAAGF,QAAQ,IAAI;YAAA,IAAAG,SAAA,CAAA;YACpC,MAAM7R,OAAO,GAAG2R,sBAAsB,CAAC7T,KAAK,EAAE4T,QAAQ,CAAC,CAAA;YACvD,OAAO,CAAAG,CAAAA,SAAA,GAAA7R,OAAO,CAAC,CAAC,CAAC,KAAV6R,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,SAAA,CAAYtT,EAAE,MAAKlF,MAAM,CAACkF,EAAE,CAAA;SACpC,CAAA;QACDlF,MAAM,CAACyY,eAAe,IAAGJ,QAAQ,IAAI;YAAA,IAAAK,QAAA,CAAA;YACnC,MAAM/R,OAAO,GAAG2R,sBAAsB,CAAC7T,KAAK,EAAE4T,QAAQ,CAAC,CAAA;YACvD,OAAO,CAAA,CAAAK,QAAA,GAAA/R,OAAO,CAACA,OAAO,CAAC3E,MAAM,GAAG,CAAC,CAAC,KAAA,OAAA,KAAA,IAA3B0W,QAAA,CAA6BxT,EAAE,MAAKlF,MAAM,CAACkF,EAAE,CAAA;SACrD,CAAA;KACF;IAEDgD,WAAW,GAA0BzD,KAAmB,IAAW;QACjEA,KAAK,CAACkU,cAAc,IAAGrY,OAAO,GAC5BmE,KAAK,CAACO,OAAO,CAACmT,mBAAmB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAjC1T,KAAK,CAACO,OAAO,CAACmT,mBAAmB,CAAG7X,OAAO,CAAC,CAAA;QAC9CmE,KAAK,CAACmU,gBAAgB,IAAGpF,YAAY,IAAI;YAAA,IAAAC,qBAAA,CAAA;YACvChP,KAAK,CAACkU,cAAc,CAClBnF,YAAY,GAAG,EAAE,GAAA,CAAAC,qBAAA,GAAGhP,KAAK,CAACkP,YAAY,CAACuE,WAAW,KAAA,OAAAzE,qBAAA,GAAI,EACxD,CAAC,CAAA;SACF,CAAA;QACDhP,KAAK,CAACuC,kBAAkB,GAAG/E,IAAI,CAC7B,IAAM;gBACJwC,KAAK,CAAC6D,QAAQ,EAAE,CAAC4P,WAAW;gBAC5BzT,KAAK,CAAC6D,QAAQ,EAAE,CAACuN,QAAQ;gBACzBpR,KAAK,CAACO,OAAO,CAAC+Q,iBAAiB;aAChC,EACD,CAACmC,WAAW,EAAErC,QAAQ,EAAEE,iBAAiB,IACtCpP,OAAiC,IAAK;gBACrC,uDAAA;gBACA,+BAAA;gBACA,IAAIkS,cAAwC,GAAG,EAAE,CAAA;gBAEjD,kDAAA;gBACA,IAAI,CAAA,CAACX,WAAW,IAAA,IAAA,IAAXA,WAAW,CAAElW,MAAM,CAAE,EAAA;oBACxB6W,cAAc,GAAGlS,OAAO,CAAA;gBAC1B,CAAC,MAAM;oBACL,MAAMmS,eAAe,GAAG,CAAC;2BAAGZ,WAAW;qBAAC,CAAA;oBAExC,mDAAA;oBACA,MAAMa,WAAW,GAAG,CAAC;2BAAGpS,OAAO;qBAAC,CAAA;oBAEhC,8CAAA;oBAEA,mEAAA;oBACA,MAAOoS,WAAW,CAAC/W,MAAM,IAAI8W,eAAe,CAAC9W,MAAM,CAAE;wBACnD,MAAMgX,cAAc,GAAGF,eAAe,CAACG,KAAK,EAAE,CAAA;wBAC9C,MAAMC,UAAU,GAAGH,WAAW,CAACrG,SAAS,EACtC3R,CAAC,GAAIA,CAAC,CAACmE,EAAE,KAAK8T,cAChB,CAAC,CAAA;wBACD,IAAIE,UAAU,GAAG,CAAC,CAAC,EAAE;4BACnBL,cAAc,CAAC/W,IAAI,CAACiX,WAAW,CAACI,MAAM,CAACD,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC,CAAE,CAAC,CAAA;wBAC5D,CAAA;oBACF,CAAA;oBAEA,qDAAA;oBACAL,cAAc,GAAG,CAAC;2BAAGA,cAAc,EAAE;2BAAGE,WAAW;qBAAC,CAAA;gBACtD,CAAA;gBAEA,OAAO9R,YAAY,CAAC4R,cAAc,EAAEhD,QAAQ,EAAEE,iBAAiB,CAAC,CAAA;aACjE,EACH/R,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,oBAAoB,CAClE,CAAC,CAAA;IACH,CAAA;AACF;ACfA,EAAA;AAEA,MAAMoU,4BAA4B,GAAGA,IAAAA,CAA2B;QAC9D5Q,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,EAAA;IACT,CAAC,CAAC,CAAA;AAEK,MAAM4Q,aAA2B,GAAG;IACzCvI,eAAe,GAAGC,KAAK,IAA8B;QACnD,OAAO;YACLxI,aAAa,EAAE6Q,4BAA4B,EAAE;YAC7C,GAAGrI,KAAAA;SACJ,CAAA;KACF;IAEDE,iBAAiB,GACfxM,KAAmB,IACa;QAChC,OAAO;YACL6U,qBAAqB,EAAE7Y,gBAAgB,CAAC,eAAe,EAAEgE,KAAK,CAAA;SAC/D,CAAA;KACF;IAEDc,YAAY,EAAEA,CACZvF,MAA6B,EAC7ByE,KAAmB,KACV;QACTzE,MAAM,CAACuZ,GAAG,IAAGlB,QAAQ,IAAI;YACvB,MAAMmB,SAAS,GAAGxZ,MAAM,CACrB+G,cAAc,EAAE,CAChBiB,GAAG,EAACjH,CAAC,GAAIA,CAAC,CAACmE,EAAE,CAAC,CACd6D,MAAM,CAACC,OAAO,CAAa,CAAA;YAE9BvE,KAAK,CAACgV,gBAAgB,EAAC5Y,GAAG,IAAI;gBAAA,IAAA6Y,UAAA,EAAAC,WAAA,CAAA;gBAC5B,IAAItB,QAAQ,KAAK,OAAO,EAAE;oBAAA,IAAAuB,SAAA,EAAAC,UAAA,CAAA;oBACxB,OAAO;wBACLrR,IAAI,EAAE,CAAAoR,CAAAA,SAAA,GAAC/Y,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAE2H,IAAI,KAAAoR,IAAAA,GAAAA,SAAA,GAAI,EAAE,EAAE7Q,MAAM,EAAChI,CAAC,GAAI,CAAA,CAACyY,SAAS,IAAA,IAAA,IAATA,SAAS,CAAEnT,QAAQ,CAACtF,CAAC,CAAC,CAAC,CAAA;wBAC5D0H,KAAK,EAAE,CACL;+BAAG,CAAA,CAAAoR,UAAA,GAAChZ,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAE4H,KAAK,KAAAoR,IAAAA,GAAAA,UAAA,GAAI,EAAE,EAAE9Q,MAAM,EAAChI,CAAC,GAAI,CAAA,CAACyY,SAAS,IAAA,QAATA,SAAS,CAAEnT,QAAQ,CAACtF,CAAC,CAAC,CAAC,CAAA,EAC1D;+BAAGyY,SAAS;yBAAA;qBAEf,CAAA;gBACH,CAAA;gBAEA,IAAInB,QAAQ,KAAK,MAAM,EAAE;oBAAA,IAAAyB,UAAA,EAAAC,WAAA,CAAA;oBACvB,OAAO;wBACLvR,IAAI,EAAE,CACJ;+BAAG,CAAA,CAAAsR,UAAA,GAACjZ,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAE2H,IAAI,KAAAsR,IAAAA,GAAAA,UAAA,GAAI,EAAE,EAAE/Q,MAAM,EAAChI,CAAC,GAAI,CAAA,CAACyY,SAAS,IAAA,QAATA,SAAS,CAAEnT,QAAQ,CAACtF,CAAC,CAAC,CAAA,CAAC,EACzD;+BAAGyY,SAAS;yBACb;wBACD/Q,KAAK,EAAE,CAAAsR,CAAAA,WAAA,GAAClZ,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAE4H,KAAK,KAAAsR,IAAAA,GAAAA,WAAA,GAAI,EAAE,EAAEhR,MAAM,EAAChI,CAAC,GAAI,CAAA,CAACyY,SAAS,IAATA,IAAAA,IAAAA,SAAS,CAAEnT,QAAQ,CAACtF,CAAC,CAAC,CAAA,CAAA;qBAC9D,CAAA;gBACH,CAAA;gBAEA,OAAO;oBACLyH,IAAI,EAAE,CAAAkR,CAAAA,UAAA,GAAC7Y,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAE2H,IAAI,KAAAkR,IAAAA,GAAAA,UAAA,GAAI,EAAE,EAAE3Q,MAAM,EAAChI,CAAC,GAAI,CAAA,CAACyY,SAAS,IAAA,IAAA,IAATA,SAAS,CAAEnT,QAAQ,CAACtF,CAAC,CAAC,CAAC,CAAA;oBAC5D0H,KAAK,EAAE,CAAAkR,CAAAA,WAAA,GAAC9Y,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAE4H,KAAK,KAAAkR,IAAAA,GAAAA,WAAA,GAAI,EAAE,EAAE5Q,MAAM,EAAChI,CAAC,GAAI,CAAA,CAACyY,SAAS,IAATA,IAAAA,IAAAA,SAAS,CAAEnT,QAAQ,CAACtF,CAAC,CAAC,CAAA,CAAA;iBAC9D,CAAA;YACH,CAAC,CAAC,CAAA;SACH,CAAA;QAEDf,MAAM,CAACga,SAAS,GAAG,MAAM;YACvB,MAAM7S,WAAW,GAAGnH,MAAM,CAAC+G,cAAc,EAAE,CAAA;YAE3C,OAAOI,WAAW,CAACrE,IAAI,EACrB/B,CAAC,IAAA;gBAAA,IAAAkZ,qBAAA,EAAAtU,IAAA,EAAAoM,qBAAA,CAAA;gBAAA,OACC,CAAAkI,CAAAA,qBAAA,GAAClZ,CAAC,CAACyE,SAAS,CAAC0U,aAAa,KAAA,IAAA,GAAAD,qBAAA,GAAI,IAAI,KAAA,CAAAtU,CAAAA,IAAA,GAAA,CAAAoM,qBAAA,GACjCtN,KAAK,CAACO,OAAO,CAACmV,mBAAmB,KAAA,IAAA,GAAApI,qBAAA,GAChCtN,KAAK,CAACO,OAAO,CAACkV,aAAa,KAAA,IAAA,GAAAvU,IAAA,GAC3B,IAAI,CAAC,CAAA;YAAA,CACX,CAAC,CAAA;SACF,CAAA;QAED3F,MAAM,CAACoa,WAAW,GAAG,MAAM;YACzB,MAAMC,aAAa,GAAGra,MAAM,CAAC+G,cAAc,EAAE,CAACiB,GAAG,EAACjH,CAAC,GAAIA,CAAC,CAACmE,EAAE,CAAC,CAAA;YAE5D,MAAM,EAAEsD,IAAI,EAAEC,KAAAA,EAAO,GAAGhE,KAAK,CAAC6D,QAAQ,EAAE,CAACC,aAAa,CAAA;YAEtD,MAAM+R,MAAM,GAAGD,aAAa,CAACvX,IAAI,EAAC/B,CAAC,GAAIyH,IAAI,IAAA,IAAA,GAAA,KAAA,CAAA,GAAJA,IAAI,CAAEnC,QAAQ,CAACtF,CAAC,CAAC,CAAC,CAAA;YACzD,MAAMwZ,OAAO,GAAGF,aAAa,CAACvX,IAAI,EAAC/B,CAAC,GAAI0H,KAAK,IAAA,IAAA,GAAA,KAAA,CAAA,GAALA,KAAK,CAAEpC,QAAQ,CAACtF,CAAC,CAAC,CAAC,CAAA;YAE3D,OAAOuZ,MAAM,GAAG,MAAM,GAAGC,OAAO,GAAG,OAAO,GAAG,KAAK,CAAA;SACnD,CAAA;QAEDva,MAAM,CAACwa,cAAc,GAAG,MAAM;YAAA,IAAAjI,qBAAA,EAAAC,sBAAA,CAAA;YAC5B,MAAM6F,QAAQ,GAAGrY,MAAM,CAACoa,WAAW,EAAE,CAAA;YAErC,OAAO/B,QAAQ,GAAA,CAAA9F,qBAAA,GAAA,CAAAC,sBAAA,GACX/N,KAAK,CAAC6D,QAAQ,EAAE,CAACC,aAAa,KAAA,IAAA,IAAA,CAAAiK,sBAAA,GAA9BA,sBAAA,CAAiC6F,QAAQ,CAAC,KAA1C7F,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,sBAAA,CAA4CiE,OAAO,CAACzW,MAAM,CAACkF,EAAE,CAAC,KAAA,OAAAqN,qBAAA,GAAI,CAAC,CAAC,GACpE,CAAC,CAAA;SACN,CAAA;KACF;IAED/F,SAAS,EAAEA,CACT9H,GAAe,EACfD,KAAmB,KACV;QACTC,GAAG,CAAC+V,qBAAqB,GAAGxY,IAAI,CAC9B,IAAM;gBACJyC,GAAG,CAACgW,mBAAmB,EAAE;gBACzBjW,KAAK,CAAC6D,QAAQ,EAAE,CAACC,aAAa,CAACC,IAAI;gBACnC/D,KAAK,CAAC6D,QAAQ,EAAE,CAACC,aAAa,CAACE,KAAK;aACrC,EACD,CAACoF,QAAQ,EAAErF,IAAI,EAAEC,KAAK,KAAK;YACzB,MAAMkS,YAAsB,GAAG,CAAC;mBAAInS,IAAI,IAAA,IAAA,GAAJA,IAAI,GAAI,EAAE,GAAG;mBAAIC,KAAK,IAAA,IAAA,GAALA,KAAK,GAAI,EAAE;aAAE,CAAA;YAElE,OAAOoF,QAAQ,CAAC9E,MAAM,EAAChI,CAAC,GAAI,CAAC4Z,YAAY,CAACtU,QAAQ,CAACtF,CAAC,CAACf,MAAM,CAACkF,EAAE,CAAC,CAAC,CAAA;SACjE,EACDlB,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,WAAW,EAAE,uBAAuB,CACpE,CAAC,CAAA;QACDN,GAAG,CAACkW,mBAAmB,GAAG3Y,IAAI,CAC5B,IAAM;gBAACyC,GAAG,CAACgW,mBAAmB,EAAE;gBAAEjW,KAAK,CAAC6D,QAAQ,EAAE,CAACC,aAAa,CAACC,IAAI;aAAC,EACtE,CAACqF,QAAQ,EAAErF,IAAI,KAAK;YAClB,MAAMqS,KAAK,GAAG,CAACrS,IAAI,IAAA,IAAA,GAAJA,IAAI,GAAI,EAAE,EACtBR,GAAG,EAACrD,QAAQ,GAAIkJ,QAAQ,CAAC/E,IAAI,EAAChE,IAAI,GAAIA,IAAI,CAAC9E,MAAM,CAACkF,EAAE,KAAKP,QAAQ,CAAE,CAAC,CACpEoE,MAAM,CAACC,OAAO,CAAC,CACfhB,GAAG,EAACjH,CAAC,GAAA,CAAK;oBAAE,GAAGA,CAAC;oBAAEsX,QAAQ,EAAE,MAAA;gBAAO,CAAC,CAAyB,CAAC,CAAA;YAEjE,OAAOwC,KAAK,CAAA;SACb,EACD7W,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,WAAW,EAAE,qBAAqB,CAClE,CAAC,CAAA;QACDN,GAAG,CAACoW,oBAAoB,GAAG7Y,IAAI,CAC7B,IAAM;gBAACyC,GAAG,CAACgW,mBAAmB,EAAE;gBAAEjW,KAAK,CAAC6D,QAAQ,EAAE,CAACC,aAAa,CAACE,KAAK;aAAC,EACvE,CAACoF,QAAQ,EAAEpF,KAAK,KAAK;YACnB,MAAMoS,KAAK,GAAG,CAACpS,KAAK,IAAA,IAAA,GAALA,KAAK,GAAI,EAAE,EACvBT,GAAG,EAACrD,QAAQ,GAAIkJ,QAAQ,CAAC/E,IAAI,EAAChE,IAAI,GAAIA,IAAI,CAAC9E,MAAM,CAACkF,EAAE,KAAKP,QAAQ,CAAE,CAAC,CACpEoE,MAAM,CAACC,OAAO,CAAC,CACfhB,GAAG,EAACjH,CAAC,GAAA,CAAK;oBAAE,GAAGA,CAAC;oBAAEsX,QAAQ,EAAE,OAAA;gBAAQ,CAAC,CAAyB,CAAC,CAAA;YAElE,OAAOwC,KAAK,CAAA;SACb,EACD7W,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,WAAW,EAAE,sBAAsB,CACnE,CAAC,CAAA;KACF;IAEDkD,WAAW,GAA0BzD,KAAmB,IAAW;QACjEA,KAAK,CAACgV,gBAAgB,IAAGnZ,OAAO,GAC9BmE,KAAK,CAACO,OAAO,CAACsU,qBAAqB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAnC7U,KAAK,CAACO,OAAO,CAACsU,qBAAqB,CAAGhZ,OAAO,CAAC,CAAA;QAEhDmE,KAAK,CAACsW,kBAAkB,IAAGvH,YAAY,IAAA;YAAA,IAAAC,qBAAA,EAAAC,mBAAA,CAAA;YAAA,OACrCjP,KAAK,CAACgV,gBAAgB,CACpBjG,YAAY,GACR4F,4BAA4B,EAAE,GAAA3F,CAAAA,qBAAA,GAAAC,CAAAA,mBAAA,GAC9BjP,KAAK,CAACkP,YAAY,KAAlBD,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,mBAAA,CAAoBnL,aAAa,KAAAkL,IAAAA,GAAAA,qBAAA,GAAI2F,4BAA4B,EACvE,CAAC,CAAA;QAAA,CAAA,CAAA;QAEH3U,KAAK,CAACuW,sBAAsB,IAAG3C,QAAQ,IAAI;YAAA,IAAA4C,qBAAA,CAAA;YACzC,MAAMC,YAAY,GAAGzW,KAAK,CAAC6D,QAAQ,EAAE,CAACC,aAAa,CAAA;YAEnD,IAAI,CAAC8P,QAAQ,EAAE;gBAAA,IAAA8C,kBAAA,EAAAC,mBAAA,CAAA;gBACb,OAAOpS,OAAO,CAAC,CAAAmS,CAAAA,kBAAA,GAAAD,YAAY,CAAC1S,IAAI,KAAA,IAAA,GAAA,KAAA,CAAA,GAAjB2S,kBAAA,CAAmBnZ,MAAM,KAAA,CAAAoZ,CAAAA,mBAAA,GAAIF,YAAY,CAACzS,KAAK,KAAlB2S,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,mBAAA,CAAoBpZ,MAAM,CAAC,CAAA,CAAA;YACzE,CAAA;YACA,OAAOgH,OAAO,CAAA,CAAAiS,qBAAA,GAACC,YAAY,CAAC7C,QAAQ,CAAC,KAAtB4C,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,qBAAA,CAAwBjZ,MAAM,CAAC,CAAA;SAC/C,CAAA;QAEDyC,KAAK,CAAC4W,kBAAkB,GAAGpZ,IAAI,CAC7B,IAAM;gBAACwC,KAAK,CAACkJ,iBAAiB,EAAE;gBAAElJ,KAAK,CAAC6D,QAAQ,EAAE,CAACC,aAAa,CAACC,IAAI;aAAC,EACtE,CAACE,UAAU,EAAEF,IAAI,KAAK;YACpB,OAAO,CAACA,IAAI,IAAJA,IAAAA,GAAAA,IAAI,GAAI,EAAE,EACfR,GAAG,EAACrD,QAAQ,GAAI+D,UAAU,CAACI,IAAI,EAAC9I,MAAM,GAAIA,MAAM,CAACkF,EAAE,KAAKP,QAAQ,CAAE,CAAC,CACnEoE,MAAM,CAACC,OAAO,CAAC,CAAA;SACnB,EACDhF,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,cAAc,EAAE,oBAAoB,CACpE,CAAC,CAAA;QAEDP,KAAK,CAAC6W,mBAAmB,GAAGrZ,IAAI,CAC9B,IAAM;gBAACwC,KAAK,CAACkJ,iBAAiB,EAAE;gBAAElJ,KAAK,CAAC6D,QAAQ,EAAE,CAACC,aAAa,CAACE,KAAK;aAAC,EACvE,CAACC,UAAU,EAAED,KAAK,KAAK;YACrB,OAAO,CAACA,KAAK,IAALA,IAAAA,GAAAA,KAAK,GAAI,EAAE,EAChBT,GAAG,EAACrD,QAAQ,GAAI+D,UAAU,CAACI,IAAI,EAAC9I,MAAM,GAAIA,MAAM,CAACkF,EAAE,KAAKP,QAAQ,CAAE,CAAC,CACnEoE,MAAM,CAACC,OAAO,CAAC,CAAA;SACnB,EACDhF,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,cAAc,EAAE,qBAAqB,CACrE,CAAC,CAAA;QAEDP,KAAK,CAAC8W,oBAAoB,GAAGtZ,IAAI,CAC/B,IAAM;gBACJwC,KAAK,CAACkJ,iBAAiB,EAAE;gBACzBlJ,KAAK,CAAC6D,QAAQ,EAAE,CAACC,aAAa,CAACC,IAAI;gBACnC/D,KAAK,CAAC6D,QAAQ,EAAE,CAACC,aAAa,CAACE,KAAK;aACrC,EACD,CAACC,UAAU,EAAEF,IAAI,EAAEC,KAAK,KAAK;YAC3B,MAAMkS,YAAsB,GAAG,CAAC;mBAAInS,IAAI,IAAA,IAAA,GAAJA,IAAI,GAAI,EAAE,GAAG;mBAAIC,KAAK,IAAA,IAAA,GAALA,KAAK,GAAI,EAAE;aAAE,CAAA;YAElE,OAAOC,UAAU,CAACK,MAAM,EAAChI,CAAC,GAAI,CAAC4Z,YAAY,CAACtU,QAAQ,CAACtF,CAAC,CAACmE,EAAE,CAAC,CAAC,CAAA;SAC5D,EACDlB,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,cAAc,EAAE,sBAAsB,CACtE,CAAC,CAAA;IACH,CAAA;AACF;AClUA,EAAA;AA2MA,EAAA;AAEO,MAAMwW,mBAAmB,GAAG;IACjCpG,IAAI,EAAE,GAAG;IACTqG,OAAO,EAAE,EAAE;IACXC,OAAO,EAAEpL,MAAM,CAACqL,gBAAAA;AAClB,EAAC;AAED,MAAMC,+BAA+B,GAAGA,IAAAA,CAA8B;QACpEC,WAAW,EAAE,IAAI;QACjBC,SAAS,EAAE,IAAI;QACfC,WAAW,EAAE,IAAI;QACjBC,eAAe,EAAE,IAAI;QACrBC,gBAAgB,EAAE,KAAK;QACvBC,iBAAiB,EAAE,EAAA;IACrB,CAAC,CAAC,CAAA;AAEK,MAAMC,YAA0B,GAAG;IACxCvL,mBAAmB,EAAEA,MAA6B;QAChD,OAAO4K,mBAAmB,CAAA;KAC3B;IACD1K,eAAe,GAAGC,KAAK,IAA6B;QAClD,OAAO;YACLqL,YAAY,EAAE,CAAA,CAAE;YAChBC,gBAAgB,EAAET,+BAA+B,EAAE;YACnD,GAAG7K,KAAAA;SACJ,CAAA;KACF;IAEDE,iBAAiB,GACfxM,KAAmB,IACY;QAC/B,OAAO;YACL6X,gBAAgB,EAAE,OAAO;YACzBC,qBAAqB,EAAE,KAAK;YAC5BC,oBAAoB,EAAE/b,gBAAgB,CAAC,cAAc,EAAEgE,KAAK,CAAC;YAC7DgY,wBAAwB,EAAEhc,gBAAgB,CAAC,kBAAkB,EAAEgE,KAAK,CAAA;SACrE,CAAA;KACF;IAEDc,YAAY,EAAEA,CACZvF,MAA6B,EAC7ByE,KAAmB,KACV;QACTzE,MAAM,CAAC0c,OAAO,GAAG,MAAM;YAAA,IAAAC,qBAAA,EAAAhX,IAAA,EAAAiX,qBAAA,CAAA;YACrB,MAAMC,UAAU,GAAGpY,KAAK,CAAC6D,QAAQ,EAAE,CAAC8T,YAAY,CAACpc,MAAM,CAACkF,EAAE,CAAC,CAAA;YAE3D,OAAO9B,IAAI,CAACW,GAAG,CACbX,IAAI,CAACU,GAAG,CAAA,CAAA6Y,qBAAA,GACN3c,MAAM,CAACwF,SAAS,CAACiW,OAAO,KAAAkB,IAAAA,GAAAA,qBAAA,GAAInB,mBAAmB,CAACC,OAAO,EAAA9V,CAAAA,IAAA,GACvDkX,UAAU,IAAVA,IAAAA,GAAAA,UAAU,GAAI7c,MAAM,CAACwF,SAAS,CAAC4P,IAAI,KAAA,IAAA,GAAAzP,IAAA,GAAI6V,mBAAmB,CAACpG,IAC7D,CAAC,EAAA,CAAAwH,qBAAA,GACD5c,MAAM,CAACwF,SAAS,CAACkW,OAAO,KAAAkB,IAAAA,GAAAA,qBAAA,GAAIpB,mBAAmB,CAACE,OAClD,CAAC,CAAA;SACF,CAAA;QAED1b,MAAM,CAAC8c,QAAQ,GAAG7a,IAAI,EACpBoW,QAAQ,GAAI;gBACVA,QAAQ;gBACRC,sBAAsB,CAAC7T,KAAK,EAAE4T,QAAQ,CAAC;gBACvC5T,KAAK,CAAC6D,QAAQ,EAAE,CAAC8T,YAAY;aAC9B,EACD,CAAC/D,QAAQ,EAAE1R,OAAO,GAChBA,OAAO,CACJoW,KAAK,CAAC,CAAC,EAAE/c,MAAM,CAACoY,QAAQ,CAACC,QAAQ,CAAC,CAAC,CACnCvK,MAAM,CAAC,CAACiG,GAAG,EAAE/T,MAAM,GAAK+T,GAAG,GAAG/T,MAAM,CAAC0c,OAAO,EAAE,EAAE,CAAC,CAAC,EACvD1Y,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,cAAc,EAAE,UAAU,CAC1D,CAAC,CAAA;QAEDhF,MAAM,CAACgd,QAAQ,GAAG/a,IAAI,EACpBoW,QAAQ,GAAI;gBACVA,QAAQ;gBACRC,sBAAsB,CAAC7T,KAAK,EAAE4T,QAAQ,CAAC;gBACvC5T,KAAK,CAAC6D,QAAQ,EAAE,CAAC8T,YAAY;aAC9B,EACD,CAAC/D,QAAQ,EAAE1R,OAAO,GAChBA,OAAO,CACJoW,KAAK,CAAC/c,MAAM,CAACoY,QAAQ,CAACC,QAAQ,CAAC,GAAG,CAAC,CAAC,CACpCvK,MAAM,CAAC,CAACiG,GAAG,EAAE/T,MAAM,GAAK+T,GAAG,GAAG/T,MAAM,CAAC0c,OAAO,EAAE,EAAE,CAAC,CAAC,EACvD1Y,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,cAAc,EAAE,UAAU,CAC1D,CAAC,CAAA;QAEDhF,MAAM,CAACid,SAAS,GAAG,MAAM;YACvBxY,KAAK,CAACyY,eAAe,EAACC,KAAA,IAAiC;gBAAA,IAAhC,EAAE,CAACnd,MAAM,CAACkF,EAAE,CAAA,EAAGkY,CAAC,EAAE,GAAGC,IAAAA,EAAM,GAAAF,KAAA,CAAA;gBAChD,OAAOE,IAAI,CAAA;YACb,CAAC,CAAC,CAAA;SACH,CAAA;QACDrd,MAAM,CAACsd,YAAY,GAAG,MAAM;YAAA,IAAAxL,qBAAA,EAAAC,qBAAA,CAAA;YAC1B,OACE,CAAA,CAAAD,qBAAA,GAAC9R,MAAM,CAACwF,SAAS,CAAC+X,cAAc,KAAAzL,IAAAA,GAAAA,qBAAA,GAAI,IAAI,KAAA,CAAA,CAAAC,qBAAA,GACvCtN,KAAK,CAACO,OAAO,CAACwY,oBAAoB,KAAAzL,IAAAA,GAAAA,qBAAA,GAAI,IAAI,CAAC,CAAA;SAE/C,CAAA;QACD/R,MAAM,CAACyd,aAAa,GAAG,MAAM;YAC3B,OAAOhZ,KAAK,CAAC6D,QAAQ,EAAE,CAAC+T,gBAAgB,CAACJ,gBAAgB,KAAKjc,MAAM,CAACkF,EAAE,CAAA;SACxE,CAAA;KACF;IAEDkC,YAAY,EAAEA,CACZhB,MAA6B,EAC7B3B,KAAmB,KACV;QACT2B,MAAM,CAACsW,OAAO,GAAG,MAAM;YACrB,IAAI3I,GAAG,GAAG,CAAC,CAAA;YAEX,MAAMrS,OAAO,IAAI0E,MAA6B,IAAK;gBACjD,IAAIA,MAAM,CAACoB,UAAU,CAACxF,MAAM,EAAE;oBAC5BoE,MAAM,CAACoB,UAAU,CAAC5F,OAAO,CAACF,OAAO,CAAC,CAAA;gBACpC,CAAC,MAAM;oBAAA,IAAAgc,qBAAA,CAAA;oBACL3J,GAAG,IAAA2J,CAAAA,qBAAA,GAAItX,MAAM,CAACpG,MAAM,CAAC0c,OAAO,EAAE,KAAAgB,IAAAA,GAAAA,qBAAA,GAAI,CAAC,CAAA;gBACrC,CAAA;aACD,CAAA;YAEDhc,OAAO,CAAC0E,MAAM,CAAC,CAAA;YAEf,OAAO2N,GAAG,CAAA;SACX,CAAA;QACD3N,MAAM,CAAC0W,QAAQ,GAAG,MAAM;YACtB,IAAI1W,MAAM,CAACpD,KAAK,GAAG,CAAC,EAAE;gBACpB,MAAM2a,iBAAiB,GAAGvX,MAAM,CAACuB,WAAW,CAACsC,OAAO,CAAC7D,MAAM,CAACpD,KAAK,GAAG,CAAC,CAAE,CAAA;gBACvE,OAAO2a,iBAAiB,CAACb,QAAQ,EAAE,GAAGa,iBAAiB,CAACjB,OAAO,EAAE,CAAA;YACnE,CAAA;YAEA,OAAO,CAAC,CAAA;SACT,CAAA;QACDtW,MAAM,CAACwX,gBAAgB,IAAGC,gBAAgB,IAAI;YAC5C,MAAM7d,MAAM,GAAGyE,KAAK,CAACuI,SAAS,CAAC5G,MAAM,CAACpG,MAAM,CAACkF,EAAE,CAAC,CAAA;YAChD,MAAM4Y,SAAS,GAAG9d,MAAM,IAAA,IAAA,GAAA,KAAA,CAAA,GAANA,MAAM,CAAEsd,YAAY,EAAE,CAAA;YAExC,QAAQS,CAAU,IAAK;gBACrB,IAAI,CAAC/d,MAAM,IAAI,CAAC8d,SAAS,EAAE;oBACzB,OAAA;gBACF,CAAA;gBAEEC,CAAC,CAASC,OAAO,IAAA,IAAA,IAAjBD,CAAC,CAASC,OAAO,EAAI,CAAA;gBAEvB,IAAIC,iBAAiB,CAACF,CAAC,CAAC,EAAE;oBACxB,6DAAA;oBACA,IAAIA,CAAC,CAACG,OAAO,IAAIH,CAAC,CAACG,OAAO,CAAClc,MAAM,GAAG,CAAC,EAAE;wBACrC,OAAA;oBACF,CAAA;gBACF,CAAA;gBAEA,MAAM8Z,SAAS,GAAG1V,MAAM,CAACsW,OAAO,EAAE,CAAA;gBAElC,MAAMR,iBAAqC,GAAG9V,MAAM,GAChDA,MAAM,CAACwB,cAAc,EAAE,CAACI,GAAG,EAACjH,CAAC,GAAI;wBAACA,CAAC,CAACf,MAAM,CAACkF,EAAE;wBAAEnE,CAAC,CAACf,MAAM,CAAC0c,OAAO,EAAE;qBAAC,CAAC,GACnE;oBAAC;wBAAC1c,MAAM,CAACkF,EAAE;wBAAElF,MAAM,CAAC0c,OAAO,EAAE;qBAAC;iBAAC,CAAA;gBAEnC,MAAMyB,OAAO,GAAGF,iBAAiB,CAACF,CAAC,CAAC,GAChC3a,IAAI,CAACC,KAAK,CAAC0a,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC,CAAEC,OAAO,CAAC,GAChCJ,CAAC,CAAgBI,OAAO,CAAA;gBAE7B,MAAMC,eAAkC,GAAG,CAAA,CAAE,CAAA;gBAE7C,MAAMC,YAAY,GAAGA,CACnBC,SAAyB,EACzBC,UAAmB,KAChB;oBACH,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;wBAClC,OAAA;oBACF,CAAA;oBAEA9Z,KAAK,CAAC+Z,mBAAmB,EAAC3d,GAAG,IAAI;wBAAA,IAAA4d,gBAAA,EAAAC,cAAA,CAAA;wBAC/B,MAAMC,cAAc,GAClBla,KAAK,CAACO,OAAO,CAACuX,qBAAqB,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;wBACxD,MAAMR,WAAW,GACf,CAACwC,UAAU,GAAA,CAAAE,CAAAA,gBAAA,GAAI5d,GAAG,IAAA,IAAA,GAAA,KAAA,CAAA,GAAHA,GAAG,CAAEgb,WAAW,KAAA4C,IAAAA,GAAAA,gBAAA,GAAI,CAAC,CAAC,IAAIE,cAAc,CAAA;wBACzD,MAAM3C,eAAe,GAAG5Y,IAAI,CAACU,GAAG,CAC9BiY,WAAW,GAAA,CAAA2C,CAAAA,cAAA,GAAI7d,GAAG,IAAA,OAAA,KAAA,IAAHA,GAAG,CAAEib,SAAS,KAAA,IAAA,GAAA4C,cAAA,GAAI,CAAC,CAAC,EACnC,CAAC,QACH,CAAC,CAAA;wBAED7d,GAAG,CAACqb,iBAAiB,CAACta,OAAO,EAACgd,KAAA,IAA4B;4BAAA,IAA3B,CAACja,QAAQ,EAAEka,UAAU,CAAC,GAAAD,KAAA,CAAA;4BACnDR,eAAe,CAACzZ,QAAQ,CAAC,GACvBvB,IAAI,CAACC,KAAK,CACRD,IAAI,CAACU,GAAG,CAAC+a,UAAU,GAAGA,UAAU,GAAG7C,eAAe,EAAE,CAAC,CAAC,GAAG,GAC3D,CAAC,GAAG,GAAG,CAAA;wBACX,CAAC,CAAC,CAAA;wBAEF,OAAO;4BACL,GAAGnb,GAAG;4BACNkb,WAAW;4BACXC,eAAAA;yBACD,CAAA;oBACH,CAAC,CAAC,CAAA;oBAEF,IACEvX,KAAK,CAACO,OAAO,CAACsX,gBAAgB,KAAK,UAAU,IAC7CgC,SAAS,KAAK,KAAK,EACnB;wBACA7Z,KAAK,CAACyY,eAAe,EAACrc,GAAG,GAAA,CAAK;gCAC5B,GAAGA,GAAG;gCACN,GAAGud,eAAAA;4BACL,CAAC,CAAC,CAAC,CAAA;oBACL,CAAA;iBACD,CAAA;gBAED,MAAMU,MAAM,IAAIP,UAAmB,GAAKF,YAAY,CAAC,MAAM,EAAEE,UAAU,CAAC,CAAA;gBAExE,MAAMQ,KAAK,IAAIR,UAAmB,IAAK;oBACrCF,YAAY,CAAC,KAAK,EAAEE,UAAU,CAAC,CAAA;oBAE/B9Z,KAAK,CAAC+Z,mBAAmB,EAAC3d,GAAG,GAAA,CAAK;4BAChC,GAAGA,GAAG;4BACNob,gBAAgB,EAAE,KAAK;4BACvBJ,WAAW,EAAE,IAAI;4BACjBC,SAAS,EAAE,IAAI;4BACfC,WAAW,EAAE,IAAI;4BACjBC,eAAe,EAAE,IAAI;4BACrBE,iBAAiB,EAAE,EAAA;wBACrB,CAAC,CAAC,CAAC,CAAA;iBACJ,CAAA;gBAED,MAAM8C,eAAe,GACnBnB,gBAAgB,IAAI,OAAOoB,QAAQ,KAAK,WAAW,GAAGA,QAAQ,GAAG,IAAI,CAAA;gBAEvE,MAAMC,WAAW,GAAG;oBAClBC,WAAW,GAAGpB,CAAa,GAAKe,MAAM,CAACf,CAAC,CAACI,OAAO,CAAC;oBACjDiB,SAAS,GAAGrB,CAAa,IAAK;wBAC5BiB,eAAe,IAAA,IAAA,IAAfA,eAAe,CAAEK,mBAAmB,CAClC,WAAW,EACXH,WAAW,CAACC,WACd,CAAC,CAAA;wBACDH,eAAe,IAAA,IAAA,IAAfA,eAAe,CAAEK,mBAAmB,CAClC,SAAS,EACTH,WAAW,CAACE,SACd,CAAC,CAAA;wBACDL,KAAK,CAAChB,CAAC,CAACI,OAAO,CAAC,CAAA;oBAClB,CAAA;iBACD,CAAA;gBAED,MAAMmB,WAAW,GAAG;oBAClBH,WAAW,GAAGpB,CAAa,IAAK;wBAC9B,IAAIA,CAAC,CAACwB,UAAU,EAAE;4BAChBxB,CAAC,CAACyB,cAAc,EAAE,CAAA;4BAClBzB,CAAC,CAAC0B,eAAe,EAAE,CAAA;wBACrB,CAAA;wBACAX,MAAM,CAACf,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC,CAAEC,OAAO,CAAC,CAAA;wBAC7B,OAAO,KAAK,CAAA;qBACb;oBACDiB,SAAS,GAAGrB,CAAa,IAAK;wBAAA,IAAA2B,WAAA,CAAA;wBAC5BV,eAAe,IAAA,IAAA,IAAfA,eAAe,CAAEK,mBAAmB,CAClC,WAAW,EACXC,WAAW,CAACH,WACd,CAAC,CAAA;wBACDH,eAAe,IAAA,IAAA,IAAfA,eAAe,CAAEK,mBAAmB,CAClC,UAAU,EACVC,WAAW,CAACF,SACd,CAAC,CAAA;wBACD,IAAIrB,CAAC,CAACwB,UAAU,EAAE;4BAChBxB,CAAC,CAACyB,cAAc,EAAE,CAAA;4BAClBzB,CAAC,CAAC0B,eAAe,EAAE,CAAA;wBACrB,CAAA;wBACAV,KAAK,CAAAW,CAAAA,WAAA,GAAC3B,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC,KAAA,IAAA,GAAA,KAAA,CAAA,GAAZwB,WAAA,CAAcvB,OAAO,CAAC,CAAA;oBAC9B,CAAA;iBACD,CAAA;gBAED,MAAMwB,kBAAkB,GAAGC,qBAAqB,EAAE,GAC9C;oBAAEC,OAAO,EAAE,KAAA;gBAAM,CAAC,GAClB,KAAK,CAAA;gBAET,IAAI5B,iBAAiB,CAACF,CAAC,CAAC,EAAE;oBACxBiB,eAAe,IAAfA,IAAAA,IAAAA,eAAe,CAAEc,gBAAgB,CAC/B,WAAW,EACXR,WAAW,CAACH,WAAW,EACvBQ,kBACF,CAAC,CAAA;oBACDX,eAAe,IAAfA,IAAAA,IAAAA,eAAe,CAAEc,gBAAgB,CAC/B,UAAU,EACVR,WAAW,CAACF,SAAS,EACrBO,kBACF,CAAC,CAAA;gBACH,CAAC,MAAM;oBACLX,eAAe,IAAfA,IAAAA,IAAAA,eAAe,CAAEc,gBAAgB,CAC/B,WAAW,EACXZ,WAAW,CAACC,WAAW,EACvBQ,kBACF,CAAC,CAAA;oBACDX,eAAe,IAAfA,IAAAA,IAAAA,eAAe,CAAEc,gBAAgB,CAC/B,SAAS,EACTZ,WAAW,CAACE,SAAS,EACrBO,kBACF,CAAC,CAAA;gBACH,CAAA;gBAEAlb,KAAK,CAAC+Z,mBAAmB,EAAC3d,GAAG,GAAA,CAAK;wBAChC,GAAGA,GAAG;wBACNgb,WAAW,EAAEsC,OAAO;wBACpBrC,SAAS;wBACTC,WAAW,EAAE,CAAC;wBACdC,eAAe,EAAE,CAAC;wBAClBE,iBAAiB;wBACjBD,gBAAgB,EAAEjc,MAAM,CAACkF,EAAAA;oBAC3B,CAAC,CAAC,CAAC,CAAA;aACJ,CAAA;SACF,CAAA;KACF;IAEDgD,WAAW,GAA0BzD,KAAmB,IAAW;QACjEA,KAAK,CAACyY,eAAe,IAAG5c,OAAO,GAC7BmE,KAAK,CAACO,OAAO,CAACwX,oBAAoB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAlC/X,KAAK,CAACO,OAAO,CAACwX,oBAAoB,CAAGlc,OAAO,CAAC,CAAA;QAC/CmE,KAAK,CAAC+Z,mBAAmB,IAAGle,OAAO,GACjCmE,KAAK,CAACO,OAAO,CAACyX,wBAAwB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAtChY,KAAK,CAACO,OAAO,CAACyX,wBAAwB,CAAGnc,OAAO,CAAC,CAAA;QACnDmE,KAAK,CAACsb,iBAAiB,IAAGvM,YAAY,IAAI;YAAA,IAAAC,qBAAA,CAAA;YACxChP,KAAK,CAACyY,eAAe,CACnB1J,YAAY,GAAG,CAAA,CAAE,GAAA,CAAAC,qBAAA,GAAGhP,KAAK,CAACkP,YAAY,CAACyI,YAAY,KAAA,IAAA,GAAA3I,qBAAA,GAAI,CAAA,CACzD,CAAC,CAAA;SACF,CAAA;QACDhP,KAAK,CAACub,mBAAmB,IAAGxM,YAAY,IAAI;YAAA,IAAAyM,sBAAA,CAAA;YAC1Cxb,KAAK,CAAC+Z,mBAAmB,CACvBhL,YAAY,GACRoI,+BAA+B,EAAE,GAAA,CAAAqE,sBAAA,GACjCxb,KAAK,CAACkP,YAAY,CAAC0I,gBAAgB,KAAA,IAAA,GAAA4D,sBAAA,GACjCrE,+BAA+B,EACvC,CAAC,CAAA;SACF,CAAA;QACDnX,KAAK,CAACyb,YAAY,GAAG,MAAA;YAAA,IAAAC,qBAAA,EAAAC,sBAAA,CAAA;YAAA,OAAAD,CAAAA,qBAAA,GAAA,CAAAC,sBAAA,GACnB3b,KAAK,CAAC0D,eAAe,EAAE,CAAC,CAAC,CAAC,KAA1BiY,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,sBAAA,CAA4BnW,OAAO,CAAC6D,MAAM,CAAC,CAACiG,GAAG,EAAE3N,MAAM,KAAK;gBAC1D,OAAO2N,GAAG,GAAG3N,MAAM,CAACsW,OAAO,EAAE,CAAA;YAC/B,CAAC,EAAE,CAAC,CAAC,KAAAyD,IAAAA,GAAAA,qBAAA,GAAI,CAAC,CAAA;QAAA,CAAA,CAAA;QACZ1b,KAAK,CAAC4b,gBAAgB,GAAG,MAAA;YAAA,IAAAC,qBAAA,EAAAC,sBAAA,CAAA;YAAA,OAAAD,CAAAA,qBAAA,GAAA,CAAAC,sBAAA,GACvB9b,KAAK,CAAC6E,mBAAmB,EAAE,CAAC,CAAC,CAAC,KAA9BiX,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,sBAAA,CAAgCtW,OAAO,CAAC6D,MAAM,CAAC,CAACiG,GAAG,EAAE3N,MAAM,KAAK;gBAC9D,OAAO2N,GAAG,GAAG3N,MAAM,CAACsW,OAAO,EAAE,CAAA;YAC/B,CAAC,EAAE,CAAC,CAAC,KAAA4D,IAAAA,GAAAA,qBAAA,GAAI,CAAC,CAAA;QAAA,CAAA,CAAA;QACZ7b,KAAK,CAAC+b,kBAAkB,GAAG,MAAA;YAAA,IAAAC,qBAAA,EAAAC,sBAAA,CAAA;YAAA,OAAAD,CAAAA,qBAAA,GAAA,CAAAC,sBAAA,GACzBjc,KAAK,CAAC4E,qBAAqB,EAAE,CAAC,CAAC,CAAC,KAAhCqX,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,sBAAA,CAAkCzW,OAAO,CAAC6D,MAAM,CAAC,CAACiG,GAAG,EAAE3N,MAAM,KAAK;gBAChE,OAAO2N,GAAG,GAAG3N,MAAM,CAACsW,OAAO,EAAE,CAAA;YAC/B,CAAC,EAAE,CAAC,CAAC,KAAA+D,IAAAA,GAAAA,qBAAA,GAAI,CAAC,CAAA;QAAA,CAAA,CAAA;QACZhc,KAAK,CAACkc,iBAAiB,GAAG,MAAA;YAAA,IAAAC,qBAAA,EAAAC,sBAAA,CAAA;YAAA,OAAAD,CAAAA,qBAAA,GAAA,CAAAC,sBAAA,GACxBpc,KAAK,CAACgF,oBAAoB,EAAE,CAAC,CAAC,CAAC,KAA/BoX,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,sBAAA,CAAiC5W,OAAO,CAAC6D,MAAM,CAAC,CAACiG,GAAG,EAAE3N,MAAM,KAAK;gBAC/D,OAAO2N,GAAG,GAAG3N,MAAM,CAACsW,OAAO,EAAE,CAAA;YAC/B,CAAC,EAAE,CAAC,CAAC,KAAAkE,IAAAA,GAAAA,qBAAA,GAAI,CAAC,CAAA;QAAA,CAAA,CAAA;IACd,CAAA;AACF,EAAC;AAED,IAAIE,gBAAgC,GAAG,IAAI,CAAA;AACpC,SAASlB,qBAAqBA,GAAG;IACtC,IAAI,OAAOkB,gBAAgB,KAAK,SAAS,EAAE,OAAOA,gBAAgB,CAAA;IAElE,IAAIC,SAAS,GAAG,KAAK,CAAA;IACrB,IAAI;QACF,MAAM/b,OAAO,GAAG;YACd,IAAI6a,OAAOA,IAAG;gBACZkB,SAAS,GAAG,IAAI,CAAA;gBAChB,OAAO,KAAK,CAAA;YACd,CAAA;SACD,CAAA;QAED,MAAMvgB,IAAI,GAAGA,KAAM,CAAA,AAAE,CAAA;QAErBwgB,MAAM,CAAClB,gBAAgB,CAAC,MAAM,EAAEtf,IAAI,EAAEwE,OAAO,CAAC,CAAA;QAC9Cgc,MAAM,CAAC3B,mBAAmB,CAAC,MAAM,EAAE7e,IAAI,CAAC,CAAA;KACzC,CAAC,OAAOygB,GAAG,EAAE;QACZF,SAAS,GAAG,KAAK,CAAA;IACnB,CAAA;IACAD,gBAAgB,GAAGC,SAAS,CAAA;IAC5B,OAAOD,gBAAgB,CAAA;AACzB,CAAA;AAEA,SAAS7C,iBAAiBA,CAACF,CAAU,EAAmB;IACtD,OAAQA,CAAC,CAAgBmD,IAAI,KAAK,YAAY,CAAA;AAChD;AC7aA,EAAA;AAEO,MAAMC,gBAA8B,GAAG;IAC5CrQ,eAAe,GAAGC,KAAK,IAA2B;QAChD,OAAO;YACLqQ,gBAAgB,EAAE,CAAA,CAAE;YACpB,GAAGrQ,KAAAA;SACJ,CAAA;KACF;IAEDE,iBAAiB,GACfxM,KAAmB,IACU;QAC7B,OAAO;YACL4c,wBAAwB,EAAE5gB,gBAAgB,CAAC,kBAAkB,EAAEgE,KAAK,CAAA;SACrE,CAAA;KACF;IAEDc,YAAY,EAAEA,CACZvF,MAA6B,EAC7ByE,KAAmB,KACV;QACTzE,MAAM,CAACshB,gBAAgB,IAAG7P,KAAK,IAAI;YACjC,IAAIzR,MAAM,CAACuhB,UAAU,EAAE,EAAE;gBACvB9c,KAAK,CAAC+c,mBAAmB,EAAC3gB,GAAG,GAAA,CAAK;wBAChC,GAAGA,GAAG;wBACN,CAACb,MAAM,CAACkF,EAAE,CAAA,EAAGuM,KAAK,IAAA,IAAA,GAALA,KAAK,GAAI,CAACzR,MAAM,CAACyL,YAAY,EAAC;oBAC7C,CAAC,CAAC,CAAC,CAAA;YACL,CAAA;SACD,CAAA;QACDzL,MAAM,CAACyL,YAAY,GAAG,MAAM;YAAA,IAAA9F,IAAA,EAAA4M,qBAAA,CAAA;YAC1B,MAAMkP,YAAY,GAAGzhB,MAAM,CAAC2G,OAAO,CAAA;YACnC,OAAA,CAAAhB,IAAA,GACG8b,YAAY,CAACzf,MAAM,GAChByf,YAAY,CAAC3e,IAAI,EAAC4e,CAAC,GAAIA,CAAC,CAACjW,YAAY,EAAE,CAAC,GAAA,CAAA8G,qBAAA,GACxC9N,KAAK,CAAC6D,QAAQ,EAAE,CAAC8Y,gBAAgB,KAAA,IAAA,GAAA,KAAA,CAAA,GAAjC7O,qBAAA,CAAoCvS,MAAM,CAACkF,EAAE,CAAC,KAAAS,IAAAA,GAAAA,IAAA,GAAK,IAAI,CAAA;SAE9D,CAAA;QAED3F,MAAM,CAACuhB,UAAU,GAAG,MAAM;YAAA,IAAAzP,qBAAA,EAAAC,qBAAA,CAAA;YACxB,OACE,CAAA,CAAAD,qBAAA,GAAC9R,MAAM,CAACwF,SAAS,CAACmc,YAAY,KAAA7P,IAAAA,GAAAA,qBAAA,GAAI,IAAI,KAAA,CAAA,CAAAC,qBAAA,GACrCtN,KAAK,CAACO,OAAO,CAAC2c,YAAY,KAAA5P,IAAAA,GAAAA,qBAAA,GAAI,IAAI,CAAC,CAAA;SAEvC,CAAA;QACD/R,MAAM,CAAC4hB,0BAA0B,GAAG,MAAM;YACxC,QAAQ7D,CAAU,IAAK;gBACrB/d,MAAM,CAACshB,gBAAgB,IAAvBthB,IAAAA,IAAAA,MAAM,CAACshB,gBAAgB,CACnBvD,CAAC,CAAgB8D,MAAM,CAAsBC,OACjD,CAAC,CAAA;aACF,CAAA;SACF,CAAA;KACF;IAEDtV,SAAS,EAAEA,CACT9H,GAAe,EACfD,KAAmB,KACV;QACTC,GAAG,CAACgW,mBAAmB,GAAGzY,IAAI,CAC5B,IAAM;gBAACyC,GAAG,CAACgJ,WAAW,EAAE;gBAAEjJ,KAAK,CAAC6D,QAAQ,EAAE,CAAC8Y,gBAAgB;aAAC,GAC5DvG,KAAK,IAAI;YACP,OAAOA,KAAK,CAAC9R,MAAM,EAACjE,IAAI,GAAIA,IAAI,CAAC9E,MAAM,CAACyL,YAAY,EAAE,CAAC,CAAA;SACxD,EACDzH,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,WAAW,EAAE,qBAAqB,CAClE,CAAC,CAAA;QACDN,GAAG,CAACqd,eAAe,GAAG9f,IAAI,CACxB,IAAM;gBACJyC,GAAG,CAACkW,mBAAmB,EAAE;gBACzBlW,GAAG,CAAC+V,qBAAqB,EAAE;gBAC3B/V,GAAG,CAACoW,oBAAoB,EAAE;aAC3B,EACD,CAACtS,IAAI,EAAEoC,MAAM,EAAEnC,KAAK,GAAK,CAAC;mBAAGD,IAAI,EAAE;mBAAGoC,MAAM,EAAE;mBAAGnC,KAAK;aAAC,EACvDzE,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,WAAW,EAAE,iBAAiB,CAC9D,CAAC,CAAA;KACF;IAEDkD,WAAW,GAA0BzD,KAAmB,IAAW;QACjE,MAAMud,wBAAwB,GAAGA,CAC/BthB,GAAW,EACXuhB,UAA0C,KACL;YACrC,OAAOhgB,IAAI,CACT,IAAM;oBACJggB,UAAU,EAAE;oBACZA,UAAU,EAAE,CACTlZ,MAAM,EAAChI,CAAC,GAAIA,CAAC,CAAC0K,YAAY,EAAE,CAAC,CAC7BzD,GAAG,EAACjH,CAAC,GAAIA,CAAC,CAACmE,EAAE,CAAC,CACd0G,IAAI,CAAC,GAAG,CAAC;iBACb,GACDjF,OAAO,IAAI;gBACT,OAAOA,OAAO,CAACoC,MAAM,EAAChI,CAAC,GAAIA,CAAC,CAAC0K,YAAY,IAAA,OAAA,KAAA,IAAd1K,CAAC,CAAC0K,YAAY,EAAI,CAAC,CAAA;aAC/C,EACDzH,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,cAAc,EAAEtE,GAAG,CACnD,CAAC,CAAA;SACF,CAAA;QAED+D,KAAK,CAACyd,qBAAqB,GAAGF,wBAAwB,CACpD,uBAAuB,EACvB,IAAMvd,KAAK,CAAC0d,iBAAiB,EAC/B,CAAC,CAAA;QACD1d,KAAK,CAAC4D,qBAAqB,GAAG2Z,wBAAwB,CACpD,uBAAuB,EACvB,IAAMvd,KAAK,CAACkJ,iBAAiB,EAC/B,CAAC,CAAA;QACDlJ,KAAK,CAAC2d,yBAAyB,GAAGJ,wBAAwB,CACxD,2BAA2B,EAC3B,IAAMvd,KAAK,CAAC4W,kBAAkB,EAChC,CAAC,CAAA;QACD5W,KAAK,CAAC4d,0BAA0B,GAAGL,wBAAwB,CACzD,4BAA4B,EAC5B,IAAMvd,KAAK,CAAC6W,mBAAmB,EACjC,CAAC,CAAA;QACD7W,KAAK,CAAC6d,2BAA2B,GAAGN,wBAAwB,CAC1D,6BAA6B,EAC7B,IAAMvd,KAAK,CAAC8W,oBAAoB,EAClC,CAAC,CAAA;QAED9W,KAAK,CAAC+c,mBAAmB,IAAGlhB,OAAO,GACjCmE,KAAK,CAACO,OAAO,CAACqc,wBAAwB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAtC5c,KAAK,CAACO,OAAO,CAACqc,wBAAwB,CAAG/gB,OAAO,CAAC,CAAA;QAEnDmE,KAAK,CAAC8d,qBAAqB,IAAG/O,YAAY,IAAI;YAAA,IAAAC,qBAAA,CAAA;YAC5ChP,KAAK,CAAC+c,mBAAmB,CACvBhO,YAAY,GAAG,CAAA,CAAE,GAAA,CAAAC,qBAAA,GAAGhP,KAAK,CAACkP,YAAY,CAACyN,gBAAgB,KAAA,IAAA,GAAA3N,qBAAA,GAAI,CAAA,CAC7D,CAAC,CAAA;SACF,CAAA;QAEDhP,KAAK,CAAC+d,uBAAuB,IAAG/Q,KAAK,IAAI;YAAA,IAAAgR,MAAA,CAAA;YACvChR,KAAK,GAAAgR,CAAAA,MAAA,GAAGhR,KAAK,KAAAgR,IAAAA,GAAAA,MAAA,GAAI,CAAChe,KAAK,CAACie,sBAAsB,EAAE,CAAA;YAEhDje,KAAK,CAAC+c,mBAAmB,CACvB/c,KAAK,CAACkJ,iBAAiB,EAAE,CAACG,MAAM,CAC9B,CAAC6U,GAAG,EAAE3iB,MAAM,GAAA,CAAM;oBAChB,GAAG2iB,GAAG;oBACN,CAAC3iB,MAAM,CAACkF,EAAE,CAAA,EAAG,CAACuM,KAAK,GAAG,CAAA,CAACzR,MAAM,CAACuhB,UAAU,IAAjBvhB,IAAAA,IAAAA,MAAM,CAACuhB,UAAU,EAAI,CAAG9P,GAAAA,KAAAA;gBACjD,CAAC,CAAC,EACF,CAAA,CACF,CACF,CAAC,CAAA;SACF,CAAA;QAEDhN,KAAK,CAACie,sBAAsB,GAAG,IAC7B,CAACje,KAAK,CAACkJ,iBAAiB,EAAE,CAAC7K,IAAI,EAAC9C,MAAM,GAAI,CAAA,CAACA,MAAM,CAACyL,YAAY,IAAnBzL,IAAAA,IAAAA,MAAM,CAACyL,YAAY,EAAI,CAAC,CAAA,CAAA;QAErEhH,KAAK,CAACme,uBAAuB,GAAG,IAC9Bne,KAAK,CAACkJ,iBAAiB,EAAE,CAAC7K,IAAI,EAAC9C,MAAM,GAAIA,MAAM,CAACyL,YAAY,IAAA,IAAA,GAAA,KAAA,CAAA,GAAnBzL,MAAM,CAACyL,YAAY,EAAI,CAAC,CAAA;QAEnEhH,KAAK,CAACoe,oCAAoC,GAAG,MAAM;YACjD,QAAQ9E,CAAU,IAAK;gBAAA,IAAA+E,OAAA,CAAA;gBACrBre,KAAK,CAAC+d,uBAAuB,CAAAM,CAAAA,OAAA,GACzB/E,CAAC,CAAgB8D,MAAM,KAAzBiB,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,OAAA,CAAgDhB,OAClD,CAAC,CAAA;aACF,CAAA;SACF,CAAA;IACH,CAAA;AACF,EAAC;AAEM,SAASxJ,sBAAsBA,CACpC7T,KAAmB,EACnB4T,QAA2C,EAC3C;IACA,OAAO,CAACA,QAAQ,GACZ5T,KAAK,CAAC4D,qBAAqB,EAAE,GAC7BgQ,QAAQ,KAAK,QAAQ,GACnB5T,KAAK,CAAC6d,2BAA2B,EAAE,GACnCjK,QAAQ,KAAK,MAAM,GACjB5T,KAAK,CAAC2d,yBAAyB,EAAE,GACjC3d,KAAK,CAAC4d,0BAA0B,EAAE,CAAA;AAC5C;ACjSA,EAAA;AAEO,MAAMU,cAA4B,GAAG;IAC1C7a,WAAW,GAA0BzD,KAAmB,IAAW;QACjEA,KAAK,CAACue,yBAAyB,GAC7Bve,KAAK,CAACO,OAAO,CAACmJ,kBAAkB,IAChC1J,KAAK,CAACO,OAAO,CAACmJ,kBAAkB,CAAC1J,KAAK,EAAE,YAAY,CAAC,CAAA;QAEvDA,KAAK,CAACwe,wBAAwB,GAAG,MAAM;YACrC,IAAIxe,KAAK,CAACO,OAAO,CAAC8O,eAAe,IAAI,CAACrP,KAAK,CAACue,yBAAyB,EAAE;gBACrE,OAAOve,KAAK,CAAC2J,sBAAsB,EAAE,CAAA;YACvC,CAAA;YAEA,OAAO3J,KAAK,CAACue,yBAAyB,EAAE,CAAA;SACzC,CAAA;QAEDve,KAAK,CAACye,6BAA6B,GACjCze,KAAK,CAACO,OAAO,CAACsJ,sBAAsB,IACpC7J,KAAK,CAACO,OAAO,CAACsJ,sBAAsB,CAAC7J,KAAK,EAAE,YAAY,CAAC,CAAA;QAC3DA,KAAK,CAAC0e,4BAA4B,GAAG,MAAM;YACzC,IAAI,CAAC1e,KAAK,CAACye,6BAA6B,EAAE;gBACxC,OAAO,IAAI3U,GAAG,EAAE,CAAA;YAClB,CAAA;YAEA,OAAO9J,KAAK,CAACye,6BAA6B,EAAE,CAAA;SAC7C,CAAA;QAEDze,KAAK,CAAC2e,6BAA6B,GACjC3e,KAAK,CAACO,OAAO,CAACyJ,sBAAsB,IACpChK,KAAK,CAACO,OAAO,CAACyJ,sBAAsB,CAAChK,KAAK,EAAE,YAAY,CAAC,CAAA;QAC3DA,KAAK,CAAC4e,4BAA4B,GAAG,MAAM;YACzC,IAAI,CAAC5e,KAAK,CAAC2e,6BAA6B,EAAE;gBACxC,OAAA;YACF,CAAA;YAEA,OAAO3e,KAAK,CAAC2e,6BAA6B,EAAE,CAAA;SAC7C,CAAA;IACH,CAAA;AACF;AC4BA,EAAA;AAEO,MAAME,eAA6B,GAAG;IAC3CxS,eAAe,GAAGC,KAAK,IAA6B;QAClD,OAAO;YACLwS,YAAY,EAAEpd,SAAS;YACvB,GAAG4K,KAAAA;SACJ,CAAA;KACF;IAEDE,iBAAiB,GACfxM,KAAmB,IACY;QAC/B,OAAO;YACL+e,oBAAoB,EAAE/iB,gBAAgB,CAAC,cAAc,EAAEgE,KAAK,CAAC;YAC7Dgf,cAAc,EAAE,MAAM;YACtBC,wBAAwB,GAAE1jB,MAAM,IAAI;gBAAA,IAAA2jB,qBAAA,CAAA;gBAClC,MAAMlS,KAAK,GAAA,CAAAkS,qBAAA,GAAGlf,KAAK,CAChB8M,eAAe,EAAE,CACjBC,QAAQ,CAAC,CAAC,CAAC,KAAAmS,IAAAA,IAAAA,CAAAA,qBAAA,GAFAA,qBAAA,CAEE/V,sBAAsB,EAAE,CACrC5N,MAAM,CAACkF,EAAE,CAAC,KAHCye,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,qBAAA,CAGC5e,QAAQ,EAAE,CAAA;gBAEzB,OAAO,OAAO0M,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,CAAA;YAC/D,CAAA;SACD,CAAA;KACF;IAEDlM,YAAY,EAAEA,CACZvF,MAA8B,EAC9ByE,KAAmB,KACV;QACTzE,MAAM,CAAC4jB,kBAAkB,GAAG,MAAM;YAAA,IAAA9R,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAA6R,qBAAA,CAAA;YAChC,OACE,CAAA/R,CAAAA,qBAAA,GAAC9R,MAAM,CAACwF,SAAS,CAACse,kBAAkB,KAAAhS,IAAAA,GAAAA,qBAAA,GAAI,IAAI,KAAA,CAAA,CAAAC,qBAAA,GAC3CtN,KAAK,CAACO,OAAO,CAAC8e,kBAAkB,KAAA,IAAA,GAAA/R,qBAAA,GAAI,IAAI,CAAC,IAAA,CAAAC,CAAAA,sBAAA,GACzCvN,KAAK,CAACO,OAAO,CAACmN,aAAa,KAAA,IAAA,GAAAH,sBAAA,GAAI,IAAI,CAAC,IAAA,CAAA6R,CAAAA,qBAAA,GACpCpf,KAAK,CAACO,OAAO,CAAC0e,wBAAwB,IAAA,OAAA,KAAA,IAAtCjf,KAAK,CAACO,OAAO,CAAC0e,wBAAwB,CAAG1jB,MAAM,CAAC,KAAA,OAAA6jB,qBAAA,GAAI,IAAI,CAAC,IAC1D,CAAC,CAAC7jB,MAAM,CAACC,UAAU,CAAA;SAEtB,CAAA;KACF;IAEDiI,WAAW,GAA0BzD,KAAmB,IAAW;QACjEA,KAAK,CAACsf,qBAAqB,GAAG,MAAM;YAClC,OAAOrT,SAAS,CAAChC,cAAc,CAAA;SAChC,CAAA;QAEDjK,KAAK,CAACuf,iBAAiB,GAAG,MAAM;YAAA,IAAArS,qBAAA,EAAAC,sBAAA,CAAA;YAC9B,MAAM,EAAE6R,cAAc,EAAEA,cAAAA,EAAgB,GAAGhf,KAAK,CAACO,OAAO,CAAA;YAExD,OAAOlE,UAAU,CAAC2iB,cAAc,CAAC,GAC7BA,cAAc,GACdA,cAAc,KAAK,MAAM,GACvBhf,KAAK,CAACsf,qBAAqB,EAAE,GAAApS,CAAAA,qBAAA,GAAAC,CAAAA,sBAAA,GAC7BnN,KAAK,CAACO,OAAO,CAAC0L,SAAS,KAAvBkB,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,sBAAA,CAA0B6R,cAAc,CAAW,KAAA9R,IAAAA,GAAAA,qBAAA,GACnDjB,SAAS,CAAC+S,cAAc,CAAoB,CAAA;SACnD,CAAA;QAEDhf,KAAK,CAACwf,eAAe,IAAG3jB,OAAO,IAAI;YACjCmE,KAAK,CAACO,OAAO,CAACwe,oBAAoB,IAAlC/e,IAAAA,IAAAA,KAAK,CAACO,OAAO,CAACwe,oBAAoB,CAAGljB,OAAO,CAAC,CAAA;SAC9C,CAAA;QAEDmE,KAAK,CAACyf,iBAAiB,IAAG1Q,YAAY,IAAI;YACxC/O,KAAK,CAACwf,eAAe,CACnBzQ,YAAY,GAAGrN,SAAS,GAAG1B,KAAK,CAACkP,YAAY,CAAC4P,YAChD,CAAC,CAAA;SACF,CAAA;IACH,CAAA;AACF;ACKA,EAAA;AAEO,MAAMY,YAA0B,GAAG;IACxCrT,eAAe,GAAGC,KAAK,IAAyB;QAC9C,OAAO;YACLqT,QAAQ,EAAE,CAAA,CAAE;YACZ,GAAGrT,KAAAA;SACJ,CAAA;KACF;IAEDE,iBAAiB,GACfxM,KAAmB,IACQ;QAC3B,OAAO;YACL4f,gBAAgB,EAAE5jB,gBAAgB,CAAC,UAAU,EAAEgE,KAAK,CAAC;YACrD6f,oBAAoB,EAAE,IAAA;SACvB,CAAA;KACF;IAEDpc,WAAW,GAA0BzD,KAAmB,IAAW;QACjE,IAAI8f,UAAU,GAAG,KAAK,CAAA;QACtB,IAAIC,MAAM,GAAG,KAAK,CAAA;QAElB/f,KAAK,CAACggB,kBAAkB,GAAG,MAAM;YAAA,IAAA9e,IAAA,EAAA+e,qBAAA,CAAA;YAC/B,IAAI,CAACH,UAAU,EAAE;gBACf9f,KAAK,CAACkgB,MAAM,CAAC,MAAM;oBACjBJ,UAAU,GAAG,IAAI,CAAA;gBACnB,CAAC,CAAC,CAAA;gBACF,OAAA;YACF,CAAA;YAEA,IAAA5e,CAAAA,IAAA,GAAA+e,CAAAA,qBAAA,GACEjgB,KAAK,CAACO,OAAO,CAAC4f,YAAY,KAAAF,IAAAA,GAAAA,qBAAA,GAC1BjgB,KAAK,CAACO,OAAO,CAAC6f,iBAAiB,KAAA,IAAA,GAAAlf,IAAA,GAC/B,CAAClB,KAAK,CAACO,OAAO,CAAC8f,eAAe,EAC9B;gBACA,IAAIN,MAAM,EAAE,OAAA;gBACZA,MAAM,GAAG,IAAI,CAAA;gBACb/f,KAAK,CAACkgB,MAAM,CAAC,MAAM;oBACjBlgB,KAAK,CAACsgB,aAAa,EAAE,CAAA;oBACrBP,MAAM,GAAG,KAAK,CAAA;gBAChB,CAAC,CAAC,CAAA;YACJ,CAAA;SACD,CAAA;QACD/f,KAAK,CAACugB,WAAW,IAAG1kB,OAAO,GAAImE,KAAK,CAACO,OAAO,CAACqf,gBAAgB,IAAA,IAAA,GAAA,KAAA,CAAA,GAA9B5f,KAAK,CAACO,OAAO,CAACqf,gBAAgB,CAAG/jB,OAAO,CAAC,CAAA;QACxEmE,KAAK,CAACwgB,qBAAqB,IAAGb,QAAQ,IAAI;YACxC,IAAIA,QAAQ,IAARA,IAAAA,GAAAA,QAAQ,GAAI,CAAC3f,KAAK,CAACygB,oBAAoB,EAAE,EAAE;gBAC7CzgB,KAAK,CAACugB,WAAW,CAAC,IAAI,CAAC,CAAA;YACzB,CAAC,MAAM;gBACLvgB,KAAK,CAACugB,WAAW,CAAC,CAAA,CAAE,CAAC,CAAA;YACvB,CAAA;SACD,CAAA;QACDvgB,KAAK,CAACsgB,aAAa,IAAGvR,YAAY,IAAI;YAAA,IAAA2R,qBAAA,EAAAzR,mBAAA,CAAA;YACpCjP,KAAK,CAACugB,WAAW,CAACxR,YAAY,GAAG,CAAA,CAAE,GAAA,CAAA2R,qBAAA,GAAA,CAAAzR,mBAAA,GAAGjP,KAAK,CAACkP,YAAY,KAAlBD,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,mBAAA,CAAoB0Q,QAAQ,KAAA,OAAAe,qBAAA,GAAI,CAAA,CAAE,CAAC,CAAA;SAC1E,CAAA;QACD1gB,KAAK,CAAC2gB,oBAAoB,GAAG,MAAM;YACjC,OAAO3gB,KAAK,CACT4gB,wBAAwB,EAAE,CAC1B7T,QAAQ,CAAC1O,IAAI,EAAC4B,GAAG,GAAIA,GAAG,CAAC4gB,YAAY,EAAE,CAAC,CAAA;SAC5C,CAAA;QACD7gB,KAAK,CAAC8gB,+BAA+B,GAAG,MAAM;YAC5C,QAAQxH,CAAU,IAAK;gBACnBA,CAAC,CAASC,OAAO,IAAA,IAAA,IAAjBD,CAAC,CAASC,OAAO,EAAI,CAAA;gBACvBvZ,KAAK,CAACwgB,qBAAqB,EAAE,CAAA;aAC9B,CAAA;SACF,CAAA;QACDxgB,KAAK,CAAC+gB,qBAAqB,GAAG,MAAM;YAClC,MAAMpB,QAAQ,GAAG3f,KAAK,CAAC6D,QAAQ,EAAE,CAAC8b,QAAQ,CAAA;YAC1C,OAAOA,QAAQ,KAAK,IAAI,IAAIvN,MAAM,CAACpC,MAAM,CAAC2P,QAAQ,CAAC,CAACthB,IAAI,CAACkG,OAAO,CAAC,CAAA;SAClE,CAAA;QACDvE,KAAK,CAACygB,oBAAoB,GAAG,MAAM;YACjC,MAAMd,QAAQ,GAAG3f,KAAK,CAAC6D,QAAQ,EAAE,CAAC8b,QAAQ,CAAA;YAE1C,wDAAA;YACA,IAAI,OAAOA,QAAQ,KAAK,SAAS,EAAE;gBACjC,OAAOA,QAAQ,KAAK,IAAI,CAAA;YAC1B,CAAA;YAEA,IAAI,CAACvN,MAAM,CAAC4O,IAAI,CAACrB,QAAQ,CAAC,CAACpiB,MAAM,EAAE;gBACjC,OAAO,KAAK,CAAA;YACd,CAAA;YAEA,2CAAA;YACA,IAAIyC,KAAK,CAACihB,WAAW,EAAE,CAAClU,QAAQ,CAAC1O,IAAI,EAAC4B,GAAG,GAAI,CAACA,GAAG,CAACihB,aAAa,EAAE,CAAC,EAAE;gBAClE,OAAO,KAAK,CAAA;YACd,CAAA;YAEA,oCAAA;YACA,OAAO,IAAI,CAAA;SACZ,CAAA;QACDlhB,KAAK,CAACmhB,gBAAgB,GAAG,MAAM;YAC7B,IAAIra,QAAQ,GAAG,CAAC,CAAA;YAEhB,MAAMsa,MAAM,GACVphB,KAAK,CAAC6D,QAAQ,EAAE,CAAC8b,QAAQ,KAAK,IAAI,GAC9BvN,MAAM,CAAC4O,IAAI,CAAChhB,KAAK,CAACihB,WAAW,EAAE,CAACI,QAAQ,CAAC,GACzCjP,MAAM,CAAC4O,IAAI,CAAChhB,KAAK,CAAC6D,QAAQ,EAAE,CAAC8b,QAAQ,CAAC,CAAA;YAE5CyB,MAAM,CAACjkB,OAAO,EAACsD,EAAE,IAAI;gBACnB,MAAM6gB,OAAO,GAAG7gB,EAAE,CAACqB,KAAK,CAAC,GAAG,CAAC,CAAA;gBAC7BgF,QAAQ,GAAGnI,IAAI,CAACU,GAAG,CAACyH,QAAQ,EAAEwa,OAAO,CAAC/jB,MAAM,CAAC,CAAA;YAC/C,CAAC,CAAC,CAAA;YAEF,OAAOuJ,QAAQ,CAAA;SAChB,CAAA;QACD9G,KAAK,CAACuhB,sBAAsB,GAAG,IAAMvhB,KAAK,CAACwhB,iBAAiB,EAAE,CAAA;QAC9DxhB,KAAK,CAACyhB,mBAAmB,GAAG,MAAM;YAChC,IAAI,CAACzhB,KAAK,CAAC0hB,oBAAoB,IAAI1hB,KAAK,CAACO,OAAO,CAACkhB,mBAAmB,EAAE;gBACpEzhB,KAAK,CAAC0hB,oBAAoB,GAAG1hB,KAAK,CAACO,OAAO,CAACkhB,mBAAmB,CAACzhB,KAAK,CAAC,CAAA;YACvE,CAAA;YAEA,IAAIA,KAAK,CAACO,OAAO,CAAC8f,eAAe,IAAI,CAACrgB,KAAK,CAAC0hB,oBAAoB,EAAE;gBAChE,OAAO1hB,KAAK,CAACuhB,sBAAsB,EAAE,CAAA;YACvC,CAAA;YAEA,OAAOvhB,KAAK,CAAC0hB,oBAAoB,EAAE,CAAA;SACpC,CAAA;KACF;IAED3Z,SAAS,EAAEA,CACT9H,GAAe,EACfD,KAAmB,KACV;QACTC,GAAG,CAAC0hB,cAAc,IAAGhC,QAAQ,IAAI;YAC/B3f,KAAK,CAACugB,WAAW,EAACnkB,GAAG,IAAI;gBAAA,IAAAwlB,SAAA,CAAA;gBACvB,MAAMC,MAAM,GAAGzlB,GAAG,KAAK,IAAI,GAAG,IAAI,GAAG,CAAC,CAAA,CAACA,GAAG,IAAHA,IAAAA,IAAAA,GAAG,CAAG6D,GAAG,CAACQ,EAAE,CAAC,CAAA,CAAA;gBAEpD,IAAIqhB,WAA8B,GAAG,CAAA,CAAE,CAAA;gBAEvC,IAAI1lB,GAAG,KAAK,IAAI,EAAE;oBAChBgW,MAAM,CAAC4O,IAAI,CAAChhB,KAAK,CAACihB,WAAW,EAAE,CAACI,QAAQ,CAAC,CAAClkB,OAAO,EAAC4kB,KAAK,IAAI;wBACzDD,WAAW,CAACC,KAAK,CAAC,GAAG,IAAI,CAAA;oBAC3B,CAAC,CAAC,CAAA;gBACJ,CAAC,MAAM;oBACLD,WAAW,GAAG1lB,GAAG,CAAA;gBACnB,CAAA;gBAEAujB,QAAQ,GAAA,CAAAiC,SAAA,GAAGjC,QAAQ,KAAA,OAAAiC,SAAA,GAAI,CAACC,MAAM,CAAA;gBAE9B,IAAI,CAACA,MAAM,IAAIlC,QAAQ,EAAE;oBACvB,OAAO;wBACL,GAAGmC,WAAW;wBACd,CAAC7hB,GAAG,CAACQ,EAAE,CAAA,EAAG,IAAA;qBACX,CAAA;gBACH,CAAA;gBAEA,IAAIohB,MAAM,IAAI,CAAClC,QAAQ,EAAE;oBACvB,MAAM,EAAE,CAAC1f,GAAG,CAACQ,EAAE,CAAA,EAAGkY,CAAC,EAAE,GAAGC,IAAAA,EAAM,GAAGkJ,WAAW,CAAA;oBAC5C,OAAOlJ,IAAI,CAAA;gBACb,CAAA;gBAEA,OAAOxc,GAAG,CAAA;YACZ,CAAC,CAAC,CAAA;SACH,CAAA;QACD6D,GAAG,CAACihB,aAAa,GAAG,MAAM;YAAA,IAAAc,qBAAA,CAAA;YACxB,MAAMrC,QAAQ,GAAG3f,KAAK,CAAC6D,QAAQ,EAAE,CAAC8b,QAAQ,CAAA;YAE1C,OAAO,CAAC,CAAA,CAAA,CAAAqC,qBAAA,GACNhiB,KAAK,CAACO,OAAO,CAAC0hB,gBAAgB,IAA9BjiB,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,KAAK,CAACO,OAAO,CAAC0hB,gBAAgB,CAAGhiB,GAAG,CAAC,KAAA+hB,IAAAA,GAAAA,qBAAA,GACpCrC,QAAQ,KAAK,IAAI,IAAA,CAAIA,QAAQ,IAAA,IAAA,GAAA,KAAA,CAAA,GAARA,QAAQ,CAAG1f,GAAG,CAACQ,EAAE,CAAC,CACzC,CAAA,CAAA;SACF,CAAA;QACDR,GAAG,CAAC4gB,YAAY,GAAG,MAAM;YAAA,IAAAqB,qBAAA,EAAA5U,qBAAA,EAAA6F,YAAA,CAAA;YACvB,OAAA,CAAA+O,qBAAA,GACEliB,KAAK,CAACO,OAAO,CAAC4hB,eAAe,IAA7BniB,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,KAAK,CAACO,OAAO,CAAC4hB,eAAe,CAAGliB,GAAG,CAAC,KAAAiiB,IAAAA,GAAAA,qBAAA,GACnC,CAAA5U,CAAAA,qBAAA,GAACtN,KAAK,CAACO,OAAO,CAAC6hB,eAAe,KAAA9U,IAAAA,GAAAA,qBAAA,GAAI,IAAI,KAAK,CAAC,CAAA,CAAA6F,CAAAA,YAAA,GAAClT,GAAG,CAACiI,OAAO,KAAXiL,IAAAA,IAAAA,YAAA,CAAa5V,MAAM,CAAA,CAAA;SAEpE,CAAA;QACD0C,GAAG,CAACoiB,uBAAuB,GAAG,MAAM;YAClC,IAAIC,eAAe,GAAG,IAAI,CAAA;YAC1B,IAAIvZ,UAAU,GAAG9I,GAAG,CAAA;YAEpB,MAAOqiB,eAAe,IAAIvZ,UAAU,CAACZ,QAAQ,CAAE;gBAC7CY,UAAU,GAAG/I,KAAK,CAAC4I,MAAM,CAACG,UAAU,CAACZ,QAAQ,EAAE,IAAI,CAAC,CAAA;gBACpDma,eAAe,GAAGvZ,UAAU,CAACmY,aAAa,EAAE,CAAA;YAC9C,CAAA;YAEA,OAAOoB,eAAe,CAAA;SACvB,CAAA;QACDriB,GAAG,CAACsiB,wBAAwB,GAAG,MAAM;YACnC,MAAMC,SAAS,GAAGviB,GAAG,CAAC4gB,YAAY,EAAE,CAAA;YAEpC,OAAO,MAAM;gBACX,IAAI,CAAC2B,SAAS,EAAE,OAAA;gBAChBviB,GAAG,CAAC0hB,cAAc,EAAE,CAAA;aACrB,CAAA;SACF,CAAA;IACH,CAAA;AACF;AC1KA,EAAA;AAEA,MAAMc,gBAAgB,GAAG,CAAC,CAAA;AAC1B,MAAMC,eAAe,GAAG,EAAE,CAAA;AAE1B,MAAMC,yBAAyB,GAAGA,IAAAA,CAAwB;QACxDC,SAAS,EAAEH,gBAAgB;QAC3BI,QAAQ,EAAEH,eAAAA;IACZ,CAAC,CAAC,CAAA;AAEK,MAAMI,aAA2B,GAAG;IACzCzW,eAAe,GAAGC,KAAK,IAA2B;QAChD,OAAO;YACL,GAAGA,KAAK;YACRyW,UAAU,EAAE;gBACV,GAAGJ,yBAAyB,EAAE;gBAC9B,GAAGrW,KAAK,IAAA,IAAA,GAAA,KAAA,CAAA,GAALA,KAAK,CAAEyW,UAAU;YACtB,CAAA;SACD,CAAA;KACF;IAEDvW,iBAAiB,GACfxM,KAAmB,IACU;QAC7B,OAAO;YACLgjB,kBAAkB,EAAEhnB,gBAAgB,CAAC,YAAY,EAAEgE,KAAK,CAAA;SACzD,CAAA;KACF;IAEDyD,WAAW,GAA0BzD,KAAmB,IAAW;QACjE,IAAI8f,UAAU,GAAG,KAAK,CAAA;QACtB,IAAIC,MAAM,GAAG,KAAK,CAAA;QAElB/f,KAAK,CAACijB,mBAAmB,GAAG,MAAM;YAAA,IAAA/hB,IAAA,EAAA+e,qBAAA,CAAA;YAChC,IAAI,CAACH,UAAU,EAAE;gBACf9f,KAAK,CAACkgB,MAAM,CAAC,MAAM;oBACjBJ,UAAU,GAAG,IAAI,CAAA;gBACnB,CAAC,CAAC,CAAA;gBACF,OAAA;YACF,CAAA;YAEA,IAAA5e,CAAAA,IAAA,GAAA+e,CAAAA,qBAAA,GACEjgB,KAAK,CAACO,OAAO,CAAC4f,YAAY,KAAAF,IAAAA,GAAAA,qBAAA,GAC1BjgB,KAAK,CAACO,OAAO,CAAC2iB,kBAAkB,KAAA,IAAA,GAAAhiB,IAAA,GAChC,CAAClB,KAAK,CAACO,OAAO,CAAC4iB,gBAAgB,EAC/B;gBACA,IAAIpD,MAAM,EAAE,OAAA;gBACZA,MAAM,GAAG,IAAI,CAAA;gBACb/f,KAAK,CAACkgB,MAAM,CAAC,MAAM;oBACjBlgB,KAAK,CAACojB,cAAc,EAAE,CAAA;oBACtBrD,MAAM,GAAG,KAAK,CAAA;gBAChB,CAAC,CAAC,CAAA;YACJ,CAAA;SACD,CAAA;QACD/f,KAAK,CAACqjB,aAAa,IAAGxnB,OAAO,IAAI;YAC/B,MAAMynB,WAAqC,IAAGlnB,GAAG,IAAI;gBACnD,IAAImnB,QAAQ,GAAG3nB,gBAAgB,CAACC,OAAO,EAAEO,GAAG,CAAC,CAAA;gBAE7C,OAAOmnB,QAAQ,CAAA;aAChB,CAAA;YAED,OAAOvjB,KAAK,CAACO,OAAO,CAACyiB,kBAAkB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAhChjB,KAAK,CAACO,OAAO,CAACyiB,kBAAkB,CAAGM,WAAW,CAAC,CAAA;SACvD,CAAA;QACDtjB,KAAK,CAACwjB,eAAe,IAAGzU,YAAY,IAAI;YAAA,IAAA0U,qBAAA,CAAA;YACtCzjB,KAAK,CAACqjB,aAAa,CACjBtU,YAAY,GACR4T,yBAAyB,EAAE,GAAA,CAAAc,qBAAA,GAC3BzjB,KAAK,CAACkP,YAAY,CAAC6T,UAAU,KAAA,IAAA,GAAAU,qBAAA,GAAId,yBAAyB,EAChE,CAAC,CAAA;SACF,CAAA;QACD3iB,KAAK,CAAC0jB,YAAY,IAAG7nB,OAAO,IAAI;YAC9BmE,KAAK,CAACqjB,aAAa,EAACjnB,GAAG,IAAI;gBACzB,IAAIwmB,SAAS,GAAGhnB,gBAAgB,CAACC,OAAO,EAAEO,GAAG,CAACwmB,SAAS,CAAC,CAAA;gBAExD,MAAMe,YAAY,GAChB,OAAO3jB,KAAK,CAACO,OAAO,CAACqjB,SAAS,KAAK,WAAW,IAC9C5jB,KAAK,CAACO,OAAO,CAACqjB,SAAS,KAAK,CAAC,CAAC,GAC1B/X,MAAM,CAACqL,gBAAgB,GACvBlX,KAAK,CAACO,OAAO,CAACqjB,SAAS,GAAG,CAAC,CAAA;gBAEjChB,SAAS,GAAGjkB,IAAI,CAACU,GAAG,CAAC,CAAC,EAAEV,IAAI,CAACW,GAAG,CAACsjB,SAAS,EAAEe,YAAY,CAAC,CAAC,CAAA;gBAE1D,OAAO;oBACL,GAAGvnB,GAAG;oBACNwmB,SAAAA;iBACD,CAAA;YACH,CAAC,CAAC,CAAA;SACH,CAAA;QACD5iB,KAAK,CAACojB,cAAc,IAAGrU,YAAY,IAAI;YAAA,IAAA8U,sBAAA,EAAA5U,mBAAA,CAAA;YACrCjP,KAAK,CAAC0jB,YAAY,CAChB3U,YAAY,GACR0T,gBAAgB,GAAAoB,CAAAA,sBAAA,GAAA5U,CAAAA,mBAAA,GAChBjP,KAAK,CAACkP,YAAY,KAAAD,IAAAA,IAAAA,CAAAA,mBAAA,GAAlBA,mBAAA,CAAoB8T,UAAU,KAA9B9T,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,mBAAA,CAAgC2T,SAAS,KAAAiB,IAAAA,GAAAA,sBAAA,GAAIpB,gBACnD,CAAC,CAAA;SACF,CAAA;QACDziB,KAAK,CAAC8jB,aAAa,IAAG/U,YAAY,IAAI;YAAA,IAAAgV,sBAAA,EAAAC,oBAAA,CAAA;YACpChkB,KAAK,CAACikB,WAAW,CACflV,YAAY,GACR2T,eAAe,GAAAqB,CAAAA,sBAAA,GAAAC,CAAAA,oBAAA,GACfhkB,KAAK,CAACkP,YAAY,KAAA8U,IAAAA,IAAAA,CAAAA,oBAAA,GAAlBA,oBAAA,CAAoBjB,UAAU,KAA9BiB,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,oBAAA,CAAgCnB,QAAQ,KAAAkB,IAAAA,GAAAA,sBAAA,GAAIrB,eAClD,CAAC,CAAA;SACF,CAAA;QACD1iB,KAAK,CAACikB,WAAW,IAAGpoB,OAAO,IAAI;YAC7BmE,KAAK,CAACqjB,aAAa,EAACjnB,GAAG,IAAI;gBACzB,MAAMymB,QAAQ,GAAGlkB,IAAI,CAACU,GAAG,CAAC,CAAC,EAAEzD,gBAAgB,CAACC,OAAO,EAAEO,GAAG,CAACymB,QAAQ,CAAC,CAAC,CAAA;gBACrE,MAAMqB,WAAW,GAAG9nB,GAAG,CAACymB,QAAQ,GAAGzmB,GAAG,CAACwmB,SAAU,CAAA;gBACjD,MAAMA,SAAS,GAAGjkB,IAAI,CAACuR,KAAK,CAACgU,WAAW,GAAGrB,QAAQ,CAAC,CAAA;gBAEpD,OAAO;oBACL,GAAGzmB,GAAG;oBACNwmB,SAAS;oBACTC,QAAAA;iBACD,CAAA;YACH,CAAC,CAAC,CAAA;SACH,CAAA;QACD,YAAA;QACA7iB,KAAK,CAACmkB,YAAY,IAAGtoB,OAAO,GAC1BmE,KAAK,CAACqjB,aAAa,EAACjnB,GAAG,IAAI;gBAAA,IAAAgoB,qBAAA,CAAA;gBACzB,IAAIC,YAAY,GAAGzoB,gBAAgB,CACjCC,OAAO,EAAA,CAAAuoB,qBAAA,GACPpkB,KAAK,CAACO,OAAO,CAACqjB,SAAS,KAAA,IAAA,GAAAQ,qBAAA,GAAI,CAAC,CAC9B,CAAC,CAAA;gBAED,IAAI,OAAOC,YAAY,KAAK,QAAQ,EAAE;oBACpCA,YAAY,GAAG1lB,IAAI,CAACU,GAAG,CAAC,CAAC,CAAC,EAAEglB,YAAY,CAAC,CAAA;gBAC3C,CAAA;gBAEA,OAAO;oBACL,GAAGjoB,GAAG;oBACNwnB,SAAS,EAAES,YAAAA;iBACZ,CAAA;YACH,CAAC,CAAC,CAAA;QAEJrkB,KAAK,CAACskB,cAAc,GAAG9mB,IAAI,CACzB,IAAM;gBAACwC,KAAK,CAACukB,YAAY,EAAE;aAAC,GAC5BX,SAAS,IAAI;YACX,IAAIY,WAAqB,GAAG,EAAE,CAAA;YAC9B,IAAIZ,SAAS,IAAIA,SAAS,GAAG,CAAC,EAAE;gBAC9BY,WAAW,GAAG,CAAC;uBAAG,IAAI/nB,KAAK,CAACmnB,SAAS,CAAC;iBAAC,CAACa,IAAI,CAAC,IAAI,CAAC,CAAClhB,GAAG,CAAC,CAACoV,CAAC,EAAEpP,CAAC,GAAKA,CAAC,CAAC,CAAA;YACrE,CAAA;YACA,OAAOib,WAAW,CAAA;SACnB,EACDjlB,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,gBAAgB,CAC9D,CAAC,CAAA;QAEDP,KAAK,CAAC0kB,kBAAkB,GAAG,IAAM1kB,KAAK,CAAC6D,QAAQ,EAAE,CAACkf,UAAU,CAACH,SAAS,GAAG,CAAC,CAAA;QAE1E5iB,KAAK,CAAC2kB,cAAc,GAAG,MAAM;YAC3B,MAAM,EAAE/B,SAAAA,EAAW,GAAG5iB,KAAK,CAAC6D,QAAQ,EAAE,CAACkf,UAAU,CAAA;YAEjD,MAAMa,SAAS,GAAG5jB,KAAK,CAACukB,YAAY,EAAE,CAAA;YAEtC,IAAIX,SAAS,KAAK,CAAC,CAAC,EAAE;gBACpB,OAAO,IAAI,CAAA;YACb,CAAA;YAEA,IAAIA,SAAS,KAAK,CAAC,EAAE;gBACnB,OAAO,KAAK,CAAA;YACd,CAAA;YAEA,OAAOhB,SAAS,GAAGgB,SAAS,GAAG,CAAC,CAAA;SACjC,CAAA;QAED5jB,KAAK,CAAC4kB,YAAY,GAAG,MAAM;YACzB,OAAO5kB,KAAK,CAAC0jB,YAAY,EAACtnB,GAAG,GAAIA,GAAG,GAAG,CAAC,CAAC,CAAA;SAC1C,CAAA;QAED4D,KAAK,CAAC6kB,QAAQ,GAAG,MAAM;YACrB,OAAO7kB,KAAK,CAAC0jB,YAAY,EAACtnB,GAAG,IAAI;gBAC/B,OAAOA,GAAG,GAAG,CAAC,CAAA;YAChB,CAAC,CAAC,CAAA;SACH,CAAA;QAED4D,KAAK,CAAC8kB,SAAS,GAAG,MAAM;YACtB,OAAO9kB,KAAK,CAAC0jB,YAAY,CAAC,CAAC,CAAC,CAAA;SAC7B,CAAA;QAED1jB,KAAK,CAAC+kB,QAAQ,GAAG,MAAM;YACrB,OAAO/kB,KAAK,CAAC0jB,YAAY,CAAC1jB,KAAK,CAACukB,YAAY,EAAE,GAAG,CAAC,CAAC,CAAA;SACpD,CAAA;QAEDvkB,KAAK,CAAC4gB,wBAAwB,GAAG,IAAM5gB,KAAK,CAACyhB,mBAAmB,EAAE,CAAA;QAClEzhB,KAAK,CAACglB,qBAAqB,GAAG,MAAM;YAClC,IACE,CAAChlB,KAAK,CAACilB,sBAAsB,IAC7BjlB,KAAK,CAACO,OAAO,CAACykB,qBAAqB,EACnC;gBACAhlB,KAAK,CAACilB,sBAAsB,GAC1BjlB,KAAK,CAACO,OAAO,CAACykB,qBAAqB,CAAChlB,KAAK,CAAC,CAAA;YAC9C,CAAA;YAEA,IAAIA,KAAK,CAACO,OAAO,CAAC4iB,gBAAgB,IAAI,CAACnjB,KAAK,CAACilB,sBAAsB,EAAE;gBACnE,OAAOjlB,KAAK,CAAC4gB,wBAAwB,EAAE,CAAA;YACzC,CAAA;YAEA,OAAO5gB,KAAK,CAACilB,sBAAsB,EAAE,CAAA;SACtC,CAAA;QAEDjlB,KAAK,CAACukB,YAAY,GAAG,MAAM;YAAA,IAAAW,sBAAA,CAAA;YACzB,OAAA,CAAAA,sBAAA,GACEllB,KAAK,CAACO,OAAO,CAACqjB,SAAS,KAAA,IAAA,GAAAsB,sBAAA,GACvBvmB,IAAI,CAACwmB,IAAI,CAACnlB,KAAK,CAAColB,WAAW,EAAE,GAAGplB,KAAK,CAAC6D,QAAQ,EAAE,CAACkf,UAAU,CAACF,QAAQ,CAAC,CAAA;SAExE,CAAA;QAED7iB,KAAK,CAAColB,WAAW,GAAG,MAAM;YAAA,IAAAC,qBAAA,CAAA;YACxB,OAAA,CAAAA,qBAAA,GACErlB,KAAK,CAACO,OAAO,CAAC+kB,QAAQ,KAAAD,IAAAA,GAAAA,qBAAA,GAAIrlB,KAAK,CAAC4gB,wBAAwB,EAAE,CAAC2E,IAAI,CAAChoB,MAAM,CAAA;SAEzE,CAAA;IACH,CAAA;AACF;AClRA,EAAA;AAEA,MAAMioB,yBAAyB,GAAGA,IAAAA,CAAwB;QACxDC,GAAG,EAAE,EAAE;QACPC,MAAM,EAAE,EAAA;IACV,CAAC,CAAC,CAAA;AAEK,MAAMC,UAAwB,GAAG;IACtCtZ,eAAe,GAAGC,KAAK,IAA2B;QAChD,OAAO;YACLsZ,UAAU,EAAEJ,yBAAyB,EAAE;YACvC,GAAGlZ,KAAAA;SACJ,CAAA;KACF;IAEDE,iBAAiB,GACfxM,KAAmB,IACU;QAC7B,OAAO;YACL6lB,kBAAkB,EAAE7pB,gBAAgB,CAAC,YAAY,EAAEgE,KAAK,CAAA;SACzD,CAAA;KACF;IAED+H,SAAS,EAAEA,CACT9H,GAAe,EACfD,KAAmB,KACV;QACTC,GAAG,CAAC6U,GAAG,GAAG,CAAClB,QAAQ,EAAEkS,eAAe,EAAEC,iBAAiB,KAAK;YAC1D,MAAMC,UAAU,GAAGF,eAAe,GAC9B7lB,GAAG,CAACyI,WAAW,EAAE,CAACnF,GAAG,EAACrC,IAAA,IAAA;gBAAA,IAAC,EAAET,EAAAA,EAAI,GAAAS,IAAA,CAAA;gBAAA,OAAKT,EAAE,CAAA;YAAA,CAAA,CAAC,GACrC,EAAE,CAAA;YACN,MAAMwlB,YAAY,GAAGF,iBAAiB,GAClC9lB,GAAG,CAAC4I,aAAa,EAAE,CAACtF,GAAG,EAACmV,KAAA,IAAA;gBAAA,IAAC,EAAEjY,EAAAA,EAAI,GAAAiY,KAAA,CAAA;gBAAA,OAAKjY,EAAE,CAAA;YAAA,CAAA,CAAC,GACvC,EAAE,CAAA;YACN,MAAM2gB,MAAM,GAAG,IAAI3Q,GAAG,CAAC,CAAC;mBAAGwV,YAAY;gBAAEhmB,GAAG,CAACQ,EAAE,EAAE;mBAAGulB,UAAU;aAAC,CAAC,CAAA;YAEhEhmB,KAAK,CAACkmB,aAAa,EAAC9pB,GAAG,IAAI;gBAAA,IAAA+pB,SAAA,EAAAC,YAAA,CAAA;gBACzB,IAAIxS,QAAQ,KAAK,QAAQ,EAAE;oBAAA,IAAAyS,QAAA,EAAAC,WAAA,CAAA;oBACzB,OAAO;wBACLb,GAAG,EAAE,CAAAY,CAAAA,QAAA,GAACjqB,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAEqpB,GAAG,KAAAY,IAAAA,GAAAA,QAAA,GAAI,EAAE,EAAE/hB,MAAM,EAAChI,CAAC,GAAI,CAAA,CAAC8kB,MAAM,IAAA,IAAA,IAANA,MAAM,CAAEmF,GAAG,CAACjqB,CAAC,CAAC,CAAC,CAAA;wBAClDopB,MAAM,EAAE,CACN;+BAAG,CAAAY,CAAAA,WAAA,GAAClqB,GAAG,IAAA,IAAA,GAAA,KAAA,CAAA,GAAHA,GAAG,CAAEspB,MAAM,KAAAY,IAAAA,GAAAA,WAAA,GAAI,EAAE,EAAEhiB,MAAM,EAAChI,CAAC,GAAI,CAAA,CAAC8kB,MAAM,IAAA,IAAA,IAANA,MAAM,CAAEmF,GAAG,CAACjqB,CAAC,CAAC,CAAA,CAAC,EACnD;+BAAGG,KAAK,CAAC+T,IAAI,CAAC4Q,MAAM,CAAC;yBAAA;qBAExB,CAAA;gBACH,CAAA;gBAEA,IAAIxN,QAAQ,KAAK,KAAK,EAAE;oBAAA,IAAA4S,SAAA,EAAAC,YAAA,CAAA;oBACtB,OAAO;wBACLhB,GAAG,EAAE,CACH;+BAAG,CAAAe,CAAAA,SAAA,GAACpqB,GAAG,IAAA,IAAA,GAAA,KAAA,CAAA,GAAHA,GAAG,CAAEqpB,GAAG,KAAAe,IAAAA,GAAAA,SAAA,GAAI,EAAE,EAAEliB,MAAM,EAAChI,CAAC,GAAI,CAAA,CAAC8kB,MAAM,IAANA,IAAAA,IAAAA,MAAM,CAAEmF,GAAG,CAACjqB,CAAC,CAAC,CAAC,CAAA,EAChD;+BAAGG,KAAK,CAAC+T,IAAI,CAAC4Q,MAAM,CAAC;yBACtB;wBACDsE,MAAM,EAAE,CAAAe,CAAAA,YAAA,GAACrqB,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAEspB,MAAM,KAAAe,IAAAA,GAAAA,YAAA,GAAI,EAAE,EAAEniB,MAAM,EAAChI,CAAC,GAAI,CAAA,CAAC8kB,MAAM,IAANA,IAAAA,IAAAA,MAAM,CAAEmF,GAAG,CAACjqB,CAAC,CAAC,CAAA,CAAA;qBACxD,CAAA;gBACH,CAAA;gBAEA,OAAO;oBACLmpB,GAAG,EAAE,CAAAU,CAAAA,SAAA,GAAC/pB,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAEqpB,GAAG,KAAAU,IAAAA,GAAAA,SAAA,GAAI,EAAE,EAAE7hB,MAAM,EAAChI,CAAC,GAAI,CAAA,CAAC8kB,MAAM,IAAA,IAAA,IAANA,MAAM,CAAEmF,GAAG,CAACjqB,CAAC,CAAC,CAAC,CAAA;oBAClDopB,MAAM,EAAE,CAAAU,CAAAA,YAAA,GAAChqB,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAEspB,MAAM,KAAAU,IAAAA,GAAAA,YAAA,GAAI,EAAE,EAAE9hB,MAAM,EAAChI,CAAC,GAAI,CAAA,CAAC8kB,MAAM,IAANA,IAAAA,IAAAA,MAAM,CAAEmF,GAAG,CAACjqB,CAAC,CAAC,CAAA,CAAA;iBACxD,CAAA;YACH,CAAC,CAAC,CAAA;SACH,CAAA;QACD2D,GAAG,CAACsV,SAAS,GAAG,MAAM;YAAA,IAAA4E,KAAA,CAAA;YACpB,MAAM,EAAEuM,gBAAgB,EAAEjR,aAAAA,EAAe,GAAGzV,KAAK,CAACO,OAAO,CAAA;YACzD,IAAI,OAAOmmB,gBAAgB,KAAK,UAAU,EAAE;gBAC1C,OAAOA,gBAAgB,CAACzmB,GAAG,CAAC,CAAA;YAC9B,CAAA;YACA,OAAAka,CAAAA,KAAA,GAAOuM,gBAAgB,IAAhBA,IAAAA,GAAAA,gBAAgB,GAAIjR,aAAa,KAAA,IAAA,GAAA0E,KAAA,GAAI,IAAI,CAAA;SACjD,CAAA;QACDla,GAAG,CAAC0V,WAAW,GAAG,MAAM;YACtB,MAAMyL,MAAM,GAAG;gBAACnhB,GAAG,CAACQ,EAAE;aAAC,CAAA;YAEvB,MAAM,EAAEglB,GAAG,EAAEC,MAAAA,EAAQ,GAAG1lB,KAAK,CAAC6D,QAAQ,EAAE,CAAC+hB,UAAU,CAAA;YAEnD,MAAMe,KAAK,GAAGvF,MAAM,CAAC/iB,IAAI,EAAC/B,CAAC,GAAImpB,GAAG,IAAA,IAAA,GAAA,KAAA,CAAA,GAAHA,GAAG,CAAE7jB,QAAQ,CAACtF,CAAC,CAAC,CAAC,CAAA;YAChD,MAAMsqB,QAAQ,GAAGxF,MAAM,CAAC/iB,IAAI,EAAC/B,CAAC,GAAIopB,MAAM,IAAA,IAAA,GAAA,KAAA,CAAA,GAANA,MAAM,CAAE9jB,QAAQ,CAACtF,CAAC,CAAC,CAAC,CAAA;YAEtD,OAAOqqB,KAAK,GAAG,KAAK,GAAGC,QAAQ,GAAG,QAAQ,GAAG,KAAK,CAAA;SACnD,CAAA;QACD3mB,GAAG,CAAC8V,cAAc,GAAG,MAAM;YAAA,IAAA8Q,KAAA,EAAAC,qBAAA,CAAA;YACzB,MAAMlT,QAAQ,GAAG3T,GAAG,CAAC0V,WAAW,EAAE,CAAA;YAClC,IAAI,CAAC/B,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAA;YAExB,MAAMmT,mBAAmB,GAAAF,CAAAA,KAAA,GACvBjT,QAAQ,KAAK,KAAK,GAAG5T,KAAK,CAACgnB,UAAU,EAAE,GAAGhnB,KAAK,CAACinB,aAAa,EAAE,KAAA,OAAA,KAAA,IADrCJ,KAAA,CAEzBtjB,GAAG,EAAC2jB,KAAA,IAAA;gBAAA,IAAC,EAAEzmB,EAAAA,EAAI,GAAAymB,KAAA,CAAA;gBAAA,OAAKzmB,EAAE,CAAA;aAAC,CAAA,CAAA;YAEtB,OAAA,CAAAqmB,qBAAA,GAAOC,mBAAmB,IAAnBA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,mBAAmB,CAAE/U,OAAO,CAAC/R,GAAG,CAACQ,EAAE,CAAC,KAAA,IAAA,GAAAqmB,qBAAA,GAAI,CAAC,CAAC,CAAA;SAClD,CAAA;KACF;IAEDrjB,WAAW,GAA0BzD,KAAmB,IAAW;QACjEA,KAAK,CAACkmB,aAAa,IAAGrqB,OAAO,GAAImE,KAAK,CAACO,OAAO,CAACslB,kBAAkB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAhC7lB,KAAK,CAACO,OAAO,CAACslB,kBAAkB,CAAGhqB,OAAO,CAAC,CAAA;QAE5EmE,KAAK,CAACmnB,eAAe,IAAGpY,YAAY,IAAA;YAAA,IAAAqY,qBAAA,EAAAnY,mBAAA,CAAA;YAAA,OAClCjP,KAAK,CAACkmB,aAAa,CACjBnX,YAAY,GACRyW,yBAAyB,EAAE,GAAA4B,CAAAA,qBAAA,GAAAnY,CAAAA,mBAAA,GAC3BjP,KAAK,CAACkP,YAAY,KAAlBD,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,mBAAA,CAAoB2W,UAAU,KAAAwB,IAAAA,GAAAA,qBAAA,GAAI5B,yBAAyB,EACjE,CAAC,CAAA;QAAA,CAAA,CAAA;QAEHxlB,KAAK,CAACqnB,mBAAmB,IAAGzT,QAAQ,IAAI;YAAA,IAAA4C,qBAAA,CAAA;YACtC,MAAMC,YAAY,GAAGzW,KAAK,CAAC6D,QAAQ,EAAE,CAAC+hB,UAAU,CAAA;YAEhD,IAAI,CAAChS,QAAQ,EAAE;gBAAA,IAAA0T,iBAAA,EAAAC,oBAAA,CAAA;gBACb,OAAOhjB,OAAO,CAAC,CAAA+iB,CAAAA,iBAAA,GAAA7Q,YAAY,CAACgP,GAAG,KAAA,IAAA,GAAA,KAAA,CAAA,GAAhB6B,iBAAA,CAAkB/pB,MAAM,KAAA,CAAAgqB,CAAAA,oBAAA,GAAI9Q,YAAY,CAACiP,MAAM,KAAnB6B,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,oBAAA,CAAqBhqB,MAAM,CAAC,CAAA,CAAA;YACzE,CAAA;YACA,OAAOgH,OAAO,CAAA,CAAAiS,qBAAA,GAACC,YAAY,CAAC7C,QAAQ,CAAC,KAAtB4C,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,qBAAA,CAAwBjZ,MAAM,CAAC,CAAA;SAC/C,CAAA;QAEDyC,KAAK,CAACwnB,cAAc,GAAG,CAACC,WAAW,EAAEC,YAAY,EAAE9T,QAAQ,KAAK;YAAA,IAAA+T,qBAAA,CAAA;YAC9D,MAAMpC,IAAI,GACR,CAAAoC,CAAAA,qBAAA,GAAA3nB,KAAK,CAACO,OAAO,CAACqnB,cAAc,KAAAD,IAAAA,GAAAA,qBAAA,GAAI,IAAI,IAChC,0EAAA;YACA,mEAAA;YACA,CAACD,YAAY,IAAA,OAAZA,YAAY,GAAI,EAAE,EAAEnkB,GAAG,EAACwe,KAAK,IAAI;gBAChC,MAAM9hB,GAAG,GAAGD,KAAK,CAAC4I,MAAM,CAACmZ,KAAK,EAAE,IAAI,CAAC,CAAA;gBACrC,OAAO9hB,GAAG,CAACoiB,uBAAuB,EAAE,GAAGpiB,GAAG,GAAG,IAAI,CAAA;YACnD,CAAC,CAAC,GACF,4CAAA;YACA,CAACynB,YAAY,IAAA,OAAZA,YAAY,GAAI,EAAE,EAAEnkB,GAAG,EACtBwe,KAAK,GAAI0F,WAAW,CAACpjB,IAAI,EAACpE,GAAG,GAAIA,GAAG,CAACQ,EAAE,KAAKshB,KAAK,CACnD,CAAC,CAAA;YAEP,OAAOwD,IAAI,CAACjhB,MAAM,CAACC,OAAO,CAAC,CAAChB,GAAG,EAACjH,CAAC,GAAA,CAAK;oBAAE,GAAGA,CAAC;oBAAEsX,QAAAA;gBAAS,CAAC,CAAC,CAAC,CAAA;SAC3D,CAAA;QAED5T,KAAK,CAACgnB,UAAU,GAAGxpB,IAAI,CACrB,IAAM;gBAACwC,KAAK,CAACihB,WAAW,EAAE,CAACsE,IAAI;gBAAEvlB,KAAK,CAAC6D,QAAQ,EAAE,CAAC+hB,UAAU,CAACH,GAAG;aAAC,EACjE,CAACoC,OAAO,EAAEC,eAAe,GACvB9nB,KAAK,CAACwnB,cAAc,CAACK,OAAO,EAAEC,eAAe,EAAE,KAAK,CAAC,EACvDvoB,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,WAAW,EAAE,YAAY,CACzD,CAAC,CAAA;QAEDP,KAAK,CAACinB,aAAa,GAAGzpB,IAAI,CACxB,IAAM;gBAACwC,KAAK,CAACihB,WAAW,EAAE,CAACsE,IAAI;gBAAEvlB,KAAK,CAAC6D,QAAQ,EAAE,CAAC+hB,UAAU,CAACF,MAAM;aAAC,EACpE,CAACmC,OAAO,EAAEE,kBAAkB,GAC1B/nB,KAAK,CAACwnB,cAAc,CAACK,OAAO,EAAEE,kBAAkB,EAAE,QAAQ,CAAC,EAC7DxoB,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,WAAW,EAAE,eAAe,CAC5D,CAAC,CAAA;QAEDP,KAAK,CAACgoB,aAAa,GAAGxqB,IAAI,CACxB,IAAM;gBACJwC,KAAK,CAACihB,WAAW,EAAE,CAACsE,IAAI;gBACxBvlB,KAAK,CAAC6D,QAAQ,EAAE,CAAC+hB,UAAU,CAACH,GAAG;gBAC/BzlB,KAAK,CAAC6D,QAAQ,EAAE,CAAC+hB,UAAU,CAACF,MAAM;aACnC,EACD,CAACmC,OAAO,EAAEpC,GAAG,EAAEC,MAAM,KAAK;YACxB,MAAMuC,YAAY,GAAG,IAAIxX,GAAG,CAAC,CAAC;mBAAIgV,GAAG,IAAA,IAAA,GAAHA,GAAG,GAAI,EAAE,GAAG;mBAAIC,MAAM,IAAA,IAAA,GAANA,MAAM,GAAI,EAAE;aAAE,CAAC,CAAA;YACjE,OAAOmC,OAAO,CAACvjB,MAAM,EAAChI,CAAC,GAAI,CAAC2rB,YAAY,CAAC1B,GAAG,CAACjqB,CAAC,CAACmE,EAAE,CAAC,CAAC,CAAA;SACpD,EACDlB,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,WAAW,EAAE,eAAe,CAC5D,CAAC,CAAA;IACH,CAAA;AACF;AChFA,EAAA;AAEO,MAAM2nB,YAA0B,GAAG;IACxC7b,eAAe,GAAGC,KAAK,IAA6B;QAClD,OAAO;YACL6b,YAAY,EAAE,CAAA,CAAE;YAChB,GAAG7b,KAAAA;SACJ,CAAA;KACF;IAEDE,iBAAiB,GACfxM,KAAmB,IACY;QAC/B,OAAO;YACLooB,oBAAoB,EAAEpsB,gBAAgB,CAAC,cAAc,EAAEgE,KAAK,CAAC;YAC7DqoB,kBAAkB,EAAE,IAAI;YACxBC,uBAAuB,EAAE,IAAI;YAC7BC,qBAAqB,EAAE,IAAA;SAIxB,CAAA;KACF;IAED9kB,WAAW,GAA0BzD,KAAmB,IAAW;QACjEA,KAAK,CAACwoB,eAAe,IAAG3sB,OAAO,GAC7BmE,KAAK,CAACO,OAAO,CAAC6nB,oBAAoB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAlCpoB,KAAK,CAACO,OAAO,CAAC6nB,oBAAoB,CAAGvsB,OAAO,CAAC,CAAA;QAC/CmE,KAAK,CAACyoB,iBAAiB,IAAG1Z,YAAY,IAAA;YAAA,IAAAqY,qBAAA,CAAA;YAAA,OACpCpnB,KAAK,CAACwoB,eAAe,CACnBzZ,YAAY,GAAG,CAAA,CAAE,GAAAqY,CAAAA,qBAAA,GAAGpnB,KAAK,CAACkP,YAAY,CAACiZ,YAAY,KAAA,OAAAf,qBAAA,GAAI,CAAA,CACzD,CAAC,CAAA;QAAA,CAAA,CAAA;QACHpnB,KAAK,CAAC0oB,qBAAqB,IAAG1b,KAAK,IAAI;YACrChN,KAAK,CAACwoB,eAAe,EAACpsB,GAAG,IAAI;gBAC3B4Q,KAAK,GACH,OAAOA,KAAK,KAAK,WAAW,GAAGA,KAAK,GAAG,CAAChN,KAAK,CAAC2oB,oBAAoB,EAAE,CAAA;gBAEtE,MAAMR,YAAY,GAAG;oBAAE,GAAG/rB,GAAAA;iBAAK,CAAA;gBAE/B,MAAMwsB,kBAAkB,GAAG5oB,KAAK,CAAC2S,qBAAqB,EAAE,CAAC5F,QAAQ,CAAA;gBAEjE,mEAAA;gBACA,+DAAA;gBACA,IAAIC,KAAK,EAAE;oBACT4b,kBAAkB,CAACzrB,OAAO,EAAC8C,GAAG,IAAI;wBAChC,IAAI,CAACA,GAAG,CAAC4oB,YAAY,EAAE,EAAE;4BACvB,OAAA;wBACF,CAAA;wBACAV,YAAY,CAACloB,GAAG,CAACQ,EAAE,CAAC,GAAG,IAAI,CAAA;oBAC7B,CAAC,CAAC,CAAA;gBACJ,CAAC,MAAM;oBACLmoB,kBAAkB,CAACzrB,OAAO,EAAC8C,GAAG,IAAI;wBAChC,OAAOkoB,YAAY,CAACloB,GAAG,CAACQ,EAAE,CAAC,CAAA;oBAC7B,CAAC,CAAC,CAAA;gBACJ,CAAA;gBAEA,OAAO0nB,YAAY,CAAA;YACrB,CAAC,CAAC,CAAA;SACH,CAAA;QACDnoB,KAAK,CAAC8oB,yBAAyB,IAAG9b,KAAK,GACrChN,KAAK,CAACwoB,eAAe,EAACpsB,GAAG,IAAI;gBAC3B,MAAM2sB,aAAa,GACjB,OAAO/b,KAAK,KAAK,WAAW,GACxBA,KAAK,GACL,CAAChN,KAAK,CAACgpB,wBAAwB,EAAE,CAAA;gBAEvC,MAAMb,YAA+B,GAAG;oBAAE,GAAG/rB,GAAAA;iBAAK,CAAA;gBAElD4D,KAAK,CAACihB,WAAW,EAAE,CAACsE,IAAI,CAACpoB,OAAO,EAAC8C,GAAG,IAAI;oBACtCgpB,mBAAmB,CAACd,YAAY,EAAEloB,GAAG,CAACQ,EAAE,EAAEsoB,aAAa,EAAE,IAAI,EAAE/oB,KAAK,CAAC,CAAA;gBACvE,CAAC,CAAC,CAAA;gBAEF,OAAOmoB,YAAY,CAAA;YACrB,CAAC,CAAC,CAAA;QAEJ,mCAAA;QACA,YAAA;QACA,YAAA;QACA,gBAAA;QACA,sDAAA;QACA,cAAA;QAEA,+CAAA;QACA,gBAAA;QACA,uBAAA;QACA,iCAAA;QACA,oBAAA;QACA,sBAAA;QACA,UAAA;QACA,0DAAA;QACA,wBAAA;QACA,2BAAA;QACA,sBAAA;QACA,UAAA;QACA,qBAAA;QACA,SAAA;QACA,mBAAA;QACA,MAAA;QAEA,sDAAA;QACA,oCAAA;QAEA,wBAAA;QACA,8BAAA;QAEA,mCAAA;QACA,0DAAA;QACA,kBAAA;QACA,iDAAA;QACA,uCAAA;QACA,SAAA;QACA,MAAA;QAEA,gCAAA;QACA,gDAAA;QACA,8CAAA;QAEA,qCAAA;QACA,wBAAA;QACA,yBAAA;QACA,8BAAA;QACA,sBAAA;QACA,0BAAA;QACA,UAAA;QACA,QAAA;QAEA,qBAAA;QACA,oBAAA;QACA,QAAA;QACA,OAAA;QAEA,0CAAA;QACA,KAAA;QACAnoB,KAAK,CAACkpB,sBAAsB,GAAG,IAAMlpB,KAAK,CAAC8M,eAAe,EAAE,CAAA;QAC5D9M,KAAK,CAACmpB,mBAAmB,GAAG3rB,IAAI,CAC9B,IAAM;gBAACwC,KAAK,CAAC6D,QAAQ,EAAE,CAACskB,YAAY;gBAAEnoB,KAAK,CAAC8M,eAAe,EAAE;aAAC,EAC9D,CAACqb,YAAY,EAAEiB,QAAQ,KAAK;YAC1B,IAAI,CAAChX,MAAM,CAAC4O,IAAI,CAACmH,YAAY,CAAC,CAAC5qB,MAAM,EAAE;gBACrC,OAAO;oBACLgoB,IAAI,EAAE,EAAE;oBACRxY,QAAQ,EAAE,EAAE;oBACZsU,QAAQ,EAAE,CAAA,CAAC;iBACZ,CAAA;YACH,CAAA;YAEA,OAAOgI,YAAY,CAACrpB,KAAK,EAAEopB,QAAQ,CAAC,CAAA;SACrC,EACD7pB,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,qBAAqB,CACnE,CAAC,CAAA;QAEDP,KAAK,CAACspB,2BAA2B,GAAG9rB,IAAI,CACtC,IAAM;gBAACwC,KAAK,CAAC6D,QAAQ,EAAE,CAACskB,YAAY;gBAAEnoB,KAAK,CAACmP,mBAAmB,EAAE;aAAC,EAClE,CAACgZ,YAAY,EAAEiB,QAAQ,KAAK;YAC1B,IAAI,CAAChX,MAAM,CAAC4O,IAAI,CAACmH,YAAY,CAAC,CAAC5qB,MAAM,EAAE;gBACrC,OAAO;oBACLgoB,IAAI,EAAE,EAAE;oBACRxY,QAAQ,EAAE,EAAE;oBACZsU,QAAQ,EAAE,CAAA,CAAC;iBACZ,CAAA;YACH,CAAA;YAEA,OAAOgI,YAAY,CAACrpB,KAAK,EAAEopB,QAAQ,CAAC,CAAA;SACrC,EACD7pB,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,6BAA6B,CAC3E,CAAC,CAAA;QAEDP,KAAK,CAACupB,0BAA0B,GAAG/rB,IAAI,CACrC,IAAM;gBAACwC,KAAK,CAAC6D,QAAQ,EAAE,CAACskB,YAAY;gBAAEnoB,KAAK,CAACwhB,iBAAiB,EAAE;aAAC,EAChE,CAAC2G,YAAY,EAAEiB,QAAQ,KAAK;YAC1B,IAAI,CAAChX,MAAM,CAAC4O,IAAI,CAACmH,YAAY,CAAC,CAAC5qB,MAAM,EAAE;gBACrC,OAAO;oBACLgoB,IAAI,EAAE,EAAE;oBACRxY,QAAQ,EAAE,EAAE;oBACZsU,QAAQ,EAAE,CAAA,CAAC;iBACZ,CAAA;YACH,CAAA;YAEA,OAAOgI,YAAY,CAACrpB,KAAK,EAAEopB,QAAQ,CAAC,CAAA;SACrC,EACD7pB,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,4BAA4B,CAC1E,CAAC,CAAA;QAED,GAAA;QAEA,sCAAA;QACA,oCAAA;QAEA,gBAAA;QACA,wBAAA;QACA,MAAA;QAEA,0EAAA;QACA,2DAAA;QACA,MAAA;QAEA,6DAAA;QACA,KAAA;QAEAP,KAAK,CAAC2oB,oBAAoB,GAAG,MAAM;YACjC,MAAMC,kBAAkB,GAAG5oB,KAAK,CAACmP,mBAAmB,EAAE,CAACpC,QAAQ,CAAA;YAC/D,MAAM,EAAEob,YAAAA,EAAc,GAAGnoB,KAAK,CAAC6D,QAAQ,EAAE,CAAA;YAEzC,IAAI2lB,iBAAiB,GAAGjlB,OAAO,CAC7BqkB,kBAAkB,CAACrrB,MAAM,IAAI6U,MAAM,CAAC4O,IAAI,CAACmH,YAAY,CAAC,CAAC5qB,MACzD,CAAC,CAAA;YAED,IAAIisB,iBAAiB,EAAE;gBACrB,IACEZ,kBAAkB,CAACvqB,IAAI,EACrB4B,GAAG,GAAIA,GAAG,CAAC4oB,YAAY,EAAE,IAAI,CAACV,YAAY,CAACloB,GAAG,CAACQ,EAAE,CACnD,CAAC,EACD;oBACA+oB,iBAAiB,GAAG,KAAK,CAAA;gBAC3B,CAAA;YACF,CAAA;YAEA,OAAOA,iBAAiB,CAAA;SACzB,CAAA;QAEDxpB,KAAK,CAACgpB,wBAAwB,GAAG,MAAM;YACrC,MAAMS,kBAAkB,GAAGzpB,KAAK,CAC7BglB,qBAAqB,EAAE,CACvBjY,QAAQ,CAACzI,MAAM,EAACrE,GAAG,GAAIA,GAAG,CAAC4oB,YAAY,EAAE,CAAC,CAAA;YAC7C,MAAM,EAAEV,YAAAA,EAAc,GAAGnoB,KAAK,CAAC6D,QAAQ,EAAE,CAAA;YAEzC,IAAI6lB,qBAAqB,GAAG,CAAC,CAACD,kBAAkB,CAAClsB,MAAM,CAAA;YAEvD,IACEmsB,qBAAqB,IACrBD,kBAAkB,CAACprB,IAAI,EAAC4B,GAAG,GAAI,CAACkoB,YAAY,CAACloB,GAAG,CAACQ,EAAE,CAAC,CAAC,EACrD;gBACAipB,qBAAqB,GAAG,KAAK,CAAA;YAC/B,CAAA;YAEA,OAAOA,qBAAqB,CAAA;SAC7B,CAAA;QAED1pB,KAAK,CAAC2pB,qBAAqB,GAAG,MAAM;YAAA,IAAAC,qBAAA,CAAA;YAClC,MAAMC,aAAa,GAAGzX,MAAM,CAAC4O,IAAI,CAAA4I,CAAAA,qBAAA,GAC/B5pB,KAAK,CAAC6D,QAAQ,EAAE,CAACskB,YAAY,KAAAyB,IAAAA,GAAAA,qBAAA,GAAI,CAAA,CACnC,CAAC,CAACrsB,MAAM,CAAA;YACR,OACEssB,aAAa,GAAG,CAAC,IACjBA,aAAa,GAAG7pB,KAAK,CAACmP,mBAAmB,EAAE,CAACpC,QAAQ,CAACxP,MAAM,CAAA;SAE9D,CAAA;QAEDyC,KAAK,CAAC8pB,yBAAyB,GAAG,MAAM;YACtC,MAAML,kBAAkB,GAAGzpB,KAAK,CAACglB,qBAAqB,EAAE,CAACjY,QAAQ,CAAA;YACjE,OAAO/M,KAAK,CAACgpB,wBAAwB,EAAE,GACnC,KAAK,GACLS,kBAAkB,CACfnlB,MAAM,EAACrE,GAAG,GAAIA,GAAG,CAAC4oB,YAAY,EAAE,CAAC,CACjCxqB,IAAI,EAAC/B,CAAC,GAAIA,CAAC,CAACytB,aAAa,EAAE,IAAIztB,CAAC,CAAC0tB,iBAAiB,EAAE,CAAC,CAAA;SAC7D,CAAA;QAEDhqB,KAAK,CAACiqB,+BAA+B,GAAG,MAAM;YAC5C,QAAQ3Q,CAAU,IAAK;gBACrBtZ,KAAK,CAAC0oB,qBAAqB,CACvBpP,CAAC,CAAgB8D,MAAM,CAAsBC,OACjD,CAAC,CAAA;aACF,CAAA;SACF,CAAA;QAEDrd,KAAK,CAACkqB,mCAAmC,GAAG,MAAM;YAChD,QAAQ5Q,CAAU,IAAK;gBACrBtZ,KAAK,CAAC8oB,yBAAyB,CAC3BxP,CAAC,CAAgB8D,MAAM,CAAsBC,OACjD,CAAC,CAAA;aACF,CAAA;SACF,CAAA;KACF;IAEDtV,SAAS,EAAEA,CACT9H,GAAe,EACfD,KAAmB,KACV;QACTC,GAAG,CAACkqB,cAAc,GAAG,CAACnd,KAAK,EAAErP,IAAI,KAAK;YACpC,MAAMysB,UAAU,GAAGnqB,GAAG,CAAC8pB,aAAa,EAAE,CAAA;YAEtC/pB,KAAK,CAACwoB,eAAe,EAACpsB,GAAG,IAAI;gBAAA,IAAAiuB,oBAAA,CAAA;gBAC3Brd,KAAK,GAAG,OAAOA,KAAK,KAAK,WAAW,GAAGA,KAAK,GAAG,CAACod,UAAU,CAAA;gBAE1D,IAAInqB,GAAG,CAAC4oB,YAAY,EAAE,IAAIuB,UAAU,KAAKpd,KAAK,EAAE;oBAC9C,OAAO5Q,GAAG,CAAA;gBACZ,CAAA;gBAEA,MAAMkuB,cAAc,GAAG;oBAAE,GAAGluB,GAAAA;iBAAK,CAAA;gBAEjC6sB,mBAAmB,CACjBqB,cAAc,EACdrqB,GAAG,CAACQ,EAAE,EACNuM,KAAK,EAAA,CAAAqd,oBAAA,GACL1sB,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAE4sB,cAAc,KAAA,IAAA,GAAAF,oBAAA,GAAI,IAAI,EAC5BrqB,KACF,CAAC,CAAA;gBAED,OAAOsqB,cAAc,CAAA;YACvB,CAAC,CAAC,CAAA;SACH,CAAA;QACDrqB,GAAG,CAAC8pB,aAAa,GAAG,MAAM;YACxB,MAAM,EAAE5B,YAAAA,EAAc,GAAGnoB,KAAK,CAAC6D,QAAQ,EAAE,CAAA;YACzC,OAAO2mB,aAAa,CAACvqB,GAAG,EAAEkoB,YAAY,CAAC,CAAA;SACxC,CAAA;QAEDloB,GAAG,CAAC+pB,iBAAiB,GAAG,MAAM;YAC5B,MAAM,EAAE7B,YAAAA,EAAc,GAAGnoB,KAAK,CAAC6D,QAAQ,EAAE,CAAA;YACzC,OAAO4mB,gBAAgB,CAACxqB,GAAG,EAAEkoB,YAAmB,CAAC,KAAK,MAAM,CAAA;SAC7D,CAAA;QAEDloB,GAAG,CAACyqB,uBAAuB,GAAG,MAAM;YAClC,MAAM,EAAEvC,YAAAA,EAAc,GAAGnoB,KAAK,CAAC6D,QAAQ,EAAE,CAAA;YACzC,OAAO4mB,gBAAgB,CAACxqB,GAAG,EAAEkoB,YAAmB,CAAC,KAAK,KAAK,CAAA;SAC5D,CAAA;QAEDloB,GAAG,CAAC4oB,YAAY,GAAG,MAAM;YAAA,IAAAvb,qBAAA,CAAA;YACvB,IAAI,OAAOtN,KAAK,CAACO,OAAO,CAAC8nB,kBAAkB,KAAK,UAAU,EAAE;gBAC1D,OAAOroB,KAAK,CAACO,OAAO,CAAC8nB,kBAAkB,CAACpoB,GAAG,CAAC,CAAA;YAC9C,CAAA;YAEA,OAAAqN,CAAAA,qBAAA,GAAOtN,KAAK,CAACO,OAAO,CAAC8nB,kBAAkB,KAAA,IAAA,GAAA/a,qBAAA,GAAI,IAAI,CAAA;SAChD,CAAA;QAEDrN,GAAG,CAAC0qB,mBAAmB,GAAG,MAAM;YAAA,IAAApd,sBAAA,CAAA;YAC9B,IAAI,OAAOvN,KAAK,CAACO,OAAO,CAACgoB,qBAAqB,KAAK,UAAU,EAAE;gBAC7D,OAAOvoB,KAAK,CAACO,OAAO,CAACgoB,qBAAqB,CAACtoB,GAAG,CAAC,CAAA;YACjD,CAAA;YAEA,OAAAsN,CAAAA,sBAAA,GAAOvN,KAAK,CAACO,OAAO,CAACgoB,qBAAqB,KAAA,IAAA,GAAAhb,sBAAA,GAAI,IAAI,CAAA;SACnD,CAAA;QAEDtN,GAAG,CAAC2qB,iBAAiB,GAAG,MAAM;YAAA,IAAAC,sBAAA,CAAA;YAC5B,IAAI,OAAO7qB,KAAK,CAACO,OAAO,CAAC+nB,uBAAuB,KAAK,UAAU,EAAE;gBAC/D,OAAOtoB,KAAK,CAACO,OAAO,CAAC+nB,uBAAuB,CAACroB,GAAG,CAAC,CAAA;YACnD,CAAA;YAEA,OAAA4qB,CAAAA,sBAAA,GAAO7qB,KAAK,CAACO,OAAO,CAAC+nB,uBAAuB,KAAA,IAAA,GAAAuC,sBAAA,GAAI,IAAI,CAAA;SACrD,CAAA;QACD5qB,GAAG,CAAC6qB,wBAAwB,GAAG,MAAM;YACnC,MAAMC,SAAS,GAAG9qB,GAAG,CAAC4oB,YAAY,EAAE,CAAA;YAEpC,QAAQvP,CAAU,IAAK;gBAAA,IAAA+E,OAAA,CAAA;gBACrB,IAAI,CAAC0M,SAAS,EAAE,OAAA;gBAChB9qB,GAAG,CAACkqB,cAAc,CAAA9L,CAAAA,OAAA,GACd/E,CAAC,CAAgB8D,MAAM,KAAzBiB,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,OAAA,CAAgDhB,OAClD,CAAC,CAAA;aACF,CAAA;SACF,CAAA;IACH,CAAA;AACF,EAAC;AAED,MAAM4L,mBAAmB,GAAGA,CAC1BqB,cAAuC,EACvC7pB,EAAU,EACVuM,KAAc,EACdge,eAAwB,EACxBhrB,KAAmB,KAChB;IAAA,IAAAmT,YAAA,CAAA;IACH,MAAMlT,GAAG,GAAGD,KAAK,CAAC4I,MAAM,CAACnI,EAAE,EAAE,IAAI,CAAC,CAAA;IAElC,uCAAA;IAEA,qDAAA;IACA,kBAAA;IACA,4DAAA;IACA,MAAA;IACA,IAAIuM,KAAK,EAAE;QACT,IAAI,CAAC/M,GAAG,CAAC2qB,iBAAiB,EAAE,EAAE;YAC5BxY,MAAM,CAAC4O,IAAI,CAACsJ,cAAc,CAAC,CAACntB,OAAO,EAAClB,GAAG,GAAI,OAAOquB,cAAc,CAACruB,GAAG,CAAC,CAAC,CAAA;QACxE,CAAA;QACA,IAAIgE,GAAG,CAAC4oB,YAAY,EAAE,EAAE;YACtByB,cAAc,CAAC7pB,EAAE,CAAC,GAAG,IAAI,CAAA;QAC3B,CAAA;IACF,CAAC,MAAM;QACL,OAAO6pB,cAAc,CAAC7pB,EAAE,CAAC,CAAA;IAC3B,CAAA;IACA,IAAA;IAEA,IAAIuqB,eAAe,IAAA7X,CAAAA,YAAA,GAAIlT,GAAG,CAACiI,OAAO,KAAA,IAAA,IAAXiL,YAAA,CAAa5V,MAAM,IAAI0C,GAAG,CAAC0qB,mBAAmB,EAAE,EAAE;QACvE1qB,GAAG,CAACiI,OAAO,CAAC/K,OAAO,EAAC8C,GAAG,GACrBgpB,mBAAmB,CAACqB,cAAc,EAAErqB,GAAG,CAACQ,EAAE,EAAEuM,KAAK,EAAEge,eAAe,EAAEhrB,KAAK,CAC3E,CAAC,CAAA;IACH,CAAA;AACF,CAAC,CAAA;AAEM,SAASqpB,YAAYA,CAC1BrpB,KAAmB,EACnBopB,QAAyB,EACR;IACjB,MAAMjB,YAAY,GAAGnoB,KAAK,CAAC6D,QAAQ,EAAE,CAACskB,YAAY,CAAA;IAElD,MAAM8C,mBAAiC,GAAG,EAAE,CAAA;IAC5C,MAAMC,mBAA+C,GAAG,CAAA,CAAE,CAAA;IAE1D,oCAAA;IACA,MAAMC,WAAW,GAAG,SAAC5F,IAAkB,EAAEvkB,KAAK,EAAuB;QACnE,OAAOukB,IAAI,CACRhiB,GAAG,EAACtD,GAAG,IAAI;YAAA,IAAAmrB,aAAA,CAAA;YACV,MAAMhB,UAAU,GAAGI,aAAa,CAACvqB,GAAG,EAAEkoB,YAAY,CAAC,CAAA;YAEnD,IAAIiC,UAAU,EAAE;gBACda,mBAAmB,CAAC5tB,IAAI,CAAC4C,GAAG,CAAC,CAAA;gBAC7BirB,mBAAmB,CAACjrB,GAAG,CAACQ,EAAE,CAAC,GAAGR,GAAG,CAAA;YACnC,CAAA;YAEA,IAAAmrB,CAAAA,aAAA,GAAInrB,GAAG,CAACiI,OAAO,KAAXkjB,IAAAA,IAAAA,aAAA,CAAa7tB,MAAM,EAAE;gBACvB0C,GAAG,GAAG;oBACJ,GAAGA,GAAG;oBACNiI,OAAO,EAAEijB,WAAW,CAAClrB,GAAG,CAACiI,OAAkB,CAAA;iBAC5C,CAAA;YACH,CAAA;YAEA,IAAIkiB,UAAU,EAAE;gBACd,OAAOnqB,GAAG,CAAA;YACZ,CAAA;QACF,CAAC,CAAC,CACDqE,MAAM,CAACC,OAAO,CAAC,CAAA;KACnB,CAAA;IAED,OAAO;QACLghB,IAAI,EAAE4F,WAAW,CAAC/B,QAAQ,CAAC7D,IAAI,CAAC;QAChCxY,QAAQ,EAAEke,mBAAmB;QAC7B5J,QAAQ,EAAE6J,mBAAAA;KACX,CAAA;AACH,CAAA;AAEO,SAASV,aAAaA,CAC3BvqB,GAAe,EACforB,SAAkC,EACzB;IAAA,IAAAC,iBAAA,CAAA;IACT,OAAAA,CAAAA,iBAAA,GAAOD,SAAS,CAACprB,GAAG,CAACQ,EAAE,CAAC,KAAA,IAAA,GAAA6qB,iBAAA,GAAI,KAAK,CAAA;AACnC,CAAA;AAEO,SAASb,gBAAgBA,CAC9BxqB,GAAe,EACforB,SAAkC,EAClCrrB,KAAmB,EACO;IAAA,IAAAurB,aAAA,CAAA;IAC1B,IAAI,CAAA,CAAAA,CAAAA,aAAA,GAACtrB,GAAG,CAACiI,OAAO,KAAXqjB,IAAAA,IAAAA,aAAA,CAAahuB,MAAM,CAAE,EAAA,OAAO,KAAK,CAAA;IAEtC,IAAIiuB,mBAAmB,GAAG,IAAI,CAAA;IAC9B,IAAIC,YAAY,GAAG,KAAK,CAAA;IAExBxrB,GAAG,CAACiI,OAAO,CAAC/K,OAAO,EAACuuB,MAAM,IAAI;QAC5B,0CAAA;QACA,IAAID,YAAY,IAAI,CAACD,mBAAmB,EAAE;YACxC,OAAA;QACF,CAAA;QAEA,IAAIE,MAAM,CAAC7C,YAAY,EAAE,EAAE;YACzB,IAAI2B,aAAa,CAACkB,MAAM,EAAEL,SAAS,CAAC,EAAE;gBACpCI,YAAY,GAAG,IAAI,CAAA;YACrB,CAAC,MAAM;gBACLD,mBAAmB,GAAG,KAAK,CAAA;YAC7B,CAAA;QACF,CAAA;QAEA,wCAAA;QACA,IAAIE,MAAM,CAACxjB,OAAO,IAAIwjB,MAAM,CAACxjB,OAAO,CAAC3K,MAAM,EAAE;YAC3C,MAAMouB,sBAAsB,GAAGlB,gBAAgB,CAACiB,MAAM,EAAEL,SAAgB,CAAC,CAAA;YACzE,IAAIM,sBAAsB,KAAK,KAAK,EAAE;gBACpCF,YAAY,GAAG,IAAI,CAAA;YACrB,CAAC,MAAM,IAAIE,sBAAsB,KAAK,MAAM,EAAE;gBAC5CF,YAAY,GAAG,IAAI,CAAA;gBACnBD,mBAAmB,GAAG,KAAK,CAAA;YAC7B,CAAC,MAAM;gBACLA,mBAAmB,GAAG,KAAK,CAAA;YAC7B,CAAA;QACF,CAAA;IACF,CAAC,CAAC,CAAA;IAEF,OAAOA,mBAAmB,GAAG,KAAK,GAAGC,YAAY,GAAG,MAAM,GAAG,KAAK,CAAA;AACpE;ACzpBO,MAAMG,mBAAmB,GAAG,aAAY;AAE/C,MAAMC,YAA4B,GAAGA,CAACC,IAAI,EAAEC,IAAI,EAAE7rB,QAAQ,KAAK;IAC7D,OAAO8rB,mBAAmB,CACxB3hB,QAAQ,CAACyhB,IAAI,CAACxrB,QAAQ,CAACJ,QAAQ,CAAC,CAAC,CAACoK,WAAW,EAAE,EAC/CD,QAAQ,CAAC0hB,IAAI,CAACzrB,QAAQ,CAACJ,QAAQ,CAAC,CAAC,CAACoK,WAAW,EAC/C,CAAC,CAAA;AACH,CAAC,CAAA;AAED,MAAM2hB,yBAAyC,GAAGA,CAACH,IAAI,EAAEC,IAAI,EAAE7rB,QAAQ,KAAK;IAC1E,OAAO8rB,mBAAmB,CACxB3hB,QAAQ,CAACyhB,IAAI,CAACxrB,QAAQ,CAACJ,QAAQ,CAAC,CAAC,EACjCmK,QAAQ,CAAC0hB,IAAI,CAACzrB,QAAQ,CAACJ,QAAQ,CAAC,CAClC,CAAC,CAAA;AACH,CAAC,CAAA;AAED,uDAAA;AACA,qBAAA;AACA,MAAMgsB,IAAoB,GAAGA,CAACJ,IAAI,EAAEC,IAAI,EAAE7rB,QAAQ,KAAK;IACrD,OAAOisB,YAAY,CACjB9hB,QAAQ,CAACyhB,IAAI,CAACxrB,QAAQ,CAACJ,QAAQ,CAAC,CAAC,CAACoK,WAAW,EAAE,EAC/CD,QAAQ,CAAC0hB,IAAI,CAACzrB,QAAQ,CAACJ,QAAQ,CAAC,CAAC,CAACoK,WAAW,EAC/C,CAAC,CAAA;AACH,CAAC,CAAA;AAED,uDAAA;AACA,qBAAA;AACA,MAAM8hB,iBAAiC,GAAGA,CAACN,IAAI,EAAEC,IAAI,EAAE7rB,QAAQ,KAAK;IAClE,OAAOisB,YAAY,CACjB9hB,QAAQ,CAACyhB,IAAI,CAACxrB,QAAQ,CAACJ,QAAQ,CAAC,CAAC,EACjCmK,QAAQ,CAAC0hB,IAAI,CAACzrB,QAAQ,CAACJ,QAAQ,CAAC,CAClC,CAAC,CAAA;AACH,CAAC,CAAA;AAED,MAAMmsB,QAAwB,GAAGA,CAACP,IAAI,EAAEC,IAAI,EAAE7rB,QAAQ,KAAK;IACzD,MAAMmQ,CAAC,GAAGyb,IAAI,CAACxrB,QAAQ,CAAOJ,QAAQ,CAAC,CAAA;IACvC,MAAMoQ,CAAC,GAAGyb,IAAI,CAACzrB,QAAQ,CAAOJ,QAAQ,CAAC,CAAA;IAEvC,4BAAA;IACA,qDAAA;IACA,kDAAA;IACA,OAAOmQ,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;AACnC,CAAC,CAAA;AAED,MAAMgc,KAAqB,GAAGA,CAACR,IAAI,EAAEC,IAAI,EAAE7rB,QAAQ,KAAK;IACtD,OAAOisB,YAAY,CAACL,IAAI,CAACxrB,QAAQ,CAACJ,QAAQ,CAAC,EAAE6rB,IAAI,CAACzrB,QAAQ,CAACJ,QAAQ,CAAC,CAAC,CAAA;AACvE,CAAC,CAAA;AAED,QAAA;AAEA,SAASisB,YAAYA,CAAC9b,CAAM,EAAEC,CAAM,EAAE;IACpC,OAAOD,CAAC,KAAKC,CAAC,GAAG,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;AACrC,CAAA;AAEA,SAASjG,QAAQA,CAACgG,CAAM,EAAE;IACxB,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;QACzB,IAAIvE,KAAK,CAACuE,CAAC,CAAC,IAAIA,CAAC,KAAKtE,QAAQ,IAAIsE,CAAC,KAAK,CAACtE,QAAQ,EAAE;YACjD,OAAO,EAAE,CAAA;QACX,CAAA;QACA,OAAO7M,MAAM,CAACmR,CAAC,CAAC,CAAA;IAClB,CAAA;IACA,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;QACzB,OAAOA,CAAC,CAAA;IACV,CAAA;IACA,OAAO,EAAE,CAAA;AACX,CAAA;AAEA,gEAAA;AACA,gEAAA;AACA,gCAAA;AACA,SAAS2b,mBAAmBA,CAACO,IAAY,EAAEC,IAAY,EAAE;IACvD,iDAAA;IACA,kCAAA;IACA,MAAMnc,CAAC,GAAGkc,IAAI,CAACzqB,KAAK,CAAC8pB,mBAAmB,CAAC,CAACtnB,MAAM,CAACC,OAAO,CAAC,CAAA;IACzD,MAAM+L,CAAC,GAAGkc,IAAI,CAAC1qB,KAAK,CAAC8pB,mBAAmB,CAAC,CAACtnB,MAAM,CAACC,OAAO,CAAC,CAAA;IAEzD,QAAA;IACA,MAAO8L,CAAC,CAAC9S,MAAM,IAAI+S,CAAC,CAAC/S,MAAM,CAAE;QAC3B,MAAMkvB,EAAE,GAAGpc,CAAC,CAACmE,KAAK,EAAG,CAAA;QACrB,MAAMkY,EAAE,GAAGpc,CAAC,CAACkE,KAAK,EAAG,CAAA;QAErB,MAAMmY,EAAE,GAAGC,QAAQ,CAACH,EAAE,EAAE,EAAE,CAAC,CAAA;QAC3B,MAAMI,EAAE,GAAGD,QAAQ,CAACF,EAAE,EAAE,EAAE,CAAC,CAAA;QAE3B,MAAMI,KAAK,GAAG;YAACH,EAAE;YAAEE,EAAE;SAAC,CAACzc,IAAI,EAAE,CAAA;QAE7B,kBAAA;QACA,IAAItE,KAAK,CAACghB,KAAK,CAAC,CAAC,CAAE,CAAC,EAAE;YACpB,IAAIL,EAAE,GAAGC,EAAE,EAAE;gBACX,OAAO,CAAC,CAAA;YACV,CAAA;YACA,IAAIA,EAAE,GAAGD,EAAE,EAAE;gBACX,OAAO,CAAC,CAAC,CAAA;YACX,CAAA;YACA,SAAA;QACF,CAAA;QAEA,mCAAA;QACA,IAAI3gB,KAAK,CAACghB,KAAK,CAAC,CAAC,CAAE,CAAC,EAAE;YACpB,OAAOhhB,KAAK,CAAC6gB,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;QAC3B,CAAA;QAEA,mBAAA;QACA,IAAIA,EAAE,GAAGE,EAAE,EAAE;YACX,OAAO,CAAC,CAAA;QACV,CAAA;QACA,IAAIA,EAAE,GAAGF,EAAE,EAAE;YACX,OAAO,CAAC,CAAC,CAAA;QACX,CAAA;IACF,CAAA;IAEA,OAAOtc,CAAC,CAAC9S,MAAM,GAAG+S,CAAC,CAAC/S,MAAM,CAAA;AAC5B,CAAA;AAEA,UAAA;AAEO,MAAMwvB,UAAU,GAAG;IACxBlB,YAAY;IACZI,yBAAyB;IACzBC,IAAI;IACJE,iBAAiB;IACjBC,QAAQ;IACRC,KAAAA;AACF;ACsJA,EAAA;AAEO,MAAMU,UAAwB,GAAG;IACtC3gB,eAAe,GAAGC,KAAK,IAAwB;QAC7C,OAAO;YACL2gB,OAAO,EAAE,EAAE;YACX,GAAG3gB,KAAAA;SACJ,CAAA;KACF;IAEDH,mBAAmB,EAAEA,MAAsD;QACzE,OAAO;YACL+gB,SAAS,EAAE,MAAM;YACjBC,aAAa,EAAE,CAAA;SAChB,CAAA;KACF;IAED3gB,iBAAiB,GACfxM,KAAmB,IACO;QAC1B,OAAO;YACLotB,eAAe,EAAEpxB,gBAAgB,CAAC,SAAS,EAAEgE,KAAK,CAAC;YACnDqtB,gBAAgB,GAAG/T,CAAU,IAAK;gBAChC,OAAQA,CAAC,CAAgBgU,QAAQ,CAAA;YACnC,CAAA;SACD,CAAA;KACF;IAEDxsB,YAAY,EAAEA,CACZvF,MAA6B,EAC7ByE,KAAmB,KACV;QACTzE,MAAM,CAACgyB,gBAAgB,GAAG,MAAM;YAC9B,MAAMC,SAAS,GAAGxtB,KAAK,CAACmP,mBAAmB,EAAE,CAACpC,QAAQ,CAACuL,KAAK,CAAC,EAAE,CAAC,CAAA;YAEhE,IAAImV,QAAQ,GAAG,KAAK,CAAA;YAEpB,KAAK,MAAMxtB,GAAG,IAAIutB,SAAS,CAAE;gBAC3B,MAAMxgB,KAAK,GAAG/M,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAEK,QAAQ,CAAC/E,MAAM,CAACkF,EAAE,CAAC,CAAA;gBAEtC,IAAI2R,MAAM,CAAC7Q,SAAS,CAAC8I,QAAQ,CAACgI,IAAI,CAACrF,KAAK,CAAC,KAAK,eAAe,EAAE;oBAC7D,OAAO+f,UAAU,CAACV,QAAQ,CAAA;gBAC5B,CAAA;gBAEA,IAAI,OAAOrf,KAAK,KAAK,QAAQ,EAAE;oBAC7BygB,QAAQ,GAAG,IAAI,CAAA;oBAEf,IAAIzgB,KAAK,CAAClL,KAAK,CAAC8pB,mBAAmB,CAAC,CAACruB,MAAM,GAAG,CAAC,EAAE;wBAC/C,OAAOwvB,UAAU,CAAClB,YAAY,CAAA;oBAChC,CAAA;gBACF,CAAA;YACF,CAAA;YAEA,IAAI4B,QAAQ,EAAE;gBACZ,OAAOV,UAAU,CAACb,IAAI,CAAA;YACxB,CAAA;YAEA,OAAOa,UAAU,CAACT,KAAK,CAAA;SACxB,CAAA;QACD/wB,MAAM,CAACmyB,cAAc,GAAG,MAAM;YAC5B,MAAM7gB,QAAQ,GAAG7M,KAAK,CAACmP,mBAAmB,EAAE,CAACpC,QAAQ,CAAC,CAAC,CAAC,CAAA;YAExD,MAAMC,KAAK,GAAGH,QAAQ,IAARA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,QAAQ,CAAEvM,QAAQ,CAAC/E,MAAM,CAACkF,EAAE,CAAC,CAAA;YAE3C,IAAI,OAAOuM,KAAK,KAAK,QAAQ,EAAE;gBAC7B,OAAO,KAAK,CAAA;YACd,CAAA;YAEA,OAAO,MAAM,CAAA;SACd,CAAA;QACDzR,MAAM,CAACoyB,YAAY,GAAG,MAAM;YAAA,IAAAC,qBAAA,EAAAC,sBAAA,CAAA;YAC1B,IAAI,CAACtyB,MAAM,EAAE;gBACX,MAAM,IAAI0G,KAAK,EAAE,CAAA;YACnB,CAAA;YAEA,OAAO5F,UAAU,CAACd,MAAM,CAACwF,SAAS,CAACmsB,SAAS,CAAC,GACzC3xB,MAAM,CAACwF,SAAS,CAACmsB,SAAS,GAC1B3xB,MAAM,CAACwF,SAAS,CAACmsB,SAAS,KAAK,MAAM,GACnC3xB,MAAM,CAACgyB,gBAAgB,EAAE,GAAA,CAAAK,qBAAA,GAAA,CAAAC,sBAAA,GACzB7tB,KAAK,CAACO,OAAO,CAACwsB,UAAU,KAAA,IAAA,GAAA,KAAA,CAAA,GAAxBc,sBAAA,CAA2BtyB,MAAM,CAACwF,SAAS,CAACmsB,SAAS,CAAW,KAAAU,IAAAA,GAAAA,qBAAA,GAChEb,UAAU,CAACxxB,MAAM,CAACwF,SAAS,CAACmsB,SAAS,CAAqB,CAAA;SACjE,CAAA;QACD3xB,MAAM,CAACuyB,aAAa,GAAG,CAACC,IAAI,EAAEC,KAAK,KAAK;YACtC,+BAAA;YACA,uCAAA;YACA,kBAAA;YACA,iEAAA;YACA,QAAA;YACA,OAAA;YACA,WAAA;YACA,IAAA;YAEA,2EAAA;YACA,MAAMC,gBAAgB,GAAG1yB,MAAM,CAAC2yB,mBAAmB,EAAE,CAAA;YACrD,MAAMC,cAAc,GAAG,OAAOJ,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,IAAI,CAAA;YAEnE/tB,KAAK,CAACouB,UAAU,EAAChyB,GAAG,IAAI;gBACtB,4CAAA;gBACA,MAAMiyB,eAAe,GAAGjyB,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAEiI,IAAI,EAAC/H,CAAC,GAAIA,CAAC,CAACmE,EAAE,KAAKlF,MAAM,CAACkF,EAAE,CAAC,CAAA;gBAC1D,MAAM6tB,aAAa,GAAGlyB,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAE6R,SAAS,EAAC3R,CAAC,GAAIA,CAAC,CAACmE,EAAE,KAAKlF,MAAM,CAACkF,EAAE,CAAC,CAAA;gBAE7D,IAAI8tB,UAAwB,GAAG,EAAE,CAAA;gBAEjC,2CAAA;gBACA,IAAIC,UAAmD,CAAA;gBACvD,IAAIC,QAAQ,GAAGN,cAAc,GAAGJ,IAAI,GAAGE,gBAAgB,KAAK,MAAM,CAAA;gBAElE,aAAA;gBACA,IAAI7xB,GAAG,IAAA,IAAA,IAAHA,GAAG,CAAEmB,MAAM,IAAIhC,MAAM,CAACmzB,eAAe,EAAE,IAAIV,KAAK,EAAE;oBACpD,IAAIK,eAAe,EAAE;wBACnBG,UAAU,GAAG,QAAQ,CAAA;oBACvB,CAAC,MAAM;wBACLA,UAAU,GAAG,KAAK,CAAA;oBACpB,CAAA;gBACF,CAAC,MAAM;oBACL,cAAA;oBACA,IAAIpyB,GAAG,IAAA,IAAA,IAAHA,GAAG,CAAEmB,MAAM,IAAI+wB,aAAa,KAAKlyB,GAAG,CAACmB,MAAM,GAAG,CAAC,EAAE;wBACnDixB,UAAU,GAAG,SAAS,CAAA;qBACvB,MAAM,IAAIH,eAAe,EAAE;wBAC1BG,UAAU,GAAG,QAAQ,CAAA;oBACvB,CAAC,MAAM;wBACLA,UAAU,GAAG,SAAS,CAAA;oBACxB,CAAA;gBACF,CAAA;gBAEA,oDAAA;gBACA,IAAIA,UAAU,KAAK,QAAQ,EAAE;oBAC3B,wFAAA;oBACA,IAAI,CAACL,cAAc,EAAE;wBACnB,8BAAA;wBACA,IAAI,CAACF,gBAAgB,EAAE;4BACrBO,UAAU,GAAG,QAAQ,CAAA;wBACvB,CAAA;oBACF,CAAA;gBACF,CAAA;gBAEA,IAAIA,UAAU,KAAK,KAAK,EAAE;oBAAA,IAAAG,qBAAA,CAAA;oBACxBJ,UAAU,GAAG,CACX;2BAAGnyB,GAAG;wBACN;4BACEqE,EAAE,EAAElF,MAAM,CAACkF,EAAE;4BACbstB,IAAI,EAAEU,QAAAA;wBACR,CAAC;qBACF,CAAA;oBACD,wBAAA;oBACAF,UAAU,CAAC7Z,MAAM,CACf,CAAC,EACD6Z,UAAU,CAAChxB,MAAM,GAAA,CAAA,CAAAoxB,qBAAA,GACd3uB,KAAK,CAACO,OAAO,CAACquB,oBAAoB,KAAAD,IAAAA,GAAAA,qBAAA,GAAI9iB,MAAM,CAACqL,gBAAgB,CAClE,CAAC,CAAA;gBACH,CAAC,MAAM,IAAIsX,UAAU,KAAK,QAAQ,EAAE;oBAClC,2BAAA;oBACAD,UAAU,GAAGnyB,GAAG,CAACmH,GAAG,EAACjH,CAAC,IAAI;wBACxB,IAAIA,CAAC,CAACmE,EAAE,KAAKlF,MAAM,CAACkF,EAAE,EAAE;4BACtB,OAAO;gCACL,GAAGnE,CAAC;gCACJyxB,IAAI,EAAEU,QAAAA;6BACP,CAAA;wBACH,CAAA;wBACA,OAAOnyB,CAAC,CAAA;oBACV,CAAC,CAAC,CAAA;gBACJ,CAAC,MAAM,IAAIkyB,UAAU,KAAK,QAAQ,EAAE;oBAClCD,UAAU,GAAGnyB,GAAG,CAACkI,MAAM,EAAChI,CAAC,GAAIA,CAAC,CAACmE,EAAE,KAAKlF,MAAM,CAACkF,EAAE,CAAC,CAAA;gBAClD,CAAC,MAAM;oBACL8tB,UAAU,GAAG;wBACX;4BACE9tB,EAAE,EAAElF,MAAM,CAACkF,EAAE;4BACbstB,IAAI,EAAEU,QAAAA;wBACR,CAAC;qBACF,CAAA;gBACH,CAAA;gBAEA,OAAOF,UAAU,CAAA;YACnB,CAAC,CAAC,CAAA;SACH,CAAA;QAEDhzB,MAAM,CAACszB,eAAe,GAAG,MAAM;YAAA,IAAA3tB,IAAA,EAAA4tB,qBAAA,CAAA;YAC7B,MAAMC,aAAa,GAAA,CAAA7tB,IAAA,GAAA,CAAA4tB,qBAAA,GACjBvzB,MAAM,CAACwF,SAAS,CAACguB,aAAa,KAAA,IAAA,GAAAD,qBAAA,GAC9B9uB,KAAK,CAACO,OAAO,CAACwuB,aAAa,KAAA,IAAA,GAAA7tB,IAAA,GAC3B3F,MAAM,CAACmyB,cAAc,EAAE,KAAK,MAAM,CAAA;YACpC,OAAOqB,aAAa,GAAG,MAAM,GAAG,KAAK,CAAA;SACtC,CAAA;QAEDxzB,MAAM,CAAC2yB,mBAAmB,IAAIF,KAAe,IAAK;YAAA,IAAA1gB,qBAAA,EAAAC,sBAAA,CAAA;YAChD,MAAMyhB,kBAAkB,GAAGzzB,MAAM,CAACszB,eAAe,EAAE,CAAA;YACnD,MAAMI,QAAQ,GAAG1zB,MAAM,CAAC2zB,WAAW,EAAE,CAAA;YAErC,IAAI,CAACD,QAAQ,EAAE;gBACb,OAAOD,kBAAkB,CAAA;YAC3B,CAAA;YAEA,IACEC,QAAQ,KAAKD,kBAAkB,IAAA,CAAA,CAAA1hB,qBAAA,GAC9BtN,KAAK,CAACO,OAAO,CAAC4uB,oBAAoB,KAAA,IAAA,GAAA7hB,qBAAA,GAAI,IAAI,CAAC,IAAA,CAAI,yCAAA;YAC/C0gB,KAAK,GAAAzgB,CAAAA,sBAAA,GAAGvN,KAAK,CAACO,OAAO,CAAC6uB,iBAAiB,KAAA,IAAA,GAAA7hB,sBAAA,GAAI,IAAI,GAAG,IAAI,CAAC,CAAA,+CAAA;cACxD;gBACA,OAAO,KAAK,CAAA;YACd,CAAA;YACA,OAAO0hB,QAAQ,KAAK,MAAM,GAAG,KAAK,GAAG,MAAM,CAAA;SAC5C,CAAA;QAED1zB,MAAM,CAAC8zB,UAAU,GAAG,MAAM;YAAA,IAAAhiB,qBAAA,EAAAwd,sBAAA,CAAA;YACxB,OACE,CAAAxd,CAAAA,qBAAA,GAAC9R,MAAM,CAACwF,SAAS,CAACuuB,aAAa,KAAAjiB,IAAAA,GAAAA,qBAAA,GAAI,IAAI,KAAA,CAAA,CAAAwd,sBAAA,GACtC7qB,KAAK,CAACO,OAAO,CAAC+uB,aAAa,KAAA,IAAA,GAAAzE,sBAAA,GAAI,IAAI,CAAC,IACrC,CAAC,CAACtvB,MAAM,CAACC,UAAU,CAAA;SAEtB,CAAA;QAEDD,MAAM,CAACmzB,eAAe,GAAG,MAAM;YAAA,IAAAhW,KAAA,EAAA6W,sBAAA,CAAA;YAC7B,OAAA7W,CAAAA,KAAA,GAAA6W,CAAAA,sBAAA,GACEh0B,MAAM,CAACwF,SAAS,CAACyuB,eAAe,KAAA,IAAA,GAAAD,sBAAA,GAChCvvB,KAAK,CAACO,OAAO,CAACivB,eAAe,KAAA9W,IAAAA,GAAAA,KAAA,GAC7B,CAAC,CAACnd,MAAM,CAACC,UAAU,CAAA;SAEtB,CAAA;QAEDD,MAAM,CAAC2zB,WAAW,GAAG,MAAM;YAAA,IAAAO,qBAAA,CAAA;YACzB,MAAMC,UAAU,GAAAD,CAAAA,qBAAA,GAAGzvB,KAAK,CAAC6D,QAAQ,EAAE,CAACopB,OAAO,KAAA,IAAA,GAAA,KAAA,CAAA,GAAxBwC,qBAAA,CAA0BprB,IAAI,EAAC/H,CAAC,GAAIA,CAAC,CAACmE,EAAE,KAAKlF,MAAM,CAACkF,EAAE,CAAC,CAAA;YAE1E,OAAO,CAACivB,UAAU,GAAG,KAAK,GAAGA,UAAU,CAAC3B,IAAI,GAAG,MAAM,GAAG,KAAK,CAAA;SAC9D,CAAA;QAEDxyB,MAAM,CAACo0B,YAAY,GAAG,MAAA;YAAA,IAAAC,sBAAA,EAAAC,sBAAA,CAAA;YAAA,OAAA,CAAAD,sBAAA,GAAA,CAAAC,sBAAA,GACpB7vB,KAAK,CAAC6D,QAAQ,EAAE,CAACopB,OAAO,KAAA,IAAA,GAAA,KAAA,CAAA,GAAxB4C,sBAAA,CAA0B5hB,SAAS,EAAC3R,CAAC,GAAIA,CAAC,CAACmE,EAAE,KAAKlF,MAAM,CAACkF,EAAE,CAAC,KAAA,IAAA,GAAAmvB,sBAAA,GAAI,CAAC,CAAC,CAAA;QAAA,CAAA,CAAA;QAEpEr0B,MAAM,CAACu0B,YAAY,GAAG,MAAM;YAC1B,iCAAA;YACA9vB,KAAK,CAACouB,UAAU,EAAChyB,GAAG,GAClBA,GAAG,IAAA,IAAA,IAAHA,GAAG,CAAEmB,MAAM,GAAGnB,GAAG,CAACkI,MAAM,EAAChI,CAAC,GAAIA,CAAC,CAACmE,EAAE,KAAKlF,MAAM,CAACkF,EAAE,CAAC,GAAG,EACtD,CAAC,CAAA;SACF,CAAA;QAEDlF,MAAM,CAACw0B,uBAAuB,GAAG,MAAM;YACrC,MAAMC,OAAO,GAAGz0B,MAAM,CAAC8zB,UAAU,EAAE,CAAA;YAEnC,QAAQ/V,CAAU,IAAK;gBACrB,IAAI,CAAC0W,OAAO,EAAE,OAAA;gBACZ1W,CAAC,CAASC,OAAO,IAAA,IAAA,IAAjBD,CAAC,CAASC,OAAO,EAAI,CAAA;gBACvBhe,MAAM,CAACuyB,aAAa,IAApBvyB,IAAAA,IAAAA,MAAM,CAACuyB,aAAa,CAClBpsB,SAAS,EACTnG,MAAM,CAACmzB,eAAe,EAAE,GAAG1uB,KAAK,CAACO,OAAO,CAAC8sB,gBAAgB,IAAA,IAAA,GAAA,KAAA,CAAA,GAA9BrtB,KAAK,CAACO,OAAO,CAAC8sB,gBAAgB,CAAG/T,CAAC,CAAC,GAAG,KACnE,CAAC,CAAA;aACF,CAAA;SACF,CAAA;KACF;IAED7V,WAAW,GAA0BzD,KAAmB,IAAW;QACjEA,KAAK,CAACouB,UAAU,IAAGvyB,OAAO,GAAImE,KAAK,CAACO,OAAO,CAAC6sB,eAAe,IAAA,IAAA,GAAA,KAAA,CAAA,GAA7BptB,KAAK,CAACO,OAAO,CAAC6sB,eAAe,CAAGvxB,OAAO,CAAC,CAAA;QACtEmE,KAAK,CAACiwB,YAAY,IAAGlhB,YAAY,IAAI;YAAA,IAAAmhB,qBAAA,EAAAjhB,mBAAA,CAAA;YACnCjP,KAAK,CAACouB,UAAU,CAACrf,YAAY,GAAG,EAAE,GAAA,CAAAmhB,qBAAA,GAAA,CAAAjhB,mBAAA,GAAGjP,KAAK,CAACkP,YAAY,KAAA,OAAA,KAAA,IAAlBD,mBAAA,CAAoBge,OAAO,KAAAiD,IAAAA,GAAAA,qBAAA,GAAI,EAAE,CAAC,CAAA;SACxE,CAAA;QACDlwB,KAAK,CAACmwB,oBAAoB,GAAG,IAAMnwB,KAAK,CAAC4S,kBAAkB,EAAE,CAAA;QAC7D5S,KAAK,CAACwhB,iBAAiB,GAAG,MAAM;YAC9B,IAAI,CAACxhB,KAAK,CAACowB,kBAAkB,IAAIpwB,KAAK,CAACO,OAAO,CAACihB,iBAAiB,EAAE;gBAChExhB,KAAK,CAACowB,kBAAkB,GAAGpwB,KAAK,CAACO,OAAO,CAACihB,iBAAiB,CAACxhB,KAAK,CAAC,CAAA;YACnE,CAAA;YAEA,IAAIA,KAAK,CAACO,OAAO,CAAC8vB,aAAa,IAAI,CAACrwB,KAAK,CAACowB,kBAAkB,EAAE;gBAC5D,OAAOpwB,KAAK,CAACmwB,oBAAoB,EAAE,CAAA;YACrC,CAAA;YAEA,OAAOnwB,KAAK,CAACowB,kBAAkB,EAAE,CAAA;SAClC,CAAA;IACH,CAAA;AACF;ACrfA,MAAME,eAAe,GAAG;IACtB9sB,OAAO;IACPkZ,gBAAgB;IAChBlJ,cAAc;IACdoB,aAAa;IACbpL,cAAc;IACd0C,eAAe;IACfoS,cAAc;IAAE,2BAAA;IAChBO,eAAe;IAAE,4BAAA;IACjBmO,UAAU;IACVlc,cAAc;IAAE,uBAAA;IAChB4O,YAAY;IACZoD,aAAa;IACb6C,UAAU;IACVuC,YAAY;IACZxQ,YAAY;CACJ,CAAA;AAEV,EAAA;AAgOO,SAASjU,WAAWA,CACzBlD,OAAoC,EACtB;IAAA,IAAAgwB,kBAAA,EAAAC,qBAAA,CAAA;IACd,IACE5wB,OAAO,CAACC,GAAG,CAACC,QAAQ,gCAAK,YAAY,IAAA,CACpCS,OAAO,CAACZ,QAAQ,IAAIY,OAAO,CAACkwB,UAAU,CAAC,EACxC;QACAtxB,OAAO,CAACC,IAAI,CAAC,4BAA4B,CAAC,CAAA;IAC5C,CAAA;IAEA,MAAMwB,SAAS,GAAG,CAAC;WAAG0vB,eAAe,EAAE;WAAAC,CAAAA,kBAAA,GAAIhwB,OAAO,CAACK,SAAS,KAAA,IAAA,GAAA2vB,kBAAA,GAAI,EAAE;KAAE,CAAA;IAEpE,IAAIvwB,KAAK,GAAG;QAAEY,SAAAA;KAAsC,CAAA;IAEpD,MAAM8vB,cAAc,GAAG1wB,KAAK,CAACY,SAAS,CAACyI,MAAM,CAAC,CAAC6U,GAAG,EAAErd,OAAO,KAAK;QAC9D,OAAOuR,MAAM,CAACue,MAAM,CAACzS,GAAG,EAAErd,OAAO,CAAC2L,iBAAiB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAzB3L,OAAO,CAAC2L,iBAAiB,CAAGxM,KAAK,CAAC,CAAC,CAAA;KAC9D,EAAE,CAAA,CAAE,CAAgC,CAAA;IAErC,MAAM4wB,YAAY,IAAIrwB,OAAoC,IAAK;QAC7D,IAAIP,KAAK,CAACO,OAAO,CAACqwB,YAAY,EAAE;YAC9B,OAAO5wB,KAAK,CAACO,OAAO,CAACqwB,YAAY,CAACF,cAAc,EAAEnwB,OAAO,CAAC,CAAA;QAC5D,CAAA;QAEA,OAAO;YACL,GAAGmwB,cAAc;YACjB,GAAGnwB,OAAAA;SACJ,CAAA;KACF,CAAA;IAED,MAAMswB,gBAAgC,GAAG,CAAA,CAAE,CAAA;IAE3C,IAAI3hB,YAAY,GAAG;QACjB,GAAG2hB,gBAAgB;QACnB,GAAAL,CAAAA,qBAAA,GAAIjwB,OAAO,CAAC2O,YAAY,KAAAshB,IAAAA,GAAAA,qBAAA,GAAI,CAAA,CAAE;KACjB,CAAA;IAEfxwB,KAAK,CAACY,SAAS,CAACzD,OAAO,EAAC0D,OAAO,IAAI;QAAA,IAAAiwB,qBAAA,CAAA;QACjC5hB,YAAY,GAAA,CAAA4hB,qBAAA,GAAIjwB,OAAO,CAACwL,eAAe,IAAvBxL,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,OAAO,CAACwL,eAAe,CAAG6C,YAAY,CAAC,KAAA4hB,IAAAA,GAAAA,qBAAA,GACrD5hB,YAA2B,CAAA;IAC/B,CAAC,CAAC,CAAA;IAEF,MAAM6Q,MAAsB,GAAG,EAAE,CAAA;IACjC,IAAIgR,aAAa,GAAG,KAAK,CAAA;IAEzB,MAAMC,YAAiC,GAAG;QACxCpwB,SAAS;QACTL,OAAO,EAAE;YACP,GAAGmwB,cAAc;YACjB,GAAGnwB,OAAAA;SACJ;QACD2O,YAAY;QACZgR,MAAM,GAAE+Q,EAAE,IAAI;YACZlR,MAAM,CAAC1iB,IAAI,CAAC4zB,EAAE,CAAC,CAAA;YAEf,IAAI,CAACF,aAAa,EAAE;gBAClBA,aAAa,GAAG,IAAI,CAAA;gBAEpB,yDAAA;gBACA,qDAAA;gBACAG,OAAO,CAACC,OAAO,EAAE,CACdC,IAAI,CAAC,MAAM;oBACV,MAAOrR,MAAM,CAACxiB,MAAM,CAAE;wBACpBwiB,MAAM,CAACvL,KAAK,EAAE,EAAG,CAAA;oBACnB,CAAA;oBACAuc,aAAa,GAAG,KAAK,CAAA;iBACtB,CAAC,CACDM,KAAK,EAACC,KAAK,GACVC,UAAU,CAAC,MAAM;wBACf,MAAMD,KAAK,CAAA;oBACb,CAAC,CACH,CAAC,CAAA;YACL,CAAA;SACD;QACDE,KAAK,EAAEA,MAAM;YACXxxB,KAAK,CAAC7D,QAAQ,CAAC6D,KAAK,CAACkP,YAAY,CAAC,CAAA;SACnC;QACDuiB,UAAU,GAAE51B,OAAO,IAAI;YACrB,MAAM61B,UAAU,GAAG91B,gBAAgB,CAACC,OAAO,EAAEmE,KAAK,CAACO,OAAO,CAAC,CAAA;YAC3DP,KAAK,CAACO,OAAO,GAAGqwB,YAAY,CAACc,UAAU,CAGtC,CAAA;SACF;QAED7tB,QAAQ,EAAEA,MAAM;YACd,OAAO7D,KAAK,CAACO,OAAO,CAAC+L,KAAK,CAAA;SAC3B;QAEDnQ,QAAQ,GAAGN,OAA4B,IAAK;YAC1CmE,KAAK,CAACO,OAAO,CAACoxB,aAAa,IAA3B3xB,IAAAA,IAAAA,KAAK,CAACO,OAAO,CAACoxB,aAAa,CAAG91B,OAAO,CAAC,CAAA;SACvC;QAED+1B,SAAS,EAAEA,CAAC3xB,GAAU,EAAE1B,KAAa,EAAE0C,MAAmB,KAAA;YAAA,IAAAihB,qBAAA,CAAA;YAAA,OAAA,CAAAA,qBAAA,GACxDliB,KAAK,CAACO,OAAO,CAACsxB,QAAQ,IAAtB7xB,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,KAAK,CAACO,OAAO,CAACsxB,QAAQ,CAAG5xB,GAAG,EAAE1B,KAAK,EAAE0C,MAAM,CAAC,KAAAihB,IAAAA,GAAAA,qBAAA,GAC5C,CAAGjhB,EAAAA,MAAM,GAAG;gBAACA,MAAM,CAACR,EAAE;gBAAElC,KAAK;aAAC,CAAC4I,IAAI,CAAC,GAAG,CAAC,GAAG5I,KAAK,CAAE,CAAA,CAAA;QAAA,CAAA;QAEpDuO,eAAe,EAAEA,MAAM;YACrB,IAAI,CAAC9M,KAAK,CAAC8xB,gBAAgB,EAAE;gBAC3B9xB,KAAK,CAAC8xB,gBAAgB,GAAG9xB,KAAK,CAACO,OAAO,CAACuM,eAAe,CAAC9M,KAAK,CAAC,CAAA;YAC/D,CAAA;YAEA,OAAOA,KAAK,CAAC8xB,gBAAgB,EAAG,CAAA;SACjC;QAED,oDAAA;QACA,8CAAA;QAEA7Q,WAAW,EAAEA,MAAM;YACjB,OAAOjhB,KAAK,CAACglB,qBAAqB,EAAE,CAAA;SACrC;QACD,+EAAA;QACApc,MAAM,EAAEA,CAACnI,EAAU,EAAEsxB,SAAmB,KAAK;YAC3C,IAAI9xB,GAAG,GAAG,CACR8xB,SAAS,GAAG/xB,KAAK,CAAC4gB,wBAAwB,EAAE,GAAG5gB,KAAK,CAACihB,WAAW,EAAE,EAClEI,QAAQ,CAAC5gB,EAAE,CAAC,CAAA;YAEd,IAAI,CAACR,GAAG,EAAE;gBACRA,GAAG,GAAGD,KAAK,CAAC8M,eAAe,EAAE,CAACuU,QAAQ,CAAC5gB,EAAE,CAAC,CAAA;gBAC1C,IAAI,CAACR,GAAG,EAAE;oBACR,IAAIL,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,WAAc,CAAF;wBACvC,MAAM,IAAImC,KAAK,CAAC,CAAsCxB,mCAAAA,EAAAA,EAAE,EAAE,CAAC,CAAA;oBAC7D,CAAA;oBACA,MAAM,IAAIwB,KAAK,EAAE,CAAA;gBACnB,CAAA;YACF,CAAA;YAEA,OAAOhC,GAAG,CAAA;SACX;QACDoB,oBAAoB,EAAE7D,IAAI,CACxB,IAAM;gBAACwC,KAAK,CAACO,OAAO,CAACa,aAAa;aAAC,GACnCA,aAAa,IAAI;YAAA,IAAA4wB,cAAA,CAAA;YACf5wB,aAAa,GAAA,CAAA4wB,cAAA,GAAI5wB,aAAa,KAAA,OAAA4wB,cAAA,GAAI,CAAA,CAEjC,CAAA;YAED,OAAO;gBACLrwB,MAAM,GAAEqP,KAAK,IAAI;oBACf,MAAM1P,iBAAiB,GAAG0P,KAAK,CAACrP,MAAM,CAACpG,MAAM,CAC1CwF,SAAqC,CAAA;oBAExC,IAAIO,iBAAiB,CAAC7F,WAAW,EAAE;wBACjC,OAAO6F,iBAAiB,CAAC7F,WAAW,CAAA;oBACtC,CAAA;oBAEA,IAAI6F,iBAAiB,CAAC9F,UAAU,EAAE;wBAChC,OAAO8F,iBAAiB,CAACb,EAAE,CAAA;oBAC7B,CAAA;oBAEA,OAAO,IAAI,CAAA;iBACZ;gBACD,2CAAA;gBACAJ,IAAI,GAAE2Q,KAAK,IAAA;oBAAA,IAAAihB,qBAAA,EAAAC,kBAAA,CAAA;oBAAA,OAAAD,CAAAA,qBAAA,GAAA,CAAAC,kBAAA,GAAIlhB,KAAK,CAACtQ,WAAW,EAAO,KAAxBwxB,IAAAA,IAAAA,kBAAA,CAA0B7nB,QAAQ,IAAA,IAAA,GAAA,KAAA,CAAA,GAAlC6nB,kBAAA,CAA0B7nB,QAAQ,EAAI,KAAA,IAAA,GAAA4nB,qBAAA,GAAI,IAAI,CAAA;gBAAA,CAAA;gBAC7D,GAAGjyB,KAAK,CAACY,SAAS,CAACyI,MAAM,CAAC,CAAC6U,GAAG,EAAErd,OAAO,KAAK;oBAC1C,OAAOuR,MAAM,CAACue,MAAM,CAACzS,GAAG,EAAErd,OAAO,CAACsL,mBAAmB,IAAA,OAAA,KAAA,IAA3BtL,OAAO,CAACsL,mBAAmB,EAAI,CAAC,CAAA;iBAC3D,EAAE,CAAA,CAAE,CAAC;gBACN,GAAG/K,aAAAA;aACJ,CAAA;SACF,EACD7B,cAAc,CAACgB,OAAO,EAAE,cAAc,EAAE,sBAAsB,CAChE,CAAC;QAED4xB,cAAc,EAAEA,IAAMnyB,KAAK,CAACO,OAAO,CAAC2B,OAAO;QAE3CyB,aAAa,EAAEnG,IAAI,CACjB,IAAM;gBAACwC,KAAK,CAACmyB,cAAc,EAAE;aAAC,GAC9BC,UAAU,IAAI;YACZ,MAAMC,cAAc,GAAG,SACrBD,UAAuC,EACvCnxB,MAA+B,EAC/BD,KAAK,EACwB;gBAAA,IAD7BA,KAAK,KAAA,KAAA,CAAA,EAAA;oBAALA,KAAK,GAAG,CAAC,CAAA;gBAAA,CAAA;gBAET,OAAOoxB,UAAU,CAAC7uB,GAAG,EAACxC,SAAS,IAAI;oBACjC,MAAMxF,MAAM,GAAGuF,YAAY,CAACd,KAAK,EAAEe,SAAS,EAAEC,KAAK,EAAEC,MAAM,CAAC,CAAA;oBAE5D,MAAMqxB,iBAAiB,GAAGvxB,SAGzB,CAAA;oBAEDxF,MAAM,CAAC2G,OAAO,GAAGowB,iBAAiB,CAACpwB,OAAO,GACtCmwB,cAAc,CAACC,iBAAiB,CAACpwB,OAAO,EAAE3G,MAAM,EAAEyF,KAAK,GAAG,CAAC,CAAC,GAC5D,EAAE,CAAA;oBAEN,OAAOzF,MAAM,CAAA;gBACf,CAAC,CAAC,CAAA;aACH,CAAA;YAED,OAAO82B,cAAc,CAACD,UAAU,CAAC,CAAA;SAClC,EACD7yB,cAAc,CAACgB,OAAO,EAAE,cAAc,EAAE,eAAe,CACzD,CAAC;QAEDmd,iBAAiB,EAAElgB,IAAI,CACrB,IAAM;gBAACwC,KAAK,CAAC2D,aAAa,EAAE;aAAC,GAC7BM,UAAU,IAAI;YACZ,OAAOA,UAAU,CAAC5B,OAAO,EAAC9G,MAAM,IAAI;gBAClC,OAAOA,MAAM,CAAC4G,cAAc,EAAE,CAAA;YAChC,CAAC,CAAC,CAAA;SACH,EACD5C,cAAc,CAACgB,OAAO,EAAE,cAAc,EAAE,mBAAmB,CAC7D,CAAC;QAEDgyB,sBAAsB,EAAE/0B,IAAI,CAC1B,IAAM;gBAACwC,KAAK,CAAC0d,iBAAiB,EAAE;aAAC,GACjC8U,WAAW,IAAI;YACb,OAAOA,WAAW,CAACnpB,MAAM,CACvB,CAACC,GAAG,EAAE/N,MAAM,KAAK;gBACf+N,GAAG,CAAC/N,MAAM,CAACkF,EAAE,CAAC,GAAGlF,MAAM,CAAA;gBACvB,OAAO+N,GAAG,CAAA;aACX,EACD,CAAA,CACF,CAAC,CAAA;SACF,EACD/J,cAAc,CAACgB,OAAO,EAAE,cAAc,EAAE,uBAAuB,CACjE,CAAC;QAED2I,iBAAiB,EAAE1L,IAAI,CACrB,IAAM;gBAACwC,KAAK,CAAC2D,aAAa,EAAE;gBAAE3D,KAAK,CAACuC,kBAAkB,EAAE;aAAC,EACzD,CAAC0B,UAAU,EAAEzB,YAAY,KAAK;YAC5B,IAAIE,WAAW,GAAGuB,UAAU,CAAC5B,OAAO,EAAC9G,MAAM,GAAIA,MAAM,CAAC+G,cAAc,EAAE,CAAC,CAAA;YACvE,OAAOE,YAAY,CAACE,WAAW,CAAC,CAAA;SACjC,EACDnD,cAAc,CAACgB,OAAO,EAAE,cAAc,EAAE,mBAAmB,CAC7D,CAAC;QAEDgI,SAAS,GAAErI,QAAQ,IAAI;YACrB,MAAM3E,MAAM,GAAGyE,KAAK,CAACuyB,sBAAsB,EAAE,CAACryB,QAAQ,CAAC,CAAA;YAEvD,IAAIN,OAAO,CAACC,GAAG,CAACC,QAAQ,gCAAK,YAAY,IAAI,CAACvE,MAAM,EAAE;gBACpD4D,OAAO,CAACmyB,KAAK,CAAC,CAA2BpxB,wBAAAA,EAAAA,QAAQ,CAAA,iBAAA,CAAmB,CAAC,CAAA;YACvE,CAAA;YAEA,OAAO3E,MAAM,CAAA;QACf,CAAA;KACD,CAAA;IAED6W,MAAM,CAACue,MAAM,CAAC3wB,KAAK,EAAEgxB,YAAY,CAAC,CAAA;IAElC,IAAK,IAAIzyB,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGyB,KAAK,CAACY,SAAS,CAACrD,MAAM,EAAEgB,KAAK,EAAE,CAAE;QAC3D,MAAMsC,OAAO,GAAGb,KAAK,CAACY,SAAS,CAACrC,KAAK,CAAC,CAAA;QACtCsC,OAAO,IAAA,IAAA,IAAPA,OAAO,CAAE4C,WAAW,IAAA,IAAA,IAApB5C,OAAO,CAAE4C,WAAW,CAAGzD,KAAK,CAAC,CAAA;IAC/B,CAAA;IAEA,OAAOA,KAAK,CAAA;AACd;AC1gBO,SAAS8M,eAAeA,GAEJ;IACzB,QAAO9M,KAAK,GACVxC,IAAI,CACF,IAAM;gBAACwC,KAAK,CAACO,OAAO,CAACkyB,IAAI;aAAC,GAExBA,IAAI,IAKD;YACH,MAAMrJ,QAAyB,GAAG;gBAChC7D,IAAI,EAAE,EAAE;gBACRxY,QAAQ,EAAE,EAAE;gBACZsU,QAAQ,EAAE,CAAA,CAAC;aACZ,CAAA;YAED,MAAMqR,UAAU,GAAG,SACjBC,YAAqB,EACrB3xB,KAAK,EACLgI,SAAsB,EACL;gBAAA,IAFjBhI,KAAK,KAAA,KAAA,CAAA,EAAA;oBAALA,KAAK,GAAG,CAAC,CAAA;gBAAA,CAAA;gBAGT,MAAMukB,IAAI,GAAG,EAAkB,CAAA;gBAE/B,IAAK,IAAIhc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGopB,YAAY,CAACp1B,MAAM,EAAEgM,CAAC,EAAE,CAAE;oBAC5C,6FAAA;oBACA,aAAA;oBACA,iDAAA;oBACA,gEAAA;oBACA,MAAA;oBACA,IAAA;oBAEA,eAAA;oBACA,MAAMtJ,GAAG,GAAG8H,SAAS,CACnB/H,KAAK,EACLA,KAAK,CAAC4xB,SAAS,CAACe,YAAY,CAACppB,CAAC,CAAC,EAAGA,CAAC,EAAEP,SAAS,CAAC,EAC/C2pB,YAAY,CAACppB,CAAC,CAAC,EACfA,CAAC,EACDvI,KAAK,EACLU,SAAS,EACTsH,SAAS,IAAA,IAAA,GAAA,KAAA,CAAA,GAATA,SAAS,CAAEvI,EACb,CAAC,CAAA;oBAED,0CAAA;oBACA2oB,QAAQ,CAACrc,QAAQ,CAAC1P,IAAI,CAAC4C,GAAG,CAAC,CAAA;oBAC3B,yCAAA;oBACAmpB,QAAQ,CAAC/H,QAAQ,CAACphB,GAAG,CAACQ,EAAE,CAAC,GAAGR,GAAG,CAAA;oBAC/B,6BAAA;oBACAslB,IAAI,CAACloB,IAAI,CAAC4C,GAAG,CAAC,CAAA;oBAEd,2BAAA;oBACA,IAAID,KAAK,CAACO,OAAO,CAACqyB,UAAU,EAAE;wBAAA,IAAAC,oBAAA,CAAA;wBAC5B5yB,GAAG,CAAC6yB,eAAe,GAAG9yB,KAAK,CAACO,OAAO,CAACqyB,UAAU,CAC5CD,YAAY,CAACppB,CAAC,CAAC,EACfA,CACF,CAAC,CAAA;wBAED,+BAAA;wBACA,IAAAspB,CAAAA,oBAAA,GAAI5yB,GAAG,CAAC6yB,eAAe,KAAnBD,IAAAA,IAAAA,oBAAA,CAAqBt1B,MAAM,EAAE;4BAC/B0C,GAAG,CAACiI,OAAO,GAAGwqB,UAAU,CAACzyB,GAAG,CAAC6yB,eAAe,EAAE9xB,KAAK,GAAG,CAAC,EAAEf,GAAG,CAAC,CAAA;wBAC/D,CAAA;oBACF,CAAA;gBACF,CAAA;gBAEA,OAAOslB,IAAI,CAAA;aACZ,CAAA;YAED6D,QAAQ,CAAC7D,IAAI,GAAGmN,UAAU,CAACD,IAAI,CAAC,CAAA;YAEhC,OAAOrJ,QAAQ,CAAA;QACjB,CAAC,EACD7pB,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,aAAa,EAAE,IACzDP,KAAK,CAACijB,mBAAmB,EAC3B,CACF,CAAC,CAAA;AACL;AC9EO,SAASxB,mBAAmBA,GAER;IACzB,QAAOzhB,KAAK,GACVxC,IAAI,CACF,IAAM;gBACJwC,KAAK,CAAC6D,QAAQ,EAAE,CAAC8b,QAAQ;gBACzB3f,KAAK,CAACuhB,sBAAsB,EAAE;gBAC9BvhB,KAAK,CAACO,OAAO,CAACsf,oBAAoB;aACnC,EACD,CAACF,QAAQ,EAAEyJ,QAAQ,EAAEvJ,oBAAoB,KAAK;YAC5C,IACE,CAACuJ,QAAQ,CAAC7D,IAAI,CAAChoB,MAAM,IACpBoiB,QAAQ,KAAK,IAAI,IAAI,CAACvN,MAAM,CAAC4O,IAAI,CAACrB,QAAQ,IAARA,IAAAA,GAAAA,QAAQ,GAAI,CAAA,CAAE,CAAC,CAACpiB,MAAO,EAC1D;gBACA,OAAO6rB,QAAQ,CAAA;YACjB,CAAA;YAEA,IAAI,CAACvJ,oBAAoB,EAAE;gBACzB,6DAAA;gBACA,OAAOuJ,QAAQ,CAAA;YACjB,CAAA;YAEA,OAAO2J,UAAU,CAAC3J,QAAQ,CAAC,CAAA;SAC5B,EACD7pB,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,qBAAqB,CACnE,CAAC,CAAA;AACL,CAAA;AAEO,SAASwyB,UAAUA,CAAwB3J,QAAyB,EAAE;IAC3E,MAAM4J,YAA0B,GAAG,EAAE,CAAA;IAErC,MAAMC,SAAS,IAAIhzB,GAAe,IAAK;QAAA,IAAAkT,YAAA,CAAA;QACrC6f,YAAY,CAAC31B,IAAI,CAAC4C,GAAG,CAAC,CAAA;QAEtB,IAAI,CAAAkT,YAAA,GAAAlT,GAAG,CAACiI,OAAO,KAAXiL,IAAAA,IAAAA,YAAA,CAAa5V,MAAM,IAAI0C,GAAG,CAACihB,aAAa,EAAE,EAAE;YAC9CjhB,GAAG,CAACiI,OAAO,CAAC/K,OAAO,CAAC81B,SAAS,CAAC,CAAA;QAChC,CAAA;KACD,CAAA;IAED7J,QAAQ,CAAC7D,IAAI,CAACpoB,OAAO,CAAC81B,SAAS,CAAC,CAAA;IAEhC,OAAO;QACL1N,IAAI,EAAEyN,YAAY;QAClBjmB,QAAQ,EAAEqc,QAAQ,CAACrc,QAAQ;QAC3BsU,QAAQ,EAAE+H,QAAQ,CAAC/H,QAAAA;KACpB,CAAA;AACH;AC/CO,SAASrX,sBAAsBA,GAGE;IACtC,OAAO,CAAChK,KAAK,EAAEE,QAAQ,GACrB1C,IAAI,CACF,MAAA;YAAA,IAAA01B,gBAAA,CAAA;YAAA,OAAM;gBAAAA,CAAAA,gBAAA,GAAClzB,KAAK,CAACuI,SAAS,CAACrI,QAAQ,CAAC,KAAA,OAAA,KAAA,IAAzBgzB,gBAAA,CAA2BxpB,kBAAkB,EAAE;aAAC,CAAA;QAAA,CAAA,GACvDypB,eAAe,IAAI;YACjB,IAAI,CAACA,eAAe,EAAE,OAAOzxB,SAAS,CAAA;YAEtC,MAAM0xB,YAAY,GAAGD,eAAe,CAACpmB,QAAQ,CAC1C1K,OAAO,EAACgxB,OAAO,IAAA;gBAAA,IAAAC,qBAAA,CAAA;gBAAA,OAAAA,CAAAA,qBAAA,GAAID,OAAO,CAAC7qB,eAAe,CAACtI,QAAQ,CAAC,KAAA,IAAA,GAAAozB,qBAAA,GAAI,EAAE,CAAA;YAAA,CAAA,CAAC,CAC3D/vB,GAAG,CAACsI,MAAM,CAAC,CACXvH,MAAM,EAAC0I,KAAK,GAAI,CAACnB,MAAM,CAACC,KAAK,CAACkB,KAAK,CAAC,CAAC,CAAA;YAExC,IAAI,CAAComB,YAAY,CAAC71B,MAAM,EAAE,OAAA;YAE1B,IAAIg2B,eAAe,GAAGH,YAAY,CAAC,CAAC,CAAE,CAAA;YACtC,IAAII,eAAe,GAAGJ,YAAY,CAACA,YAAY,CAAC71B,MAAM,GAAG,CAAC,CAAE,CAAA;YAE5D,KAAK,MAAMyP,KAAK,IAAIomB,YAAY,CAAE;gBAChC,IAAIpmB,KAAK,GAAGumB,eAAe,EAAEA,eAAe,GAAGvmB,KAAK,CAC/C;qBAAA,IAAIA,KAAK,GAAGwmB,eAAe,EAAEA,eAAe,GAAGxmB,KAAK,CAAA;YAC3D,CAAA;YAEA,OAAO;gBAACumB,eAAe;gBAAEC,eAAe;aAAC,CAAA;SAC1C,EACDj0B,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,wBAAwB,CACtE,CAAC,CAAA;AACL;AC7BO,SAASkzB,UAAUA,CACxBlO,IAAkB,EAClBmO,aAAuC,EACvC1zB,KAAmB,EACnB;IACA,IAAIA,KAAK,CAACO,OAAO,CAACmM,kBAAkB,EAAE;QACpC,OAAOinB,uBAAuB,CAACpO,IAAI,EAAEmO,aAAa,EAAE1zB,KAAK,CAAC,CAAA;IAC5D,CAAA;IAEA,OAAO4zB,sBAAsB,CAACrO,IAAI,EAAEmO,aAAa,EAAE1zB,KAAK,CAAC,CAAA;AAC3D,CAAA;AAEA,SAAS2zB,uBAAuBA,CAC9BE,YAA0B,EAC1BC,SAA4C,EAC5C9zB,KAAmB,EACF;IAAA,IAAA+zB,qBAAA,CAAA;IACjB,MAAMC,mBAAiC,GAAG,EAAE,CAAA;IAC5C,MAAMC,mBAA+C,GAAG,CAAA,CAAE,CAAA;IAC1D,MAAMntB,QAAQ,GAAA,CAAAitB,qBAAA,GAAG/zB,KAAK,CAACO,OAAO,CAACoM,qBAAqB,KAAA,IAAA,GAAAonB,qBAAA,GAAI,GAAG,CAAA;IAE3D,MAAMG,iBAAiB,GAAG,SAACL,YAA0B,EAAE7yB,KAAK,EAAS;QAAA,IAAdA,KAAK,KAAA,KAAA,CAAA,EAAA;YAALA,KAAK,GAAG,CAAC,CAAA;QAAA,CAAA;QAC9D,MAAMukB,IAAkB,GAAG,EAAE,CAAA;QAE7B,gCAAA;QACA,IAAK,IAAIhc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsqB,YAAY,CAACt2B,MAAM,EAAEgM,CAAC,EAAE,CAAE;YAAA,IAAA4J,YAAA,CAAA;YAC5C,IAAIlT,GAAG,GAAG4zB,YAAY,CAACtqB,CAAC,CAAE,CAAA;YAE1B,MAAM4qB,MAAM,GAAGpsB,SAAS,CACtB/H,KAAK,EACLC,GAAG,CAACQ,EAAE,EACNR,GAAG,CAAC+H,QAAQ,EACZ/H,GAAG,CAAC1B,KAAK,EACT0B,GAAG,CAACe,KAAK,EACTU,SAAS,EACTzB,GAAG,CAACkI,QACN,CAAC,CAAA;YACDgsB,MAAM,CAAC5nB,aAAa,GAAGtM,GAAG,CAACsM,aAAa,CAAA;YAExC,IAAI,CAAA4G,YAAA,GAAAlT,GAAG,CAACiI,OAAO,KAAA,IAAA,IAAXiL,YAAA,CAAa5V,MAAM,IAAIyD,KAAK,GAAG8F,QAAQ,EAAE;gBAC3CqtB,MAAM,CAACjsB,OAAO,GAAGgsB,iBAAiB,CAACj0B,GAAG,CAACiI,OAAO,EAAElH,KAAK,GAAG,CAAC,CAAC,CAAA;gBAC1Df,GAAG,GAAGk0B,MAAM,CAAA;gBAEZ,IAAIL,SAAS,CAAC7zB,GAAG,CAAC,IAAI,CAACk0B,MAAM,CAACjsB,OAAO,CAAC3K,MAAM,EAAE;oBAC5CgoB,IAAI,CAACloB,IAAI,CAAC4C,GAAG,CAAC,CAAA;oBACdg0B,mBAAmB,CAACh0B,GAAG,CAACQ,EAAE,CAAC,GAAGR,GAAG,CAAA;oBACjC+zB,mBAAmB,CAAC32B,IAAI,CAAC4C,GAAG,CAAC,CAAA;oBAC7B,SAAA;gBACF,CAAA;gBAEA,IAAI6zB,SAAS,CAAC7zB,GAAG,CAAC,IAAIk0B,MAAM,CAACjsB,OAAO,CAAC3K,MAAM,EAAE;oBAC3CgoB,IAAI,CAACloB,IAAI,CAAC4C,GAAG,CAAC,CAAA;oBACdg0B,mBAAmB,CAACh0B,GAAG,CAACQ,EAAE,CAAC,GAAGR,GAAG,CAAA;oBACjC+zB,mBAAmB,CAAC32B,IAAI,CAAC4C,GAAG,CAAC,CAAA;oBAC7B,SAAA;gBACF,CAAA;YACF,CAAC,MAAM;gBACLA,GAAG,GAAGk0B,MAAM,CAAA;gBACZ,IAAIL,SAAS,CAAC7zB,GAAG,CAAC,EAAE;oBAClBslB,IAAI,CAACloB,IAAI,CAAC4C,GAAG,CAAC,CAAA;oBACdg0B,mBAAmB,CAACh0B,GAAG,CAACQ,EAAE,CAAC,GAAGR,GAAG,CAAA;oBACjC+zB,mBAAmB,CAAC32B,IAAI,CAAC4C,GAAG,CAAC,CAAA;gBAC/B,CAAA;YACF,CAAA;QACF,CAAA;QAEA,OAAOslB,IAAI,CAAA;KACZ,CAAA;IAED,OAAO;QACLA,IAAI,EAAE2O,iBAAiB,CAACL,YAAY,CAAC;QACrC9mB,QAAQ,EAAEinB,mBAAmB;QAC7B3S,QAAQ,EAAE4S,mBAAAA;KACX,CAAA;AACH,CAAA;AAEA,SAASL,sBAAsBA,CAC7BC,YAA0B,EAC1BC,SAAmC,EACnC9zB,KAAmB,EACF;IAAA,IAAAo0B,sBAAA,CAAA;IACjB,MAAMJ,mBAAiC,GAAG,EAAE,CAAA;IAC5C,MAAMC,mBAA+C,GAAG,CAAA,CAAE,CAAA;IAC1D,MAAMntB,QAAQ,GAAA,CAAAstB,sBAAA,GAAGp0B,KAAK,CAACO,OAAO,CAACoM,qBAAqB,KAAA,IAAA,GAAAynB,sBAAA,GAAI,GAAG,CAAA;IAE3D,oCAAA;IACA,MAAMF,iBAAiB,GAAG,SAACL,YAA0B,EAAE7yB,KAAK,EAAS;QAAA,IAAdA,KAAK,KAAA,KAAA,CAAA,EAAA;YAALA,KAAK,GAAG,CAAC,CAAA;QAAA,CAAA;QAC9D,qCAAA;QAEA,MAAMukB,IAAkB,GAAG,EAAE,CAAA;QAE7B,kCAAA;QACA,IAAK,IAAIhc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsqB,YAAY,CAACt2B,MAAM,EAAEgM,CAAC,EAAE,CAAE;YAC5C,IAAItJ,GAAG,GAAG4zB,YAAY,CAACtqB,CAAC,CAAE,CAAA;YAE1B,MAAM8qB,IAAI,GAAGP,SAAS,CAAC7zB,GAAG,CAAC,CAAA;YAE3B,IAAIo0B,IAAI,EAAE;gBAAA,IAAAjJ,aAAA,CAAA;gBACR,IAAI,CAAAA,aAAA,GAAAnrB,GAAG,CAACiI,OAAO,KAAA,IAAA,IAAXkjB,aAAA,CAAa7tB,MAAM,IAAIyD,KAAK,GAAG8F,QAAQ,EAAE;oBAC3C,MAAMqtB,MAAM,GAAGpsB,SAAS,CACtB/H,KAAK,EACLC,GAAG,CAACQ,EAAE,EACNR,GAAG,CAAC+H,QAAQ,EACZ/H,GAAG,CAAC1B,KAAK,EACT0B,GAAG,CAACe,KAAK,EACTU,SAAS,EACTzB,GAAG,CAACkI,QACN,CAAC,CAAA;oBACDgsB,MAAM,CAACjsB,OAAO,GAAGgsB,iBAAiB,CAACj0B,GAAG,CAACiI,OAAO,EAAElH,KAAK,GAAG,CAAC,CAAC,CAAA;oBAC1Df,GAAG,GAAGk0B,MAAM,CAAA;gBACd,CAAA;gBAEA5O,IAAI,CAACloB,IAAI,CAAC4C,GAAG,CAAC,CAAA;gBACd+zB,mBAAmB,CAAC32B,IAAI,CAAC4C,GAAG,CAAC,CAAA;gBAC7Bg0B,mBAAmB,CAACh0B,GAAG,CAACQ,EAAE,CAAC,GAAGR,GAAG,CAAA;YACnC,CAAA;QACF,CAAA;QAEA,OAAOslB,IAAI,CAAA;KACZ,CAAA;IAED,OAAO;QACLA,IAAI,EAAE2O,iBAAiB,CAACL,YAAY,CAAC;QACrC9mB,QAAQ,EAAEinB,mBAAmB;QAC7B3S,QAAQ,EAAE4S,mBAAAA;KACX,CAAA;AACH;AC7HO,SAASvqB,kBAAkBA,GAGP;IACzB,OAAO,CAAC1J,KAAK,EAAEE,QAAQ,GACrB1C,IAAI,CACF,IAAM;gBACJwC,KAAK,CAAC2J,sBAAsB,EAAE;gBAC9B3J,KAAK,CAAC6D,QAAQ,EAAE,CAAC0I,aAAa;gBAC9BvM,KAAK,CAAC6D,QAAQ,EAAE,CAACib,YAAY;gBAC7B9e,KAAK,CAACmP,mBAAmB,EAAE;aAC5B,EACD,CAACmlB,WAAW,EAAE/nB,aAAa,EAAEuS,YAAY,KAAK;YAC5C,IACE,CAACwV,WAAW,CAAC/O,IAAI,CAAChoB,MAAM,IACvB,CAAA,CAACgP,aAAa,IAAA,IAAA,IAAbA,aAAa,CAAEhP,MAAM,CAAI,IAAA,CAACuhB,YAAa,EACzC;gBACA,OAAOwV,WAAW,CAAA;YACpB,CAAA;YAEA,MAAMC,aAAa,GAAG,CACpB;mBAAGhoB,aAAa,CAAChJ,GAAG,EAACjH,CAAC,GAAIA,CAAC,CAACmE,EAAE,CAAC,CAAC6D,MAAM,EAAChI,CAAC,GAAIA,CAAC,KAAK4D,QAAQ,CAAC;gBAC3D4e,YAAY,GAAG,YAAY,GAAGpd,SAAS;aACxC,CAAC4C,MAAM,CAACC,OAAO,CAAa,CAAA;YAE7B,MAAMiwB,cAAc,IAAIv0B,GAAe,IAAK;gBAC1C,+CAAA;gBACA,IAAK,IAAIsJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgrB,aAAa,CAACh3B,MAAM,EAAEgM,CAAC,EAAE,CAAE;oBAC7C,IAAItJ,GAAG,CAACsM,aAAa,CAACgoB,aAAa,CAAChrB,CAAC,CAAC,CAAE,KAAK,KAAK,EAAE;wBAClD,OAAO,KAAK,CAAA;oBACd,CAAA;gBACF,CAAA;gBACA,OAAO,IAAI,CAAA;aACZ,CAAA;YAED,OAAOkqB,UAAU,CAACa,WAAW,CAAC/O,IAAI,EAAEiP,cAAc,EAAEx0B,KAAK,CAAC,CAAA;SAC3D,EACDT,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,oBAAoB,CAClE,CAAC,CAAA;AACL;ACxCO,SAASsJ,sBAAsBA,GAGV;IAC1B,OAAO,CAAC7J,KAAK,EAAEE,QAAQ,GACrB1C,IAAI,CACF,MAAA;YAAA,IAAA01B,gBAAA,CAAA;YAAA,OAAM;gBAAAA,CAAAA,gBAAA,GAAClzB,KAAK,CAACuI,SAAS,CAACrI,QAAQ,CAAC,KAAA,OAAA,KAAA,IAAzBgzB,gBAAA,CAA2BxpB,kBAAkB,EAAE;aAAC,CAAA;QAAA,CAAA,GACvDypB,eAAe,IAAI;YACjB,IAAI,CAACA,eAAe,EAAE,OAAO,IAAIrpB,GAAG,EAAE,CAAA;YAEtC,IAAI2qB,mBAAmB,GAAG,IAAI3qB,GAAG,EAAe,CAAA;YAEhD,IAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4pB,eAAe,CAACpmB,QAAQ,CAACxP,MAAM,EAAEgM,CAAC,EAAE,CAAE;gBACxD,MAAMyG,MAAM,GACVmjB,eAAe,CAACpmB,QAAQ,CAACxD,CAAC,CAAC,CAAEf,eAAe,CAAStI,QAAQ,CAAC,CAAA;gBAEhE,IAAK,IAAIw0B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1kB,MAAM,CAACzS,MAAM,EAAEm3B,CAAC,EAAE,CAAE;oBACtC,MAAM1nB,KAAK,GAAGgD,MAAM,CAAC0kB,CAAC,CAAE,CAAA;oBAExB,IAAID,mBAAmB,CAAClO,GAAG,CAACvZ,KAAK,CAAC,EAAE;wBAAA,IAAA2nB,qBAAA,CAAA;wBAClCF,mBAAmB,CAACG,GAAG,CACrB5nB,KAAK,EACL,CAAA2nB,CAAAA,qBAAA,GAACF,mBAAmB,CAACI,GAAG,CAAC7nB,KAAK,CAAC,KAAA2nB,IAAAA,GAAAA,qBAAA,GAAI,CAAC,IAAI,CAC1C,CAAC,CAAA;oBACH,CAAC,MAAM;wBACLF,mBAAmB,CAACG,GAAG,CAAC5nB,KAAK,EAAE,CAAC,CAAC,CAAA;oBACnC,CAAA;gBACF,CAAA;YACF,CAAA;YAEA,OAAOynB,mBAAmB,CAAA;QAC5B,CAAC,EACDl1B,cAAc,CACZS,KAAK,CAACO,OAAO,EACb,YAAY,EACZ,CAAA,uBAAA,EAA0BL,QAAQ,CAAA,CACpC,CACF,CAAC,CAAA;AACL;ACpCO,SAASiP,mBAAmBA,GAER;IACzB,QAAOnP,KAAK,GACVxC,IAAI,CACF,IAAM;gBACJwC,KAAK,CAAC2J,sBAAsB,EAAE;gBAC9B3J,KAAK,CAAC6D,QAAQ,EAAE,CAAC0I,aAAa;gBAC9BvM,KAAK,CAAC6D,QAAQ,EAAE,CAACib,YAAY;aAC9B,EACD,CAACsK,QAAQ,EAAE7c,aAAa,EAAEuS,YAAY,KAAK;YACzC,IACE,CAACsK,QAAQ,CAAC7D,IAAI,CAAChoB,MAAM,IACpB,CAAA,CAACgP,aAAa,IAAA,IAAA,IAAbA,aAAa,CAAEhP,MAAM,CAAI,IAAA,CAACuhB,YAAa,EACzC;gBACA,IAAK,IAAIvV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6f,QAAQ,CAACrc,QAAQ,CAACxP,MAAM,EAAEgM,CAAC,EAAE,CAAE;oBACjD6f,QAAQ,CAACrc,QAAQ,CAACxD,CAAC,CAAC,CAAEgD,aAAa,GAAG,CAAA,CAAE,CAAA;oBACxC6c,QAAQ,CAACrc,QAAQ,CAACxD,CAAC,CAAC,CAAEoF,iBAAiB,GAAG,CAAA,CAAE,CAAA;gBAC9C,CAAA;gBACA,OAAOya,QAAQ,CAAA;YACjB,CAAA;YAEA,MAAM0L,qBAAoD,GAAG,EAAE,CAAA;YAC/D,MAAMC,qBAAoD,GAAG,EAAE,CAAA;YAE9D,CAACxoB,aAAa,IAAA,OAAbA,aAAa,GAAI,EAAE,EAAEpP,OAAO,EAACb,CAAC,IAAI;gBAAA,IAAA04B,qBAAA,CAAA;gBAClC,MAAMz5B,MAAM,GAAGyE,KAAK,CAACuI,SAAS,CAACjM,CAAC,CAACmE,EAAE,CAAC,CAAA;gBAEpC,IAAI,CAAClF,MAAM,EAAE;oBACX,OAAA;gBACF,CAAA;gBAEA,MAAM6Q,QAAQ,GAAG7Q,MAAM,CAAC0R,WAAW,EAAE,CAAA;gBAErC,IAAI,CAACb,QAAQ,EAAE;oBACb,IAAIxM,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,WAAc,CAAF;wBACvCX,OAAO,CAAC6C,IAAI,CACV,CAAA,iEAAA,EAAoEzG,MAAM,CAACkF,EAAE,CAAA,CAAA,CAC/E,CAAC,CAAA;oBACH,CAAA;oBACA,OAAA;gBACF,CAAA;gBAEAq0B,qBAAqB,CAACz3B,IAAI,CAAC;oBACzBoD,EAAE,EAAEnE,CAAC,CAACmE,EAAE;oBACR2L,QAAQ;oBACR2c,aAAa,EAAA,CAAAiM,qBAAA,GAAE5oB,QAAQ,CAACb,kBAAkB,IAAA,OAAA,KAAA,IAA3Ba,QAAQ,CAACb,kBAAkB,CAAGjP,CAAC,CAAC0Q,KAAK,CAAC,KAAA,OAAAgoB,qBAAA,GAAI14B,CAAC,CAAC0Q,KAAAA;gBAC7D,CAAC,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;YAEF,MAAMunB,aAAa,GAAG,CAAChoB,aAAa,IAAA,IAAA,GAAbA,aAAa,GAAI,EAAE,EAAEhJ,GAAG,EAACjH,CAAC,GAAIA,CAAC,CAACmE,EAAE,CAAC,CAAA;YAE1D,MAAMue,cAAc,GAAGhf,KAAK,CAACuf,iBAAiB,EAAE,CAAA;YAEhD,MAAM0V,yBAAyB,GAAGj1B,KAAK,CACpCkJ,iBAAiB,EAAE,CACnB5E,MAAM,EAAC/I,MAAM,GAAIA,MAAM,CAAC4jB,kBAAkB,EAAE,CAAC,CAAA;YAEhD,IACEL,YAAY,IACZE,cAAc,IACdiW,yBAAyB,CAAC13B,MAAM,EAChC;gBACAg3B,aAAa,CAACl3B,IAAI,CAAC,YAAY,CAAC,CAAA;gBAEhC43B,yBAAyB,CAAC93B,OAAO,EAAC5B,MAAM,IAAI;oBAAA,IAAA25B,qBAAA,CAAA;oBAC1CH,qBAAqB,CAAC13B,IAAI,CAAC;wBACzBoD,EAAE,EAAElF,MAAM,CAACkF,EAAE;wBACb2L,QAAQ,EAAE4S,cAAc;wBACxB+J,aAAa,EAAAmM,CAAAA,qBAAA,GACXlW,cAAc,CAACzT,kBAAkB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAjCyT,cAAc,CAACzT,kBAAkB,CAAGuT,YAAY,CAAC,KAAA,IAAA,GAAAoW,qBAAA,GACjDpW,YAAAA;oBACJ,CAAC,CAAC,CAAA;gBACJ,CAAC,CAAC,CAAA;YACJ,CAAA;YAEA,IAAIqW,mBAAmB,CAAA;YACvB,IAAIC,mBAAmB,CAAA;YAEvB,wDAAA;YACA,IAAK,IAAIV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtL,QAAQ,CAACrc,QAAQ,CAACxP,MAAM,EAAEm3B,CAAC,EAAE,CAAE;gBACjD,MAAMz0B,GAAG,GAAGmpB,QAAQ,CAACrc,QAAQ,CAAC2nB,CAAC,CAAE,CAAA;gBAEjCz0B,GAAG,CAACsM,aAAa,GAAG,CAAA,CAAE,CAAA;gBAEtB,IAAIuoB,qBAAqB,CAACv3B,MAAM,EAAE;oBAChC,IAAK,IAAIgM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGurB,qBAAqB,CAACv3B,MAAM,EAAEgM,CAAC,EAAE,CAAE;wBACrD4rB,mBAAmB,GAAGL,qBAAqB,CAACvrB,CAAC,CAAE,CAAA;wBAC/C,MAAM9I,EAAE,GAAG00B,mBAAmB,CAAC10B,EAAE,CAAA;wBAEjC,2CAAA;wBACAR,GAAG,CAACsM,aAAa,CAAC9L,EAAE,CAAC,GAAG00B,mBAAmB,CAAC/oB,QAAQ,CAClDnM,GAAG,EACHQ,EAAE,EACF00B,mBAAmB,CAACpM,aAAa,GACjCsM,UAAU,IAAI;4BACZp1B,GAAG,CAAC0O,iBAAiB,CAAClO,EAAE,CAAC,GAAG40B,UAAU,CAAA;wBACxC,CACF,CAAC,CAAA;oBACH,CAAA;gBACF,CAAA;gBAEA,IAAIN,qBAAqB,CAACx3B,MAAM,EAAE;oBAChC,IAAK,IAAIgM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwrB,qBAAqB,CAACx3B,MAAM,EAAEgM,CAAC,EAAE,CAAE;wBACrD6rB,mBAAmB,GAAGL,qBAAqB,CAACxrB,CAAC,CAAE,CAAA;wBAC/C,MAAM9I,EAAE,GAAG20B,mBAAmB,CAAC30B,EAAE,CAAA;wBACjC,wDAAA;wBACA,IACE20B,mBAAmB,CAAChpB,QAAQ,CAC1BnM,GAAG,EACHQ,EAAE,EACF20B,mBAAmB,CAACrM,aAAa,GACjCsM,UAAU,IAAI;4BACZp1B,GAAG,CAAC0O,iBAAiB,CAAClO,EAAE,CAAC,GAAG40B,UAAU,CAAA;wBACxC,CACF,CAAC,EACD;4BACAp1B,GAAG,CAACsM,aAAa,CAAC+oB,UAAU,GAAG,IAAI,CAAA;4BACnC,MAAA;wBACF,CAAA;oBACF,CAAA;oBAEA,IAAIr1B,GAAG,CAACsM,aAAa,CAAC+oB,UAAU,KAAK,IAAI,EAAE;wBACzCr1B,GAAG,CAACsM,aAAa,CAAC+oB,UAAU,GAAG,KAAK,CAAA;oBACtC,CAAA;gBACF,CAAA;YACF,CAAA;YAEA,MAAMd,cAAc,IAAIv0B,GAAe,IAAK;gBAC1C,+CAAA;gBACA,IAAK,IAAIsJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgrB,aAAa,CAACh3B,MAAM,EAAEgM,CAAC,EAAE,CAAE;oBAC7C,IAAItJ,GAAG,CAACsM,aAAa,CAACgoB,aAAa,CAAChrB,CAAC,CAAC,CAAE,KAAK,KAAK,EAAE;wBAClD,OAAO,KAAK,CAAA;oBACd,CAAA;gBACF,CAAA;gBACA,OAAO,IAAI,CAAA;aACZ,CAAA;YAED,oDAAA;YACA,OAAOkqB,UAAU,CAACrK,QAAQ,CAAC7D,IAAI,EAAEiP,cAAc,EAAEx0B,KAAK,CAAC,CAAA;QACzD,CAAC,EACDT,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,qBAAqB,EAAE,IACjEP,KAAK,CAACijB,mBAAmB,EAC3B,CACF,CAAC,CAAA;AACL;ACjJO,SAASrQ,kBAAkBA,GAEP;IACzB,QAAO5S,KAAK,GACVxC,IAAI,CACF,IAAM;gBAACwC,KAAK,CAAC6D,QAAQ,EAAE,CAACuN,QAAQ;gBAAEpR,KAAK,CAAC2S,qBAAqB,EAAE;aAAC,EAChE,CAACvB,QAAQ,EAAEgY,QAAQ,KAAK;YACtB,IAAI,CAACA,QAAQ,CAAC7D,IAAI,CAAChoB,MAAM,IAAI,CAAC6T,QAAQ,CAAC7T,MAAM,EAAE;gBAC7C6rB,QAAQ,CAAC7D,IAAI,CAACpoB,OAAO,EAAC8C,GAAG,IAAI;oBAC3BA,GAAG,CAACe,KAAK,GAAG,CAAC,CAAA;oBACbf,GAAG,CAACkI,QAAQ,GAAGzG,SAAS,CAAA;gBAC1B,CAAC,CAAC,CAAA;gBACF,OAAO0nB,QAAQ,CAAA;YACjB,CAAA;YAEA,sDAAA;YACA,MAAMmM,gBAAgB,GAAGnkB,QAAQ,CAAC9M,MAAM,EAACpE,QAAQ,GAC/CF,KAAK,CAACuI,SAAS,CAACrI,QAAQ,CAC1B,CAAC,CAAA;YAED,MAAMs1B,eAA6B,GAAG,EAAE,CAAA;YACxC,MAAMC,eAA2C,GAAG,CAAA,CAAE,CAAA;YACtD,yCAAA;YACA,sDAAA;YACA,wCAAA;YACA,qDAAA;YAEA,6BAAA;YACA,MAAMC,kBAAkB,GAAG,SACzBnQ,IAAkB,EAClBvkB,KAAK,EACLmH,QAAiB,EACd;gBAAA,IAFHnH,KAAK,KAAA,KAAA,CAAA,EAAA;oBAALA,KAAK,GAAG,CAAC,CAAA;gBAAA,CAAA;gBAGT,mCAAA;gBACA,mEAAA;gBACA,IAAIA,KAAK,IAAIu0B,gBAAgB,CAACh4B,MAAM,EAAE;oBACpC,OAAOgoB,IAAI,CAAChiB,GAAG,EAACtD,GAAG,IAAI;wBACrBA,GAAG,CAACe,KAAK,GAAGA,KAAK,CAAA;wBAEjBw0B,eAAe,CAACn4B,IAAI,CAAC4C,GAAG,CAAC,CAAA;wBACzBw1B,eAAe,CAACx1B,GAAG,CAACQ,EAAE,CAAC,GAAGR,GAAG,CAAA;wBAE7B,IAAIA,GAAG,CAACiI,OAAO,EAAE;4BACfjI,GAAG,CAACiI,OAAO,GAAGwtB,kBAAkB,CAACz1B,GAAG,CAACiI,OAAO,EAAElH,KAAK,GAAG,CAAC,EAAEf,GAAG,CAACQ,EAAE,CAAC,CAAA;wBAClE,CAAA;wBAEA,OAAOR,GAAG,CAAA;oBACZ,CAAC,CAAC,CAAA;gBACJ,CAAA;gBAEA,MAAMC,QAAgB,GAAGq1B,gBAAgB,CAACv0B,KAAK,CAAE,CAAA;gBAEjD,yCAAA;gBACA,MAAM20B,YAAY,GAAGC,OAAO,CAACrQ,IAAI,EAAErlB,QAAQ,CAAC,CAAA;gBAE5C,sCAAA;gBACA,MAAM21B,qBAAqB,GAAGp5B,KAAK,CAAC+T,IAAI,CAACmlB,YAAY,CAACG,OAAO,EAAE,CAAC,CAACvyB,GAAG,CAClE,CAAArC,IAAA,EAA+B3C,KAAK,KAAK;oBAAA,IAAxC,CAACw3B,aAAa,EAAEC,WAAW,CAAC,GAAA90B,IAAA,CAAA;oBAC3B,IAAIT,EAAE,GAAG,CAAA,EAAGP,QAAQ,CAAA,CAAA,EAAI61B,aAAa,CAAE,CAAA,CAAA;oBACvCt1B,EAAE,GAAG0H,QAAQ,GAAG,CAAA,EAAGA,QAAQ,CAAI1H,CAAAA,EAAAA,EAAE,CAAE,CAAA,GAAGA,EAAE,CAAA;oBAExC,sDAAA;oBACA,MAAMyH,OAAO,GAAGwtB,kBAAkB,CAACM,WAAW,EAAEh1B,KAAK,GAAG,CAAC,EAAEP,EAAE,CAAC,CAAA;oBAE9DyH,OAAO,CAAC/K,OAAO,EAACuuB,MAAM,IAAI;wBACxBA,MAAM,CAACvjB,QAAQ,GAAG1H,EAAE,CAAA;oBACtB,CAAC,CAAC,CAAA;oBAEF,kDAAA;oBACA,MAAMoP,QAAQ,GAAG7O,KAAK,GAClBnE,SAAS,CAACm5B,WAAW,GAAE/1B,GAAG,GAAIA,GAAG,CAACiI,OAAO,CAAC,GAC1C8tB,WAAW,CAAA;oBAEf,MAAM/1B,GAAG,GAAG8H,SAAS,CACnB/H,KAAK,EACLS,EAAE,EACFoP,QAAQ,CAAC,CAAC,CAAC,CAAE7H,QAAQ,EACrBzJ,KAAK,EACLyC,KAAK,EACLU,SAAS,EACTyG,QACF,CAAC,CAAA;oBAEDiK,MAAM,CAACue,MAAM,CAAC1wB,GAAG,EAAE;wBACjB8S,gBAAgB,EAAE7S,QAAQ;wBAC1B61B,aAAa;wBACb7tB,OAAO;wBACP2H,QAAQ;wBACRvP,QAAQ,GAAGJ,QAAgB,IAAK;4BAC9B,mDAAA;4BACA,IAAIq1B,gBAAgB,CAAC3zB,QAAQ,CAAC1B,QAAQ,CAAC,EAAE;gCACvC,IAAID,GAAG,CAACmI,YAAY,CAACE,cAAc,CAACpI,QAAQ,CAAC,EAAE;oCAC7C,OAAOD,GAAG,CAACmI,YAAY,CAAClI,QAAQ,CAAC,CAAA;gCACnC,CAAA;gCAEA,IAAI81B,WAAW,CAAC,CAAC,CAAC,EAAE;oCAAA,IAAAC,qBAAA,CAAA;oCAClBh2B,GAAG,CAACmI,YAAY,CAAClI,QAAQ,CAAC,GAAA+1B,CAAAA,qBAAA,GACxBD,WAAW,CAAC,CAAC,CAAC,CAAC11B,QAAQ,CAACJ,QAAQ,CAAC,KAAA+1B,IAAAA,GAAAA,qBAAA,GAAIv0B,SAAS,CAAA;gCAClD,CAAA;gCAEA,OAAOzB,GAAG,CAACmI,YAAY,CAAClI,QAAQ,CAAC,CAAA;4BACnC,CAAA;4BAEA,IAAID,GAAG,CAAC+S,oBAAoB,CAAC1K,cAAc,CAACpI,QAAQ,CAAC,EAAE;gCACrD,OAAOD,GAAG,CAAC+S,oBAAoB,CAAC9S,QAAQ,CAAC,CAAA;4BAC3C,CAAA;4BAEA,uBAAA;4BACA,MAAM3E,MAAM,GAAGyE,KAAK,CAACuI,SAAS,CAACrI,QAAQ,CAAC,CAAA;4BACxC,MAAMg2B,WAAW,GAAG36B,MAAM,IAAA,IAAA,GAAA,KAAA,CAAA,GAANA,MAAM,CAAE+W,gBAAgB,EAAE,CAAA;4BAE9C,IAAI4jB,WAAW,EAAE;gCACfj2B,GAAG,CAAC+S,oBAAoB,CAAC9S,QAAQ,CAAC,GAAGg2B,WAAW,CAC9Ch2B,QAAQ,EACR2P,QAAQ,EACRmmB,WACF,CAAC,CAAA;gCAED,OAAO/1B,GAAG,CAAC+S,oBAAoB,CAAC9S,QAAQ,CAAC,CAAA;4BAC3C,CAAA;wBACF,CAAA;oBACF,CAAC,CAAC,CAAA;oBAEFgI,OAAO,CAAC/K,OAAO,EAACuuB,MAAM,IAAI;wBACxB8J,eAAe,CAACn4B,IAAI,CAACquB,MAAM,CAAC,CAAA;wBAC5B+J,eAAe,CAAC/J,MAAM,CAACjrB,EAAE,CAAC,GAAGirB,MAAM,CAAA;oBACnC,iCAAA;oBACA,sCAAA;oBACA,6CAAA;oBACA,WAAA;oBACA,qCAAA;oBACA,4CAAA;oBACA,IAAA;oBACF,CAAC,CAAC,CAAA;oBAEF,OAAOzrB,GAAG,CAAA;gBACZ,CACF,CAAC,CAAA;gBAED,OAAO41B,qBAAqB,CAAA;aAC7B,CAAA;YAED,MAAMG,WAAW,GAAGN,kBAAkB,CAACtM,QAAQ,CAAC7D,IAAI,EAAE,CAAC,CAAC,CAAA;YAExDyQ,WAAW,CAAC74B,OAAO,EAACuuB,MAAM,IAAI;gBAC5B8J,eAAe,CAACn4B,IAAI,CAACquB,MAAM,CAAC,CAAA;gBAC5B+J,eAAe,CAAC/J,MAAM,CAACjrB,EAAE,CAAC,GAAGirB,MAAM,CAAA;YACnC,iCAAA;YACA,sCAAA;YACA,6CAAA;YACA,WAAA;YACA,qCAAA;YACA,4CAAA;YACA,IAAA;YACF,CAAC,CAAC,CAAA;YAEF,OAAO;gBACLnG,IAAI,EAAEyQ,WAAW;gBACjBjpB,QAAQ,EAAEyoB,eAAe;gBACzBnU,QAAQ,EAAEoU,eAAAA;aACX,CAAA;SACF,EACDl2B,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,oBAAoB,EAAE,MAAM;YACtEP,KAAK,CAACkgB,MAAM,CAAC,MAAM;gBACjBlgB,KAAK,CAACggB,kBAAkB,EAAE,CAAA;gBAC1BhgB,KAAK,CAACijB,mBAAmB,EAAE,CAAA;YAC7B,CAAC,CAAC,CAAA;QACJ,CAAC,CACH,CAAC,CAAA;AACL,CAAA;AAEA,SAAS2S,OAAOA,CAAwBrQ,IAAkB,EAAErlB,QAAgB,EAAE;IAC5E,MAAMi2B,QAAQ,GAAG,IAAIrsB,GAAG,EAAqB,CAAA;IAE7C,OAAOyb,IAAI,CAAClc,MAAM,CAAC,CAAC9F,GAAG,EAAEtD,GAAG,KAAK;QAC/B,MAAMm2B,MAAM,GAAG,CAAGn2B,EAAAA,GAAG,CAAC0R,gBAAgB,CAACzR,QAAQ,CAAC,CAAE,CAAA,CAAA;QAClD,MAAMm2B,QAAQ,GAAG9yB,GAAG,CAACsxB,GAAG,CAACuB,MAAM,CAAC,CAAA;QAChC,IAAI,CAACC,QAAQ,EAAE;YACb9yB,GAAG,CAACqxB,GAAG,CAACwB,MAAM,EAAE;gBAACn2B,GAAG;aAAC,CAAC,CAAA;QACxB,CAAC,MAAM;YACLo2B,QAAQ,CAACh5B,IAAI,CAAC4C,GAAG,CAAC,CAAA;QACpB,CAAA;QACA,OAAOsD,GAAG,CAAA;KACX,EAAE4yB,QAAQ,CAAC,CAAA;AACd;ACzLO,SAASnR,qBAAqBA,CAAwBrnB,IAE5D,EAAkD;IACjD,QAAOqC,KAAK,GACVxC,IAAI,CACF,IAAM;gBACJwC,KAAK,CAAC6D,QAAQ,EAAE,CAACkf,UAAU;gBAC3B/iB,KAAK,CAAC4gB,wBAAwB,EAAE;gBAChC5gB,KAAK,CAACO,OAAO,CAACsf,oBAAoB,GAC9Bne,SAAS,GACT1B,KAAK,CAAC6D,QAAQ,EAAE,CAAC8b,QAAQ;aAC9B,EACD,CAACoD,UAAU,EAAEqG,QAAQ,KAAK;YACxB,IAAI,CAACA,QAAQ,CAAC7D,IAAI,CAAChoB,MAAM,EAAE;gBACzB,OAAO6rB,QAAQ,CAAA;YACjB,CAAA;YAEA,MAAM,EAAEvG,QAAQ,EAAED,SAAAA,EAAW,GAAGG,UAAU,CAAA;YAC1C,IAAI,EAAEwC,IAAI,EAAExY,QAAQ,EAAEsU,QAAAA,EAAU,GAAG+H,QAAQ,CAAA;YAC3C,MAAMkN,SAAS,GAAGzT,QAAQ,GAAGD,SAAS,CAAA;YACtC,MAAM2T,OAAO,GAAGD,SAAS,GAAGzT,QAAQ,CAAA;YAEpC0C,IAAI,GAAGA,IAAI,CAACjN,KAAK,CAACge,SAAS,EAAEC,OAAO,CAAC,CAAA;YAErC,IAAIC,iBAAkC,CAAA;YAEtC,IAAI,CAACx2B,KAAK,CAACO,OAAO,CAACsf,oBAAoB,EAAE;gBACvC2W,iBAAiB,GAAGzD,UAAU,CAAC;oBAC7BxN,IAAI;oBACJxY,QAAQ;oBACRsU,QAAAA;gBACF,CAAC,CAAC,CAAA;YACJ,CAAC,MAAM;gBACLmV,iBAAiB,GAAG;oBAClBjR,IAAI;oBACJxY,QAAQ;oBACRsU,QAAAA;iBACD,CAAA;YACH,CAAA;YAEAmV,iBAAiB,CAACzpB,QAAQ,GAAG,EAAE,CAAA;YAE/B,MAAMkmB,SAAS,IAAIhzB,GAAe,IAAK;gBACrCu2B,iBAAiB,CAACzpB,QAAQ,CAAC1P,IAAI,CAAC4C,GAAG,CAAC,CAAA;gBACpC,IAAIA,GAAG,CAACiI,OAAO,CAAC3K,MAAM,EAAE;oBACtB0C,GAAG,CAACiI,OAAO,CAAC/K,OAAO,CAAC81B,SAAS,CAAC,CAAA;gBAChC,CAAA;aACD,CAAA;YAEDuD,iBAAiB,CAACjR,IAAI,CAACpoB,OAAO,CAAC81B,SAAS,CAAC,CAAA;YAEzC,OAAOuD,iBAAiB,CAAA;SACzB,EACDj3B,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,uBAAuB,CACrE,CAAC,CAAA;AACL;ACvDO,SAASihB,iBAAiBA,GAEN;IACzB,QAAOxhB,KAAK,GACVxC,IAAI,CACF,IAAM;gBAACwC,KAAK,CAAC6D,QAAQ,EAAE,CAACopB,OAAO;gBAAEjtB,KAAK,CAACmwB,oBAAoB,EAAE;aAAC,EAC9D,CAAClD,OAAO,EAAE7D,QAAQ,KAAK;YACrB,IAAI,CAACA,QAAQ,CAAC7D,IAAI,CAAChoB,MAAM,IAAI,CAAA,CAAC0vB,OAAO,IAAA,IAAA,IAAPA,OAAO,CAAE1vB,MAAM,CAAE,EAAA;gBAC7C,OAAO6rB,QAAQ,CAAA;YACjB,CAAA;YAEA,MAAMqN,YAAY,GAAGz2B,KAAK,CAAC6D,QAAQ,EAAE,CAACopB,OAAO,CAAA;YAE7C,MAAMyJ,cAA4B,GAAG,EAAE,CAAA;YAEvC,8DAAA;YACA,MAAMC,gBAAgB,GAAGF,YAAY,CAACnyB,MAAM,EAAC8L,IAAI,IAAA;gBAAA,IAAA8iB,gBAAA,CAAA;gBAAA,OAAA,CAAAA,gBAAA,GAC/ClzB,KAAK,CAACuI,SAAS,CAAC6H,IAAI,CAAC3P,EAAE,CAAC,KAAxByyB,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,gBAAA,CAA0B7D,UAAU,EAAE,CAAA;YAAA,CACxC,CAAC,CAAA;YAED,MAAMuH,cAOL,GAAG,CAAA,CAAE,CAAA;YAEND,gBAAgB,CAACx5B,OAAO,EAAC05B,SAAS,IAAI;gBACpC,MAAMt7B,MAAM,GAAGyE,KAAK,CAACuI,SAAS,CAACsuB,SAAS,CAACp2B,EAAE,CAAC,CAAA;gBAC5C,IAAI,CAAClF,MAAM,EAAE,OAAA;gBAEbq7B,cAAc,CAACC,SAAS,CAACp2B,EAAE,CAAC,GAAG;oBAC7B0sB,aAAa,EAAE5xB,MAAM,CAACwF,SAAS,CAACosB,aAAa;oBAC7C2J,aAAa,EAAEv7B,MAAM,CAACwF,SAAS,CAAC+1B,aAAa;oBAC7C5J,SAAS,EAAE3xB,MAAM,CAACoyB,YAAY,EAAC;iBAChC,CAAA;YACH,CAAC,CAAC,CAAA;YAEF,MAAMoJ,QAAQ,IAAIxR,IAAkB,IAAK;gBACvC,8DAAA;gBACA,aAAA;gBACA,MAAMyR,UAAU,GAAGzR,IAAI,CAAChiB,GAAG,EAACtD,GAAG,GAAA,CAAK;wBAAE,GAAGA,GAAAA;oBAAI,CAAC,CAAC,CAAC,CAAA;gBAEhD+2B,UAAU,CAAC5mB,IAAI,CAAC,CAAC0b,IAAI,EAAEC,IAAI,KAAK;oBAC9B,IAAK,IAAIxiB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGotB,gBAAgB,CAACp5B,MAAM,EAAEgM,CAAC,IAAI,CAAC,CAAE;wBAAA,IAAA0tB,eAAA,CAAA;wBACnD,MAAMJ,SAAS,GAAGF,gBAAgB,CAACptB,CAAC,CAAE,CAAA;wBACtC,MAAM2tB,UAAU,GAAGN,cAAc,CAACC,SAAS,CAACp2B,EAAE,CAAE,CAAA;wBAChD,MAAM0sB,aAAa,GAAG+J,UAAU,CAAC/J,aAAa,CAAA;wBAC9C,MAAMgK,MAAM,GAAA,CAAAF,eAAA,GAAGJ,SAAS,IAAA,IAAA,GAAA,KAAA,CAAA,GAATA,SAAS,CAAE9I,IAAI,KAAA,IAAA,GAAAkJ,eAAA,GAAI,KAAK,CAAA;wBAEvC,IAAIG,OAAO,GAAG,CAAC,CAAA;wBAEf,2DAAA;wBACA,IAAIjK,aAAa,EAAE;4BACjB,MAAMkK,MAAM,GAAGvL,IAAI,CAACxrB,QAAQ,CAACu2B,SAAS,CAACp2B,EAAE,CAAC,CAAA;4BAC1C,MAAM62B,MAAM,GAAGvL,IAAI,CAACzrB,QAAQ,CAACu2B,SAAS,CAACp2B,EAAE,CAAC,CAAA;4BAE1C,MAAM82B,UAAU,GAAGF,MAAM,KAAK31B,SAAS,CAAA;4BACvC,MAAM81B,UAAU,GAAGF,MAAM,KAAK51B,SAAS,CAAA;4BAEvC,IAAI61B,UAAU,IAAIC,UAAU,EAAE;gCAC5B,IAAIrK,aAAa,KAAK,OAAO,EAAE,OAAOoK,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;gCACzD,IAAIpK,aAAa,KAAK,MAAM,EAAE,OAAOoK,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;gCACxDH,OAAO,GACLG,UAAU,IAAIC,UAAU,GACpB,CAAC,GACDD,UAAU,GACRpK,aAAa,GACb,CAACA,aAAa,CAAA;4BACxB,CAAA;wBACF,CAAA;wBAEA,IAAIiK,OAAO,KAAK,CAAC,EAAE;4BACjBA,OAAO,GAAGF,UAAU,CAAChK,SAAS,CAACpB,IAAI,EAAEC,IAAI,EAAE8K,SAAS,CAACp2B,EAAE,CAAC,CAAA;wBAC1D,CAAA;wBAEA,0DAAA;wBACA,IAAI22B,OAAO,KAAK,CAAC,EAAE;4BACjB,IAAID,MAAM,EAAE;gCACVC,OAAO,IAAI,CAAC,CAAC,CAAA;4BACf,CAAA;4BAEA,IAAIF,UAAU,CAACJ,aAAa,EAAE;gCAC5BM,OAAO,IAAI,CAAC,CAAC,CAAA;4BACf,CAAA;4BAEA,OAAOA,OAAO,CAAA;wBAChB,CAAA;oBACF,CAAA;oBAEA,OAAOtL,IAAI,CAACvtB,KAAK,GAAGwtB,IAAI,CAACxtB,KAAK,CAAA;gBAChC,CAAC,CAAC,CAAA;gBAEF,mCAAA;gBACAy4B,UAAU,CAAC75B,OAAO,EAAC8C,GAAG,IAAI;oBAAA,IAAAkT,YAAA,CAAA;oBACxBujB,cAAc,CAACr5B,IAAI,CAAC4C,GAAG,CAAC,CAAA;oBACxB,IAAAkT,CAAAA,YAAA,GAAIlT,GAAG,CAACiI,OAAO,KAAXiL,IAAAA,IAAAA,YAAA,CAAa5V,MAAM,EAAE;wBACvB0C,GAAG,CAACiI,OAAO,GAAG6uB,QAAQ,CAAC92B,GAAG,CAACiI,OAAO,CAAC,CAAA;oBACrC,CAAA;gBACF,CAAC,CAAC,CAAA;gBAEF,OAAO8uB,UAAU,CAAA;aAClB,CAAA;YAED,OAAO;gBACLzR,IAAI,EAAEwR,QAAQ,CAAC3N,QAAQ,CAAC7D,IAAI,CAAC;gBAC7BxY,QAAQ,EAAE2pB,cAAc;gBACxBrV,QAAQ,EAAE+H,QAAQ,CAAC/H,QAAAA;aACpB,CAAA;QACH,CAAC,EACD9hB,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,mBAAmB,EAAE,IAC/DP,KAAK,CAACijB,mBAAmB,EAC3B,CACF,CAAC,CAAA;AACL", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33], "debugId": null}}]}