<?php

namespace App\Http\Controllers;

use Admission\Models\StudentDetails;
use Admission\Models\StudentDetailsView;
use Carbon\Carbon;
use Circulars\Models\Circulars;
use Enquiry\Models\Enquiry;
use Fees\Models\StudentPayments;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use StaffLeaveApply\Models\LeaveApply;
use StudentAttendance\Models\StudentAttendance;
use Passbook\Models\Passbook;
use Users\Models\User;
use Users\Models\UserDetails;
use Years\Models\YearWithUser;

class HomeController extends Controller
{
    public function index()
    {
        $circulars = Circulars::orderBy('id', 'desc')->take(10)->get();
        $department = departmentForStudentPortal();
        $subdomain = request()->getHost();

        $activeYearid = getActiveYearId();

        $studentBirthdays = Cache::remember("student_birthday_query_{$subdomain}", 1200, function () use ($activeYearid) {
            return StudentDetailsView::where('year_id', $activeYearid)
                ->where('status', "ACTIVE")
                ->whereRaw('EXTRACT(MONTH FROM student_details_view.date_of_birth) = ?', [Carbon::now()->month])
                ->whereRaw('EXTRACT(DAY FROM student_details_view.date_of_birth) >= ?', [Carbon::now()->day])
                ->leftJoin('student_details', 'student_details.id', '=', 'student_details_view.id')
                ->orderByRaw('EXTRACT(DAY FROM student_details_view.date_of_birth)')
                ->where('student_details_view.class_uuid', Auth::id())
                ->select(
                    'student_details_view.first_name',
                    'student_details_view.last_name',
                    'student_details_view.class_name',
                    'student_details_view.middle_name',
                    'student_details_view.date_of_birth',
                    'student_details_view.photo'
                )
                ->get();
        });

        $enquiryCount = Enquiry::whereDate('created_at', date('Y-m-d'))->where('class_uuid', Auth::id())->count();
        $feeCollectionToday = StudentPayments::whereDate('payment_date', date('Y-m-d'))
                ->leftJoin('student_details', 'student_details.id', '=', 'student_payment_details.student_id')
                ->where('student_details.class_uuid', Auth::id())
                ->sum('paid_amount');

        $totalStudentsCount = StudentDetailsView::where('year_id', $activeYearid)
            ->where('status', "ACTIVE")
            ->where('class_uuid', Auth::id())
            ->count();

        $totalAdmissions = StudentDetails::whereDate('created_at', date('Y-m-d'))->where('class_uuid', Auth::id())->count();

        $totalPresent = StudentAttendance::whereDate('date', date('Y-m-d'))->where('present', 1)->where('class_uuid', Auth::id())->count();

        $totalAbsent = StudentAttendance::leftJoin('student_details_view', 'student_attendance.student_id', '=', 'student_details_view.id')
            ->whereDate('date', date('Y-m-d'))
            ->where('present', 0)
            ->where('student_attendance.class_uuid', Auth::id())
            ->select(
                'student_details_view.id as sid',
                'student_details_view.first_name',
                'student_details_view.last_name',
                'student_details_view.department_name',
                'student_details_view.class_name',
                'student_details_view.gr_no',
                'student_attendance.*',
            )->get();

        $year = geActiveYearName();

        return view('home', compact(
            'department',
            'circulars',
            'enquiryCount',
            'feeCollectionToday',
            'totalStudentsCount',
            'totalAdmissions',
            'studentBirthdays',
            'totalPresent',
            'totalAbsent'
        ));
    }

    public function changeYear($id) {
        $year = YearWithUser::where("user_id", Auth::id())->first();

        Cache::forget('active_year_id_' . Auth::id());
        Cache::forget('fee_collection_query');

        if (isset($year)) {
            $year->update(["year_id" => $id]);
        } else {
            YearWithUser::create([
                'year_id' => $id,
                'user_id' => Auth::id()
            ]);
        }

        return response()->json(['success' => 'Year Updated Successfully!!']);
    }
}
