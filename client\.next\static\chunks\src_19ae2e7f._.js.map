{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/chatService.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\n\r\nexport const fetchingprivateMessages = async (userId1: string, userId2: string) => {\r\n  try {\r\n    const response = await axiosInstance.get(`chat/messages/private?userId1=${userId1}&userId2=${userId2}`, {\r\n      withCredentials: true\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error fetching private messages:', error);\r\n  }\r\n}\r\n\r\nexport const fetchingMessageUsers = async (userId: string, userType: 'student' | 'class') => {\r\n  try {\r\n    const response = await axiosInstance.get(`chat/messages/users?userId=${userId}&userType=${userType}`, {\r\n      withCredentials: true\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error fetching message users:', error);\r\n  }\r\n}\r\n\r\nexport const fetchUserDetails = async (userId: string, userType: 'student' | 'class') => {\r\n  try {\r\n    if (userType === 'class') {\r\n      const response = await axiosInstance.get(`classes/details/${userId}`);\r\n      return {\r\n        id: response.data.id,\r\n        firstName: response.data.firstName,\r\n        lastName: response.data.lastName,\r\n        email: response.data.email\r\n      };\r\n    }\r\n  } catch (error) {\r\n    console.error('Error fetching user details:', error);\r\n    return null;\r\n  }\r\n}\r\n\r\nexport const fetchingUnreadMessageUsers = async (userId: string, userType: 'student' | 'class') => {\r\n  try {\r\n    const response = await axiosInstance.get(`chat/messages/unread-users?userId=${userId}&userType=${userType}`, {\r\n      withCredentials: true\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error fetching unread message users:', error);\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEO,MAAM,0BAA0B,OAAO,SAAiB;IAC7D,IAAI;QACF,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,8BAA8B,EAAE,QAAQ,SAAS,EAAE,SAAS,EAAE;YACtG,iBAAiB;QACnB;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;IACpD;AACF;AAEO,MAAM,uBAAuB,OAAO,QAAgB;IACzD,IAAI;QACF,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,2BAA2B,EAAE,OAAO,UAAU,EAAE,UAAU,EAAE;YACpG,iBAAiB;QACnB;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;IACjD;AACF;AAEO,MAAM,mBAAmB,OAAO,QAAgB;IACrD,IAAI;QACF,IAAI,aAAa,SAAS;YACxB,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,gBAAgB,EAAE,QAAQ;YACpE,OAAO;gBACL,IAAI,SAAS,IAAI,CAAC,EAAE;gBACpB,WAAW,SAAS,IAAI,CAAC,SAAS;gBAClC,UAAU,SAAS,IAAI,CAAC,QAAQ;gBAChC,OAAO,SAAS,IAAI,CAAC,KAAK;YAC5B;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;IACT;AACF;AAEO,MAAM,6BAA6B,OAAO,QAAgB;IAC/D,IAAI;QACF,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,kCAAkC,EAAE,OAAO,UAAU,EAAE,UAAU,EAAE;YAC3G,iBAAiB;QACnB;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;IACxD;AACF", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/SharedChat.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect, useState, useRef, FormEvent, useMemo, useCallback } from 'react';\r\nimport {\r\n    Avatar,\r\n    AvatarFallback,\r\n} from '@/components/ui/avatar';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport {\r\n    ArrowLeft,\r\n    Search,\r\n    Send,\r\n    X,\r\n    MessageSquare,\r\n    Users,\r\n    Check,\r\n    CheckCheck,\r\n    RefreshCw\r\n} from 'lucide-react';\r\nimport { io, Socket } from 'socket.io-client';\r\nimport { format } from 'date-fns';\r\nimport { toast } from 'sonner';\r\nimport { ChatMessage, OnlineUser, SharedChatProps } from '@/lib/types';\r\nimport Image from 'next/image';\r\nimport { useIsMobile } from '@/hooks/use-mobile';\r\nimport { fetchingMessageUsers, fetchingprivateMessages, fetchUserDetails } from '@/services/chatService';\r\nimport { useRouter } from 'next/navigation';\r\nimport EmojiPicker, { EmojiStyle } from 'emoji-picker-react';\r\n\r\nexport default function SharedChat({ userType, isAuthenticated, username, userId, initialSelectedUser, initialSelectedUserId, initialSelectedUserName }: SharedChatProps) {\r\n    const [privateMessages, setPrivateMessages] = useState<ChatMessage[]>([]);\r\n    const [messageInput, setMessageInput] = useState('');\r\n    const [isUsernameSet, setIsUsernameSet] = useState(!!username);\r\n    const [onlineUsers, setOnlineUsers] = useState<OnlineUser[]>([]);\r\n    const [offlineMessageUsers, setOfflineMessageUsers] = useState<Array<{ username: string; userId: string }>>([]);\r\n    const [searchQuery, setSearchQuery] = useState('');\r\n    const [selectedUser, setSelectedUser] = useState<string | null>(initialSelectedUser || null);\r\n    const [selectedUserId, setSelectedUserId] = useState<string | null>(null);\r\n    const [currentRoomId, setCurrentRoomId] = useState<string | null>(null);\r\n    const [sidebarOpen, setSidebarOpen] = useState(true);\r\n    const [showEmojiPicker, setShowEmojiPicker] = useState(false);\r\n    const [seenMessages, setSeenMessages] = useState<Set<string>>(new Set());\r\n    const [recipientIsViewing, setRecipientIsViewing] = useState<boolean>(false);\r\n    const [unreadMessageCounts, setUnreadMessageCounts] = useState<Map<string, number>>(new Map());\r\n    const [userFilter, setUserFilter] = useState<'all' | 'unread'>('all');\r\n    const [isRefreshing, setIsRefreshing] = useState(false);\r\n    const [urlCleaned, setUrlCleaned] = useState(false);\r\n    const router = useRouter();\r\n\r\n    const messagesEndRef = useRef<HTMLDivElement>(null);\r\n    const socketRef = useRef<Socket | null>(null);\r\n    const emojiPickerRef = useRef<HTMLDivElement>(null);\r\n    const isMobile = useIsMobile();\r\n\r\n    const loadMessages = useCallback(async (currentUserId: string, targetUserId: string) => {\r\n        try {\r\n            const data = await fetchingprivateMessages(currentUserId, targetUserId);\r\n            setPrivateMessages(data || []);\r\n        } catch {\r\n            toast.error('Failed to load conversation history.');\r\n        }\r\n    }, []);\r\n\r\n    const cleanUrl = useCallback(() => {\r\n        if ((initialSelectedUserId || initialSelectedUserName) && !urlCleaned) {\r\n            router.replace('/student/chat');\r\n            setUrlCleaned(true);\r\n        }\r\n    }, [initialSelectedUserId, initialSelectedUserName, urlCleaned, router]);\r\n\r\n    const handleEmojiClick = (emojiData: any) => {\r\n        setMessageInput((prev) => prev + emojiData.emoji);\r\n    };\r\n\r\n    const toggleEmojiPicker = (e: React.MouseEvent) => {\r\n        e.preventDefault();\r\n        setShowEmojiPicker((prev) => !prev);\r\n    };\r\n\r\n    useEffect(() => {\r\n        setIsUsernameSet(!!username);\r\n    }, [username]);\r\n\r\n    useEffect(() => {\r\n        const handleClickOutside = (event: MouseEvent) => {\r\n            if (emojiPickerRef.current && !emojiPickerRef.current.contains(event.target as Node)) {\r\n                setShowEmojiPicker(false);\r\n            }\r\n        };\r\n\r\n        if (showEmojiPicker) {\r\n            document.addEventListener('mousedown', handleClickOutside);\r\n        }\r\n\r\n        return () => {\r\n            document.removeEventListener('mousedown', handleClickOutside);\r\n        };\r\n    }, [showEmojiPicker]);\r\n\r\n    useEffect(() => {\r\n        if (isAuthenticated && isUsernameSet && username) {\r\n            if (socketRef.current) {\r\n                socketRef.current.disconnect();\r\n            }\r\n\r\n            socketRef.current = io(process.env.NEXT_PUBLIC_API_BASE_URL, {\r\n                withCredentials: true,\r\n            });\r\n\r\n            socketRef.current.on('connect', () => {\r\n                socketRef.current?.emit('join', { username, userType, userId });\r\n                socketRef.current?.emit('getOnlineUsers');\r\n                socketRef.current?.emit('getUnreadCounts', { userId, userType });\r\n\r\n                if (selectedUserId) {\r\n                    socketRef.current?.emit('joinChatRoom', {\r\n                        userId: userId,\r\n                        recipientId: selectedUserId\r\n                    });\r\n                }\r\n            });\r\n\r\n            socketRef.current.on('connect_error', (error) => {\r\n                toast.error(`Connection error: ${error.message}`);\r\n            });\r\n\r\n            socketRef.current.on('roomJoined', (data: { roomId: string }) => {\r\n                setCurrentRoomId(data.roomId);\r\n            });\r\n\r\n            socketRef.current.on('roomLeft', () => {\r\n                setCurrentRoomId(null);\r\n            });\r\n\r\n            socketRef.current.on('messagesMarkedAsSeen', (data: { byUserId: string, messageIds: string[] }) => {\r\n                if (data.byUserId === selectedUserId) {\r\n                    setSeenMessages(prev => {\r\n                        const newSet = new Set(prev);\r\n                        data.messageIds.forEach(messageId => {\r\n                            newSet.add(messageId);\r\n                        });\r\n                        return newSet;\r\n                    });\r\n                }\r\n            });\r\n\r\n            socketRef.current.on('privateMessage', (message: ChatMessage) => {\r\n                const isCurrentConversation = selectedUserId && (\r\n                    (message.senderId === userId && message.recipientId === selectedUserId) ||\r\n                    (message.senderId === selectedUserId && message.recipientId === userId)\r\n                );\r\n\r\n                if (isCurrentConversation) {\r\n                    setPrivateMessages(prev => {\r\n                        const messageExists = prev.some(msg => msg.id === message.id);\r\n                        if (messageExists) {\r\n                            return prev;\r\n                        }\r\n                        console.log('Adding message to current conversation:', message);\r\n                        return [...prev, message];\r\n                    });\r\n                }\r\n\r\n                if (message.senderId !== userId && !offlineMessageUsers.some(user => user.userId === message.senderId)) {\r\n                    setOfflineMessageUsers(prev => [\r\n                        ...prev,\r\n                        { username: message.sender, userId: message.senderId }\r\n                    ]);\r\n                }\r\n            });\r\n\r\n            socketRef.current.on('onlineUsers', (users: OnlineUser[]) => {\r\n                const uniqueUsers = Array.from(new Map(users.map(user => [user.userId, user])).values());\r\n                setOnlineUsers(uniqueUsers);\r\n            });\r\n\r\n            socketRef.current.on('userStartedViewing', (data: { viewerId: string }) => {\r\n                if (data.viewerId === selectedUserId) {\r\n                    setRecipientIsViewing(true);\r\n                }\r\n            });\r\n\r\n            socketRef.current.on('userStoppedViewing', (data: { viewerId: string }) => {\r\n                if (data.viewerId === selectedUserId) {\r\n                    setRecipientIsViewing(false);\r\n                }\r\n            });\r\n\r\n            socketRef.current.on('unreadCountUpdate', (data: { senderId: string, senderName: string, unreadCount: number }) => {\r\n                setUnreadMessageCounts(prev => {\r\n                    const newMap = new Map(prev);\r\n                    if (data.unreadCount === 0) {\r\n                        newMap.delete(data.senderId);\r\n                    } else {\r\n                        newMap.set(data.senderId, data.unreadCount);\r\n                    }\r\n                    return newMap;\r\n                });\r\n            });\r\n\r\n            socketRef.current.on('unreadCountsData', (data: Array<{ userId: string, unreadCount: number }>) => {\r\n                const unreadCountsMap = new Map<string, number>();\r\n                data.forEach((user: any) => {\r\n                    unreadCountsMap.set(user.userId, user.unreadCount);\r\n                });\r\n                setUnreadMessageCounts(unreadCountsMap);\r\n            });\r\n\r\n            socketRef.current.on('updateMessageUsers', (data: { username: string, userId: string }) => {\r\n                setOfflineMessageUsers(prev => {\r\n                    if (!prev.some(user => user.userId === data.userId)) {\r\n                        return [...prev, { username: data.username, userId: data.userId }];\r\n                    }\r\n                    return prev;\r\n                });\r\n            });\r\n\r\n            socketRef.current.on('error', (error: { message: string }) => {\r\n                toast.error(error.message);\r\n            });\r\n\r\n            return () => {\r\n                if (currentRoomId && userId && selectedUserId && socketRef.current) {\r\n                    socketRef.current.emit('leaveChatRoom', {\r\n                        userId: userId,\r\n                        recipientId: selectedUserId\r\n                    });\r\n                }\r\n\r\n                socketRef.current?.off('connect');\r\n                socketRef.current?.off('connect_error');\r\n                socketRef.current?.off('privateMessage');\r\n                socketRef.current?.off('onlineUsers');\r\n                socketRef.current?.off('error');\r\n                socketRef.current?.off('roomJoined');\r\n                socketRef.current?.off('roomLeft');\r\n                socketRef.current?.off('messagesMarkedAsSeen');\r\n                socketRef.current?.off('userStartedViewing');\r\n                socketRef.current?.off('userStoppedViewing');\r\n                socketRef.current?.off('unreadCountUpdate');\r\n                socketRef.current?.off('unreadCountsData');\r\n                socketRef.current?.off('updateMessageUsers');\r\n                socketRef.current?.disconnect();\r\n            };\r\n        }\r\n    }, [username, isUsernameSet, isAuthenticated, userType, userId, selectedUser, selectedUserId]);\r\n\r\n    useEffect(() => {\r\n        if (selectedUserId && userId && isUsernameSet) {\r\n            loadMessages(userId, selectedUserId);\r\n\r\n            const refreshInterval = setInterval(() => {\r\n                loadMessages(userId, selectedUserId);\r\n            }, 60000);\r\n\r\n            return () => clearInterval(refreshInterval);\r\n        }\r\n    }, [selectedUserId, userId, isUsernameSet, loadMessages]);\r\n\r\n    useEffect(() => {\r\n        const fetchMessageUsers = async () => {\r\n            if (isUsernameSet && userId) {\r\n                try {\r\n                    const data = await fetchingMessageUsers(userId, userType);\r\n                    const uniqueUsers = Array.from(new Map(data.map((user: any) => [user.userId, user])).values());\r\n                    setOfflineMessageUsers(uniqueUsers as Array<{ username: string; userId: string }>);\r\n                } catch {\r\n                    // Silently handle error\r\n                }\r\n            }\r\n        };\r\n\r\n        fetchMessageUsers();\r\n\r\n        const refreshInterval = setInterval(fetchMessageUsers, 30000);\r\n\r\n        return () => clearInterval(refreshInterval);\r\n    }, [isUsernameSet, userId, userType]);\r\n\r\n    useEffect(() => {\r\n        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\r\n    }, [privateMessages]);\r\n\r\n    useEffect(() => {\r\n        if ((initialSelectedUser || initialSelectedUserId) && isMobile) {\r\n            setSidebarOpen(false);\r\n        } else {\r\n            setSidebarOpen(!isMobile);\r\n        }\r\n    }, [initialSelectedUser, initialSelectedUserId, isMobile]);\r\n\r\n    useEffect(() => {\r\n        const handleBeforeUnload = () => {\r\n            if (selectedUserId && selectedUser) {\r\n                sessionStorage.setItem('currentChatUser', JSON.stringify({\r\n                    userId: selectedUserId,\r\n                    userName: selectedUser\r\n                }));\r\n            }\r\n\r\n            if (currentRoomId && userId && selectedUserId && socketRef.current) {\r\n                socketRef.current.emit('leaveChatRoom', {\r\n                    userId: userId,\r\n                    recipientId: selectedUserId\r\n                });\r\n            }\r\n        };\r\n\r\n        const handleLoad = () => {\r\n            const savedChatUser = sessionStorage.getItem('currentChatUser');\r\n            if (savedChatUser && !selectedUserId && !initialSelectedUserId) {\r\n                try {\r\n                    const { userId: restoredUserId, userName } = JSON.parse(savedChatUser);\r\n                    setSelectedUser(userName);\r\n                    setSelectedUserId(restoredUserId);\r\n\r\n                    setOfflineMessageUsers(prev => {\r\n                        const userExists = prev.some(user => user.userId === restoredUserId);\r\n                        if (!userExists) {\r\n                            return [...prev, { username: userName, userId: restoredUserId }];\r\n                        }\r\n                        return prev;\r\n                    });\r\n\r\n                    if (userId && restoredUserId) {\r\n                        loadMessages(userId, restoredUserId);\r\n                    }\r\n                } catch (error) {\r\n                    console.error('Error restoring chat state:', error);\r\n                }\r\n            }\r\n        };\r\n\r\n        window.addEventListener('beforeunload', handleBeforeUnload);\r\n\r\n        const isPageRefresh = sessionStorage.getItem('currentChatUser');\r\n        if (isPageRefresh) {\r\n            handleLoad();\r\n        }\r\n\r\n        return () => {\r\n            window.removeEventListener('beforeunload', handleBeforeUnload);\r\n        };\r\n    }, [selectedUserId, selectedUser, initialSelectedUserId, userId, currentRoomId]);\r\n\r\n    useEffect(() => {\r\n        if (selectedUserId && userId && privateMessages.length > 0) {\r\n            const unseenMessages = privateMessages.filter(msg =>\r\n                msg.sender === selectedUser &&\r\n                msg.recipient === username &&\r\n                !seenMessages.has(msg.id)\r\n            );\r\n\r\n            if (unseenMessages.length > 0) {\r\n                const unseenMessageIds = unseenMessages.map(msg => msg.id);\r\n\r\n                socketRef.current?.emit('markMessagesAsSeen', {\r\n                    senderId: selectedUserId,\r\n                    recipientId: userId,\r\n                    messageIds: unseenMessageIds\r\n                });\r\n            }\r\n        }\r\n    }, [selectedUserId, userId, privateMessages, selectedUser, username, seenMessages]);\r\n\r\n    useEffect(() => {\r\n        if (selectedUserId && userId && socketRef.current && socketRef.current.connected) {\r\n            socketRef.current.emit('joinChatRoom', {\r\n                userId: userId,\r\n                recipientId: selectedUserId\r\n            });\r\n        }\r\n\r\n        return () => {\r\n            if (currentRoomId && userId && selectedUserId && socketRef.current) {\r\n                socketRef.current.emit('leaveChatRoom', {\r\n                    userId: userId,\r\n                    recipientId: selectedUserId\r\n                });\r\n            }\r\n        };\r\n    }, [selectedUserId, userId, currentRoomId]);\r\n\r\n    const handleSendMessage = async (e: FormEvent) => {\r\n        e.preventDefault();\r\n\r\n        if (!messageInput.trim() || !selectedUser || !userId) {\r\n            return;\r\n        }\r\n\r\n        const messageText = messageInput.trim();\r\n        setMessageInput('');\r\n\r\n        try {\r\n\r\n            if (!selectedUserId) {\r\n                toast.error('No recipient selected. Please select a user first.');\r\n                setMessageInput(messageText);\r\n                return;\r\n            }\r\n\r\n            const recipientType = userType === 'student' ? 'class' : 'student';\r\n\r\n            const messageData = {\r\n                text: messageText,\r\n                senderId: userId,\r\n                recipientId: selectedUserId,\r\n                senderType: userType,\r\n                recipientType: recipientType,\r\n                recipientUsername: selectedUser\r\n            };\r\n\r\n            socketRef.current?.emit('sendPrivateMessage', messageData);\r\n\r\n            cleanUrl();\r\n\r\n            if (!isUserOnline(selectedUser)) {\r\n                toast.info(`${selectedUser} is offline. Your message will be delivered when they come online.`);\r\n            }\r\n        } catch {\r\n            toast.error('Failed to send message. Please try again.');\r\n            setMessageInput(messageText);\r\n        }\r\n    };\r\n\r\n    const handleUserSelect = async (user: any) => {\r\n        leaveCurrentRoom();\r\n\r\n        setPrivateMessages([]);\r\n        setSelectedUser(user.username);\r\n        setSeenMessages(new Set());\r\n        setRecipientIsViewing(false);\r\n\r\n        const targetUserId = user.userId || user.username;\r\n        setSelectedUserId(targetUserId);\r\n\r\n        setUnreadMessageCounts(prev => {\r\n            const newMap = new Map(prev);\r\n            newMap.delete(targetUserId);\r\n            return newMap;\r\n        });\r\n\r\n        cleanUrl();\r\n\r\n        if (isMobile) {\r\n            setSidebarOpen(false);\r\n        }\r\n\r\n        if (targetUserId && userId) {\r\n            loadMessages(userId, targetUserId);\r\n        }\r\n    };\r\n\r\n    const leaveCurrentRoom = () => {\r\n        if (currentRoomId && userId && selectedUserId && socketRef.current) {\r\n            socketRef.current.emit('leaveChatRoom', {\r\n                userId: userId,\r\n                recipientId: selectedUserId\r\n            });\r\n        }\r\n    };\r\n\r\n    const handleBackToSidebar = () => {\r\n        leaveCurrentRoom();\r\n\r\n        setSidebarOpen(true);\r\n        if (isMobile) {\r\n            setSelectedUser(null);\r\n            setSelectedUserId(null);\r\n        }\r\n    };\r\n\r\n    const formatTime = useMemo(() => {\r\n        return (timestamp: Date) => format(new Date(timestamp), 'h:mm a');\r\n    }, []);\r\n\r\n    const allAvailableUsers = useMemo(() => {\r\n        const userMap = new Map<string, { username: string; userType: string; userId: string }>();\r\n\r\n        offlineMessageUsers.forEach(messageUser => {\r\n            userMap.set(messageUser.userId, {\r\n                username: messageUser.username,\r\n                userType: userType === 'student' ? 'class' : 'student',\r\n                userId: messageUser.userId\r\n            });\r\n        });\r\n\r\n        onlineUsers.forEach(onlineUser => {\r\n            if (onlineUser.username !== username && onlineUser.userType !== userType) {\r\n                userMap.set(onlineUser.userId || onlineUser.username, {\r\n                    username: onlineUser.username,\r\n                    userType: onlineUser.userType,\r\n                    userId: onlineUser.userId || onlineUser.username\r\n                });\r\n            }\r\n        });\r\n\r\n        if (initialSelectedUserId && initialSelectedUserName) {\r\n            userMap.set(initialSelectedUserId, {\r\n                username: initialSelectedUserName,\r\n                userType: userType === 'student' ? 'class' : 'student',\r\n                userId: initialSelectedUserId\r\n            });\r\n        }\r\n\r\n        const filteredUsers = Array.from(userMap.values()).filter(user => {\r\n            const matchesSearch = user.username.toLowerCase().includes(searchQuery.toLowerCase());\r\n            const isNotCurrentUser = user.username !== username;\r\n            const hasDifferentUserType = user.userType !== userType;\r\n\r\n            return matchesSearch && isNotCurrentUser && hasDifferentUserType;\r\n        });\r\n\r\n        return filteredUsers;\r\n    }, [offlineMessageUsers, onlineUsers, searchQuery, username, userType, initialSelectedUserId, initialSelectedUserName]);\r\n\r\n    const isUserOnline = useMemo(() => {\r\n        const onlineUserIds = new Set(onlineUsers.map(user => user.userId));\r\n        return (username: string) => {\r\n            const user = allAvailableUsers.find(u => u.username === username);\r\n            if (user) {\r\n                return onlineUserIds.has(user.userId);\r\n            }\r\n\r\n            if (username === initialSelectedUserName && initialSelectedUserId) {\r\n                return onlineUserIds.has(initialSelectedUserId);\r\n            }\r\n\r\n            const onlineUser = onlineUsers.find(u => u.username === username);\r\n            return !!onlineUser;\r\n        };\r\n    }, [onlineUsers, allAvailableUsers, initialSelectedUserName, initialSelectedUserId]);\r\n\r\n    const unreadUsersCount = useMemo(() => {\r\n        return allAvailableUsers.filter(user => {\r\n            const userIdToCheck = user.userId;\r\n            return unreadMessageCounts.has(userIdToCheck);\r\n        }).length;\r\n    }, [allAvailableUsers, unreadMessageCounts]);\r\n\r\n    const filteredUsers = useMemo(() => {\r\n        if (userFilter === 'unread') {\r\n            return allAvailableUsers.filter(user => {\r\n                const userIdToCheck = user.userId;\r\n                return unreadMessageCounts.has(userIdToCheck);\r\n            });\r\n        }\r\n        return allAvailableUsers;\r\n    }, [allAvailableUsers, userFilter, unreadMessageCounts]);\r\n\r\n    useEffect(() => {\r\n        if (initialSelectedUserId && !selectedUserId && userId && isAuthenticated) {\r\n            const initializeUser = async () => {\r\n                let displayName = initialSelectedUserName;\r\n\r\n                if (!displayName) {\r\n                    const targetUserType = userType === 'student' ? 'class' : 'student';\r\n                    const userDetails = await fetchUserDetails(initialSelectedUserId, targetUserType);\r\n                    if (userDetails) {\r\n                        displayName = `${userDetails.firstName} ${userDetails.lastName}`.trim();\r\n                    } else {\r\n                        displayName = `User ${initialSelectedUserId.slice(0, 8)}`;\r\n                    }\r\n                }\r\n\r\n                setSelectedUser(displayName);\r\n                setSelectedUserId(initialSelectedUserId);\r\n\r\n                setOfflineMessageUsers(prev => {\r\n                    const userExists = prev.some(user => user.userId === initialSelectedUserId);\r\n                    if (!userExists) {\r\n                        return [...prev, { username: displayName, userId: initialSelectedUserId }];\r\n                    }\r\n                    return prev;\r\n                });\r\n\r\n                loadMessages(userId, initialSelectedUserId);\r\n            };\r\n\r\n            initializeUser();\r\n        }\r\n    }, [initialSelectedUserId, initialSelectedUserName, selectedUserId, userId, isAuthenticated, userType]);\r\n\r\n    useEffect(() => {\r\n        if (initialSelectedUser && !selectedUserId && allAvailableUsers.length > 0 && !initialSelectedUserId && userId) {\r\n            const foundUser = allAvailableUsers.find(user => user.username === initialSelectedUser);\r\n            if (foundUser) {\r\n                setSelectedUser(foundUser.username);\r\n                setSelectedUserId(foundUser.userId);\r\n\r\n                loadMessages(userId, foundUser.userId);\r\n            }\r\n        }\r\n    }, [initialSelectedUser, allAvailableUsers, selectedUserId, userId, initialSelectedUserId]);\r\n\r\n    if (!isAuthenticated) {\r\n        return (\r\n            <div className=\"flex items-center justify-center min-h-[calc(100vh-64px)] bg-background p-4\">\r\n                <div className=\"w-full max-w-md p-4 sm:p-6 bg-card rounded-lg shadow-lg\">\r\n                    <h2 className=\"text-xl sm:text-2xl font-bold mb-4 sm:mb-6 text-center\">Login Required</h2>\r\n                    <div className=\"flex flex-col items-center justify-center text-center\">\r\n                        <p className=\"text-center text-muted-foreground mb-4 sm:mb-6 text-sm sm:text-base\">\r\n                            Please login as a student to access the chat feature.\r\n                        </p>\r\n                        <Button\r\n                            onClick={() => router.push('/')}\r\n                            className=\"w-full bg-orange-500 hover:bg-orange-600\"\r\n                        >\r\n                            Go to Login\r\n                        </Button>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        );\r\n    }\r\n\r\n    return (\r\n        <div className=\"flex h-[calc(100vh-64px)] bg-background text-foreground relative overflow-hidden\">\r\n            {sidebarOpen && (\r\n                <aside className={`border-r border-gray-200 flex flex-col bg-gradient-to-b from-white to-gray-50 shadow-lg ${isMobile\r\n                    ? 'absolute inset-0 z-50 w-full'\r\n                    : 'relative w-80 min-w-80 lg:w-96 lg:min-w-96'\r\n                    }`}>\r\n                    <div className=\"p-4 flex items-center justify-between border-b border-gray-200 bg-white/80 backdrop-blur-sm\">\r\n                        <div className=\"flex items-center gap-3\">\r\n                            <div className=\"relative\">\r\n                                <Image\r\n                                    src=\"/logo.png\"\r\n                                    alt=\"Uest Logo\"\r\n                                    width={isMobile ? 100 : 140}\r\n                                    height={isMobile ? 25 : 35}\r\n                                    className=\"object-contain cursor-pointer hover:opacity-80 transition-all duration-300 hover:scale-105\"\r\n                                    onClick={() => router.push('/')}\r\n                                />\r\n                            </div>\r\n                        </div>\r\n                        <div className=\"flex items-center gap-2\">\r\n                            <Button\r\n                                variant=\"outline\"\r\n                                size={isMobile ? \"sm\" : \"default\"}\r\n                                disabled={isRefreshing}\r\n                                onClick={async () => {\r\n                                    setIsRefreshing(true);\r\n                                    try {\r\n                                        socketRef.current?.emit('getOnlineUsers');\r\n                                        socketRef.current?.emit('getUnreadCounts', { userId, userType });\r\n\r\n                                        if (isUsernameSet && userId) {\r\n                                            const data = await fetchingMessageUsers(userId, userType);\r\n                                            const uniqueUsers = Array.from(new Map(data.map((user: any) => [user.userId, user])).values());\r\n\r\n                                            const newUsers = uniqueUsers as Array<{ username: string; userId: string }>;\r\n\r\n                                            if (selectedUserId && selectedUser) {\r\n                                                const selectedUserExists = newUsers.some(user => user.userId === selectedUserId);\r\n                                                if (!selectedUserExists) {\r\n                                                    newUsers.push({ username: selectedUser, userId: selectedUserId });\r\n                                                }\r\n                                            }\r\n\r\n                                            setOfflineMessageUsers(newUsers);\r\n                                            toast.success('Chat list refreshed!');\r\n                                        }\r\n                                    } catch {\r\n                                        toast.error('Failed to refresh chat list');\r\n                                    } finally {\r\n                                        setIsRefreshing(false);\r\n                                    }\r\n                                }}\r\n                                className={`bg-white/90 border border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 rounded-xl font-medium transition-all duration-300 shadow-sm hover:shadow-md disabled:opacity-50 ${isMobile ? 'px-3 py-2 text-xs' : 'px-4 py-2 text-sm'\r\n                                    }`}\r\n                                title=\"Refresh chat list\"\r\n                            >\r\n                                <RefreshCw className={`${isMobile ? 'h-4 w-4' : 'h-4 w-4'} ${isRefreshing ? 'animate-spin' : ''}`} />\r\n                                {!isMobile && <span className=\"ml-2\">{isRefreshing ? 'Refreshing...' : 'Refresh'}</span>}\r\n                            </Button>\r\n                            {isMobile && (\r\n                                <Button\r\n                                    variant=\"ghost\"\r\n                                    size=\"icon\"\r\n                                    className=\"rounded-xl h-10 w-10 hover:bg-gray-100 transition-all duration-300\"\r\n                                    onClick={() => setSidebarOpen(false)}\r\n                                >\r\n                                    <X className=\"h-5 w-5\" />\r\n                                </Button>\r\n                            )}\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div className={`${isMobile ? 'p-3' : 'p-4'} bg-white/50`}>\r\n                        <div className=\"relative group\">\r\n                            <Search className={`absolute left-4 top-1/2 -translate-y-1/2 text-gray-400 group-focus-within:text-gray-600 transition-colors duration-300 ${isMobile ? 'h-4 w-4' : 'h-5 w-5'}`} />\r\n                            <Input\r\n                                placeholder=\"Search conversations...\"\r\n                                className={`pl-12 pr-4 bg-white border border-gray-300 rounded-xl text-gray-900 placeholder:text-gray-400 focus:border-black focus:ring-2 focus:ring-gray-100 transition-all duration-300 shadow-sm hover:shadow-md ${isMobile ? 'py-2.5 text-sm' : 'py-3 text-base'\r\n                                    }`}\r\n                                value={searchQuery}\r\n                                onChange={(e) => setSearchQuery(e.target.value)}\r\n                            />\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div className={`${isMobile ? 'px-3 pb-3' : 'px-4 pb-4'}`}>\r\n                        <div className=\"bg-gray-100/80 rounded-2xl p-1.5 shadow-inner\">\r\n                            <div className=\"flex gap-1\">\r\n                                <button\r\n                                    onClick={() => setUserFilter('all')}\r\n                                    className={`relative flex-1 flex items-center justify-center gap-2 rounded-xl font-semibold transition-all duration-300 transform hover:scale-[1.02] ${\r\n                                        userFilter === 'all'\r\n                                            ? 'bg-white text-gray-900 shadow-lg border border-gray-200'\r\n                                            : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'\r\n                                    } ${isMobile ? 'px-3 py-2.5 text-xs' : 'px-4 py-3 text-sm'}`}\r\n                                >\r\n                                    <div className={`flex items-center gap-2 ${isMobile ? 'flex-col gap-1' : ''}`}>\r\n                                        <Users className={`${isMobile ? 'h-3 w-3' : 'h-4 w-4'}`} />\r\n                                        <span className={isMobile ? 'text-[10px] leading-tight' : ''}>\r\n                                            {isMobile ? 'All' : 'All Users'}\r\n                                        </span>\r\n                                        <span className={`inline-flex items-center justify-center min-w-[20px] h-5 px-1.5 text-xs font-bold rounded-full transition-colors ${\r\n                                            userFilter === 'all'\r\n                                                ? 'bg-black text-white'\r\n                                                : 'bg-gray-200 text-gray-600'\r\n                                        }`}>\r\n                                            {allAvailableUsers.length}\r\n                                        </span>\r\n                                    </div>\r\n                                </button>\r\n\r\n                                <button\r\n                                    onClick={() => setUserFilter('unread')}\r\n                                    className={`relative flex-1 flex items-center justify-center gap-2 rounded-xl font-semibold transition-all duration-300 transform hover:scale-[1.02] ${\r\n                                        userFilter === 'unread'\r\n                                            ? 'bg-white text-gray-900 shadow-lg border border-gray-200'\r\n                                            : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'\r\n                                    } ${isMobile ? 'px-3 py-2.5 text-xs' : 'px-4 py-3 text-sm'}`}\r\n                                >\r\n                                    <div className={`flex items-center gap-2 ${isMobile ? 'flex-col gap-1' : ''}`}>\r\n                                        <div className=\"relative\">\r\n                                            <MessageSquare className={`${isMobile ? 'h-3 w-3' : 'h-4 w-4'}`} />\r\n                                            {unreadUsersCount > 0 && (\r\n                                                <div className=\"absolute -top-1 -right-1 bg-red-500 text-white text-[8px] rounded-full h-2 w-2 animate-pulse\"></div>\r\n                                            )}\r\n                                        </div>\r\n                                        <span className={isMobile ? 'text-[10px] leading-tight' : ''}>\r\n                                            {isMobile ? 'Unread' : 'Unread Only'}\r\n                                        </span>\r\n                                        <span className={`inline-flex items-center justify-center min-w-[20px] h-5 px-1.5 text-xs font-bold rounded-full transition-colors ${\r\n                                            userFilter === 'unread'\r\n                                                ? unreadUsersCount > 0\r\n                                                    ? 'bg-red-100 text-red-800'\r\n                                                    : 'bg-gray-100 text-gray-600'\r\n                                                : unreadUsersCount > 0\r\n                                                    ? 'bg-red-500 text-white animate-pulse'\r\n                                                    : 'bg-gray-200 text-gray-600'\r\n                                        }`}>\r\n                                            {unreadUsersCount}\r\n                                        </span>\r\n                                    </div>\r\n                                </button>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div className=\"flex-1 overflow-y-auto overscroll-contain\">\r\n                        <div className={`space-y-2 ${isMobile ? 'px-2 pb-2' : 'px-3 pb-3'}`}>\r\n                            {filteredUsers.length > 0 || (selectedUser && initialSelectedUserId) ? (\r\n                                <>\r\n                                {selectedUser && initialSelectedUserId &&\r\n                                 !filteredUsers.find(u => u.userId === initialSelectedUserId) && (\r\n                                    <div\r\n                                        key={initialSelectedUserId}\r\n                                        className={`group transition-all duration-300 cursor-pointer rounded-2xl hover:shadow-lg hover:scale-[1.02] ${isMobile ? 'p-3' : 'p-4'\r\n                                            } ${selectedUserId === initialSelectedUserId\r\n                                                ? 'bg-gradient-to-r from-black to-gray-800 text-white shadow-xl border border-gray-700'\r\n                                                : 'bg-white border border-gray-200 hover:bg-gray-50 hover:border-gray-300'\r\n                                            }`}\r\n                                        onClick={() => handleUserSelect({\r\n                                            username: selectedUser,\r\n                                            userType: userType === 'student' ? 'class' : 'student',\r\n                                            userId: initialSelectedUserId\r\n                                        })}\r\n                                    >\r\n                                        <div className=\"flex gap-3 items-center\">\r\n                                            <div className=\"relative\">\r\n                                                <Avatar className={`border-2 shadow-md transition-all duration-300 group-hover:scale-110 ${isMobile ? 'h-10 w-10' : 'h-12 w-12'} border-white/50`}>\r\n                                                    <AvatarFallback className={`font-semibold transition-colors duration-300 ${isMobile ? 'text-xs' : 'text-sm'} bg-white text-black`}>\r\n                                                        {selectedUser?.substring(0, 2).toUpperCase()}\r\n                                                    </AvatarFallback>\r\n                                                </Avatar>\r\n                                                <div className={`absolute -bottom-0.5 -right-0.5 rounded-full border-2 flex items-center justify-center transition-all duration-300 ${isMobile ? 'h-4 w-4' : 'h-5 w-5'\r\n                                                    } border-white ${isUserOnline(selectedUser || '') ? 'bg-green-500 shadow-lg' : 'bg-gray-400'\r\n                                                    }`}>\r\n                                                    <div className={`rounded-full transition-all duration-300 ${isMobile ? 'h-2 w-2' : 'h-2.5 w-2.5'\r\n                                                        } ${isUserOnline(selectedUser || '') ? 'bg-green-300 animate-pulse' : 'bg-gray-300'\r\n                                                        }`}></div>\r\n                                                </div>\r\n                                            </div>\r\n                                            <div className=\"flex-1 min-w-0\">\r\n                                                <div className=\"flex justify-between items-center\">\r\n                                                    <h3 className={`font-semibold truncate transition-colors duration-300 ${isMobile ? 'text-sm' : 'text-base'} text-white`}>\r\n                                                        {selectedUser}\r\n                                                    </h3>\r\n                                                </div>\r\n                                                <div className=\"flex items-center gap-2 mt-1\">\r\n                                                    <div className={`inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium transition-colors duration-300 bg-white/20 text-white`}>\r\n                                                        Tutor\r\n                                                    </div>\r\n                                                    <div className={`inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium transition-colors duration-300 bg-white/20 text-white`}>\r\n                                                        <div className={`w-2 h-2 rounded-full ${isUserOnline(selectedUser || '') ? 'bg-green-500' : 'bg-gray-400'}`}></div>\r\n                                                        {isUserOnline(selectedUser || '') ? 'Online' : 'Offline'}\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n                                )}\r\n                                {filteredUsers.map((user) => (\r\n                                    <div\r\n                                        key={user.userId} \r\n                                        className={`group transition-all duration-300 cursor-pointer rounded-2xl hover:shadow-lg hover:scale-[1.02] ${isMobile ? 'p-3' : 'p-4'\r\n                                            } ${selectedUser === user.username\r\n                                                ? 'bg-gradient-to-r from-black to-gray-800 text-white shadow-xl border border-gray-700'\r\n                                                : 'bg-white border border-gray-200 hover:bg-gray-50 hover:border-gray-300'\r\n                                            }`}\r\n                                        onClick={() => handleUserSelect(user)}\r\n                                    >\r\n                                        <div className=\"flex gap-3 items-center\">\r\n                                            <div className=\"relative\">\r\n                                                <Avatar className={`border-2 shadow-md transition-all duration-300 group-hover:scale-110 ${isMobile ? 'h-10 w-10' : 'h-12 w-12'} ${\r\n                                                    selectedUser === user.username\r\n                                                        ? 'border-white/50'\r\n                                                        : 'border-gray-300 group-hover:border-gray-400'\r\n                                                }`}>\r\n                                                    <AvatarFallback className={`font-semibold transition-colors duration-300 ${isMobile ? 'text-xs' : 'text-sm'} ${selectedUser === user.username\r\n                                                        ? 'bg-white text-black'\r\n                                                        : 'bg-gradient-to-br from-gray-100 to-gray-200 text-gray-700 group-hover:from-gray-200 group-hover:to-gray-300'\r\n                                                        }`}>\r\n                                                        {user.username.substring(0, 2).toUpperCase()}\r\n                                                    </AvatarFallback>\r\n                                                </Avatar>\r\n                                                <div className={`absolute -bottom-0.5 -right-0.5 rounded-full border-2 flex items-center justify-center transition-all duration-300 ${isMobile ? 'h-4 w-4' : 'h-5 w-5'\r\n                                                    } ${selectedUser === user.username ? 'border-white' : 'border-white'\r\n                                                    } ${isUserOnline(user.username) ? 'bg-green-500 shadow-lg' : 'bg-gray-400'\r\n                                                    }`}>\r\n                                                    <div className={`rounded-full transition-all duration-300 ${isMobile ? 'h-2 w-2' : 'h-2.5 w-2.5'\r\n                                                        } ${isUserOnline(user.username) ? 'bg-green-300 animate-pulse' : 'bg-gray-300'\r\n                                                        }`}></div>\r\n                                                </div>\r\n                                            </div>\r\n                                            <div className=\"flex-1 min-w-0\">\r\n                                                <div className=\"flex justify-between items-center\">\r\n                                                    <h3 className={`font-semibold truncate transition-colors duration-300 ${isMobile ? 'text-sm' : 'text-base'} ${selectedUser === user.username\r\n                                                        ? 'text-white'\r\n                                                        : 'text-gray-900 group-hover:text-black'\r\n                                                        }`}>\r\n                                                        {user.username}\r\n                                                    </h3>\r\n                                                    {unreadMessageCounts.has(user.userId) && (\r\n                                                        <div className=\"bg-gradient-to-r from-red-500 to-red-600 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center font-bold shadow-lg animate-pulse\">\r\n                                                            {unreadMessageCounts.get(user.userId)}\r\n                                                        </div>\r\n                                                    )}\r\n                                                </div>\r\n                                                <div className=\"flex items-center gap-2 mt-1\">\r\n                                                    <div className={`inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium transition-colors duration-300 ${\r\n                                                        selectedUser === user.username\r\n                                                            ? 'bg-white/20 text-white'\r\n                                                            : user.userType === 'student'\r\n                                                                ? 'bg-gray-100 text-gray-700 group-hover:bg-gray-200'\r\n                                                                : 'bg-gray-100 text-gray-700 group-hover:bg-gray-200'\r\n                                                    }`}>\r\n                                                        {user.userType === 'student' ? 'Student' : 'Tutor'}\r\n                                                    </div>\r\n                                                    <div className={`inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium transition-colors duration-300 ${\r\n                                                        selectedUser === user.username\r\n                                                            ? 'bg-white/20 text-white'\r\n                                                            : isUserOnline(user.username)\r\n                                                                ? 'bg-green-100 text-green-700 group-hover:bg-green-200'\r\n                                                                : 'bg-gray-100 text-gray-600 group-hover:bg-gray-200'\r\n                                                    }`}>\r\n                                                        <div className={`w-2 h-2 rounded-full ${isUserOnline(user.username) ? 'bg-green-500' : 'bg-gray-400'}`}></div>\r\n                                                        {isUserOnline(user.username) ? 'Online' : 'Offline'}\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n                                ))}\r\n                                </>\r\n                            ) : (\r\n                                <div className=\"p-6 text-center\">\r\n                                    <div className=\"bg-white rounded-2xl p-6 border border-gray-200 shadow-sm\">\r\n                                        <div className=\"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n                                            {userFilter === 'unread' ? (\r\n                                                <MessageSquare className=\"h-8 w-8 text-gray-400\" />\r\n                                            ) : (\r\n                                                <Users className=\"h-8 w-8 text-gray-400\" />\r\n                                            )}\r\n                                        </div>\r\n                                        <h3 className={`font-semibold text-gray-900 mb-2 ${isMobile ? 'text-sm' : 'text-base'}`}>\r\n                                            {userFilter === 'unread' ? 'No unread messages' : 'No users found'}\r\n                                        </h3>\r\n                                        <p className=\"text-xs text-gray-600 mb-3\">\r\n                                            {userFilter === 'unread'\r\n                                                ? 'All messages have been read or no conversations yet'\r\n                                                : `You can only chat with ${userType === 'student' ? 'tutors' : 'students'} who have exchanged messages with you`\r\n                                            }\r\n                                        </p>\r\n                                        <p className=\"text-xs text-gray-500\">\r\n                                            {userFilter === 'unread'\r\n                                                ? 'Switch to \"All Users\" to see all conversations'\r\n                                                : 'Users will appear here when you exchange messages with them'\r\n                                            }\r\n                                        </p>\r\n                                    </div>\r\n                                </div>\r\n                            )}\r\n                        </div>\r\n                    </div>\r\n                </aside>\r\n            )}\r\n\r\n            {isMobile && sidebarOpen && (\r\n                <div\r\n                    className=\"absolute inset-0 bg-black/20 z-40\"\r\n                    onClick={() => setSidebarOpen(false)}\r\n                />\r\n            )}\r\n\r\n            <main className=\"flex-1 flex flex-col min-w-0 bg-white\">\r\n                <div className={`border-b-2 border-gray-200 flex items-center gap-3 bg-white ${isMobile ? 'p-3' : 'p-4'}`}>\r\n                    {isMobile && !sidebarOpen && (\r\n                        <Button variant=\"ghost\" size=\"icon\" className={`flex-shrink-0 rounded-xl hover:bg-gray-100 ${isMobile ? 'h-8 w-8' : 'h-10 w-10'}`} onClick={handleBackToSidebar}>\r\n                            <ArrowLeft className={`${isMobile ? 'h-4 w-4' : 'h-5 w-5'}`} />\r\n                        </Button>\r\n                    )}\r\n                    <div className=\"flex gap-3 items-center min-w-0 flex-1\">\r\n                        <div className=\"relative\">\r\n                            <Avatar className={`border-2 border-gray-300 flex-shrink-0 shadow-md ${isMobile ? 'h-9 w-9' : 'h-12 w-12'}`}>\r\n                                {selectedUser ? (\r\n                                    <AvatarFallback className={`font-semibold bg-gray-100 text-black ${isMobile ? 'text-xs' : 'text-sm'}`}>\r\n                                        {selectedUser.substring(0, 2).toUpperCase()}\r\n                                    </AvatarFallback>\r\n                                ) : (\r\n                                    <AvatarFallback className=\"bg-gray-100\">\r\n                                        <Users className={`text-gray-500 ${isMobile ? 'h-4 w-4' : 'h-6 w-6'}`} />\r\n                                    </AvatarFallback>\r\n                                )}\r\n                            </Avatar>\r\n                            {selectedUser && (\r\n                                <div className={`absolute -bottom-1 -right-1 rounded-full border-2 border-white flex items-center justify-center ${isMobile ? 'h-3 w-3' : 'h-4 w-4'\r\n                                    } ${isUserOnline(selectedUser) ? 'bg-green-500' : 'bg-gray-400'\r\n                                    }`}>\r\n                                    <div className={`rounded-full ${isMobile ? 'h-1.5 w-1.5' : 'h-2 w-2'\r\n                                        } ${isUserOnline(selectedUser) ? 'bg-green-400 animate-pulse' : 'bg-gray-300'\r\n                                        }`}></div>\r\n                                </div>\r\n                            )}\r\n                        </div>\r\n                        <div className=\"min-w-0 flex-1\">\r\n                            <h1 className={`font-semibold flex items-center gap-2 truncate text-black ${isMobile ? 'text-base' : 'text-lg'}`}>\r\n                                {selectedUser ? (\r\n                                    <span className=\"truncate\">{selectedUser}</span>\r\n                                ) : 'Select a user'}\r\n                            </h1>\r\n                            <p className={`text-gray-600 truncate ${isMobile ? 'text-xs' : 'text-sm'}`}>\r\n                                {selectedUser ? (\r\n                                    isUserOnline(selectedUser) ? 'Online' : 'Offline (messages will be delivered when online)'\r\n                                ) : 'Choose someone to chat with'}\r\n                            </p>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <div className={`flex-1 overflow-y-auto bg-gray-50 ${isMobile ? 'p-3' : 'p-4'}`}>\r\n                    <div className={`mx-auto ${isMobile ? 'space-y-3 max-w-full' : 'space-y-4 max-w-4xl'}`}>\r\n                        {selectedUser ? (\r\n                            privateMessages.length > 0 ? (\r\n                                privateMessages.map((message) => {\r\n                                    const isCurrentUser = message.sender === username;\r\n                                    const senderName = message.sender || 'Unknown';\r\n\r\n                                    return (\r\n                                        <div\r\n                                            key={message.id}\r\n                                            className={`flex items-end ${isCurrentUser ? 'justify-end' : ''} ${isMobile ? 'gap-2' : 'gap-3'}`}\r\n                                        >\r\n                                            {!isCurrentUser && (\r\n                                                <Avatar className={`border-2 border-gray-300 shadow-sm ${isMobile ? 'h-6 w-6' : 'h-8 w-8'}`}>\r\n                                                    <AvatarFallback className={`bg-gray-200 text-black font-semibold ${isMobile ? 'text-xs' : 'text-xs'}`}>\r\n                                                        {senderName.substring(0, 2).toUpperCase()}\r\n                                                    </AvatarFallback>\r\n                                                </Avatar>\r\n                                            )}\r\n                                            <div className={`${isCurrentUser ? 'text-right' : ''} ${isMobile ? 'max-w-[80%]' : 'max-w-[70%]'}`}>\r\n                                                <div\r\n                                                    className={`${isCurrentUser\r\n                                                        ? 'bg-black text-white'\r\n                                                        : 'bg-white text-black border-2 border-gray-200'\r\n                                                        } rounded-2xl shadow-lg break-words ${isMobile ? 'p-3' : 'p-4'}`}\r\n                                                >\r\n                                                    <div className={`leading-relaxed ${isMobile ? 'text-sm' : 'text-base'}`}>\r\n                                                        {message.text}\r\n                                                    </div>\r\n                                                    <div className={`text-xs mt-2 flex items-end justify-end gap-1 ${isCurrentUser\r\n                                                        ? 'text-gray-300'\r\n                                                        : 'text-gray-500'\r\n                                                        }`}>\r\n                                                        {formatTime(message.timestamp)}\r\n                                                        {isCurrentUser && (\r\n                                                            <span>\r\n                                                                {seenMessages.has(message.id) ? (\r\n                                                                    <CheckCheck className={`${isMobile ? 'h-3 w-3' : 'h-4 w-4'} text-blue-500`} />\r\n                                                                ) : recipientIsViewing ? (\r\n                                                                    <CheckCheck className={`${isMobile ? 'h-3 w-3' : 'h-4 w-4'} text-blue-500`} />\r\n                                                                ) : isUserOnline(selectedUser) ? (\r\n                                                                    <CheckCheck className={`${isMobile ? 'h-3 w-3' : 'h-4 w-4'} text-gray-400`} />\r\n                                                                ) : (\r\n                                                                    <Check className={`${isMobile ? 'h-3 w-3' : 'h-4 w-4'} text-gray-400`} />\r\n                                                                )}\r\n                                                            </span>\r\n                                                        )}\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                    );\r\n                                })\r\n                            ) : (\r\n                                <div className=\"flex flex-col items-center justify-center h-full py-12 text-center\">\r\n                                    <MessageSquare className=\"text-gray-400 mb-4 h-16 w-16\" />\r\n                                    <p className=\"text-gray-600 text-lg font-medium\">No messages yet</p>\r\n                                    <p className=\"text-gray-500 text-sm mt-2\">Send a message to start the conversation</p>\r\n                                </div>\r\n                            )\r\n                        ) : (\r\n                            <div className=\"flex flex-col items-center justify-center h-full py-12 text-center\">\r\n                                <MessageSquare className=\"text-gray-400 mb-4 h-16 w-16\" />\r\n                                <p className=\"text-gray-600 text-lg font-medium\">Select a user to start chatting</p>\r\n                                <p className=\"text-gray-500 text-sm mt-2\">Choose a user from the sidebar to start a private conversation</p>\r\n                                <p className=\"text-gray-500 text-sm mt-4 max-w-md\">\r\n                                    Note: You can only chat with {userType === 'student' ? 'tutors' : 'students'} who have exchanged messages with you.\r\n                                    {filteredUsers.length === 0 && (\r\n                                        <span className=\"block mt-2\">There are currently no users to chat with. When you exchange messages with someone, they will appear in the sidebar.</span>\r\n                                    )}\r\n                                </p>\r\n                            </div>\r\n                        )}\r\n                        <div ref={messagesEndRef} />\r\n                    </div>\r\n                </div>\r\n\r\n                <form onSubmit={handleSendMessage} className={`border-t-2 border-gray-200 bg-white ${isMobile ? 'p-3' : 'p-4'}`}>\r\n                    <div className={`flex items-center mx-auto ${isMobile ? 'gap-2 max-w-full' : 'gap-3 max-w-4xl'}`}>\r\n                        <button\r\n                            type=\"button\"\r\n                            onClick={toggleEmojiPicker}\r\n                            className={`bg-white border-2 border-gray-200 text-black hover:bg-gray-100 rounded-xl font-medium transition-all duration-300 ${isMobile ? 'text-lg px-2 py-1' : 'text-2xl px-3 py-1'\r\n                                }`}\r\n                        >\r\n                            😊\r\n                        </button>\r\n\r\n                        {showEmojiPicker && (\r\n                            <div\r\n                                ref={emojiPickerRef}\r\n                                className={`absolute z-10 ${isMobile ? 'bottom-12 left-4 right-4' : 'bottom-12 left-96'\r\n                                    }`}\r\n                            >\r\n                                <EmojiPicker\r\n                                    onEmojiClick={handleEmojiClick}\r\n                                    emojiStyle={EmojiStyle.APPLE}\r\n                                    searchDisabled={true}\r\n                                    width={isMobile ? '100%' : undefined}\r\n                                />\r\n                            </div>\r\n                        )}\r\n                        <Input\r\n                            placeholder={selectedUser ? \"Type your message...\" : \"Select a user to start chatting\"}\r\n                            className={`flex-1 bg-gray-50 border-2 border-gray-200 text-black placeholder:text-gray-500 focus:border-black rounded-xl transition-all duration-300 ${isMobile ? 'px-3 py-2 text-sm' : 'px-4 py-3 text-base'\r\n                                }`}\r\n                            value={messageInput}\r\n                            onChange={(e) => setMessageInput(e.target.value)}\r\n                            disabled={!selectedUser}\r\n                            maxLength={250}\r\n                        />\r\n                        <Button\r\n                            type=\"submit\"\r\n                            size={isMobile ? \"default\" : \"lg\"}\r\n                            disabled={!messageInput.trim() || !selectedUser}\r\n                            className={`bg-black text-white hover:bg-gray-800 rounded-xl font-medium transition-all duration-300 disabled:opacity-50 ${isMobile ? 'px-4 py-2' : 'px-6 py-3'\r\n                                }`}\r\n                        >\r\n                            <Send className={`${isMobile ? 'h-4 w-4 mr-1' : 'h-5 w-5 mr-2'}`} />\r\n                            {isMobile ? 'Send' : 'Send'}\r\n                        </Button>\r\n                    </div>\r\n                </form>\r\n            </main>\r\n        </div>\r\n    );\r\n}"], "names": [], "mappings": ";;;AA0GmC;;AAxGnC;AACA;AAIA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;;;AA5BA;;;;;;;;;;;;;;AA8Be,SAAS,WAAW,EAAE,QAAQ,EAAE,eAAe,EAAE,QAAQ,EAAE,MAAM,EAAE,mBAAmB,EAAE,qBAAqB,EAAE,uBAAuB,EAAmB;;IACpK,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IACxE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,CAAC;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA+C,EAAE;IAC9G,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,uBAAuB;IACvF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAClE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACtE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB,IAAI;IACxF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAiB;IACxC,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE,OAAO,eAAuB;YAC3D,IAAI;gBACA,MAAM,OAAO,MAAM,CAAA,GAAA,iIAAA,CAAA,0BAAuB,AAAD,EAAE,eAAe;gBAC1D,mBAAmB,QAAQ,EAAE;YACjC,EAAE,OAAM;gBACJ,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YAChB;QACJ;+CAAG,EAAE;IAEL,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4CAAE;YACzB,IAAI,CAAC,yBAAyB,uBAAuB,KAAK,CAAC,YAAY;gBACnE,OAAO,OAAO,CAAC;gBACf,cAAc;YAClB;QACJ;2CAAG;QAAC;QAAuB;QAAyB;QAAY;KAAO;IAEvE,MAAM,mBAAmB,CAAC;QACtB,gBAAgB,CAAC,OAAS,OAAO,UAAU,KAAK;IACpD;IAEA,MAAM,oBAAoB,CAAC;QACvB,EAAE,cAAc;QAChB,mBAAmB,CAAC,OAAS,CAAC;IAClC;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACN,iBAAiB,CAAC,CAAC;QACvB;+BAAG;QAAC;KAAS;IAEb,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACN,MAAM;2DAAqB,CAAC;oBACxB,IAAI,eAAe,OAAO,IAAI,CAAC,eAAe,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBAClF,mBAAmB;oBACvB;gBACJ;;YAEA,IAAI,iBAAiB;gBACjB,SAAS,gBAAgB,CAAC,aAAa;YAC3C;YAEA;wCAAO;oBACH,SAAS,mBAAmB,CAAC,aAAa;gBAC9C;;QACJ;+BAAG;QAAC;KAAgB;IAEpB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACN,IAAI,mBAAmB,iBAAiB,UAAU;gBAC9C,IAAI,UAAU,OAAO,EAAE;oBACnB,UAAU,OAAO,CAAC,UAAU;gBAChC;gBAEA,UAAU,OAAO,GAAG,CAAA,GAAA,kLAAA,CAAA,KAAE,AAAD,8DAAwC;oBACzD,iBAAiB;gBACrB;gBAEA,UAAU,OAAO,CAAC,EAAE,CAAC;4CAAW;wBAC5B,UAAU,OAAO,EAAE,KAAK,QAAQ;4BAAE;4BAAU;4BAAU;wBAAO;wBAC7D,UAAU,OAAO,EAAE,KAAK;wBACxB,UAAU,OAAO,EAAE,KAAK,mBAAmB;4BAAE;4BAAQ;wBAAS;wBAE9D,IAAI,gBAAgB;4BAChB,UAAU,OAAO,EAAE,KAAK,gBAAgB;gCACpC,QAAQ;gCACR,aAAa;4BACjB;wBACJ;oBACJ;;gBAEA,UAAU,OAAO,CAAC,EAAE,CAAC;4CAAiB,CAAC;wBACnC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,kBAAkB,EAAE,MAAM,OAAO,EAAE;oBACpD;;gBAEA,UAAU,OAAO,CAAC,EAAE,CAAC;4CAAc,CAAC;wBAChC,iBAAiB,KAAK,MAAM;oBAChC;;gBAEA,UAAU,OAAO,CAAC,EAAE,CAAC;4CAAY;wBAC7B,iBAAiB;oBACrB;;gBAEA,UAAU,OAAO,CAAC,EAAE,CAAC;4CAAwB,CAAC;wBAC1C,IAAI,KAAK,QAAQ,KAAK,gBAAgB;4BAClC;wDAAgB,CAAA;oCACZ,MAAM,SAAS,IAAI,IAAI;oCACvB,KAAK,UAAU,CAAC,OAAO;gEAAC,CAAA;4CACpB,OAAO,GAAG,CAAC;wCACf;;oCACA,OAAO;gCACX;;wBACJ;oBACJ;;gBAEA,UAAU,OAAO,CAAC,EAAE,CAAC;4CAAkB,CAAC;wBACpC,MAAM,wBAAwB,kBAAkB,CAC5C,AAAC,QAAQ,QAAQ,KAAK,UAAU,QAAQ,WAAW,KAAK,kBACvD,QAAQ,QAAQ,KAAK,kBAAkB,QAAQ,WAAW,KAAK,MACpE;wBAEA,IAAI,uBAAuB;4BACvB;wDAAmB,CAAA;oCACf,MAAM,gBAAgB,KAAK,IAAI;8EAAC,CAAA,MAAO,IAAI,EAAE,KAAK,QAAQ,EAAE;;oCAC5D,IAAI,eAAe;wCACf,OAAO;oCACX;oCACA,QAAQ,GAAG,CAAC,2CAA2C;oCACvD,OAAO;2CAAI;wCAAM;qCAAQ;gCAC7B;;wBACJ;wBAEA,IAAI,QAAQ,QAAQ,KAAK,UAAU,CAAC,oBAAoB,IAAI;oDAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,QAAQ,QAAQ;oDAAG;4BACpG;wDAAuB,CAAA,OAAQ;2CACxB;wCACH;4CAAE,UAAU,QAAQ,MAAM;4CAAE,QAAQ,QAAQ,QAAQ;wCAAC;qCACxD;;wBACL;oBACJ;;gBAEA,UAAU,OAAO,CAAC,EAAE,CAAC;4CAAe,CAAC;wBACjC,MAAM,cAAc,MAAM,IAAI,CAAC,IAAI,IAAI,MAAM,GAAG;gEAAC,CAAA,OAAQ;oCAAC,KAAK,MAAM;oCAAE;iCAAK;gEAAG,MAAM;wBACrF,eAAe;oBACnB;;gBAEA,UAAU,OAAO,CAAC,EAAE,CAAC;4CAAsB,CAAC;wBACxC,IAAI,KAAK,QAAQ,KAAK,gBAAgB;4BAClC,sBAAsB;wBAC1B;oBACJ;;gBAEA,UAAU,OAAO,CAAC,EAAE,CAAC;4CAAsB,CAAC;wBACxC,IAAI,KAAK,QAAQ,KAAK,gBAAgB;4BAClC,sBAAsB;wBAC1B;oBACJ;;gBAEA,UAAU,OAAO,CAAC,EAAE,CAAC;4CAAqB,CAAC;wBACvC;oDAAuB,CAAA;gCACnB,MAAM,SAAS,IAAI,IAAI;gCACvB,IAAI,KAAK,WAAW,KAAK,GAAG;oCACxB,OAAO,MAAM,CAAC,KAAK,QAAQ;gCAC/B,OAAO;oCACH,OAAO,GAAG,CAAC,KAAK,QAAQ,EAAE,KAAK,WAAW;gCAC9C;gCACA,OAAO;4BACX;;oBACJ;;gBAEA,UAAU,OAAO,CAAC,EAAE,CAAC;4CAAoB,CAAC;wBACtC,MAAM,kBAAkB,IAAI;wBAC5B,KAAK,OAAO;oDAAC,CAAC;gCACV,gBAAgB,GAAG,CAAC,KAAK,MAAM,EAAE,KAAK,WAAW;4BACrD;;wBACA,uBAAuB;oBAC3B;;gBAEA,UAAU,OAAO,CAAC,EAAE,CAAC;4CAAsB,CAAC;wBACxC;oDAAuB,CAAA;gCACnB,IAAI,CAAC,KAAK,IAAI;4DAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,KAAK,MAAM;4DAAG;oCACjD,OAAO;2CAAI;wCAAM;4CAAE,UAAU,KAAK,QAAQ;4CAAE,QAAQ,KAAK,MAAM;wCAAC;qCAAE;gCACtE;gCACA,OAAO;4BACX;;oBACJ;;gBAEA,UAAU,OAAO,CAAC,EAAE,CAAC;4CAAS,CAAC;wBAC3B,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO;oBAC7B;;gBAEA;4CAAO;wBACH,IAAI,iBAAiB,UAAU,kBAAkB,UAAU,OAAO,EAAE;4BAChE,UAAU,OAAO,CAAC,IAAI,CAAC,iBAAiB;gCACpC,QAAQ;gCACR,aAAa;4BACjB;wBACJ;wBAEA,UAAU,OAAO,EAAE,IAAI;wBACvB,UAAU,OAAO,EAAE,IAAI;wBACvB,UAAU,OAAO,EAAE,IAAI;wBACvB,UAAU,OAAO,EAAE,IAAI;wBACvB,UAAU,OAAO,EAAE,IAAI;wBACvB,UAAU,OAAO,EAAE,IAAI;wBACvB,UAAU,OAAO,EAAE,IAAI;wBACvB,UAAU,OAAO,EAAE,IAAI;wBACvB,UAAU,OAAO,EAAE,IAAI;wBACvB,UAAU,OAAO,EAAE,IAAI;wBACvB,UAAU,OAAO,EAAE,IAAI;wBACvB,UAAU,OAAO,EAAE,IAAI;wBACvB,UAAU,OAAO,EAAE,IAAI;wBACvB,UAAU,OAAO,EAAE;oBACvB;;YACJ;QACJ;+BAAG;QAAC;QAAU;QAAe;QAAiB;QAAU;QAAQ;QAAc;KAAe;IAE7F,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACN,IAAI,kBAAkB,UAAU,eAAe;gBAC3C,aAAa,QAAQ;gBAErB,MAAM,kBAAkB;4DAAY;wBAChC,aAAa,QAAQ;oBACzB;2DAAG;gBAEH;4CAAO,IAAM,cAAc;;YAC/B;QACJ;+BAAG;QAAC;QAAgB;QAAQ;QAAe;KAAa;IAExD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACN,MAAM;0DAAoB;oBACtB,IAAI,iBAAiB,QAAQ;wBACzB,IAAI;4BACA,MAAM,OAAO,MAAM,CAAA,GAAA,iIAAA,CAAA,uBAAoB,AAAD,EAAE,QAAQ;4BAChD,MAAM,cAAc,MAAM,IAAI,CAAC,IAAI,IAAI,KAAK,GAAG;sFAAC,CAAC,OAAc;wCAAC,KAAK,MAAM;wCAAE;qCAAK;sFAAG,MAAM;4BAC3F,uBAAuB;wBAC3B,EAAE,OAAM;wBACJ,wBAAwB;wBAC5B;oBACJ;gBACJ;;YAEA;YAEA,MAAM,kBAAkB,YAAY,mBAAmB;YAEvD;wCAAO,IAAM,cAAc;;QAC/B;+BAAG;QAAC;QAAe;QAAQ;KAAS;IAEpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACN,eAAe,OAAO,EAAE,eAAe;gBAAE,UAAU;YAAS;QAChE;+BAAG;QAAC;KAAgB;IAEpB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACN,IAAI,CAAC,uBAAuB,qBAAqB,KAAK,UAAU;gBAC5D,eAAe;YACnB,OAAO;gBACH,eAAe,CAAC;YACpB;QACJ;+BAAG;QAAC;QAAqB;QAAuB;KAAS;IAEzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACN,MAAM;2DAAqB;oBACvB,IAAI,kBAAkB,cAAc;wBAChC,eAAe,OAAO,CAAC,mBAAmB,KAAK,SAAS,CAAC;4BACrD,QAAQ;4BACR,UAAU;wBACd;oBACJ;oBAEA,IAAI,iBAAiB,UAAU,kBAAkB,UAAU,OAAO,EAAE;wBAChE,UAAU,OAAO,CAAC,IAAI,CAAC,iBAAiB;4BACpC,QAAQ;4BACR,aAAa;wBACjB;oBACJ;gBACJ;;YAEA,MAAM;mDAAa;oBACf,MAAM,gBAAgB,eAAe,OAAO,CAAC;oBAC7C,IAAI,iBAAiB,CAAC,kBAAkB,CAAC,uBAAuB;wBAC5D,IAAI;4BACA,MAAM,EAAE,QAAQ,cAAc,EAAE,QAAQ,EAAE,GAAG,KAAK,KAAK,CAAC;4BACxD,gBAAgB;4BAChB,kBAAkB;4BAElB;mEAAuB,CAAA;oCACnB,MAAM,aAAa,KAAK,IAAI;sFAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;;oCACrD,IAAI,CAAC,YAAY;wCACb,OAAO;+CAAI;4CAAM;gDAAE,UAAU;gDAAU,QAAQ;4CAAe;yCAAE;oCACpE;oCACA,OAAO;gCACX;;4BAEA,IAAI,UAAU,gBAAgB;gCAC1B,aAAa,QAAQ;4BACzB;wBACJ,EAAE,OAAO,OAAO;4BACZ,QAAQ,KAAK,CAAC,+BAA+B;wBACjD;oBACJ;gBACJ;;YAEA,OAAO,gBAAgB,CAAC,gBAAgB;YAExC,MAAM,gBAAgB,eAAe,OAAO,CAAC;YAC7C,IAAI,eAAe;gBACf;YACJ;YAEA;wCAAO;oBACH,OAAO,mBAAmB,CAAC,gBAAgB;gBAC/C;;QACJ;+BAAG;QAAC;QAAgB;QAAc;QAAuB;QAAQ;KAAc;IAE/E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACN,IAAI,kBAAkB,UAAU,gBAAgB,MAAM,GAAG,GAAG;gBACxD,MAAM,iBAAiB,gBAAgB,MAAM;2DAAC,CAAA,MAC1C,IAAI,MAAM,KAAK,gBACf,IAAI,SAAS,KAAK,YAClB,CAAC,aAAa,GAAG,CAAC,IAAI,EAAE;;gBAG5B,IAAI,eAAe,MAAM,GAAG,GAAG;oBAC3B,MAAM,mBAAmB,eAAe,GAAG;iEAAC,CAAA,MAAO,IAAI,EAAE;;oBAEzD,UAAU,OAAO,EAAE,KAAK,sBAAsB;wBAC1C,UAAU;wBACV,aAAa;wBACb,YAAY;oBAChB;gBACJ;YACJ;QACJ;+BAAG;QAAC;QAAgB;QAAQ;QAAiB;QAAc;QAAU;KAAa;IAElF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACN,IAAI,kBAAkB,UAAU,UAAU,OAAO,IAAI,UAAU,OAAO,CAAC,SAAS,EAAE;gBAC9E,UAAU,OAAO,CAAC,IAAI,CAAC,gBAAgB;oBACnC,QAAQ;oBACR,aAAa;gBACjB;YACJ;YAEA;wCAAO;oBACH,IAAI,iBAAiB,UAAU,kBAAkB,UAAU,OAAO,EAAE;wBAChE,UAAU,OAAO,CAAC,IAAI,CAAC,iBAAiB;4BACpC,QAAQ;4BACR,aAAa;wBACjB;oBACJ;gBACJ;;QACJ;+BAAG;QAAC;QAAgB;QAAQ;KAAc;IAE1C,MAAM,oBAAoB,OAAO;QAC7B,EAAE,cAAc;QAEhB,IAAI,CAAC,aAAa,IAAI,MAAM,CAAC,gBAAgB,CAAC,QAAQ;YAClD;QACJ;QAEA,MAAM,cAAc,aAAa,IAAI;QACrC,gBAAgB;QAEhB,IAAI;YAEA,IAAI,CAAC,gBAAgB;gBACjB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,gBAAgB;gBAChB;YACJ;YAEA,MAAM,gBAAgB,aAAa,YAAY,UAAU;YAEzD,MAAM,cAAc;gBAChB,MAAM;gBACN,UAAU;gBACV,aAAa;gBACb,YAAY;gBACZ,eAAe;gBACf,mBAAmB;YACvB;YAEA,UAAU,OAAO,EAAE,KAAK,sBAAsB;YAE9C;YAEA,IAAI,CAAC,aAAa,eAAe;gBAC7B,2IAAA,CAAA,QAAK,CAAC,IAAI,CAAC,GAAG,aAAa,kEAAkE,CAAC;YAClG;QACJ,EAAE,OAAM;YACJ,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,gBAAgB;QACpB;IACJ;IAEA,MAAM,mBAAmB,OAAO;QAC5B;QAEA,mBAAmB,EAAE;QACrB,gBAAgB,KAAK,QAAQ;QAC7B,gBAAgB,IAAI;QACpB,sBAAsB;QAEtB,MAAM,eAAe,KAAK,MAAM,IAAI,KAAK,QAAQ;QACjD,kBAAkB;QAElB,uBAAuB,CAAA;YACnB,MAAM,SAAS,IAAI,IAAI;YACvB,OAAO,MAAM,CAAC;YACd,OAAO;QACX;QAEA;QAEA,IAAI,UAAU;YACV,eAAe;QACnB;QAEA,IAAI,gBAAgB,QAAQ;YACxB,aAAa,QAAQ;QACzB;IACJ;IAEA,MAAM,mBAAmB;QACrB,IAAI,iBAAiB,UAAU,kBAAkB,UAAU,OAAO,EAAE;YAChE,UAAU,OAAO,CAAC,IAAI,CAAC,iBAAiB;gBACpC,QAAQ;gBACR,aAAa;YACjB;QACJ;IACJ;IAEA,MAAM,sBAAsB;QACxB;QAEA,eAAe;QACf,IAAI,UAAU;YACV,gBAAgB;YAChB,kBAAkB;QACtB;IACJ;IAEA,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;0CAAE;YACvB;kDAAO,CAAC,YAAoB,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,YAAY;;QAC5D;yCAAG,EAAE;IAEL,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;iDAAE;YAC9B,MAAM,UAAU,IAAI;YAEpB,oBAAoB,OAAO;yDAAC,CAAA;oBACxB,QAAQ,GAAG,CAAC,YAAY,MAAM,EAAE;wBAC5B,UAAU,YAAY,QAAQ;wBAC9B,UAAU,aAAa,YAAY,UAAU;wBAC7C,QAAQ,YAAY,MAAM;oBAC9B;gBACJ;;YAEA,YAAY,OAAO;yDAAC,CAAA;oBAChB,IAAI,WAAW,QAAQ,KAAK,YAAY,WAAW,QAAQ,KAAK,UAAU;wBACtE,QAAQ,GAAG,CAAC,WAAW,MAAM,IAAI,WAAW,QAAQ,EAAE;4BAClD,UAAU,WAAW,QAAQ;4BAC7B,UAAU,WAAW,QAAQ;4BAC7B,QAAQ,WAAW,MAAM,IAAI,WAAW,QAAQ;wBACpD;oBACJ;gBACJ;;YAEA,IAAI,yBAAyB,yBAAyB;gBAClD,QAAQ,GAAG,CAAC,uBAAuB;oBAC/B,UAAU;oBACV,UAAU,aAAa,YAAY,UAAU;oBAC7C,QAAQ;gBACZ;YACJ;YAEA,MAAM,gBAAgB,MAAM,IAAI,CAAC,QAAQ,MAAM,IAAI,MAAM;uEAAC,CAAA;oBACtD,MAAM,gBAAgB,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;oBAClF,MAAM,mBAAmB,KAAK,QAAQ,KAAK;oBAC3C,MAAM,uBAAuB,KAAK,QAAQ,KAAK;oBAE/C,OAAO,iBAAiB,oBAAoB;gBAChD;;YAEA,OAAO;QACX;gDAAG;QAAC;QAAqB;QAAa;QAAa;QAAU;QAAU;QAAuB;KAAwB;IAEtH,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;4CAAE;YACzB,MAAM,gBAAgB,IAAI,IAAI,YAAY,GAAG;oDAAC,CAAA,OAAQ,KAAK,MAAM;;YACjE;oDAAO,CAAC;oBACJ,MAAM,OAAO,kBAAkB,IAAI;iEAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;;oBACxD,IAAI,MAAM;wBACN,OAAO,cAAc,GAAG,CAAC,KAAK,MAAM;oBACxC;oBAEA,IAAI,aAAa,2BAA2B,uBAAuB;wBAC/D,OAAO,cAAc,GAAG,CAAC;oBAC7B;oBAEA,MAAM,aAAa,YAAY,IAAI;uEAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;;oBACxD,OAAO,CAAC,CAAC;gBACb;;QACJ;2CAAG;QAAC;QAAa;QAAmB;QAAyB;KAAsB;IAEnF,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;gDAAE;YAC7B,OAAO,kBAAkB,MAAM;wDAAC,CAAA;oBAC5B,MAAM,gBAAgB,KAAK,MAAM;oBACjC,OAAO,oBAAoB,GAAG,CAAC;gBACnC;uDAAG,MAAM;QACb;+CAAG;QAAC;QAAmB;KAAoB;IAE3C,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;6CAAE;YAC1B,IAAI,eAAe,UAAU;gBACzB,OAAO,kBAAkB,MAAM;yDAAC,CAAA;wBAC5B,MAAM,gBAAgB,KAAK,MAAM;wBACjC,OAAO,oBAAoB,GAAG,CAAC;oBACnC;;YACJ;YACA,OAAO;QACX;4CAAG;QAAC;QAAmB;QAAY;KAAoB;IAEvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACN,IAAI,yBAAyB,CAAC,kBAAkB,UAAU,iBAAiB;gBACvE,MAAM;2DAAiB;wBACnB,IAAI,cAAc;wBAElB,IAAI,CAAC,aAAa;4BACd,MAAM,iBAAiB,aAAa,YAAY,UAAU;4BAC1D,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,mBAAgB,AAAD,EAAE,uBAAuB;4BAClE,IAAI,aAAa;gCACb,cAAc,GAAG,YAAY,SAAS,CAAC,CAAC,EAAE,YAAY,QAAQ,EAAE,CAAC,IAAI;4BACzE,OAAO;gCACH,cAAc,CAAC,KAAK,EAAE,sBAAsB,KAAK,CAAC,GAAG,IAAI;4BAC7D;wBACJ;wBAEA,gBAAgB;wBAChB,kBAAkB;wBAElB;mEAAuB,CAAA;gCACnB,MAAM,aAAa,KAAK,IAAI;sFAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;;gCACrD,IAAI,CAAC,YAAY;oCACb,OAAO;2CAAI;wCAAM;4CAAE,UAAU;4CAAa,QAAQ;wCAAsB;qCAAE;gCAC9E;gCACA,OAAO;4BACX;;wBAEA,aAAa,QAAQ;oBACzB;;gBAEA;YACJ;QACJ;+BAAG;QAAC;QAAuB;QAAyB;QAAgB;QAAQ;QAAiB;KAAS;IAEtG,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACN,IAAI,uBAAuB,CAAC,kBAAkB,kBAAkB,MAAM,GAAG,KAAK,CAAC,yBAAyB,QAAQ;gBAC5G,MAAM,YAAY,kBAAkB,IAAI;sDAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;;gBACnE,IAAI,WAAW;oBACX,gBAAgB,UAAU,QAAQ;oBAClC,kBAAkB,UAAU,MAAM;oBAElC,aAAa,QAAQ,UAAU,MAAM;gBACzC;YACJ;QACJ;+BAAG;QAAC;QAAqB;QAAmB;QAAgB;QAAQ;KAAsB;IAE1F,IAAI,CAAC,iBAAiB;QAClB,qBACI,6LAAC;YAAI,WAAU;sBACX,cAAA,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAG,WAAU;kCAAyD;;;;;;kCACvE,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAE,WAAU;0CAAsE;;;;;;0CAGnF,6LAAC,qIAAA,CAAA,SAAM;gCACH,SAAS,IAAM,OAAO,IAAI,CAAC;gCAC3B,WAAU;0CACb;;;;;;;;;;;;;;;;;;;;;;;IAOrB;IAEA,qBACI,6LAAC;QAAI,WAAU;;YACV,6BACG,6LAAC;gBAAM,WAAW,CAAC,wFAAwF,EAAE,WACvG,iCACA,8CACA;;kCACF,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAI,WAAU;0CACX,cAAA,6LAAC;oCAAI,WAAU;8CACX,cAAA,6LAAC,gIAAA,CAAA,UAAK;wCACF,KAAI;wCACJ,KAAI;wCACJ,OAAO,WAAW,MAAM;wCACxB,QAAQ,WAAW,KAAK;wCACxB,WAAU;wCACV,SAAS,IAAM,OAAO,IAAI,CAAC;;;;;;;;;;;;;;;;0CAIvC,6LAAC;gCAAI,WAAU;;kDACX,6LAAC,qIAAA,CAAA,SAAM;wCACH,SAAQ;wCACR,MAAM,WAAW,OAAO;wCACxB,UAAU;wCACV,SAAS;4CACL,gBAAgB;4CAChB,IAAI;gDACA,UAAU,OAAO,EAAE,KAAK;gDACxB,UAAU,OAAO,EAAE,KAAK,mBAAmB;oDAAE;oDAAQ;gDAAS;gDAE9D,IAAI,iBAAiB,QAAQ;oDACzB,MAAM,OAAO,MAAM,CAAA,GAAA,iIAAA,CAAA,uBAAoB,AAAD,EAAE,QAAQ;oDAChD,MAAM,cAAc,MAAM,IAAI,CAAC,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC,OAAc;4DAAC,KAAK,MAAM;4DAAE;yDAAK,GAAG,MAAM;oDAE3F,MAAM,WAAW;oDAEjB,IAAI,kBAAkB,cAAc;wDAChC,MAAM,qBAAqB,SAAS,IAAI,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;wDACjE,IAAI,CAAC,oBAAoB;4DACrB,SAAS,IAAI,CAAC;gEAAE,UAAU;gEAAc,QAAQ;4DAAe;wDACnE;oDACJ;oDAEA,uBAAuB;oDACvB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gDAClB;4CACJ,EAAE,OAAM;gDACJ,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;4CAChB,SAAU;gDACN,gBAAgB;4CACpB;wCACJ;wCACA,WAAW,CAAC,yLAAyL,EAAE,WAAW,sBAAsB,qBAClO;wCACN,OAAM;;0DAEN,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAW,GAAG,WAAW,YAAY,UAAU,CAAC,EAAE,eAAe,iBAAiB,IAAI;;;;;;4CAChG,CAAC,0BAAY,6LAAC;gDAAK,WAAU;0DAAQ,eAAe,kBAAkB;;;;;;;;;;;;oCAE1E,0BACG,6LAAC,qIAAA,CAAA,SAAM;wCACH,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,eAAe;kDAE9B,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAM7B,6LAAC;wBAAI,WAAW,GAAG,WAAW,QAAQ,MAAM,YAAY,CAAC;kCACrD,cAAA,6LAAC;4BAAI,WAAU;;8CACX,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAW,CAAC,uHAAuH,EAAE,WAAW,YAAY,WAAW;;;;;;8CAC/K,6LAAC,oIAAA,CAAA,QAAK;oCACF,aAAY;oCACZ,WAAW,CAAC,wMAAwM,EAAE,WAAW,mBAAmB,kBAC9O;oCACN,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;kCAK1D,6LAAC;wBAAI,WAAW,GAAG,WAAW,cAAc,aAAa;kCACrD,cAAA,6LAAC;4BAAI,WAAU;sCACX,cAAA,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCACG,SAAS,IAAM,cAAc;wCAC7B,WAAW,CAAC,yIAAyI,EACjJ,eAAe,QACT,4DACA,sDACT,CAAC,EAAE,WAAW,wBAAwB,qBAAqB;kDAE5D,cAAA,6LAAC;4CAAI,WAAW,CAAC,wBAAwB,EAAE,WAAW,mBAAmB,IAAI;;8DACzE,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAW,GAAG,WAAW,YAAY,WAAW;;;;;;8DACvD,6LAAC;oDAAK,WAAW,WAAW,8BAA8B;8DACrD,WAAW,QAAQ;;;;;;8DAExB,6LAAC;oDAAK,WAAW,CAAC,iHAAiH,EAC/H,eAAe,QACT,wBACA,6BACR;8DACG,kBAAkB,MAAM;;;;;;;;;;;;;;;;;kDAKrC,6LAAC;wCACG,SAAS,IAAM,cAAc;wCAC7B,WAAW,CAAC,yIAAyI,EACjJ,eAAe,WACT,4DACA,sDACT,CAAC,EAAE,WAAW,wBAAwB,qBAAqB;kDAE5D,cAAA,6LAAC;4CAAI,WAAW,CAAC,wBAAwB,EAAE,WAAW,mBAAmB,IAAI;;8DACzE,6LAAC;oDAAI,WAAU;;sEACX,6LAAC,2NAAA,CAAA,gBAAa;4DAAC,WAAW,GAAG,WAAW,YAAY,WAAW;;;;;;wDAC9D,mBAAmB,mBAChB,6LAAC;4DAAI,WAAU;;;;;;;;;;;;8DAGvB,6LAAC;oDAAK,WAAW,WAAW,8BAA8B;8DACrD,WAAW,WAAW;;;;;;8DAE3B,6LAAC;oDAAK,WAAW,CAAC,iHAAiH,EAC/H,eAAe,WACT,mBAAmB,IACf,4BACA,8BACJ,mBAAmB,IACf,wCACA,6BACZ;8DACG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQzB,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC;4BAAI,WAAW,CAAC,UAAU,EAAE,WAAW,cAAc,aAAa;sCAC9D,cAAc,MAAM,GAAG,KAAM,gBAAgB,sCAC1C;;oCACC,gBAAgB,yBAChB,CAAC,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,wCACnC,6LAAC;wCAEG,WAAW,CAAC,gGAAgG,EAAE,WAAW,QAAQ,MAC5H,CAAC,EAAE,mBAAmB,wBACjB,wFACA,0EACJ;wCACN,SAAS,IAAM,iBAAiB;gDAC5B,UAAU;gDACV,UAAU,aAAa,YAAY,UAAU;gDAC7C,QAAQ;4CACZ;kDAEA,cAAA,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;oDAAI,WAAU;;sEACX,6LAAC,qIAAA,CAAA,SAAM;4DAAC,WAAW,CAAC,qEAAqE,EAAE,WAAW,cAAc,YAAY,gBAAgB,CAAC;sEAC7I,cAAA,6LAAC,qIAAA,CAAA,iBAAc;gEAAC,WAAW,CAAC,6CAA6C,EAAE,WAAW,YAAY,UAAU,oBAAoB,CAAC;0EAC5H,cAAc,UAAU,GAAG,GAAG;;;;;;;;;;;sEAGvC,6LAAC;4DAAI,WAAW,CAAC,mHAAmH,EAAE,WAAW,YAAY,UACxJ,cAAc,EAAE,aAAa,gBAAgB,MAAM,2BAA2B,eAC7E;sEACF,cAAA,6LAAC;gEAAI,WAAW,CAAC,yCAAyC,EAAE,WAAW,YAAY,cAC9E,CAAC,EAAE,aAAa,gBAAgB,MAAM,+BAA+B,eACpE;;;;;;;;;;;;;;;;;8DAGd,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DAAI,WAAU;sEACX,cAAA,6LAAC;gEAAG,WAAW,CAAC,sDAAsD,EAAE,WAAW,YAAY,YAAY,WAAW,CAAC;0EAClH;;;;;;;;;;;sEAGT,6LAAC;4DAAI,WAAU;;8EACX,6LAAC;oEAAI,WAAW,CAAC,iIAAiI,CAAC;8EAAE;;;;;;8EAGrJ,6LAAC;oEAAI,WAAW,CAAC,iIAAiI,CAAC;;sFAC/I,6LAAC;4EAAI,WAAW,CAAC,qBAAqB,EAAE,aAAa,gBAAgB,MAAM,iBAAiB,eAAe;;;;;;wEAC1G,aAAa,gBAAgB,MAAM,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;uCAvC1D;;;;;oCA8CZ,cAAc,GAAG,CAAC,CAAC,qBAChB,6LAAC;4CAEG,WAAW,CAAC,gGAAgG,EAAE,WAAW,QAAQ,MAC5H,CAAC,EAAE,iBAAiB,KAAK,QAAQ,GAC5B,wFACA,0EACJ;4CACN,SAAS,IAAM,iBAAiB;sDAEhC,cAAA,6LAAC;gDAAI,WAAU;;kEACX,6LAAC;wDAAI,WAAU;;0EACX,6LAAC,qIAAA,CAAA,SAAM;gEAAC,WAAW,CAAC,qEAAqE,EAAE,WAAW,cAAc,YAAY,CAAC,EAC7H,iBAAiB,KAAK,QAAQ,GACxB,oBACA,+CACR;0EACE,cAAA,6LAAC,qIAAA,CAAA,iBAAc;oEAAC,WAAW,CAAC,6CAA6C,EAAE,WAAW,YAAY,UAAU,CAAC,EAAE,iBAAiB,KAAK,QAAQ,GACvI,wBACA,+GACA;8EACD,KAAK,QAAQ,CAAC,SAAS,CAAC,GAAG,GAAG,WAAW;;;;;;;;;;;0EAGlD,6LAAC;gEAAI,WAAW,CAAC,mHAAmH,EAAE,WAAW,YAAY,UACxJ,CAAC,EAAE,iBAAiB,KAAK,QAAQ,GAAG,iBAAiB,eACrD,CAAC,EAAE,aAAa,KAAK,QAAQ,IAAI,2BAA2B,eAC3D;0EACF,cAAA,6LAAC;oEAAI,WAAW,CAAC,yCAAyC,EAAE,WAAW,YAAY,cAC9E,CAAC,EAAE,aAAa,KAAK,QAAQ,IAAI,+BAA+B,eAC/D;;;;;;;;;;;;;;;;;kEAGd,6LAAC;wDAAI,WAAU;;0EACX,6LAAC;gEAAI,WAAU;;kFACX,6LAAC;wEAAG,WAAW,CAAC,sDAAsD,EAAE,WAAW,YAAY,YAAY,CAAC,EAAE,iBAAiB,KAAK,QAAQ,GACtI,eACA,wCACA;kFACD,KAAK,QAAQ;;;;;;oEAEjB,oBAAoB,GAAG,CAAC,KAAK,MAAM,mBAChC,6LAAC;wEAAI,WAAU;kFACV,oBAAoB,GAAG,CAAC,KAAK,MAAM;;;;;;;;;;;;0EAIhD,6LAAC;gEAAI,WAAU;;kFACX,6LAAC;wEAAI,WAAW,CAAC,2GAA2G,EACxH,iBAAiB,KAAK,QAAQ,GACxB,2BACA,KAAK,QAAQ,KAAK,YACd,sDACA,qDACZ;kFACG,KAAK,QAAQ,KAAK,YAAY,YAAY;;;;;;kFAE/C,6LAAC;wEAAI,WAAW,CAAC,2GAA2G,EACxH,iBAAiB,KAAK,QAAQ,GACxB,2BACA,aAAa,KAAK,QAAQ,IACtB,yDACA,qDACZ;;0FACE,6LAAC;gFAAI,WAAW,CAAC,qBAAqB,EAAE,aAAa,KAAK,QAAQ,IAAI,iBAAiB,eAAe;;;;;;4EACrG,aAAa,KAAK,QAAQ,IAAI,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;2CA/DrD,KAAK,MAAM;;;;;;6DAwExB,6LAAC;gCAAI,WAAU;0CACX,cAAA,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAI,WAAU;sDACV,eAAe,yBACZ,6LAAC,2NAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;qEAEzB,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAGzB,6LAAC;4CAAG,WAAW,CAAC,iCAAiC,EAAE,WAAW,YAAY,aAAa;sDAClF,eAAe,WAAW,uBAAuB;;;;;;sDAEtD,6LAAC;4CAAE,WAAU;sDACR,eAAe,WACV,wDACA,CAAC,uBAAuB,EAAE,aAAa,YAAY,WAAW,WAAW,qCAAqC,CAAC;;;;;;sDAGzH,6LAAC;4CAAE,WAAU;sDACR,eAAe,WACV,mDACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAWrC,YAAY,6BACT,6LAAC;gBACG,WAAU;gBACV,SAAS,IAAM,eAAe;;;;;;0BAItC,6LAAC;gBAAK,WAAU;;kCACZ,6LAAC;wBAAI,WAAW,CAAC,4DAA4D,EAAE,WAAW,QAAQ,OAAO;;4BACpG,YAAY,CAAC,6BACV,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAO,WAAW,CAAC,2CAA2C,EAAE,WAAW,YAAY,aAAa;gCAAE,SAAS;0CACxI,cAAA,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAW,GAAG,WAAW,YAAY,WAAW;;;;;;;;;;;0CAGnE,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAI,WAAU;;0DACX,6LAAC,qIAAA,CAAA,SAAM;gDAAC,WAAW,CAAC,iDAAiD,EAAE,WAAW,YAAY,aAAa;0DACtG,6BACG,6LAAC,qIAAA,CAAA,iBAAc;oDAAC,WAAW,CAAC,qCAAqC,EAAE,WAAW,YAAY,WAAW;8DAChG,aAAa,SAAS,CAAC,GAAG,GAAG,WAAW;;;;;yEAG7C,6LAAC,qIAAA,CAAA,iBAAc;oDAAC,WAAU;8DACtB,cAAA,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAW,CAAC,cAAc,EAAE,WAAW,YAAY,WAAW;;;;;;;;;;;;;;;;4CAIhF,8BACG,6LAAC;gDAAI,WAAW,CAAC,gGAAgG,EAAE,WAAW,YAAY,UACrI,CAAC,EAAE,aAAa,gBAAgB,iBAAiB,eAChD;0DACF,cAAA,6LAAC;oDAAI,WAAW,CAAC,aAAa,EAAE,WAAW,gBAAgB,UACtD,CAAC,EAAE,aAAa,gBAAgB,+BAA+B,eAC9D;;;;;;;;;;;;;;;;;kDAIlB,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAG,WAAW,CAAC,0DAA0D,EAAE,WAAW,cAAc,WAAW;0DAC3G,6BACG,6LAAC;oDAAK,WAAU;8DAAY;;;;;2DAC5B;;;;;;0DAER,6LAAC;gDAAE,WAAW,CAAC,uBAAuB,EAAE,WAAW,YAAY,WAAW;0DACrE,eACG,aAAa,gBAAgB,WAAW,qDACxC;;;;;;;;;;;;;;;;;;;;;;;;kCAMpB,6LAAC;wBAAI,WAAW,CAAC,kCAAkC,EAAE,WAAW,QAAQ,OAAO;kCAC3E,cAAA,6LAAC;4BAAI,WAAW,CAAC,QAAQ,EAAE,WAAW,yBAAyB,uBAAuB;;gCACjF,eACG,gBAAgB,MAAM,GAAG,IACrB,gBAAgB,GAAG,CAAC,CAAC;oCACjB,MAAM,gBAAgB,QAAQ,MAAM,KAAK;oCACzC,MAAM,aAAa,QAAQ,MAAM,IAAI;oCAErC,qBACI,6LAAC;wCAEG,WAAW,CAAC,eAAe,EAAE,gBAAgB,gBAAgB,GAAG,CAAC,EAAE,WAAW,UAAU,SAAS;;4CAEhG,CAAC,+BACE,6LAAC,qIAAA,CAAA,SAAM;gDAAC,WAAW,CAAC,mCAAmC,EAAE,WAAW,YAAY,WAAW;0DACvF,cAAA,6LAAC,qIAAA,CAAA,iBAAc;oDAAC,WAAW,CAAC,qCAAqC,EAAE,WAAW,YAAY,WAAW;8DAChG,WAAW,SAAS,CAAC,GAAG,GAAG,WAAW;;;;;;;;;;;0DAInD,6LAAC;gDAAI,WAAW,GAAG,gBAAgB,eAAe,GAAG,CAAC,EAAE,WAAW,gBAAgB,eAAe;0DAC9F,cAAA,6LAAC;oDACG,WAAW,GAAG,gBACR,wBACA,+CACD,mCAAmC,EAAE,WAAW,QAAQ,OAAO;;sEAEpE,6LAAC;4DAAI,WAAW,CAAC,gBAAgB,EAAE,WAAW,YAAY,aAAa;sEAClE,QAAQ,IAAI;;;;;;sEAEjB,6LAAC;4DAAI,WAAW,CAAC,8CAA8C,EAAE,gBAC3D,kBACA,iBACA;;gEACD,WAAW,QAAQ,SAAS;gEAC5B,+BACG,6LAAC;8EACI,aAAa,GAAG,CAAC,QAAQ,EAAE,kBACxB,6LAAC,qNAAA,CAAA,aAAU;wEAAC,WAAW,GAAG,WAAW,YAAY,UAAU,cAAc,CAAC;;;;;+EAC1E,mCACA,6LAAC,qNAAA,CAAA,aAAU;wEAAC,WAAW,GAAG,WAAW,YAAY,UAAU,cAAc,CAAC;;;;;+EAC1E,aAAa,8BACb,6LAAC,qNAAA,CAAA,aAAU;wEAAC,WAAW,GAAG,WAAW,YAAY,UAAU,cAAc,CAAC;;;;;6FAE1E,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAW,GAAG,WAAW,YAAY,UAAU,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCAlCxF,QAAQ,EAAE;;;;;gCA2C3B,mBAEA,6LAAC;oCAAI,WAAU;;sDACX,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;yDAIlD,6LAAC;oCAAI,WAAU;;sDACX,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAC1C,6LAAC;4CAAE,WAAU;;gDAAsC;gDACjB,aAAa,YAAY,WAAW;gDAAW;gDAC5E,cAAc,MAAM,KAAK,mBACtB,6LAAC;oDAAK,WAAU;8DAAa;;;;;;;;;;;;;;;;;;8CAK7C,6LAAC;oCAAI,KAAK;;;;;;;;;;;;;;;;;kCAIlB,6LAAC;wBAAK,UAAU;wBAAmB,WAAW,CAAC,oCAAoC,EAAE,WAAW,QAAQ,OAAO;kCAC3G,cAAA,6LAAC;4BAAI,WAAW,CAAC,0BAA0B,EAAE,WAAW,qBAAqB,mBAAmB;;8CAC5F,6LAAC;oCACG,MAAK;oCACL,SAAS;oCACT,WAAW,CAAC,kHAAkH,EAAE,WAAW,sBAAsB,sBAC3J;8CACT;;;;;;gCAIA,iCACG,6LAAC;oCACG,KAAK;oCACL,WAAW,CAAC,cAAc,EAAE,WAAW,6BAA6B,qBAC9D;8CAEN,cAAA,6LAAC,sLAAA,CAAA,UAAW;wCACR,cAAc;wCACd,YAAY,sLAAA,CAAA,aAAU,CAAC,KAAK;wCAC5B,gBAAgB;wCAChB,OAAO,WAAW,SAAS;;;;;;;;;;;8CAIvC,6LAAC,oIAAA,CAAA,QAAK;oCACF,aAAa,eAAe,yBAAyB;oCACrD,WAAW,CAAC,0IAA0I,EAAE,WAAW,sBAAsB,uBACnL;oCACN,OAAO;oCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oCAC/C,UAAU,CAAC;oCACX,WAAW;;;;;;8CAEf,6LAAC,qIAAA,CAAA,SAAM;oCACH,MAAK;oCACL,MAAM,WAAW,YAAY;oCAC7B,UAAU,CAAC,aAAa,IAAI,MAAM,CAAC;oCACnC,WAAW,CAAC,6GAA6G,EAAE,WAAW,cAAc,aAC9I;;sDAEN,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAW,GAAG,WAAW,iBAAiB,gBAAgB;;;;;;wCAC/D,WAAW,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjD;GAhjCwB;;QAkBL,qIAAA,CAAA,YAAS;QAKP,gIAAA,CAAA,cAAW;;;KAvBR", "debugId": null}}, {"offset": {"line": 1937, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app/classes/chat/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useState } from 'react';\r\nimport { useSelector } from 'react-redux';\r\nimport { RootState } from '@/store';\r\nimport SharedChat from '@/app-components/SharedChat';\r\n\r\nexport default function ClassesChat() {\r\n    const [username, setUsername] = useState('');\r\n    const [userId, setUserId] = useState('');\r\n\r\n    const { user, isAuthenticated } = useSelector((state: RootState) => state.user);\r\n\r\n    useEffect(() => {\r\n        if (isAuthenticated && user) {\r\n            const userName = `${user.firstName} ${user.lastName}` || user.email.split('@')[0];\r\n            setUsername(userName);\r\n            setUserId(user.id);\r\n        }\r\n    }, [isAuthenticated, user]);\r\n    \r\n    return (\r\n        <SharedChat\r\n            userType=\"class\"\r\n            isAuthenticated={isAuthenticated}\r\n            username={username}\r\n            userId={userId}\r\n            loginPath=\"/\"\r\n        />\r\n    );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;;;AALA;;;;AAOe,SAAS;;IACpB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;mCAAE,CAAC,QAAqB,MAAM,IAAI;;IAE9E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACN,IAAI,mBAAmB,MAAM;gBACzB,MAAM,WAAW,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE,IAAI,KAAK,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;gBACjF,YAAY;gBACZ,UAAU,KAAK,EAAE;YACrB;QACJ;gCAAG;QAAC;QAAiB;KAAK;IAE1B,qBACI,6LAAC,0IAAA,CAAA,UAAU;QACP,UAAS;QACT,iBAAiB;QACjB,UAAU;QACV,QAAQ;QACR,WAAU;;;;;;AAGtB;GAvBwB;;QAIc,4JAAA,CAAA,cAAW;;;KAJzB", "debugId": null}}]}