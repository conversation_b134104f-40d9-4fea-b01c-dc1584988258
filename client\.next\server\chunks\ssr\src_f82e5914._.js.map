{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from 'clsx';\r\nimport { twMerge } from 'tailwind-merge';\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\nexport const truncateThought = (text: string, wordLimit: number = 5): string => {\r\n  const words = text.trim().split(/\\s+/);\r\n  if (words.length <= wordLimit) return text;\r\n  return words.slice(0, wordLimit).join(' ') + '...';\r\n};\r\n\r\nexport const setStudentAuthToken = (token: string) => {\r\n  localStorage.setItem('studentToken', token);\r\n};\r\n\r\nexport const getStudentAuthToken = (): string | null => {\r\n  return localStorage.getItem('studentToken');\r\n};\r\n\r\nexport const clearStudentAuthToken = () => {\r\n  localStorage.removeItem('studentToken');\r\n};\r\n\r\nexport const isStudentAuthenticated = (): boolean => {\r\n  return !!getStudentAuthToken();\r\n};"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,MAAM,kBAAkB,CAAC,MAAc,YAAoB,CAAC;IACjE,MAAM,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC;IAChC,IAAI,MAAM,MAAM,IAAI,WAAW,OAAO;IACtC,OAAO,MAAM,KAAK,CAAC,GAAG,WAAW,IAAI,CAAC,OAAO;AAC/C;AAEO,MAAM,sBAAsB,CAAC;IAClC,aAAa,OAAO,CAAC,gBAAgB;AACvC;AAEO,MAAM,sBAAsB;IACjC,OAAO,aAAa,OAAO,CAAC;AAC9B;AAEO,MAAM,wBAAwB;IACnC,aAAa,UAAU,CAAC;AAC1B;AAEO,MAAM,yBAAyB;IACpC,OAAO,CAAC,CAAC;AACX", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        'bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        '@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn('leading-none font-semibold', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn('col-start-2 row-span-2 row-start-1 self-start justify-self-end', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return <div data-slot=\"card-content\" className={cn('px-6', className)} {...props} />;\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn('flex items-center px-6 [.border-t]:pt-6', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardAction, CardDescription, CardContent };\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kEAAkE;QAC/E,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBAAO,8OAAC;QAAI,aAAU;QAAe,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QAAa,GAAG,KAAK;;;;;;AAClF;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: 'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',\r\n        destructive:\r\n          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\r\n        outline:\r\n          'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50',\r\n        secondary: 'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',\r\n        ghost: 'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',\r\n        link: 'text-primary underline-offset-4 hover:underline',\r\n      },\r\n      size: {\r\n        default: 'h-9 px-4 py-2 has-[>svg]:px-3',\r\n        sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',\r\n        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',\r\n        icon: 'size-9',\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default',\r\n      size: 'default',\r\n    },\r\n  }\r\n);\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<'button'> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean;\r\n  }) {\r\n  const Comp = asChild ? Slot : 'button';\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 199, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/store/hooks.ts"], "sourcesContent": ["import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';\r\nimport type { RootState, AppDispatch } from './index';\r\n\r\n// Use throughout your app instead of plain `useDispatch` and `useSelector`\r\nexport const useAppDispatch = () => useDispatch<AppDispatch>();\r\nexport const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAIO,MAAM,iBAAiB,IAAM,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;AACvC,MAAM,iBAAkD,yJAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 213, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/ProfileCompletionIndicator.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { Button } from '@/components/ui/button';\r\nimport { User, ArrowRight } from 'lucide-react';\r\nimport { useSelector } from 'react-redux';\r\nimport { RootState } from '@/store';\r\n\r\nconst ProfileCompletionIndicator = () => {\r\n  const router = useRouter();\r\n  const { profileData } = useSelector((state: RootState) => state.studentProfile);\r\n\r\n  // Check if student is logged in\r\n  const isLoggedIn = typeof window !== 'undefined' && localStorage.getItem('studentToken') !== null;\r\n\r\n  // Check if student has any profile data - only check if profile ID exists\r\n  const hasProfile = profileData?.profile?.id !== undefined;\r\n\r\n  // Only show the indicator if student is logged in and doesn't have a profile\r\n  if (!isLoggedIn || hasProfile) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div className=\"bg-white dark:bg-gray-900 border border-orange-200 overflow-hidden  dark:border-orange-900\">\r\n      <div className=\"px-3 py-1.5 flex items-center justify-between\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          <div className=\"bg-[#ff914d] p-1.5 rounded-full\">\r\n            <User className=\"h-3 w-3 text-white\" />\r\n          </div>\r\n          <p className=\"text-xs font-medium text-gray-800 dark:text-gray-200\">\r\n            Please Complete your profile \r\n          </p>\r\n        </div>\r\n        <Button\r\n          onClick={() => router.push('/student/profile')}\r\n          className=\"bg-[#ff914d] hover:bg-[#e07c3a] text-white text-xs px-2 py-0.5 h-6 min-w-0\"\r\n          size=\"sm\"\r\n        >\r\n         Complete now <ArrowRight className=\"h-3 w-3\" />\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProfileCompletionIndicator;\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;;AASA,MAAM,6BAA6B;IACjC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAqB,MAAM,cAAc;IAE9E,gCAAgC;IAChC,MAAM,aAAa,gBAAkB,eAAe,aAAa,OAAO,CAAC,oBAAoB;IAE7F,0EAA0E;IAC1E,MAAM,aAAa,aAAa,SAAS,OAAO;IAEhD,6EAA6E;IAC7E,wCAA+B;QAC7B,OAAO;IACT;;AAuBF;uCAEe", "debugId": null}}, {"offset": {"line": 246, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/avatar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as AvatarPrimitive from '@radix-ui/react-avatar';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Avatar({ className, ...props }: React.ComponentProps<typeof AvatarPrimitive.Root>) {\r\n  return (\r\n    <AvatarPrimitive.Root\r\n      data-slot=\"avatar\"\r\n      className={cn('relative flex size-8 shrink-0 overflow-hidden rounded-full', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AvatarImage({ className, ...props }: React.ComponentProps<typeof AvatarPrimitive.Image>) {\r\n  return (\r\n    <AvatarPrimitive.Image\r\n      data-slot=\"avatar-image\"\r\n      className={cn('aspect-square size-full', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AvatarFallback({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\r\n  return (\r\n    <AvatarPrimitive.Fallback\r\n      data-slot=\"avatar-fallback\"\r\n      className={cn('bg-muted flex size-full items-center justify-center rounded-full', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback };\r\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EAAE,SAAS,EAAE,GAAG,OAA0D;IACxF,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8DAA8D;QAC3E,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAA2D;IAC9F,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oEAAoE;QACjF,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 298, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/popover.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as PopoverPrimitive from '@radix-ui/react-popover';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Popover({ ...props }: React.ComponentProps<typeof PopoverPrimitive.Root>) {\r\n  return <PopoverPrimitive.Root data-slot=\"popover\" {...props} />;\r\n}\r\n\r\nfunction PopoverTrigger({ ...props }: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\r\n  return <PopoverPrimitive.Trigger data-slot=\"popover-trigger\" {...props} />;\r\n}\r\n\r\nfunction PopoverContent({\r\n  className,\r\n  align = 'center',\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\r\n  return (\r\n    <PopoverPrimitive.Portal>\r\n      <PopoverPrimitive.Content\r\n        data-slot=\"popover-content\"\r\n        align={align}\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden',\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </PopoverPrimitive.Portal>\r\n  );\r\n}\r\n\r\nfunction PopoverAnchor({ ...props }: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\r\n  return <PopoverPrimitive.Anchor data-slot=\"popover-anchor\" {...props} />;\r\n}\r\n\r\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor };\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,QAAQ,EAAE,GAAG,OAA2D;IAC/E,qBAAO,8OAAC,mKAAA,CAAA,OAAqB;QAAC,aAAU;QAAW,GAAG,KAAK;;;;;;AAC7D;AAEA,SAAS,eAAe,EAAE,GAAG,OAA8D;IACzF,qBAAO,8OAAC,mKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,GAAG,OACmD;IACtD,qBACE,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,keACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,cAAc,EAAE,GAAG,OAA6D;IACvF,qBAAO,8OAAC,mKAAA,CAAA,SAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE", "debugId": null}}, {"offset": {"line": 367, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/studentAuthServices.ts"], "sourcesContent": ["import {\r\n    StudentRegisterData,\r\n    StudentLoginData,\r\n    AuthResponse,\r\n    GoogleAuthData,\r\n} from '@/lib/types';\r\nimport { axiosInstance } from '@/lib/axios';\r\n\r\nexport const registerStudent = async (StudentRegisterData: StudentRegisterData) => {\r\n    const response = await axiosInstance.post(`/student/register`, StudentRegisterData);\r\n    return response.data\r\n}\r\n\r\nexport const loginStudent = async (StudentLoginData: StudentLoginData) => {\r\n    const response = await axiosInstance.post(`/student/login`, StudentLoginData);\r\n    return response.data;\r\n}\r\n\r\nexport const googleAuthStudent = async (googleAuthData: GoogleAuthData) => {\r\n    const response = await axiosInstance.post(`/student/google-auth`, googleAuthData);\r\n    return response.data;\r\n};\r\n\r\nexport const logoutStudent = async (): Promise<AuthResponse> => {\r\n    // Clear student token from localStorage\r\n    localStorage.removeItem('studentToken');\r\n    localStorage.removeItem('student_data');\r\n\r\n    return {\r\n        success: true,\r\n        message: 'Logged out successfully',\r\n    };\r\n};\r\n\r\nexport const forgotPassword = async (email: string) => {\r\n    const response = await axiosInstance.post(`/student/forgot-password`, { email });\r\n    return response.data;\r\n}\r\n\r\nexport const studentverifyEmail = async (token: string) => {\r\n    const response = await axiosInstance.post(`/student/verify-email`, { token });\r\n    return response.data;\r\n};\r\n\r\nexport const studentresendVerificationEmail = async (email: string) => {\r\n    const response = await axiosInstance.post(`/student/resend-verification`, { email });\r\n    return response.data;\r\n};\r\n\r\nexport const resetStudentPassword = async (email: string, token: string, newPassword: string) => {\r\n    const response = await axiosInstance.post(`/student/reset-password`, {\r\n        email,\r\n        token,\r\n        newPassword,\r\n    });\r\n    return response.data;\r\n};"], "names": [], "mappings": ";;;;;;;;;;AAMA;;AAEO,MAAM,kBAAkB,OAAO;IAClC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,iBAAiB,CAAC,EAAE;IAC/D,OAAO,SAAS,IAAI;AACxB;AAEO,MAAM,eAAe,OAAO;IAC/B,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,EAAE;IAC5D,OAAO,SAAS,IAAI;AACxB;AAEO,MAAM,oBAAoB,OAAO;IACpC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,EAAE;IAClE,OAAO,SAAS,IAAI;AACxB;AAEO,MAAM,gBAAgB;IACzB,wCAAwC;IACxC,aAAa,UAAU,CAAC;IACxB,aAAa,UAAU,CAAC;IAExB,OAAO;QACH,SAAS;QACT,SAAS;IACb;AACJ;AAEO,MAAM,iBAAiB,OAAO;IACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,wBAAwB,CAAC,EAAE;QAAE;IAAM;IAC9E,OAAO,SAAS,IAAI;AACxB;AAEO,MAAM,qBAAqB,OAAO;IACrC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,qBAAqB,CAAC,EAAE;QAAE;IAAM;IAC3E,OAAO,SAAS,IAAI;AACxB;AAEO,MAAM,iCAAiC,OAAO;IACjD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,4BAA4B,CAAC,EAAE;QAAE;IAAM;IAClF,OAAO,SAAS,IAAI;AACxB;AAEO,MAAM,uBAAuB,OAAO,OAAe,OAAe;IACrE,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,uBAAuB,CAAC,EAAE;QACjE;QACA;QACA;IACJ;IACA,OAAO,SAAS,IAAI;AACxB", "debugId": null}}, {"offset": {"line": 432, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/Header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from \"next/link\";\r\nimport Image from \"next/image\";\r\nimport { Menu, X, User, ShoppingBag, Briefcase, Share2, UserCircle, ChevronRight } from \"lucide-react\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { RootState } from \"@/store\";\r\nimport { useAppDispatch } from \"@/store/hooks\";\r\nimport { useEffect, useState, useRef } from \"react\";\r\nimport { GraduationCap, Flame } from \"lucide-react\";\r\nimport { isStudentAuthenticated, clearStudentAuthToken } from \"@/lib/utils\";\r\nimport ProfileCompletionIndicator from \"./ProfileCompletionIndicator\";\r\nimport {\r\n  Avatar,\r\n  AvatarFallback,\r\n} from \"@/components/ui/avatar\";\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from \"@/components/ui/popover\";\r\nimport { toast } from \"sonner\";\r\nimport { logoutStudent } from \"@/services/studentAuthServices\";\r\nimport { clearUser } from \"@/store/slices/userSlice\";\r\nimport { clearStudentProfileData } from \"@/store/slices/studentProfileSlice\";\r\nimport { fetchStudentProfile } from \"@/store/thunks/studentProfileThunks\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { axiosInstance } from \"@/lib/axios\";\r\nimport { motion, useMotionValue, useAnimationFrame } from \"framer-motion\";\r\n\r\nconst Header = () => {\r\n  const { isAuthenticated, user } = useSelector((state: RootState) => state.user);\r\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\r\n  const [isStudentLoggedIn, setIsStudentLoggedIn] = useState(false);\r\n  const [studentData, setStudentData] = useState<any>(null);\r\n  const dispatch = useAppDispatch();\r\n  const router = useRouter();\r\n\r\n  const contentRef = useRef<HTMLDivElement>(null);\r\n  const [contentWidth, setContentWidth] = useState(0);\r\n  const [isHovering, setIsHovering] = useState(false);\r\n  const x = useMotionValue(0); // Motion value to control the x position\r\n\r\n  const speed = (contentWidth / 20);\r\n\r\n  useEffect(() => {\r\n    const isLoggedIn = isStudentAuthenticated();\r\n    setIsStudentLoggedIn(isLoggedIn);\r\n\r\n    if (isLoggedIn) {\r\n      const storedData = localStorage.getItem('student_data');\r\n      if (storedData) {\r\n        setStudentData(JSON.parse(storedData));\r\n      }\r\n\r\n      dispatch(fetchStudentProfile());\r\n    }\r\n\r\n    const handleStorageChange = () => {\r\n      const newLoginStatus = isStudentAuthenticated();\r\n      setIsStudentLoggedIn(newLoginStatus);\r\n\r\n      if (newLoginStatus) {\r\n        const storedData = localStorage.getItem('student_data');\r\n        if (storedData) {\r\n          setStudentData(JSON.parse(storedData));\r\n        }\r\n\r\n        dispatch(fetchStudentProfile());\r\n      } else {\r\n        setStudentData(null);\r\n      }\r\n    };\r\n\r\n    window.addEventListener('storage', handleStorageChange);\r\n\r\n    if (contentRef.current) {\r\n      const width = contentRef.current.getBoundingClientRect().width;\r\n      setContentWidth(width);\r\n    }\r\n\r\n    return () => {\r\n      window.removeEventListener('storage', handleStorageChange);\r\n    };\r\n  }, [dispatch]);\r\n\r\n  // Custom animation loop using useAnimationFrame\r\n  useAnimationFrame((time, delta) => {\r\n    if (isHovering || contentWidth === 0) return;\r\n    const currentX = x.get();\r\n    const deltaX = (speed * delta) / 1000;\r\n    let newX = currentX - deltaX;\r\n    if (newX <= -contentWidth) {\r\n      newX = 0;\r\n    }\r\n    x.set(newX);\r\n  });\r\n\r\n  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);\r\n\r\n  const handleStudentLogout = async () => {\r\n    try {\r\n      const response = await logoutStudent();\r\n\r\n      if (response.success !== false) {\r\n        // Clear local state\r\n        clearStudentAuthToken();\r\n        setIsStudentLoggedIn(false);\r\n        setStudentData(null);\r\n        localStorage.removeItem('student_data');\r\n\r\n        // Clear Redux state\r\n        dispatch(clearStudentProfileData());\r\n\r\n        toast.success(\"Logged out successfully\");\r\n        window.dispatchEvent(new Event('storage'));\r\n      } else {\r\n        toast.error(response.message || \"Failed to logout\");\r\n        clearStudentAuthToken();\r\n        setIsStudentLoggedIn(false);\r\n        setStudentData(null);\r\n        localStorage.removeItem('student_data');\r\n        dispatch(clearStudentProfileData());\r\n      }\r\n    } catch (error) {\r\n      console.log(\"Failed to logout\", error);\r\n      toast.error(\"Failed to logout\");\r\n      localStorage.removeItem('student_data');\r\n      clearStudentAuthToken();\r\n      setIsStudentLoggedIn(false);\r\n      setStudentData(null);\r\n      dispatch(clearStudentProfileData());\r\n    }\r\n  };\r\n\r\n\r\n  const navLinks = [\r\n    { href: \"/verified-classes\", label: \"Find Tutor\", icon: <GraduationCap className=\"w-4 h-4\" /> },\r\n    { href: \"/uwhiz\", label: \"U - Whiz\", icon: <Flame className=\"w-4 h-4\" />, isNew: true },\r\n    { href: \"/careers\", label: \"Career\", icon: <Briefcase className=\"w-4 h-4\" />},\r\n  ];\r\n\r\n  const bannerContent = (\r\n    <div className=\"inline-flex items-center space-x-4 whitespace-nowrap\">\r\n      <span className=\"text-sm md:text-xl font-semibold text-black\">\r\n        U Whiz – Super Kids Exam is live! Win ₹1,00,000 – So hurry up, Apply now and be a champion\r\n      </span>\r\n      <button\r\n        className=\"inline-flex items-center justify-center rounded-md font-bold bg-white text-black px-3 py-1 text-sm hover:bg-[#FD904B] hover:text-black transition\"\r\n        style={{ border: '2px solid black' }}\r\n        onClick={() => router.push(`/uwhiz-info/${1}`)}\r\n      >\r\n        Apply Now <ChevronRight className=\"ml-2 h-4 w-4\" />\r\n      </button>\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <>\r\n      <header className=\"sticky top-0 z-50 w-full bg-black overflow-x-hidden\">\r\n        <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"flex h-20 items-center justify-between\">\r\n            <Link\r\n              href=\"/\"\r\n              className=\"flex items-center space-x-2 transition-transform hover:scale-105\"\r\n            >\r\n              <Image\r\n                src=\"/logo_black.png\"\r\n                alt=\"Preply Logo\"\r\n                width={200}\r\n                height={50}\r\n                className=\"rounded-sm\"\r\n              />\r\n            </Link>\r\n\r\n            <nav className=\"hidden md:flex items-center space-x-4\">\r\n              {navLinks.map((link) => (\r\n                <Link\r\n                  key={link.href}\r\n                  href={link.href}\r\n                  className=\"relative flex items-center gap-2 border border-gray-700 rounded-md px-4 py-2 text-sm font-medium text-gray-300 transition-all hover:border-orange-500 hover:text-orange-400\"\r\n                >\r\n                  {link.icon}\r\n                  {link.label}\r\n                  {link.label === \"Find School\" && (\r\n                    <span className=\"ml-2 text-xs px-2 py-0.5 rounded-full bg-yellow-500 text-black\">\r\n                      Coming Soon\r\n                    </span>\r\n                  )}\r\n                  {link.isNew && (\r\n                    <span className=\"ml-2 text-xs px-2 py-0.5 rounded-full bg-orange-500 text-white animate-pulse\">\r\n                      Trending\r\n                    </span>\r\n                  )}\r\n                </Link>\r\n              ))}\r\n            </nav>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"icon\"\r\n              className=\"md:hidden text-orange-400 hover:bg-orange-500/10\"\r\n              onClick={toggleMenu}\r\n            >\r\n              {isMenuOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\r\n            </Button>\r\n\r\n            {/* Desktop Actions */}\r\n            <div className=\"hidden md:flex items-center space-x-4\">\r\n              {isAuthenticated && (\r\n                <Link href=\"/coins\" passHref>\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    size=\"icon\"\r\n                    className=\"relative rounded-full group bg-black\"\r\n                  >\r\n                    <div className=\"absolute rounded-full inset-0 opacity-0 group-hover:opacity-20 transition-opacity\" />\r\n                    <div className=\"relative z-10 \">\r\n                      <Image\r\n                        src=\"/uest_coin.png\"\r\n                        alt=\"Coin Icon\"\r\n                        width={60}\r\n                        height={60}\r\n                        className=\"object-contain\"\r\n                      />\r\n                    </div>\r\n                  </Button>\r\n                </Link>\r\n              )}\r\n\r\n              <div className=\"h-8 border-l border-orange-500/20\" />\r\n\r\n              {isAuthenticated && (\r\n                <Popover>\r\n                  <PopoverTrigger asChild>\r\n                    <Avatar className=\"cursor-pointer border-2 border-[#ff914d] hover:border-[#ff914d]/80 transition-colors\">\r\n                      <AvatarFallback className=\"bg-white text-black\">\r\n                        {user?.firstName && user?.lastName\r\n                        ? `${user.firstName[0]}${user.lastName[0]}`.toUpperCase()\r\n                          : \"CT\"}\r\n                      </AvatarFallback>\r\n                    </Avatar>\r\n                  </PopoverTrigger>\r\n                  <PopoverContent className=\"w-64 bg-white\">\r\n                    <div className=\"flex items-center gap-3 mb-3 pb-2 border-b\">\r\n                      <Avatar className=\"h-12 w-12 border-2 border-[#ff914d]\">\r\n                        <AvatarFallback className=\"bg-white text-black\">\r\n                          {user?.firstName && user?.lastName\r\n                          ? `${user.firstName[0]}${user.lastName[0]}`.toUpperCase()\r\n                            : \"CT\"}\r\n                        </AvatarFallback>\r\n                      </Avatar>\r\n                      <div>\r\n                        <p className=\"font-medium text-black\">\r\n                          {user?.firstName && user?.lastName\r\n                            ? `${user.firstName} ${user.lastName}`\r\n                            : user?.className || \"Class Account\"}\r\n                        </p>\r\n                        <p className=\"text-xs text-gray-600\">{user?.email || \"<EMAIL>\"}</p>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"space-y-2\">\r\n                      <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                        <Link href=\"/classes/profile\" className=\"flex items-center\">\r\n                          <User className=\"mr-2 h-4 w-4\" />\r\n                          <span>My Dashboard</span>\r\n                        </Link>\r\n                      </Button>\r\n                      <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                        <Link href=\"/classes/referral-dashboard\" className=\"flex items-center\">\r\n                          <Share2 className=\"mr-2 h-4 w-4\" />\r\n                          <span>Referral Dashboard</span>\r\n                        </Link>\r\n                      </Button>\r\n\r\n                      <Button\r\n                        variant=\"outline\"\r\n                        className=\"w-full border-[#ff914d] text-[#ff914d] hover:bg-[#ff914d]/10\"\r\n                        onClick={async () => {\r\n                          try {\r\n                            const response = await axiosInstance.post(\"/auth-client/logout\", {});\r\n                            if (response.data.success) {\r\n                              router.push(\"/\");\r\n                              dispatch(clearUser());\r\n                              localStorage.removeItem(\"token\");\r\n                              toast.success(\"Logged out successfully\");\r\n                            }\r\n                          } catch (error) {\r\n                            console.error(\"Logout error:\", error);\r\n                            toast.error(\"Failed to logout\");\r\n                          }\r\n                        }}\r\n                      >\r\n                        Logout\r\n                      </Button>\r\n                    </div>\r\n                  </PopoverContent>\r\n                </Popover>\r\n              )}\r\n\r\n              {!isAuthenticated && !isStudentLoggedIn && (\r\n                <Button\r\n                  className=\"bg-customOrange hover:bg-[#E88143] text-white mr-4\"\r\n                  asChild\r\n                >\r\n                  <Link href=\"/class/login\">Join as a Tutor/Class</Link>\r\n                </Button>\r\n              )}\r\n\r\n              {!isAuthenticated && !isStudentLoggedIn && (\r\n                <Button\r\n                  variant=\"outline\"\r\n                  className=\"bg-black border-orange-500 hover:bg-orange-900/50 text-white hover:text-white\"\r\n                  asChild\r\n                >\r\n                  <Link href=\"/student/login\">Student Login</Link>\r\n                </Button>\r\n              )}\r\n\r\n              {isStudentLoggedIn && (\r\n                <Link href=\"/coins\" passHref>\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    size=\"icon\"\r\n                    className=\"relative rounded-full group bg-black\"\r\n                  >\r\n                    <div className=\"absolute rounded-full inset-0 opacity-0 group-hover:opacity-20 transition-opacity\" />\r\n                    <div className=\"relative z-10 \">\r\n                      <Image\r\n                        src=\"/uest_coin.png\"\r\n                        alt=\"Coin Icon\"\r\n                        width={60}\r\n                        height={60}\r\n                        className=\"object-contain\"\r\n                      />\r\n                    </div>\r\n                  </Button>\r\n                </Link>\r\n              )}\r\n\r\n              {isStudentLoggedIn && (\r\n                <Popover>\r\n                  <PopoverTrigger asChild>\r\n                    <Avatar className=\"cursor-pointer border-2 border-[#ff914d] hover:border-[#ff914d]/80 transition-colors\">\r\n                      <AvatarFallback className=\"bg-white text-black\">\r\n                        {studentData?.firstName && studentData?.lastName\r\n                        ? `${studentData.firstName[0]}${studentData.lastName[0]}`.toUpperCase()\r\n                          : \"ST\"}\r\n                      </AvatarFallback>\r\n                    </Avatar>\r\n                  </PopoverTrigger>\r\n                  <PopoverContent className=\"w-64 bg-white\">\r\n                    <div className=\"flex items-center gap-3 mb-3 pb-2 border-b\">\r\n                      <Avatar className=\"h-12 w-12 border-2 border-[#ff914d]\">\r\n                        <AvatarFallback className=\"bg-white text-black\">\r\n                          {studentData?.firstName && studentData?.lastName\r\n                          ? `${studentData.firstName[0]}${studentData.lastName[0]}`.toUpperCase()\r\n                            : \"ST\"}\r\n                        </AvatarFallback>\r\n                      </Avatar>\r\n                      <div>\r\n                        <p className=\"font-medium text-black\">\r\n                          {studentData?.firstName && studentData?.lastName\r\n                            ? `${studentData.firstName} ${studentData.lastName}`\r\n                            : \"Student Account\"}\r\n                        </p>\r\n                        <p className=\"text-xs text-gray-600\">{studentData?.email || \"<EMAIL>\"}</p>\r\n                      </div>\r\n                    </div>\r\n\r\n                  <div className=\"space-y-2\">\r\n                    <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                      <Link href=\"/student/profile\" className=\"flex items-center\">\r\n                        <UserCircle className=\"mr-2 h-4 w-4\" />\r\n                        <span>Profile</span>\r\n                      </Link>\r\n                    </Button>\r\n                    <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                      <Link href=\"/student/wishlist\" className=\"flex items-center\">\r\n                        <ShoppingBag className=\"mr-2 h-4 w-4\" />\r\n                        <span>My Wishlist</span>\r\n                      </Link>\r\n                    </Button>\r\n                    <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                      <Link href=\"/student/referral-dashboard\" className=\"flex items-center\">\r\n                        <Share2 className=\"mr-2 h-4 w-4\" />\r\n                        <span>Referral Dashboard</span>\r\n                      </Link>\r\n                    </Button>\r\n                    {/* Referral dashboard removed - only available for Classes */}\r\n\r\n                      <Button variant=\"outline\" className=\"w-full border-[#ff914d] text-[#ff914d] hover:bg-[#ff914d]/10\" onClick={handleStudentLogout}>\r\n                        Logout\r\n                      </Button>\r\n                    </div>\r\n                  </PopoverContent>\r\n                </Popover>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"w-screen bg-[#FD904B] border-y border-black relative mt-1\">\r\n            <div className=\"absolute top-0 right-0 h-full w-[20vw] bg-[#FD904B] block md:hidden z-0\"></div>\r\n            <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 relative z-10 overflow-hidden\">\r\n              <motion.div\r\n                className=\"inline-flex py-2 px-4\"\r\n                style={{ x }} // Bind the x position to the motion value\r\n                onMouseEnter={() => setIsHovering(true)}\r\n                onMouseLeave={() => setIsHovering(false)}\r\n              >\r\n                <div ref={contentRef} className=\"inline-flex items-center space-x-4 whitespace-nowrap pr-8\">\r\n                  {bannerContent}\r\n                </div>\r\n                <div className=\"inline-flex items-center space-x-4 whitespace-nowrap pr-8\">\r\n                  {bannerContent}\r\n                </div>\r\n              </motion.div>\r\n            </div>\r\n          </div>\r\n      </header>\r\n\r\n      <div>\r\n      {/* Mobile Sidebar Menu */}\r\n      <div\r\n        className={`fixed inset-y-0 right-0 z-50 w-72 bg-black/95 shadow-2xl transform transition-all duration-300 ease-in-out md:hidden border-l border-orange-500/20 ${isMenuOpen ? \"translate-x-0\" : \"translate-x-full\"\r\n          }`}\r\n      >\r\n        <div className=\"flex flex-col h-full p-6\">\r\n          {/* Close Button */}\r\n          <div className=\"flex justify-end\">\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"icon\"\r\n              className=\"text-orange-400 hover:bg-orange-500/10 rounded-full\"\r\n              onClick={toggleMenu}\r\n            >\r\n              <X className=\"h-6 w-6\" />\r\n            </Button>\r\n          </div>\r\n\r\n\r\n\r\n          {/* Mobile Navigation */}\r\n            <nav className=\"flex flex-col space-y-2 mt-8\">\r\n              {navLinks.map((link) => (\r\n                <Link\r\n                  key={link.href}\r\n                  href={link.href}\r\n                  className=\"px-4 py-3 border text-base font-medium text-gray-300 hover:text-orange-400 hover:bg-orange-500/10 rounded-lg transition-colors flex justify-between items-center\"\r\n                  onClick={toggleMenu}\r\n                >\r\n                  <div className=\"flex items-center gap-3\">\r\n                    {link.icon}\r\n                    <span>{link.label}</span>\r\n                  </div>\r\n                  {link.label === \"Find School\" && (\r\n                    <span className=\"ml-2 text-xs px-2 py-0.5 rounded-full bg-yellow-500 text-black animate-pulse\">\r\n                      Coming Soon\r\n                    </span>\r\n                  )}\r\n                  {link.isNew && (\r\n                    <span className=\"ml-2 text-xs px-2 py-0.5 rounded-full bg-orange-500 text-white\">\r\n                      New\r\n                    </span>\r\n                  )}\r\n                </Link>\r\n              ))}\r\n            </nav>\r\n\r\n          {/* Mobile Actions */}\r\n            <div className=\"mt-auto space-y-4\">\r\n              {isAuthenticated && (\r\n                <>\r\n                  <Link href=\"/classes/profile\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"w-full group relative border-orange-500 hover:border-orange-400 bg-black\"\r\n                      onClick={toggleMenu}\r\n                    >\r\n                      <div className=\"absolute inset-0 bg-gradient-to-r from-orange-500 to-yellow-500 opacity-0 group-hover:opacity-20 transition-opacity\" />\r\n                      <div className=\"relative z-10 flex items-center justify-center gap-3 py-2\">\r\n                        <div className=\"p-1.5 rounded-full bg-gradient-to-br from-orange-500 to-yellow-500\">\r\n                          <User className=\"h-5 w-5 text-white\" />\r\n                        </div>\r\n                        <span className=\"font-medium text-gray-300\">Dashboard</span>\r\n                      </div>\r\n                    </Button>\r\n                  </Link>\r\n\r\n                  <Link href=\"/classes/referral-dashboard\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"w-full group relative border-orange-500 hover:border-orange-400 mt-3 bg-black\"\r\n                      onClick={toggleMenu}\r\n                    >\r\n                      <div className=\"absolute inset-0 bg-gradient-to-r from-orange-500 to-yellow-500 opacity-0 group-hover:opacity-20 transition-opacity\" />\r\n                      <div className=\"relative z-10 flex items-center justify-center gap-3 py-2\">\r\n                        <div className=\"p-1.5 rounded-full bg-gradient-to-br from-orange-500 to-yellow-500\">\r\n                          <Share2 className=\"h-5 w-5 text-white\" />\r\n                        </div>\r\n                        <span className=\"font-medium text-gray-300\">Referral Dashboard</span>\r\n                      </div>\r\n                    </Button>\r\n                  </Link>\r\n\r\n                <Link href=\"/coins\" passHref>\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    className=\"w-full group relative border-orange-500 hover:border-orange-400 bg-black mt-3\"\r\n                    onClick={toggleMenu}\r\n                  >\r\n                    <div className=\"absolute inset-0 bg-gradient-to-r from-orange-500 to-yellow-500 opacity-0 group-hover:opacity-20 transition-opacity\" />\r\n                    <div className=\"relative z-10 flex items-center justify-center gap-3 py-2\">\r\n                      <div className=\"p-1.5 rounded-full bg-gradient-to-br from-orange-500 to-yellow-500\">\r\n                        <Image\r\n                          src=\"/uest_coin.png\"\r\n                          alt=\"Coin Icon\"\r\n                          width={20}\r\n                          height={20}\r\n                          className=\"object-contain\"\r\n                        />\r\n                      </div>\r\n                      <span className=\"font-medium text-gray-300\">My Coins</span>\r\n                    </div>\r\n                  </Button>\r\n                </Link>\r\n\r\n                <Button\r\n                  variant=\"outline\"\r\n                  className=\"w-full border-orange-500 text-orange-500 hover:bg-orange-500/10 mt-3\"\r\n                  onClick={async () => {\r\n                    try {\r\n                      const response = await axiosInstance.post(\"/auth-client/logout\", {});\r\n                      if (response.data.success) {\r\n                        router.push(\"/\");\r\n                        dispatch(clearUser());\r\n                        localStorage.removeItem(\"token\");\r\n                        toast.success(\"Logged out successfully\");\r\n                      }\r\n                    } catch (error) {\r\n                      console.error(\"Logout error:\", error);\r\n                      toast.error(\"Failed to logout\");\r\n                    }\r\n                    toggleMenu();\r\n                  }}\r\n                >\r\n                  <div className=\"flex items-center justify-center gap-3\">\r\n                    <User className=\"h-5 w-5\" />\r\n                    <span>Logout</span>\r\n                  </div>\r\n                </Button>\r\n              </>\r\n            )}\r\n\r\n              {isStudentLoggedIn && (\r\n                <>\r\n                  {studentData?.firstName && studentData?.lastName && (\r\n                    <div className=\"p-3 border border-[#ff914d]/20 rounded-lg bg-white\">\r\n                      <div className=\"flex items-center gap-3\">\r\n                        <Avatar className=\"h-12 w-12 border-2 border-[#ff914d]\">\r\n                          <AvatarFallback className=\"bg-white text-black\">\r\n                            {(`${studentData.firstName[0]}${studentData.lastName[0]}`).toUpperCase()}\r\n                          </AvatarFallback>\r\n                        </Avatar>\r\n                        <div>\r\n                          <p className=\"font-medium text-black\">{`${studentData.firstName} ${studentData.lastName}`}</p>\r\n                          <p className=\"text-xs text-gray-600\">{studentData.email}</p>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                <Button\r\n                  asChild\r\n                  className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\"\r\n                  onClick={toggleMenu}\r\n                >\r\n                  <Link href=\"/student/profile\" className=\"flex items-center justify-center gap-3\">\r\n                    <UserCircle className=\"h-5 w-5\" />\r\n                    <span>Profile</span>\r\n                  </Link>\r\n                </Button>\r\n\r\n                <Button\r\n                  asChild\r\n                  className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\"\r\n                  onClick={toggleMenu}\r\n                >\r\n                  <Link href=\"/student/wishlist\" className=\"flex items-center justify-center gap-3\">\r\n                    <ShoppingBag className=\"h-5 w-5\" />\r\n                    <span>My Wishlist</span>\r\n                  </Link>\r\n                </Button>\r\n\r\n                <Button\r\n                  asChild\r\n                  className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\"\r\n                  onClick={toggleMenu}\r\n                >\r\n                  <Link href=\"/student/referral-dashboard\" className=\"flex items-center justify-center gap-3\">\r\n                    <Share2 className=\"h-5 w-5\" />\r\n                    <span>Referral Dashboard</span>\r\n                  </Link>\r\n                </Button>\r\n\r\n                  <Link href=\"/coins\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"w-full group relative border-orange-500 hover:border-orange-400 bg-black mb-3\"\r\n                      onClick={toggleMenu}\r\n                    >\r\n                      <div className=\"absolute inset-0 bg-gradient-to-r from-orange-500 to-yellow-500 opacity-0 group-hover:opacity-20 transition-opacity\" />\r\n                      <div className=\"relative z-10 flex items-center justify-center gap-3\">\r\n                        <div className=\"p-1.5 rounded-full bg-gradient-to-br from-orange-500 to-yellow-500\">\r\n                          <Image\r\n                            src=\"/uest_coin.png\"\r\n                            alt=\"Coin Icon\"\r\n                            width={20}\r\n                            height={20}\r\n                            className=\"object-contain\"\r\n                          />\r\n                        </div>\r\n                        <span className=\"font-medium text-gray-300\">My Coins</span>\r\n                      </div>\r\n                    </Button>\r\n                  </Link>\r\n\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    className=\"w-full border-[#ff914d] text-[#ff914d] hover:bg-[#ff914d]/10\"\r\n                    onClick={() => {\r\n                      handleStudentLogout();\r\n                      toggleMenu();\r\n                    }}\r\n                  >\r\n                    <div className=\"flex items-center justify-center gap-3\">\r\n                      <User className=\"h-5 w-5\" />\r\n                      <span>Logout</span>\r\n                    </div>\r\n                  </Button>\r\n                </>\r\n              )}\r\n\r\n              {!isAuthenticated && !isStudentLoggedIn && (\r\n                <div className=\"space-y-3 pt-3\">\r\n                  <Button\r\n                    variant=\"default\"\r\n                    className=\"w-full bg-orange-500 hover:bg-orange-600\"\r\n                    asChild\r\n                  >\r\n                    <Link href=\"/class/login\" onClick={toggleMenu}>\r\n                      Tutor/Classes Login\r\n                    </Link>\r\n                  </Button>\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    className=\"w-full border-customOrange text-orange-500 hover:bg-orange\"\r\n                    asChild\r\n                  >\r\n                    <Link href=\"/student/login\" onClick={toggleMenu}>\r\n                      Student Login\r\n                    </Link>\r\n                  </Button>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {isStudentLoggedIn && (\r\n          <div className=\"mt-4 mx-10 sm:px-4\">\r\n            <ProfileCompletionIndicator />\r\n          </div>\r\n        )}\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Header;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AAIA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AA7BA;;;;;;;;;;;;;;;;;;;;;;AA+BA,MAAM,SAAS;IACb,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAqB,MAAM,IAAI;IAC9E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACpD,MAAM,WAAW,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAC9B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,IAAI,CAAA,GAAA,kLAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,yCAAyC;IAEtE,MAAM,QAAS,eAAe;IAE9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa,CAAA,GAAA,mHAAA,CAAA,yBAAsB,AAAD;QACxC,qBAAqB;QAErB,IAAI,YAAY;YACd,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,IAAI,YAAY;gBACd,eAAe,KAAK,KAAK,CAAC;YAC5B;YAEA,SAAS,CAAA,GAAA,8IAAA,CAAA,sBAAmB,AAAD;QAC7B;QAEA,MAAM,sBAAsB;YAC1B,MAAM,iBAAiB,CAAA,GAAA,mHAAA,CAAA,yBAAsB,AAAD;YAC5C,qBAAqB;YAErB,IAAI,gBAAgB;gBAClB,MAAM,aAAa,aAAa,OAAO,CAAC;gBACxC,IAAI,YAAY;oBACd,eAAe,KAAK,KAAK,CAAC;gBAC5B;gBAEA,SAAS,CAAA,GAAA,8IAAA,CAAA,sBAAmB,AAAD;YAC7B,OAAO;gBACL,eAAe;YACjB;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QAEnC,IAAI,WAAW,OAAO,EAAE;YACtB,MAAM,QAAQ,WAAW,OAAO,CAAC,qBAAqB,GAAG,KAAK;YAC9D,gBAAgB;QAClB;QAEA,OAAO;YACL,OAAO,mBAAmB,CAAC,WAAW;QACxC;IACF,GAAG;QAAC;KAAS;IAEb,gDAAgD;IAChD,CAAA,GAAA,qLAAA,CAAA,oBAAiB,AAAD,EAAE,CAAC,MAAM;QACvB,IAAI,cAAc,iBAAiB,GAAG;QACtC,MAAM,WAAW,EAAE,GAAG;QACtB,MAAM,SAAS,AAAC,QAAQ,QAAS;QACjC,IAAI,OAAO,WAAW;QACtB,IAAI,QAAQ,CAAC,cAAc;YACzB,OAAO;QACT;QACA,EAAE,GAAG,CAAC;IACR;IAEA,MAAM,aAAa,IAAM,cAAc,CAAC;IAExC,MAAM,sBAAsB;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,sIAAA,CAAA,gBAAa,AAAD;YAEnC,IAAI,SAAS,OAAO,KAAK,OAAO;gBAC9B,oBAAoB;gBACpB,CAAA,GAAA,mHAAA,CAAA,wBAAqB,AAAD;gBACpB,qBAAqB;gBACrB,eAAe;gBACf,aAAa,UAAU,CAAC;gBAExB,oBAAoB;gBACpB,SAAS,CAAA,GAAA,6IAAA,CAAA,0BAAuB,AAAD;gBAE/B,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,aAAa,CAAC,IAAI,MAAM;YACjC,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,SAAS,OAAO,IAAI;gBAChC,CAAA,GAAA,mHAAA,CAAA,wBAAqB,AAAD;gBACpB,qBAAqB;gBACrB,eAAe;gBACf,aAAa,UAAU,CAAC;gBACxB,SAAS,CAAA,GAAA,6IAAA,CAAA,0BAAuB,AAAD;YACjC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,oBAAoB;YAChC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,aAAa,UAAU,CAAC;YACxB,CAAA,GAAA,mHAAA,CAAA,wBAAqB,AAAD;YACpB,qBAAqB;YACrB,eAAe;YACf,SAAS,CAAA,GAAA,6IAAA,CAAA,0BAAuB,AAAD;QACjC;IACF;IAGA,MAAM,WAAW;QACf;YAAE,MAAM;YAAqB,OAAO;YAAc,oBAAM,8OAAC,wNAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;QAAa;QAC9F;YAAE,MAAM;YAAU,OAAO;YAAY,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YAAc,OAAO;QAAK;QACtF;YAAE,MAAM;YAAY,OAAO;YAAU,oBAAM,8OAAC,4MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;QAAY;KAC7E;IAED,MAAM,8BACJ,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAK,WAAU;0BAA8C;;;;;;0BAG9D,8OAAC;gBACC,WAAU;gBACV,OAAO;oBAAE,QAAQ;gBAAkB;gBACnC,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,YAAY,EAAE,GAAG;;oBAC9C;kCACW,8OAAC,sNAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;;;;;;;;;;;;;IAKxC,qBACE;;0BACE,8OAAC;gBAAO,WAAU;;kCAChB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CAEV,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;8CAId,8OAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAU;;gDAET,KAAK,IAAI;gDACT,KAAK,KAAK;gDACV,KAAK,KAAK,KAAK,+BACd,8OAAC;oDAAK,WAAU;8DAAiE;;;;;;gDAIlF,KAAK,KAAK,kBACT,8OAAC;oDAAK,WAAU;8DAA+E;;;;;;;2CAZ5F,KAAK,IAAI;;;;;;;;;;8CAmBpB,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;8CAER,2BAAa,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;6DAAe,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAI5D,8OAAC;oCAAI,WAAU;;wCACZ,iCACC,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,QAAQ;sDAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;;kEAEV,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4DACJ,KAAI;4DACJ,KAAI;4DACJ,OAAO;4DACP,QAAQ;4DACR,WAAU;;;;;;;;;;;;;;;;;;;;;;sDAOpB,8OAAC;4CAAI,WAAU;;;;;;wCAEd,iCACC,8OAAC,mIAAA,CAAA,UAAO;;8DACN,8OAAC,mIAAA,CAAA,iBAAc;oDAAC,OAAO;8DACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDAAC,WAAU;kEAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;4DAAC,WAAU;sEACvB,MAAM,aAAa,MAAM,WACxB,GAAG,KAAK,SAAS,CAAC,EAAE,GAAG,KAAK,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACnD;;;;;;;;;;;;;;;;8DAIV,8OAAC,mIAAA,CAAA,iBAAc;oDAAC,WAAU;;sEACxB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kIAAA,CAAA,SAAM;oEAAC,WAAU;8EAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;wEAAC,WAAU;kFACvB,MAAM,aAAa,MAAM,WACxB,GAAG,KAAK,SAAS,CAAC,EAAE,GAAG,KAAK,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACnD;;;;;;;;;;;8EAGR,8OAAC;;sFACC,8OAAC;4EAAE,WAAU;sFACV,MAAM,aAAa,MAAM,WACtB,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE,GACpC,MAAM,aAAa;;;;;;sFAEzB,8OAAC;4EAAE,WAAU;sFAAyB,MAAM,SAAS;;;;;;;;;;;;;;;;;;sEAIzD,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kIAAA,CAAA,SAAM;oEAAC,OAAO;oEAAC,WAAU;8EACxB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEAAC,MAAK;wEAAmB,WAAU;;0FACtC,8OAAC,kMAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;0FAChB,8OAAC;0FAAK;;;;;;;;;;;;;;;;;8EAGV,8OAAC,kIAAA,CAAA,SAAM;oEAAC,OAAO;oEAAC,WAAU;8EACxB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEAAC,MAAK;wEAA8B,WAAU;;0FACjD,8OAAC,0MAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;0FAClB,8OAAC;0FAAK;;;;;;;;;;;;;;;;;8EAIV,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,WAAU;oEACV,SAAS;wEACP,IAAI;4EACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,uBAAuB,CAAC;4EAClE,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gFACzB,OAAO,IAAI,CAAC;gFACZ,SAAS,CAAA,GAAA,mIAAA,CAAA,YAAS,AAAD;gFACjB,aAAa,UAAU,CAAC;gFACxB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;4EAChB;wEACF,EAAE,OAAO,OAAO;4EACd,QAAQ,KAAK,CAAC,iBAAiB;4EAC/B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wEACd;oEACF;8EACD;;;;;;;;;;;;;;;;;;;;;;;;wCAQR,CAAC,mBAAmB,CAAC,mCACpB,8OAAC,kIAAA,CAAA,SAAM;4CACL,WAAU;4CACV,OAAO;sDAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;0DAAe;;;;;;;;;;;wCAI7B,CAAC,mBAAmB,CAAC,mCACpB,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;4CACV,OAAO;sDAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;0DAAiB;;;;;;;;;;;wCAI/B,mCACC,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,QAAQ;sDAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;;kEAEV,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4DACJ,KAAI;4DACJ,KAAI;4DACJ,OAAO;4DACP,QAAQ;4DACR,WAAU;;;;;;;;;;;;;;;;;;;;;;wCAOnB,mCACC,8OAAC,mIAAA,CAAA,UAAO;;8DACN,8OAAC,mIAAA,CAAA,iBAAc;oDAAC,OAAO;8DACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDAAC,WAAU;kEAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;4DAAC,WAAU;sEACvB,aAAa,aAAa,aAAa,WACtC,GAAG,YAAY,SAAS,CAAC,EAAE,GAAG,YAAY,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACjE;;;;;;;;;;;;;;;;8DAIV,8OAAC,mIAAA,CAAA,iBAAc;oDAAC,WAAU;;sEACxB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kIAAA,CAAA,SAAM;oEAAC,WAAU;8EAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;wEAAC,WAAU;kFACvB,aAAa,aAAa,aAAa,WACtC,GAAG,YAAY,SAAS,CAAC,EAAE,GAAG,YAAY,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACjE;;;;;;;;;;;8EAGR,8OAAC;;sFACC,8OAAC;4EAAE,WAAU;sFACV,aAAa,aAAa,aAAa,WACpC,GAAG,YAAY,SAAS,CAAC,CAAC,EAAE,YAAY,QAAQ,EAAE,GAClD;;;;;;sFAEN,8OAAC;4EAAE,WAAU;sFAAyB,aAAa,SAAS;;;;;;;;;;;;;;;;;;sEAIlE,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kIAAA,CAAA,SAAM;oEAAC,OAAO;oEAAC,WAAU;8EACxB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEAAC,MAAK;wEAAmB,WAAU;;0FACtC,8OAAC,kNAAA,CAAA,aAAU;gFAAC,WAAU;;;;;;0FACtB,8OAAC;0FAAK;;;;;;;;;;;;;;;;;8EAGV,8OAAC,kIAAA,CAAA,SAAM;oEAAC,OAAO;oEAAC,WAAU;8EACxB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEAAC,MAAK;wEAAoB,WAAU;;0FACvC,8OAAC,oNAAA,CAAA,cAAW;gFAAC,WAAU;;;;;;0FACvB,8OAAC;0FAAK;;;;;;;;;;;;;;;;;8EAGV,8OAAC,kIAAA,CAAA,SAAM;oEAAC,OAAO;oEAAC,WAAU;8EACxB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEAAC,MAAK;wEAA8B,WAAU;;0FACjD,8OAAC,0MAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;0FAClB,8OAAC;0FAAK;;;;;;;;;;;;;;;;;8EAKR,8OAAC,kIAAA,CAAA,SAAM;oEAAC,SAAQ;oEAAU,WAAU;oEAA+D,SAAS;8EAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAU/I,8OAAC;wBAAI,WAAU;;0CACX,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,OAAO;wCAAE;oCAAE;oCACX,cAAc,IAAM,cAAc;oCAClC,cAAc,IAAM,cAAc;;sDAElC,8OAAC;4CAAI,KAAK;4CAAY,WAAU;sDAC7B;;;;;;sDAEH,8OAAC;4CAAI,WAAU;sDACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOb,8OAAC;;kCAED,8OAAC;wBACC,WAAW,CAAC,mJAAmJ,EAAE,aAAa,kBAAkB,oBAC5L;kCAEJ,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS;kDAET,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAOf,8OAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAU;4CACV,SAAS;;8DAET,8OAAC;oDAAI,WAAU;;wDACZ,KAAK,IAAI;sEACV,8OAAC;sEAAM,KAAK,KAAK;;;;;;;;;;;;gDAElB,KAAK,KAAK,KAAK,+BACd,8OAAC;oDAAK,WAAU;8DAA+E;;;;;;gDAIhG,KAAK,KAAK,kBACT,8OAAC;oDAAK,WAAU;8DAAiE;;;;;;;2CAf9E,KAAK,IAAI;;;;;;;;;;8CAwBpB,8OAAC;oCAAI,WAAU;;wCACZ,iCACC;;8DACE,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAmB,QAAQ;8DACpC,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;;0EAET,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;;;;;;kFAElB,8OAAC;wEAAK,WAAU;kFAA4B;;;;;;;;;;;;;;;;;;;;;;;8DAKlD,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAA8B,QAAQ;8DAC/C,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;;0EAET,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,0MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;;;;;;kFAEpB,8OAAC;wEAAK,WAAU;kFAA4B;;;;;;;;;;;;;;;;;;;;;;;8DAKpD,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,QAAQ;8DAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;;0EAET,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4EACJ,KAAI;4EACJ,KAAI;4EACJ,OAAO;4EACP,QAAQ;4EACR,WAAU;;;;;;;;;;;kFAGd,8OAAC;wEAAK,WAAU;kFAA4B;;;;;;;;;;;;;;;;;;;;;;;8DAKlD,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,SAAS;wDACP,IAAI;4DACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,uBAAuB,CAAC;4DAClE,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gEACzB,OAAO,IAAI,CAAC;gEACZ,SAAS,CAAA,GAAA,mIAAA,CAAA,YAAS,AAAD;gEACjB,aAAa,UAAU,CAAC;gEACxB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;4DAChB;wDACF,EAAE,OAAO,OAAO;4DACd,QAAQ,KAAK,CAAC,iBAAiB;4DAC/B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wDACd;wDACA;oDACF;8DAEA,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;;wCAMX,mCACC;;gDACG,aAAa,aAAa,aAAa,0BACtC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kIAAA,CAAA,SAAM;gEAAC,WAAU;0EAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;oEAAC,WAAU;8EACvB,AAAC,GAAG,YAAY,SAAS,CAAC,EAAE,GAAG,YAAY,QAAQ,CAAC,EAAE,EAAE,CAAE,WAAW;;;;;;;;;;;0EAG1E,8OAAC;;kFACC,8OAAC;wEAAE,WAAU;kFAA0B,GAAG,YAAY,SAAS,CAAC,CAAC,EAAE,YAAY,QAAQ,EAAE;;;;;;kFACzF,8OAAC;wEAAE,WAAU;kFAAyB,YAAY,KAAK;;;;;;;;;;;;;;;;;;;;;;;8DAMjE,8OAAC,kIAAA,CAAA,SAAM;oDACL,OAAO;oDACP,WAAU;oDACV,SAAS;8DAET,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAmB,WAAU;;0EACtC,8OAAC,kNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;0EACtB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;8DAIV,8OAAC,kIAAA,CAAA,SAAM;oDACL,OAAO;oDACP,WAAU;oDACV,SAAS;8DAET,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAoB,WAAU;;0EACvC,8OAAC,oNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;8DAIV,8OAAC,kIAAA,CAAA,SAAM;oDACL,OAAO;oDACP,WAAU;oDACV,SAAS;8DAET,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAA8B,WAAU;;0EACjD,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;8DAIR,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,QAAQ;8DAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;;0EAET,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4EACJ,KAAI;4EACJ,KAAI;4EACJ,OAAO;4EACP,QAAQ;4EACR,WAAU;;;;;;;;;;;kFAGd,8OAAC;wEAAK,WAAU;kFAA4B;;;;;;;;;;;;;;;;;;;;;;;8DAKlD,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,SAAS;wDACP;wDACA;oDACF;8DAEA,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;;wCAMb,CAAC,mBAAmB,CAAC,mCACpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,OAAO;8DAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAe,SAAS;kEAAY;;;;;;;;;;;8DAIjD,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,OAAO;8DAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAiB,SAAS;kEAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAU5D,mCACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,uJAAA,CAAA,UAA0B;;;;;;;;;;;;;;;;;;AAMvC;uCAEe", "debugId": null}}, {"offset": {"line": 1985, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/Footer.tsx"], "sourcesContent": ["'use client';\r\nimport React from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport {\r\n  FaFacebookF,\r\n  FaTwitter,\r\n  FaInstagram,\r\n  FaLinkedinIn,\r\n  FaTumblr,\r\n  FaPinterestP,\r\n  FaFilePdf,\r\n  FaEnvelope,\r\n} from 'react-icons/fa';\r\n\r\nconst Footer = () => {\r\n  return (\r\n    <footer className=\"bg-black text-gray-300 px-6 py-16\">\r\n      <div className=\"container mx-auto max-w-7xl space-y-16\">\r\n        <div className=\"flex flex-col md:flex-row items-center justify-between gap-6\">\r\n          <Link href=\"/\" className=\"flex items-center gap-2\">\r\n            <Image\r\n              src=\"/logo_black.png\"\r\n              alt=\"Logo\"\r\n              width={200}\r\n              height={40}\r\n              className=\"object-contain\"\r\n            />\r\n          </Link>\r\n\r\n          <div className=\"flex flex-wrap justify-center gap-1\">\r\n            {[\r\n              { href: 'mailto:<EMAIL>', icon: FaEnvelope, label: 'Email Us' },\r\n              {\r\n                href: 'https://www.uest.in/wp-content/uploads/2025/03/Welcome-A-UEST-Product-1.pdf',\r\n                icon: FaFilePdf,\r\n                label: 'Brochure',\r\n              },\r\n              {\r\n                href: 'https://x.com/uest189161?t=hLD2wWnt_Zf5b5rTnkSl2Q&s=09',\r\n                icon: FaTwitter,\r\n                label: 'Twitter',\r\n              },\r\n              {\r\n                href: 'https://www.facebook.com/share/1FNYcyqawH/',\r\n                icon: FaFacebookF,\r\n                label: 'Facebook',\r\n              },\r\n              {\r\n                href: 'https://www.instagram.com/uest_edtech?igsh=MWljYWt5YnQyeW9kdg==',\r\n                icon: FaInstagram,\r\n                label: 'Instagram',\r\n              },\r\n              {\r\n                href: 'https://www.linkedin.com/company/uest-edtech/',\r\n                icon: FaLinkedinIn,\r\n                label: 'LinkedIn',\r\n              },\r\n              { href: 'https://pin.it/1Di0EFtAa', icon: FaPinterestP, label: 'Pinterest' },\r\n              {\r\n                href: 'https://www.tumblr.com/uestedtech?source=share',\r\n                icon: FaTumblr,\r\n                label: 'Tumblr',\r\n              },\r\n            ].map(({ href, icon: Icon, label }) => (\r\n              <div key={label} className=\"flex flex-col items-center\">\r\n                <Link\r\n                  href={href}\r\n                  className=\"flex items-center justify-center w-12 h-12 hover:border-gray-400 transition\"\r\n                  title={label}\r\n                >\r\n                  <Icon className=\"text-xl text-white hover:text-gray-400 transition\" />\r\n                </Link>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10\">\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">About</h3>\r\n            <ul className=\"space-y-2 text-sm\">\r\n              <li>\r\n                <Link href=\"/verified-classes\" className=\"hover:text-white transition\">\r\n                  Find Tutors\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/support\" className=\"hover:text-white transition\">\r\n                  Support\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"#\" className=\"hover:text-white transition\">\r\n                  Pricing\r\n                </Link>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">For Students</h3>\r\n            <ul className=\"space-y-2 text-sm\">\r\n              <li>\r\n                <Link href=\"/verified-classes\" className=\"hover:text-white transition\">\r\n                  Find Online Tutor\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/support\" className=\"hover:text-white transition\">\r\n                  FAQs\r\n                </Link>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">Contact</h3>\r\n            <address className=\"not-italic text-sm space-y-1 leading-relaxed\">\r\n              <p>Head Office</p>\r\n              <p>86, 87 Capital Market, Morbi - 363641</p>\r\n              <p>Contact: +91 96 877 877 88</p>\r\n              <p>Email: <EMAIL></p>\r\n            </address>\r\n          </div>\r\n\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">Apps</h3>\r\n           <Link href=\"https://play.google.com/store/apps/details?id=com.uest\" target=\"_blank\">\r\n            <Image\r\n              src=\"/playstore.png\"\r\n              alt=\"Google Play Store\"\r\n              width={180}\r\n              height={50}\r\n              className=\"object-contain\"\r\n            />\r\n          </Link>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Bottom Bar */}\r\n        <div className=\"border-t border-gray-800 pt-6 text-sm flex flex-col md:flex-row justify-between items-center gap-4\">\r\n          <p>© 2025 uest.in. All rights reserved.</p>\r\n          <div className=\"flex gap-4\">\r\n            <Link href=\"/terms-and-conditions\" className=\"hover:text-white transition\">\r\n              Terms & Conditions\r\n            </Link>\r\n            <Link href=\"/privacy-policy\" className=\"hover:text-white transition\">\r\n              Privacy Policy\r\n            </Link>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n};\r\n\r\nexport default Footer;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAeA,MAAM,SAAS;IACb,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCACvB,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;;;;;;sCAId,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,MAAM;oCAA8B,MAAM,8IAAA,CAAA,aAAU;oCAAE,OAAO;gCAAW;gCAC1E;oCACE,MAAM;oCACN,MAAM,8IAAA,CAAA,YAAS;oCACf,OAAO;gCACT;gCACA;oCACE,MAAM;oCACN,MAAM,8IAAA,CAAA,YAAS;oCACf,OAAO;gCACT;gCACA;oCACE,MAAM;oCACN,MAAM,8IAAA,CAAA,cAAW;oCACjB,OAAO;gCACT;gCACA;oCACE,MAAM;oCACN,MAAM,8IAAA,CAAA,cAAW;oCACjB,OAAO;gCACT;gCACA;oCACE,MAAM;oCACN,MAAM,8IAAA,CAAA,eAAY;oCAClB,OAAO;gCACT;gCACA;oCAAE,MAAM;oCAA4B,MAAM,8IAAA,CAAA,eAAY;oCAAE,OAAO;gCAAY;gCAC3E;oCACE,MAAM;oCACN,MAAM,8IAAA,CAAA,WAAQ;oCACd,OAAO;gCACT;6BACD,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,iBAChC,8OAAC;oCAAgB,WAAU;8CACzB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM;wCACN,WAAU;wCACV,OAAO;kDAEP,cAAA,8OAAC;4CAAK,WAAU;;;;;;;;;;;mCANV;;;;;;;;;;;;;;;;8BAahB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAoB,WAAU;0DAA8B;;;;;;;;;;;sDAIzE,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAA8B;;;;;;;;;;;sDAIhE,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;0DAA8B;;;;;;;;;;;;;;;;;;;;;;;sCAO7D,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAoB,WAAU;0DAA8B;;;;;;;;;;;sDAIzE,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAA8B;;;;;;;;;;;;;;;;;;;;;;;sCAOpE,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAQ,WAAU;;sDACjB,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;sCAIP,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACvD,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAyD,QAAO;8CAC1E,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAOhB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;sCAAE;;;;;;sCACH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAwB,WAAU;8CAA8B;;;;;;8CAG3E,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAkB,WAAU;8CAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjF;uCAEe", "debugId": null}}, {"offset": {"line": 2386, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from '@/lib/utils';\r\n\r\nfunction Skeleton({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"skeleton\"\r\n      className={cn('bg-accent animate-pulse rounded-md', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Skeleton };\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2411, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app/verified-classes/FilterInput.tsx"], "sourcesContent": ["import React from 'react';\r\n\r\nexport const FilterInput = ({\r\n  label,\r\n  value,\r\n  onChange,\r\n}: {\r\n  label: string;\r\n  value: string;\r\n  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;\r\n}) => {\r\n  const inputRef = React.useRef<HTMLInputElement>(null);\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const newValue = e.target.value.replace(/\\s+/g, ' ');\r\n    e.target.value = newValue;\r\n    onChange(e);\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex flex-col\">\r\n      <label className=\"text-sm font-medium text-muted-foreground mb-1\">{label}</label>\r\n      <input\r\n        ref={inputRef}\r\n        type=\"text\"\r\n        placeholder={`Enter ${label}`}\r\n        className=\"border bg-white dark:bg-black rounded-lg px-3 py-2 text-sm text-black dark:text-white\r\n                 focus:border-customOrange focus:ring focus:ring-customOrange/20 focus:outline-none\r\n                 transition-all duration-200\"\r\n        value={value}\r\n        onChange={handleChange}\r\n        maxLength={50}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEO,MAAM,cAAc,CAAC,EAC1B,KAAK,EACL,KAAK,EACL,QAAQ,EAKT;IACC,MAAM,WAAW,qMAAA,CAAA,UAAK,CAAC,MAAM,CAAmB;IAEhD,MAAM,eAAe,CAAC;QACpB,MAAM,WAAW,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ;QAChD,EAAE,MAAM,CAAC,KAAK,GAAG;QACjB,SAAS;IACX;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAM,WAAU;0BAAkD;;;;;;0BACnE,8OAAC;gBACC,KAAK;gBACL,MAAK;gBACL,aAAa,CAAC,MAAM,EAAE,OAAO;gBAC7B,WAAU;gBAGV,OAAO;gBACP,UAAU;gBACV,WAAW;;;;;;;;;;;;AAInB", "debugId": null}}, {"offset": {"line": 2462, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/lib/helper.ts"], "sourcesContent": ["import { FormId, completeForm } from '@/store/slices/formProgressSlice';\r\nimport { AppDispatch } from '@/store';\r\n/* eslint-disable */\r\nexport const evaluateCompletedForms = (classData: any, dispatch: AppDispatch) => {\r\n  if (classData.contactNo) {\r\n    dispatch(completeForm(FormId.PROFILE));\r\n  }\r\n\r\n  if (classData.ClassAbout?.tutorBio?.length > 50) {\r\n    dispatch(completeForm(FormId.DESCRIPTION));\r\n  }\r\n\r\n  if (classData.ClassAbout?.profilePhoto && classData.ClassAbout?.classesLogo) {\r\n    dispatch(completeForm(FormId.PHOTO_LOGO));\r\n  }\r\n\r\n  if (classData.education?.length > 0) {\r\n    dispatch(completeForm(FormId.EDUCATION));\r\n  }\r\n\r\n  if (classData.certificates?.length > 0) {\r\n    dispatch(completeForm(FormId.CERTIFICATES));\r\n  }\r\n\r\n  if (classData.experience?.length > 0) {\r\n    dispatch(completeForm(FormId.EXPERIENCE));\r\n  }\r\n\r\n  if (classData.tuitionClasses?.length > 0) {\r\n    dispatch(completeForm(FormId.TUTIONCLASS));\r\n  }\r\n};\r\n\r\nexport function convertTo24HourFormat(time12h: string): string {\r\n  if (!time12h) return '';\r\n  const [time, modifier] = time12h.split(' ');\r\n\r\n  let [hours, minutes] = time.split(':');\r\n\r\n  if (hours === '12') hours = '00';\r\n  if (modifier === 'PM') hours = String(parseInt(hours) + 12);\r\n\r\n  return `${hours.padStart(2, '0')}:${minutes}`;\r\n}\r\n\r\nexport const safeParseArray = (value: any): string[] => {\r\n  if (!value) return [];\r\n  try {\r\n    const parsed = typeof value === 'string' ? JSON.parse(value) : value;\r\n    return Array.isArray(parsed) ? parsed : [parsed];\r\n  } catch {\r\n    return [value]; // fallback to treating as plain string\r\n  }\r\n};\r\n\r\nexport const parseAndJoinArray = (value: any): string => {\r\n  try {\r\n    const parsed = typeof value === 'string' ? JSON.parse(value) : value;\r\n    return Array.isArray(parsed) ? parsed.join(', ') : parsed || 'N/A';\r\n  } catch {\r\n    return value || 'N/A';\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;;AAAA;;AAGO,MAAM,yBAAyB,CAAC,WAAgB;IACrD,IAAI,UAAU,SAAS,EAAE;QACvB,SAAS,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD,EAAE,2IAAA,CAAA,SAAM,CAAC,OAAO;IACtC;IAEA,IAAI,UAAU,UAAU,EAAE,UAAU,SAAS,IAAI;QAC/C,SAAS,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD,EAAE,2IAAA,CAAA,SAAM,CAAC,WAAW;IAC1C;IAEA,IAAI,UAAU,UAAU,EAAE,gBAAgB,UAAU,UAAU,EAAE,aAAa;QAC3E,SAAS,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD,EAAE,2IAAA,CAAA,SAAM,CAAC,UAAU;IACzC;IAEA,IAAI,UAAU,SAAS,EAAE,SAAS,GAAG;QACnC,SAAS,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD,EAAE,2IAAA,CAAA,SAAM,CAAC,SAAS;IACxC;IAEA,IAAI,UAAU,YAAY,EAAE,SAAS,GAAG;QACtC,SAAS,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD,EAAE,2IAAA,CAAA,SAAM,CAAC,YAAY;IAC3C;IAEA,IAAI,UAAU,UAAU,EAAE,SAAS,GAAG;QACpC,SAAS,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD,EAAE,2IAAA,CAAA,SAAM,CAAC,UAAU;IACzC;IAEA,IAAI,UAAU,cAAc,EAAE,SAAS,GAAG;QACxC,SAAS,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD,EAAE,2IAAA,CAAA,SAAM,CAAC,WAAW;IAC1C;AACF;AAEO,SAAS,sBAAsB,OAAe;IACnD,IAAI,CAAC,SAAS,OAAO;IACrB,MAAM,CAAC,MAAM,SAAS,GAAG,QAAQ,KAAK,CAAC;IAEvC,IAAI,CAAC,OAAO,QAAQ,GAAG,KAAK,KAAK,CAAC;IAElC,IAAI,UAAU,MAAM,QAAQ;IAC5B,IAAI,aAAa,MAAM,QAAQ,OAAO,SAAS,SAAS;IAExD,OAAO,GAAG,MAAM,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,SAAS;AAC/C;AAEO,MAAM,iBAAiB,CAAC;IAC7B,IAAI,CAAC,OAAO,OAAO,EAAE;IACrB,IAAI;QACF,MAAM,SAAS,OAAO,UAAU,WAAW,KAAK,KAAK,CAAC,SAAS;QAC/D,OAAO,MAAM,OAAO,CAAC,UAAU,SAAS;YAAC;SAAO;IAClD,EAAE,OAAM;QACN,OAAO;YAAC;SAAM,EAAE,uCAAuC;IACzD;AACF;AAEO,MAAM,oBAAoB,CAAC;IAChC,IAAI;QACF,MAAM,SAAS,OAAO,UAAU,WAAW,KAAK,KAAK,CAAC,SAAS;QAC/D,OAAO,MAAM,OAAO,CAAC,UAAU,OAAO,IAAI,CAAC,QAAQ,UAAU;IAC/D,EAAE,OAAM;QACN,OAAO,SAAS;IAClB;AACF", "debugId": null}}, {"offset": {"line": 2528, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app/verified-classes/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { Suspense, useEffect, useState } from 'react';\r\nimport { useRouter, useSearchParams } from 'next/navigation';\r\nimport { motion } from 'framer-motion';\r\nimport { Card, CardHeader, CardContent, CardFooter } from '@/components/ui/card';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Star, ChevronLeft, ChevronRight, Filter, X } from 'lucide-react';\r\nimport { axiosInstance } from '@/lib/axios';\r\nimport Header from '@/app-components/Header';\r\nimport Footer from '@/app-components/Footer';\r\nimport { toast } from 'sonner';\r\nimport Image from 'next/image';\r\nimport { Skeleton } from '@/components/ui/skeleton';\r\nimport { FilterInput } from './FilterInput';\r\nimport { parseAndJoinArray } from '@/lib/helper';\r\n\r\ninterface Constant {\r\n  id: string;\r\n  name: string;\r\n  details: { id: string; value: string }[];\r\n}\r\n\r\ninterface TuitionClass {\r\n  education: string;\r\n  coachingType: string;\r\n  boardType?: string;\r\n  medium?: string;\r\n  section?: string;\r\n  subject?: string;\r\n  details?: string;\r\n  pricingPerMonth: number;\r\n  pricingPerCourse: number;\r\n  timeSlots: { id: string; from: string; to: string }[];\r\n}\r\n\r\ninterface Tutor {\r\n  id: string;\r\n  firstName: string;\r\n  lastName: string;\r\n  className: string;\r\n  ClassAbout: {\r\n    tutorBio: string;\r\n    classesLogo: string;\r\n  };\r\n  tuitionClasses: TuitionClass[];\r\n  averageRating?: number;\r\n  reviewCount?: number;\r\n}\r\n\r\nconst TutorListContent = () => {\r\n  const router = useRouter();\r\n  const [teachers, setTeachers] = useState<Tutor[]>([]);\r\n  const [constants, setConstants] = useState<Constant[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [page, setPage] = useState(1);\r\n  const [totalPages, setTotalPages] = useState(1);\r\n  const [isFilterOpen, setIsFilterOpen] = useState(false);\r\n  const searchParams = useSearchParams();\r\n\r\n  const [filters, setFilters] = useState({\r\n    education: searchParams.get('education') || '',\r\n    details: searchParams.get('details') || '',\r\n    boardType: searchParams.get('boardType') || '',\r\n    medium: searchParams.get('medium') || '',\r\n    section: searchParams.get('section') || '',\r\n    coachingType: searchParams.get('coachingType') || '',\r\n    subject: searchParams.get('subject') || '',\r\n    firstName: searchParams.get('firstName') || '',\r\n    lastName: searchParams.get('lastName') || '',\r\n    className: searchParams.get('className') || '',\r\n    sortByRating: true,\r\n    sortByReviewCount: true,\r\n  });\r\n\r\n  const getConstantValues = (name: string) => {\r\n    return constants.find((cat) => cat.name === name)?.details || [];\r\n  };\r\n\r\n  const fetchConstants = async () => {\r\n    try {\r\n      const res = await axiosInstance.get('/constant');\r\n      setConstants(res.data);\r\n    } catch {\r\n      toast.error('Failed to fetch filters');\r\n    }\r\n  };\r\n\r\n  const fetchTutors = async () => {\r\n    setLoading(true);\r\n    try {\r\n      const res = await axiosInstance.get('/classes/approved-tutors', {\r\n        params: {\r\n          page,\r\n          limit: 9,\r\n          ...filters,\r\n        },\r\n      });\r\n      const tutors = res.data.data;\r\n      setTeachers(tutors);\r\n      setTotalPages(res.data.totalPages);\r\n    } catch {\r\n      toast.error('Failed to fetch tutors');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const resetFilters = () => {\r\n    setFilters({\r\n      education: '',\r\n      details: '',\r\n      boardType: '',\r\n      medium: '',\r\n      section: '',\r\n      coachingType: '',\r\n      subject: '',\r\n      firstName: '',\r\n      lastName: '',\r\n      className: '',\r\n      sortByRating: true, // Always sort by rating\r\n      sortByReviewCount: true, // Always sort by review count\r\n    });\r\n    setPage(1);\r\n    fetchTutors();\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchConstants();\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    fetchTutors();\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [page]);\r\n\r\n  const containerVariants = {\r\n    hidden: { opacity: 0 },\r\n    visible: {\r\n      opacity: 1,\r\n      transition: {\r\n        staggerChildren: 0.1,\r\n      },\r\n    },\r\n  };\r\n\r\n  const itemVariants = {\r\n    hidden: { y: 20, opacity: 0 },\r\n    visible: {\r\n      y: 0,\r\n      opacity: 1,\r\n      transition: {\r\n        type: 'spring',\r\n        stiffness: 100,\r\n      },\r\n    },\r\n  };\r\n\r\n  const FilterSelect = ({\r\n    label,\r\n    value,\r\n    onChange,\r\n    options,\r\n  }: {\r\n    label: string;\r\n    value: string;\r\n    onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;\r\n    options: { id: string; value: string }[];\r\n  }) => (\r\n    <div className=\"flex flex-col\">\r\n      <label className=\"text-sm font-medium text-muted-foreground mb-1\">{label}</label>\r\n      <select\r\n        className=\"border rounded-lg px-3 py-2 text-sm\r\n                   dark:bg-black\r\n                   text-gray-900 dark:text-white\"\r\n        value={value}\r\n        onChange={onChange}\r\n      >\r\n        <option value=\"\" className=\"bg-white dark:bg-zinc-900\">\r\n          All {label}\r\n        </option>\r\n        {options.map((item) => (\r\n          <option\r\n            key={item.id}\r\n            value={item.value}\r\n            className=\"bg-white dark:bg-zinc-900 text-gray-900 dark:text-gray-100\"\r\n          >\r\n            {item.value}\r\n          </option>\r\n        ))}\r\n      </select>\r\n    </div>\r\n  );\r\n\r\n  // Dynamic options for Details based on selected Education\r\n  const selectedEducation = filters.education;\r\n  const detailsOptions = selectedEducation ? getConstantValues(selectedEducation) : [];\r\n\r\n  return (\r\n    <motion.section\r\n      initial=\"hidden\"\r\n      animate=\"visible\"\r\n      variants={containerVariants}\r\n      className=\"container mx-auto px-4 sm:px-6 lg:px-8 py-20\"\r\n    >\r\n      <motion.div\r\n        initial={{ opacity: 0, y: -20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ duration: 0.5 }}\r\n        className=\"text-center mb-12\"\r\n      >\r\n        <h1 className=\"text-4xl font-bold\">Find Your Perfect Tutor</h1>\r\n        <p className=\"mt-4 text-lg font-medium bg-gradient-to-r from-gray-700 to-gray-500 dark:from-gray-300 dark:to-gray-400 bg-clip-text text-transparent\">\r\n          Discover experienced tutors who can help you achieve your learning goals\r\n        </p>\r\n      </motion.div>\r\n\r\n      <motion.div\r\n        className=\"mb-8 bg-white/30 dark:bg-black/30 backdrop-blur-lg rounded-xl p-6\"\r\n        initial={{ opacity: 0 }}\r\n        animate={{ opacity: 1 }}\r\n        transition={{ delay: 0.2 }}\r\n      >\r\n        <div className=\"flex justify-between items-center mb-4\">\r\n          <div className=\"flex items-center gap-2\">\r\n            <Filter className=\"w-5 h-5 text-customOrange\" />\r\n            <h3 className=\"text-xl font-semibold\">Filters</h3>\r\n          </div>\r\n          <div className=\"flex items-center gap-4\">\r\n            <Button\r\n              variant=\"outline\"\r\n              className=\"hover:bg-customOrange/10\"\r\n              onClick={() => setIsFilterOpen(!isFilterOpen)}\r\n            >\r\n              {isFilterOpen ? <X className=\"w-4 h-4\" /> : <Filter className=\"w-4 h-4\" />}\r\n            </Button>\r\n          </div>\r\n        </div>\r\n\r\n        <div\r\n          className={`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 transition-all duration-300 ${isFilterOpen ? 'block' : 'hidden'\r\n            }`}\r\n        >\r\n          <FilterSelect\r\n            label=\"Category\"\r\n            value={filters.education}\r\n            onChange={(e) => {\r\n              setFilters((prev) => ({\r\n                ...prev,\r\n                education: e.target.value,\r\n                details: '',\r\n              }));\r\n            }}\r\n            options={constants\r\n              .filter((cat) =>\r\n                [\r\n                  \"Education\",\r\n                  \"Drama\",\r\n                  \"Music\",\r\n                  \"Art & Craft\",\r\n                  \"Sports\",\r\n                  \"Languages\",\r\n                  \"Technology\",\r\n                  \"Dance\",\r\n                  \"Computer Classes\",\r\n                  \"Cooking Classes\",\r\n                  \"Garba Classes\",\r\n                  \"Vaidik Maths\",\r\n                  \"Gymnastic Classes\",\r\n                  \"Yoga Classes\",\r\n                  \"Aviation Classes\",\r\n                  \"Designing Classes\"\r\n                ].includes(cat.name)\r\n              )\r\n              .map((cat) => ({ id: cat.id, value: cat.name }))}\r\n          />\r\n          {filters.education && filters.education !== 'Education' && (\r\n            <FilterSelect\r\n              label=\"Details\"\r\n              value={filters.details}\r\n              onChange={(e) => setFilters((prev) => ({ ...prev, details: e.target.value }))}\r\n              options={detailsOptions}\r\n            />\r\n          )}\r\n          {(!filters.education || filters.education === 'Education') && (\r\n            <>\r\n              <FilterSelect\r\n                label=\"Board Type\"\r\n                value={filters.boardType}\r\n                onChange={(e) => setFilters((prev) => ({ ...prev, boardType: e.target.value }))}\r\n                options={getConstantValues('Board Type')}\r\n              />\r\n              <FilterSelect\r\n                label=\"Medium\"\r\n                value={filters.medium}\r\n                onChange={(e) => setFilters((prev) => ({ ...prev, medium: e.target.value }))}\r\n                options={getConstantValues('Medium')}\r\n              />\r\n              <FilterSelect\r\n                label=\"Section\"\r\n                value={filters.section}\r\n                onChange={(e) => setFilters((prev) => ({ ...prev, section: e.target.value }))}\r\n                options={getConstantValues('Section')}\r\n              />\r\n              <FilterSelect\r\n                label=\"Subject\"\r\n                value={filters.subject}\r\n                onChange={(e) => setFilters((prev) => ({ ...prev, subject: e.target.value }))}\r\n                options={getConstantValues('Subject')}\r\n              />\r\n            </>\r\n          )}\r\n          <FilterSelect\r\n            label=\"Coaching Type\"\r\n            value={filters.coachingType}\r\n            onChange={(e) => setFilters((prev) => ({ ...prev, coachingType: e.target.value }))}\r\n            options={getConstantValues('Coaching Type')}\r\n          />\r\n          <FilterInput\r\n            label=\"First Name\"\r\n            value={filters.firstName}\r\n            onChange={(e) => setFilters((prev) => ({ ...prev, firstName: e.target.value }))}\r\n          />\r\n          <FilterInput\r\n            label=\"Class Name\"\r\n            value={filters.className}\r\n            onChange={(e) => setFilters((prev) => ({ ...prev, className: e.target.value }))}\r\n          />\r\n        </div>\r\n        <div className={`flex gap-4 mt-4 ${isFilterOpen ? 'block' : 'hidden'}`}>\r\n          <Button\r\n            className=\"w-[200px] bg-customOrange hover:bg-customOrange/90\"\r\n            onClick={() => {\r\n              setPage(1);\r\n              fetchTutors();\r\n            }}\r\n          >\r\n            Apply Filters\r\n          </Button>\r\n          <Button variant=\"default\" className=\"w-[200px]\" onClick={resetFilters}>\r\n            Reset\r\n          </Button>\r\n        </div>\r\n      </motion.div>\r\n\r\n      {loading ? (\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n          {[...Array(6)].map((_, i) => (\r\n            <Skeleton key={i} className=\"h-96 w-full rounded-xl\" />\r\n          ))}\r\n        </div>\r\n      ) : teachers.length === 0 ? (\r\n        <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className=\"text-center py-10\">\r\n          <p className=\"text-muted-foreground\">No tutors found. Try adjusting your filters.</p>\r\n        </motion.div>\r\n      ) : (\r\n        <>\r\n          <motion.div\r\n            variants={containerVariants}\r\n            initial=\"hidden\"\r\n            animate=\"visible\"\r\n            className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\"\r\n          >\r\n            {teachers.map((teacher, i) => (\r\n              <motion.div key={i} variants={itemVariants} whileHover={{ y: -5 }} className=\"h-full\">\r\n                <Card className=\"h-full bg-white/50 dark:bg-black/50 backdrop-blur-sm transition-all duration-300\">\r\n                  <CardHeader className=\"flex flex-row items-center gap-4\">\r\n                    <motion.div\r\n                      className=\"relative w-30 h-30 rounded-full overflow-hidden ring-2 ring-customOrange/20\"\r\n                      whileHover={{ scale: 1.05 }}\r\n                    >\r\n                      <Image\r\n                        src={\r\n                          teacher.ClassAbout && teacher.ClassAbout.classesLogo\r\n                            ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${teacher.ClassAbout.classesLogo}`\r\n                            : '/default-profile.jpg'\r\n                        }\r\n                        alt={teacher.firstName}\r\n                        fill\r\n                        className=\"object-cover\"\r\n                      />\r\n                    </motion.div>\r\n                    <div className=\"flex-1\">\r\n                      <h3 className=\"text-lg font-semibold hover:text-customOrange transition-colors\">\r\n                        {teacher.firstName} {teacher.lastName}\r\n                      </h3>\r\n                      <p className=\"text-sm text-muted-foreground\">{teacher.className}</p>\r\n                    </div>\r\n                  </CardHeader>\r\n                  <CardContent className=\"flex-1 space-y-4\">\r\n                    <p className=\"line-clamp-2 text-sm text-muted-foreground\">\r\n                      {(teacher.ClassAbout && teacher.ClassAbout.tutorBio) || 'No bio available.'}\r\n                    </p>\r\n                    {teacher.tuitionClasses.length > 0 && (\r\n                      <div className=\"space-y-2 p-4 rounded-lg bg-black/5 dark:bg-white/5\">\r\n                        <div className=\"grid grid-cols-2 gap-2 text-sm\">\r\n                          <div className=\"space-y-1\">\r\n                            <p className=\"font-medium\">Category</p>\r\n                            <p className=\"text-muted-foreground\">\r\n                              {parseAndJoinArray(teacher.tuitionClasses[0].education) || 'N/A'}\r\n                            </p>\r\n                          </div>\r\n                          <div className=\"space-y-1\">\r\n                            <p className=\"font-medium\">Coaching Type</p>\r\n                            <p className=\"text-muted-foreground\">\r\n                              {parseAndJoinArray(teacher.tuitionClasses[0].coachingType) || 'N/A'}\r\n                            </p>\r\n                          </div>\r\n                          {teacher.tuitionClasses[0].education === 'Education' && (\r\n                            <>\r\n                              <div className=\"space-y-1\">\r\n                                <p className=\"font-medium\">Board</p>\r\n                                <p className=\"text-muted-foreground\">\r\n                                  {parseAndJoinArray(teacher.tuitionClasses[0].boardType) || 'N/A'}\r\n                                </p>\r\n                              </div>\r\n                              <div className=\"space-y-1\">\r\n                                <p className=\"font-medium\">Medium</p>\r\n                                <p className=\"text-muted-foreground\">\r\n                                  {parseAndJoinArray(teacher.tuitionClasses[0].medium) || 'N/A'}\r\n                                </p>\r\n                              </div>\r\n                            </>\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n                  </CardContent>\r\n                  <CardFooter className=\"flex flex-col items-start gap-4\">\r\n                    <div className=\"flex items-center gap-1 pt-2\">\r\n                      <Star className=\"h-4 w-4 fill-yellow-400 text-yellow-400\" />\r\n                      <span className=\"font-semibold text-foreground\">\r\n                        {teacher.averageRating ? teacher.averageRating.toFixed(1) : \"0\"}\r\n                      </span>\r\n                      <span>({teacher.reviewCount || 0} reviews)</span>\r\n                    </div>\r\n                    <div className=\"flex gap-2 w-full\">\r\n                      <Button\r\n                        className=\"flex-1 bg-customOrange hover:bg-[#E88143]\"\r\n                        onClick={() => router.push(`/classes-details/${teacher.id}`)}\r\n                      >\r\n                        View Profile\r\n                      </Button>\r\n                      <Button\r\n                        variant=\"outline\"\r\n                        className=\"flex-1 hover:bg-orange-50\"\r\n                        onClick={() => {\r\n                          const userName = `${teacher.firstName} ${teacher.lastName}`;\r\n                          router.push(`/student/chat?userId=${teacher.id}&userName=${encodeURIComponent(userName)}`);\r\n                        }}\r\n                      >\r\n                        Message\r\n                      </Button>\r\n                    </div>\r\n                  </CardFooter>\r\n                </Card>\r\n              </motion.div>\r\n            ))}\r\n          </motion.div>\r\n\r\n          <div className=\"flex justify-center items-center mt-8 gap-4\">\r\n            <Button\r\n              variant=\"outline\"\r\n              disabled={page === 1}\r\n              onClick={() => setPage(page - 1)}\r\n              className=\"flex gap-2\"\r\n            >\r\n              <ChevronLeft className=\"h-4 w-4\" /> Previous\r\n            </Button>\r\n            <span className=\"text-sm text-muted-foreground\">\r\n              Page {page} of {totalPages}\r\n            </span>\r\n            <Button\r\n              variant=\"outline\"\r\n              disabled={page === totalPages}\r\n              onClick={() => setPage(page + 1)}\r\n              className=\"flex gap-2\"\r\n            >\r\n              Next <ChevronRight className=\"h-4 w-4\" />\r\n            </Button>\r\n          </div>\r\n        </>\r\n      )}\r\n    </motion.section>\r\n  );\r\n};\r\n\r\nconst TutorListPage = () => {\r\n  return (\r\n    <div className=\"min-h-screen\">\r\n      <Header />\r\n      <Suspense\r\n        fallback={\r\n          <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 py-20\">\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n              {[...Array(6)].map((_, i) => (\r\n                <Skeleton key={i} className=\"h-96 w-full rounded-xl\" />\r\n              ))}\r\n            </div>\r\n          </div>\r\n        }\r\n      >\r\n        <TutorListContent />\r\n      </Suspense>\r\n      <Footer />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TutorListPage;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA;;;;;;;;;;;;;;;;AAkDA,MAAM,mBAAmB;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IACpD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IAEnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,WAAW,aAAa,GAAG,CAAC,gBAAgB;QAC5C,SAAS,aAAa,GAAG,CAAC,cAAc;QACxC,WAAW,aAAa,GAAG,CAAC,gBAAgB;QAC5C,QAAQ,aAAa,GAAG,CAAC,aAAa;QACtC,SAAS,aAAa,GAAG,CAAC,cAAc;QACxC,cAAc,aAAa,GAAG,CAAC,mBAAmB;QAClD,SAAS,aAAa,GAAG,CAAC,cAAc;QACxC,WAAW,aAAa,GAAG,CAAC,gBAAgB;QAC5C,UAAU,aAAa,GAAG,CAAC,eAAe;QAC1C,WAAW,aAAa,GAAG,CAAC,gBAAgB;QAC5C,cAAc;QACd,mBAAmB;IACrB;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAO,UAAU,IAAI,CAAC,CAAC,MAAQ,IAAI,IAAI,KAAK,OAAO,WAAW,EAAE;IAClE;IAEA,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,MAAM,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;YACpC,aAAa,IAAI,IAAI;QACvB,EAAE,OAAM;YACN,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,cAAc;QAClB,WAAW;QACX,IAAI;YACF,MAAM,MAAM,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,4BAA4B;gBAC9D,QAAQ;oBACN;oBACA,OAAO;oBACP,GAAG,OAAO;gBACZ;YACF;YACA,MAAM,SAAS,IAAI,IAAI,CAAC,IAAI;YAC5B,YAAY;YACZ,cAAc,IAAI,IAAI,CAAC,UAAU;QACnC,EAAE,OAAM;YACN,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe;QACnB,WAAW;YACT,WAAW;YACX,SAAS;YACT,WAAW;YACX,QAAQ;YACR,SAAS;YACT,cAAc;YACd,SAAS;YACT,WAAW;YACX,UAAU;YACV,WAAW;YACX,cAAc;YACd,mBAAmB;QACrB;QACA,QAAQ;QACR;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACA,uDAAuD;IACzD,GAAG;QAAC;KAAK;IAET,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,GAAG;YAAI,SAAS;QAAE;QAC5B,SAAS;YACP,GAAG;YACH,SAAS;YACT,YAAY;gBACV,MAAM;gBACN,WAAW;YACb;QACF;IACF;IAEA,MAAM,eAAe,CAAC,EACpB,KAAK,EACL,KAAK,EACL,QAAQ,EACR,OAAO,EAMR,iBACC,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAM,WAAU;8BAAkD;;;;;;8BACnE,8OAAC;oBACC,WAAU;oBAGV,OAAO;oBACP,UAAU;;sCAEV,8OAAC;4BAAO,OAAM;4BAAG,WAAU;;gCAA4B;gCAChD;;;;;;;wBAEN,QAAQ,GAAG,CAAC,CAAC,qBACZ,8OAAC;gCAEC,OAAO,KAAK,KAAK;gCACjB,WAAU;0CAET,KAAK,KAAK;+BAJN,KAAK,EAAE;;;;;;;;;;;;;;;;;IAWtB,0DAA0D;IAC1D,MAAM,oBAAoB,QAAQ,SAAS;IAC3C,MAAM,iBAAiB,oBAAoB,kBAAkB,qBAAqB,EAAE;IAEpF,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,OAAO;QACb,SAAQ;QACR,SAAQ;QACR,UAAU;QACV,WAAU;;0BAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;;kCAEV,8OAAC;wBAAG,WAAU;kCAAqB;;;;;;kCACnC,8OAAC;wBAAE,WAAU;kCAAwI;;;;;;;;;;;;0BAKvJ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,OAAO;gBAAI;;kCAEzB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCAAG,WAAU;kDAAwB;;;;;;;;;;;;0CAExC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,gBAAgB,CAAC;8CAE/B,6BAAe,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;6DAAe,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAKpE,8OAAC;wBACC,WAAW,CAAC,iFAAiF,EAAE,eAAe,UAAU,UACpH;;0CAEJ,8OAAC;gCACC,OAAM;gCACN,OAAO,QAAQ,SAAS;gCACxB,UAAU,CAAC;oCACT,WAAW,CAAC,OAAS,CAAC;4CACpB,GAAG,IAAI;4CACP,WAAW,EAAE,MAAM,CAAC,KAAK;4CACzB,SAAS;wCACX,CAAC;gCACH;gCACA,SAAS,UACN,MAAM,CAAC,CAAC,MACP;wCACE;wCACA;wCACA;wCACA;wCACA;wCACA;wCACA;wCACA;wCACA;wCACA;wCACA;wCACA;wCACA;wCACA;wCACA;wCACA;qCACD,CAAC,QAAQ,CAAC,IAAI,IAAI,GAEpB,GAAG,CAAC,CAAC,MAAQ,CAAC;wCAAE,IAAI,IAAI,EAAE;wCAAE,OAAO,IAAI,IAAI;oCAAC,CAAC;;;;;;4BAEjD,QAAQ,SAAS,IAAI,QAAQ,SAAS,KAAK,6BAC1C,8OAAC;gCACC,OAAM;gCACN,OAAO,QAAQ,OAAO;gCACtB,UAAU,CAAC,IAAM,WAAW,CAAC,OAAS,CAAC;4CAAE,GAAG,IAAI;4CAAE,SAAS,EAAE,MAAM,CAAC,KAAK;wCAAC,CAAC;gCAC3E,SAAS;;;;;;4BAGZ,CAAC,CAAC,QAAQ,SAAS,IAAI,QAAQ,SAAS,KAAK,WAAW,mBACvD;;kDACE,8OAAC;wCACC,OAAM;wCACN,OAAO,QAAQ,SAAS;wCACxB,UAAU,CAAC,IAAM,WAAW,CAAC,OAAS,CAAC;oDAAE,GAAG,IAAI;oDAAE,WAAW,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCAC7E,SAAS,kBAAkB;;;;;;kDAE7B,8OAAC;wCACC,OAAM;wCACN,OAAO,QAAQ,MAAM;wCACrB,UAAU,CAAC,IAAM,WAAW,CAAC,OAAS,CAAC;oDAAE,GAAG,IAAI;oDAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCAC1E,SAAS,kBAAkB;;;;;;kDAE7B,8OAAC;wCACC,OAAM;wCACN,OAAO,QAAQ,OAAO;wCACtB,UAAU,CAAC,IAAM,WAAW,CAAC,OAAS,CAAC;oDAAE,GAAG,IAAI;oDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCAC3E,SAAS,kBAAkB;;;;;;kDAE7B,8OAAC;wCACC,OAAM;wCACN,OAAO,QAAQ,OAAO;wCACtB,UAAU,CAAC,IAAM,WAAW,CAAC,OAAS,CAAC;oDAAE,GAAG,IAAI;oDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCAC3E,SAAS,kBAAkB;;;;;;;;0CAIjC,8OAAC;gCACC,OAAM;gCACN,OAAO,QAAQ,YAAY;gCAC3B,UAAU,CAAC,IAAM,WAAW,CAAC,OAAS,CAAC;4CAAE,GAAG,IAAI;4CAAE,cAAc,EAAE,MAAM,CAAC,KAAK;wCAAC,CAAC;gCAChF,SAAS,kBAAkB;;;;;;0CAE7B,8OAAC,iJAAA,CAAA,cAAW;gCACV,OAAM;gCACN,OAAO,QAAQ,SAAS;gCACxB,UAAU,CAAC,IAAM,WAAW,CAAC,OAAS,CAAC;4CAAE,GAAG,IAAI;4CAAE,WAAW,EAAE,MAAM,CAAC,KAAK;wCAAC,CAAC;;;;;;0CAE/E,8OAAC,iJAAA,CAAA,cAAW;gCACV,OAAM;gCACN,OAAO,QAAQ,SAAS;gCACxB,UAAU,CAAC,IAAM,WAAW,CAAC,OAAS,CAAC;4CAAE,GAAG,IAAI;4CAAE,WAAW,EAAE,MAAM,CAAC,KAAK;wCAAC,CAAC;;;;;;;;;;;;kCAGjF,8OAAC;wBAAI,WAAW,CAAC,gBAAgB,EAAE,eAAe,UAAU,UAAU;;0CACpE,8OAAC,kIAAA,CAAA,SAAM;gCACL,WAAU;gCACV,SAAS;oCACP,QAAQ;oCACR;gCACF;0CACD;;;;;;0CAGD,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,WAAU;gCAAY,SAAS;0CAAc;;;;;;;;;;;;;;;;;;YAM1E,wBACC,8OAAC;gBAAI,WAAU;0BACZ;uBAAI,MAAM;iBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,oIAAA,CAAA,WAAQ;wBAAS,WAAU;uBAAb;;;;;;;;;uBAGjB,SAAS,MAAM,KAAK,kBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBAAC,SAAS;oBAAE,SAAS;gBAAE;gBAAG,SAAS;oBAAE,SAAS;gBAAE;gBAAG,WAAU;0BACtE,cAAA,8OAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;qCAGvC;;kCACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU;wBACV,SAAQ;wBACR,SAAQ;wBACR,WAAU;kCAET,SAAS,GAAG,CAAC,CAAC,SAAS,kBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAAS,UAAU;gCAAc,YAAY;oCAAE,GAAG,CAAC;gCAAE;gCAAG,WAAU;0CAC3E,cAAA,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,8OAAC,gIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,YAAY;wDAAE,OAAO;oDAAK;8DAE1B,cAAA,8OAAC,6HAAA,CAAA,UAAK;wDACJ,KACE,QAAQ,UAAU,IAAI,QAAQ,UAAU,CAAC,WAAW,GAChD,8DAA0C,QAAQ,UAAU,CAAC,WAAW,EAAE,GAC1E;wDAEN,KAAK,QAAQ,SAAS;wDACtB,IAAI;wDACJ,WAAU;;;;;;;;;;;8DAGd,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;;gEACX,QAAQ,SAAS;gEAAC;gEAAE,QAAQ,QAAQ;;;;;;;sEAEvC,8OAAC;4DAAE,WAAU;sEAAiC,QAAQ,SAAS;;;;;;;;;;;;;;;;;;sDAGnE,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC;oDAAE,WAAU;8DACV,AAAC,QAAQ,UAAU,IAAI,QAAQ,UAAU,CAAC,QAAQ,IAAK;;;;;;gDAEzD,QAAQ,cAAc,CAAC,MAAM,GAAG,mBAC/B,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAE,WAAU;kFAAc;;;;;;kFAC3B,8OAAC;wEAAE,WAAU;kFACV,CAAA,GAAA,oHAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,cAAc,CAAC,EAAE,CAAC,SAAS,KAAK;;;;;;;;;;;;0EAG/D,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAE,WAAU;kFAAc;;;;;;kFAC3B,8OAAC;wEAAE,WAAU;kFACV,CAAA,GAAA,oHAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,cAAc,CAAC,EAAE,CAAC,YAAY,KAAK;;;;;;;;;;;;4DAGjE,QAAQ,cAAc,CAAC,EAAE,CAAC,SAAS,KAAK,6BACvC;;kFACE,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAE,WAAU;0FAAc;;;;;;0FAC3B,8OAAC;gFAAE,WAAU;0FACV,CAAA,GAAA,oHAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,cAAc,CAAC,EAAE,CAAC,SAAS,KAAK;;;;;;;;;;;;kFAG/D,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAE,WAAU;0FAAc;;;;;;0FAC3B,8OAAC;gFAAE,WAAU;0FACV,CAAA,GAAA,oHAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,cAAc,CAAC,EAAE,CAAC,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDASxE,8OAAC,gIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;4DAAK,WAAU;sEACb,QAAQ,aAAa,GAAG,QAAQ,aAAa,CAAC,OAAO,CAAC,KAAK;;;;;;sEAE9D,8OAAC;;gEAAK;gEAAE,QAAQ,WAAW,IAAI;gEAAE;;;;;;;;;;;;;8DAEnC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DACL,WAAU;4DACV,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,iBAAiB,EAAE,QAAQ,EAAE,EAAE;sEAC5D;;;;;;sEAGD,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,WAAU;4DACV,SAAS;gEACP,MAAM,WAAW,GAAG,QAAQ,SAAS,CAAC,CAAC,EAAE,QAAQ,QAAQ,EAAE;gEAC3D,OAAO,IAAI,CAAC,CAAC,qBAAqB,EAAE,QAAQ,EAAE,CAAC,UAAU,EAAE,mBAAmB,WAAW;4DAC3F;sEACD;;;;;;;;;;;;;;;;;;;;;;;;+BAtFQ;;;;;;;;;;kCAgGrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,UAAU,SAAS;gCACnB,SAAS,IAAM,QAAQ,OAAO;gCAC9B,WAAU;;kDAEV,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAErC,8OAAC;gCAAK,WAAU;;oCAAgC;oCACxC;oCAAK;oCAAK;;;;;;;0CAElB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,UAAU,SAAS;gCACnB,SAAS,IAAM,QAAQ,OAAO;gCAC9B,WAAU;;oCACX;kDACM,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3C;AAEA,MAAM,gBAAgB;IACpB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,mIAAA,CAAA,UAAM;;;;;0BACP,8OAAC,qMAAA,CAAA,WAAQ;gBACP,wBACE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,oIAAA,CAAA,WAAQ;gCAAS,WAAU;+BAAb;;;;;;;;;;;;;;;0BAMvB,cAAA,8OAAC;;;;;;;;;;0BAEH,8OAAC,mIAAA,CAAA,UAAM;;;;;;;;;;;AAGb;uCAEe", "debugId": null}}]}