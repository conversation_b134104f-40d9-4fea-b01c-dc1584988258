import { axiosInstance } from '@/lib/axios';

export const saveTerminatedStudent = async (data: any) => {
  try {
    const response = await axiosInstance.post('/quizTermination', data,{
       headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return response.data;
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to save termination log: ${error.response?.data?.message || error.message}`,
    };
  }
};

export const countUwhizAttemp = async (studentId: string,examId:number) => {
  try {
    const response = await axiosInstance.get(`check-attempt/count?studentId=${studentId}&examId=${examId}`,{
      headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return response.data;
  } catch (error: any) {
    return {
      success: false,
      error: `Failed To Get Count of termination: ${error.response?.data?.message || error.message}`,
    };
  }
};