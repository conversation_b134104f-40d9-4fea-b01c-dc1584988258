const mix = require('laravel-mix');
mix.disableNotifications();
/*
 |--------------------------------------------------------------------------
 | Mix Asset Management
 |--------------------------------------------------------------------------
 |
 | Mix provides a clean, fluent API for defining some Webpack build steps
 | for your Laravel application. By default, we are compiling the Sass
 | file for the application as well as bundling up all the JS files.
 |
 */

mix.combine([
'public/assets/css/adminlte.min.css',
'public/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css',
'public/plugins/datatables-responsive/css/responsive.bootstrap4.min.css',
'public/plugins/datatables-buttons/css/buttons.bootstrap4.min.css',
'public/plugins/select2/css/select2.min.css',
'public/plugins/calendar/calendar-main.min.css',
'public/plugins/toastr/toastr.min.css',
'public/plugins/jquery-ui/jquery-ui.css',
'public/plugins/timepicker/timepicker.min.css',
'public/assets/css/style.css', 'public/assets/css/darkMode.css'], 'public/build/css/common-build.css').version();

mix.combine(['public/plugins/jquery/jquery.min.js',
'public/plugins/bootstrap/js/bootstrap.bundle.min.js',
'public/plugins/datatables/jquery.dataTables.min.js',
'public/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js',
'public/plugins/datatables-responsive/js/dataTables.responsive.min.js',
'public/plugins/datatables-responsive/js/responsive.bootstrap4.min.js',
'public/plugins/select2/js/select2.full.min.js',
'public/plugins/jquery-validation/jquery.validate.min.js',
'public/plugins/jquery-validation/additional-methods.min.js',
'public/plugins/calendar/calendar-main.min.js',
'public/plugins/toastr/toastr.min.js',
'public/assets/js/adminlte.min.js',
'public/plugins/jquery-ui/jquery-ui.js',
'public/assets/js/dayjs.min.js',
'public/plugins/moment/moment.min.js',
'public/plugins/timepicker/timepicker.js',
'public/plugins/sweetalert/<EMAIL>',
'public/vendor/jsvalidation/js/jsvalidation.min.js',
'public/assets/js/custom.js'], 'public/build/js/common-build.js').version();


mix.js('modules/AnnualCalendar/resources/views/js/index.js', 'public/js/page-level-js/annualCalendar').version();

mix.js('modules/Holiday/resources/views/js/index.js', 'public/js/page-level-js/holiday').version();
mix.js('modules/Holiday/resources/views/js/create.js', 'public/js/page-level-js/holiday').version();

mix.js('modules/Event/resources/views/js/index.js', 'public/js/page-level-js/event').version();
mix.js('modules/Event/resources/views/js/create.js', 'public/js/page-level-js/event').version();


mix.js('modules/Circulars/resources/views/js/index.js', 'public/js/page-level-js/circulars').version();
mix.js('modules/Circulars/resources/views/js/create.js', 'public/js/page-level-js/circulars').version();

mix.js('modules/Document/resources/views/js/index.js', 'public/js/page-level-js/documents').version();
mix.js('modules/Document/resources/views/js/create.js', 'public/js/page-level-js/documents').version();

mix.js('modules/Department/resources/views/js/index.js', 'public/js/page-level-js/department').version();
mix.js('modules/Department/resources/views/js/create.js', 'public/js/page-level-js/department').version();
mix.js('modules/Department/resources/views/js/edit.js', 'public/js/page-level-js/department').version();

mix.js('modules/Timetable/resources/views/ViewTimetable/js/index.js', 'public/js/page-level-js/timetable/timetable').version();

mix.js('modules/Timetable/resources/views/js/index.js', 'public/js/page-level-js/timetable/mastertimetable').version();

mix.js('modules/Classroom/resources/views/js/index.js', 'public/js/page-level-js/timetable/classroom').version();
mix.js('modules/Classroom/resources/views/js/create.js', 'public/js/page-level-js/timetable/classroom').version();
mix.js('modules/Classroom/resources/views/js/edit.js', 'public/js/page-level-js/timetable/classroom').version();

mix.js('modules/Resources/resources/views/js/index.js', 'public/js/page-level-js/timetable/resource').version();
mix.js('modules/Resources/resources/views/js/create.js', 'public/js/page-level-js/timetable/resource').version();
mix.js('modules/Resources/resources/views/js/edit.js', 'public/js/page-level-js/timetable/resource').version();

mix.js('modules/Subject/resources/views/js/index.js', 'public/js/page-level-js/timetable/subject').version();
mix.js('modules/Subject/resources/views/js/create.js', 'public/js/page-level-js/timetable/subject').version();
mix.js('modules/Subject/resources/views/js/edit.js', 'public/js/page-level-js/timetable/subject').version();

mix.js('modules/Timeslots/resources/views/js/index.js', 'public/js/page-level-js/timetable/timeslots').version();
mix.js('modules/Timeslots/resources/views/js/create.js', 'public/js/page-level-js/timetable/timeslots').version();
mix.js('modules/Timeslots/resources/views/js/edit.js', 'public/js/page-level-js/timetable/timeslots').version();

mix.js('modules/Enquiry/resources/views/js/index.js', 'public/js/page-level-js/Enquiry').version();
mix.js('modules/Enquiry/resources/views/js/create.js', 'public/js/page-level-js/Enquiry').version();
mix.js('modules/Enquiry/resources/views/js/edit.js', 'public/js/page-level-js/Enquiry').version();
mix.js('modules/Enquiry/resources/views/js/followups.js', 'public/js/page-level-js/Enquiry').version();
mix.js('modules/Enquiry/resources/views/js/feesindex.js', 'public/js/page-level-js/Enquiry').version();

mix.js('modules/Years/resources/views/js/index.js', 'public/js/page-level-js/AcademicSetup/Years').version();
mix.js('modules/Years/resources/views/js/create.js', 'public/js/page-level-js/AcademicSetup/Years').version();
mix.js('modules/Years/resources/views/js/edit.js', 'public/js/page-level-js/AcademicSetup/Years').version();

mix.js('modules/Admission/resources/views/js/create.js', 'public/js/page-level-js/Admission/js').version();
mix.js('modules/Admission/resources/views/js/index.js', 'public/js/page-level-js/Admission/js').version();
mix.js('modules/Admission/resources/views/js/feesetup.js', 'public/js/page-level-js/Admission/js').version();

mix.js('modules/Fees/resources/views/Category/js/index.js', 'public/js/page-level-js/Fees/Category/js').version();

mix.js('modules/Fees/resources/views/ClassroomWiseFee/js/index.js', 'public/js/page-level-js/Fees/ClassroomWiseFee/js').version();

mix.js('modules/Passbook/resources/views/js/index.js', 'public/js/page-level-js/Passbook').version();

mix.js('modules/StudentAttendance/resources/views/js/index.js', 'public/js/page-level-js/Student/Attendance').version();
