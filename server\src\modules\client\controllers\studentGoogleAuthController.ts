import { Request, Response } from 'express';
import jwt from 'jsonwebtoken';
import { sendError, sendSuccess } from '@/utils/response';
import {
  createStudentWithGoogle,
  findStudentByEmail,
  findStudentByGoogleId,
  updateStudentWithGoogle
} from '../services/studentGoogleAuthService';
import { findUserByEmail } from '../services/authService';
import dotenv from 'dotenv';
import prisma from '@/config/prismaClient';
import { getReferralLinkByCode, createReferral } from '../services/referralService';
import { transporter } from '@/utils/email';
import {
  createCongratulationsTemplate,
  createAdminNotificationTemplate
} from '@/utils/emailTemplates';

dotenv.config();

const JWT_SECRET = process.env.JWT_SECRET || 'secret123';
const COOKIE_NAME = "student_jwt";


export const googleAuthStudent = async (req: Request, res: Response): Promise<any> => {
  const { googleId, email, firstName, lastName, profilePhoto, referralCode } = req.body;
  const clientType = req.headers['client-type'];

  if (!googleId || !email) {
    return sendError(res, 'Google ID and email are required', 400);
  }

  try {
    const existingUser = await findUserByEmail(email);
    if (existingUser) {
      return sendError(res, 'User already exists as a tutor', 400);
    }
    let student = await findStudentByGoogleId(googleId);
    if (!student) {
      const studentByEmail = await findStudentByEmail(email);

      if (studentByEmail) {
        if (!studentByEmail.googleId) {
          student = await updateStudentWithGoogle(
            studentByEmail.id,
            googleId,
            profilePhoto
          );
        } else if (studentByEmail.googleId !== googleId) {
          student = studentByEmail;

          if (!studentByEmail.isVerified) {
            student = await prisma.student.update({
              where: { id: studentByEmail.id },
              data: { isVerified: true }
            });
          }
        } else {
          student = studentByEmail;

          if (!studentByEmail.isVerified) {
            student = await prisma.student.update({
              where: { id: studentByEmail.id },
              data: { isVerified: true }
            });
          }
        }
      } else {
        try {
          student = await createStudentWithGoogle(
            firstName || '',
            lastName || '',
            email,
            googleId,
            profilePhoto
          );

          // Send congratulations email to the student
          const congratulationsEmailHtml = createCongratulationsTemplate(`${firstName || ''} ${lastName || ''}`);
          await transporter.sendMail({
            from: process.env.EMAIL_USER,
            to: email,
            subject: 'Welcome to Uest.in!',
            html: congratulationsEmailHtml,
          });

          // Handle referral tracking for new student
          if (referralCode) {
            try {
              const referralLink = await getReferralLinkByCode(referralCode);
              if (referralLink && referralLink.isActive) {
                await createReferral(
                  referralLink.id,
                  student.id,
                  'STUDENT',
                  `${firstName || ''} ${lastName || ''}`,
                  email
                );
              }
            } catch (error) {
              console.log('Referral tracking error:', error);
              // Don't fail registration if referral tracking fails
            }
          }

          // Send notification email to admin
          const adminNotificationHtml = createAdminNotificationTemplate({
            firstName: firstName || '',
            lastName: lastName || '',
            email,
            contact: ''
          });

          // Get admin email from environment variable or use a default
          const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';

          await transporter.sendMail({
            from: process.env.EMAIL_USER,
            to: adminEmail,
            subject: 'New Student Registration via Google',
            html: adminNotificationHtml,
          });
        } catch (error: any) {
          return sendError(res, `Failed to create student account: ${error?.message || 'Unknown error'}`, 500);
        }
      }
    } else {
      if (!student.isVerified) {
        student = await prisma.student.update({
          where: { id: student.id },
          data: { isVerified: true }
        });
      }
    }

    const token = jwt.sign({ id: student.id, email: student.email }, JWT_SECRET, {
      expiresIn: '1d',
    });

    if (clientType === 'mobile-app') {
      return sendSuccess(res, { user: student, token }, 'Login successful');
    } else {
      res.cookie(COOKIE_NAME, token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
        maxAge: 24 * 60 * 60 * 1000,
      });
      return sendSuccess(res, { user: student, token }, 'Login successful');
    }
  } catch (error: any) {
    console.error('Google auth error:', error);
    return sendError(
      res,
      'Failed to authenticate with Google. Please try again or use a different account.',
      500
    );
  }
};
