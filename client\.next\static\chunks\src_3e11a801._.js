(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/src_3e11a801._.js", {

"[project]/src/lib/utils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "clearStudentAuthToken": (()=>clearStudentAuthToken),
    "cn": (()=>cn),
    "getStudentAuthToken": (()=>getStudentAuthToken),
    "isStudentAuthenticated": (()=>isStudentAuthenticated),
    "setStudentAuthToken": (()=>setStudentAuthToken),
    "truncateThought": (()=>truncateThought)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-client] (ecmascript)");
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
const truncateThought = (text, wordLimit = 5)=>{
    const words = text.trim().split(/\s+/);
    if (words.length <= wordLimit) return text;
    return words.slice(0, wordLimit).join(' ') + '...';
};
const setStudentAuthToken = (token)=>{
    localStorage.setItem('studentToken', token);
};
const getStudentAuthToken = ()=>{
    return localStorage.getItem('studentToken');
};
const clearStudentAuthToken = ()=>{
    localStorage.removeItem('studentToken');
};
const isStudentAuthenticated = ()=>{
    return !!getStudentAuthToken();
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/avatar.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Avatar": (()=>Avatar),
    "AvatarFallback": (()=>AvatarFallback),
    "AvatarImage": (()=>AvatarImage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$avatar$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-avatar/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
'use client';
;
;
;
function Avatar({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$avatar$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Root"], {
        "data-slot": "avatar",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('relative flex size-8 shrink-0 overflow-hidden rounded-full', className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/avatar.tsx",
        lineNumber: 10,
        columnNumber: 5
    }, this);
}
_c = Avatar;
function AvatarImage({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$avatar$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Image"], {
        "data-slot": "avatar-image",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('aspect-square size-full', className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/avatar.tsx",
        lineNumber: 20,
        columnNumber: 5
    }, this);
}
_c1 = AvatarImage;
function AvatarFallback({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$avatar$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fallback"], {
        "data-slot": "avatar-fallback",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('bg-muted flex size-full items-center justify-center rounded-full', className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/avatar.tsx",
        lineNumber: 33,
        columnNumber: 5
    }, this);
}
_c2 = AvatarFallback;
;
var _c, _c1, _c2;
__turbopack_context__.k.register(_c, "Avatar");
__turbopack_context__.k.register(_c1, "AvatarImage");
__turbopack_context__.k.register(_c2, "AvatarFallback");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/button.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Button": (()=>Button),
    "buttonVariants": (()=>buttonVariants)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slot$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-slot/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/class-variance-authority/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
;
;
;
;
const buttonVariants = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cva"])("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive", {
    variants: {
        variant: {
            default: 'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',
            destructive: 'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',
            outline: 'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50',
            secondary: 'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',
            ghost: 'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',
            link: 'text-primary underline-offset-4 hover:underline'
        },
        size: {
            default: 'h-9 px-4 py-2 has-[>svg]:px-3',
            sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',
            lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',
            icon: 'size-9'
        }
    },
    defaultVariants: {
        variant: 'default',
        size: 'default'
    }
});
function Button({ className, variant, size, asChild = false, ...props }) {
    const Comp = asChild ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slot$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Slot"] : 'button';
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Comp, {
        "data-slot": "button",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])(buttonVariants({
            variant,
            size,
            className
        })),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/button.tsx",
        lineNumber: 48,
        columnNumber: 5
    }, this);
}
_c = Button;
;
var _c;
__turbopack_context__.k.register(_c, "Button");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/input.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Input": (()=>Input)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
;
;
function Input({ className, type, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
        type: type,
        "data-slot": "input",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm', 'focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]', 'aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive', className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/input.tsx",
        lineNumber: 7,
        columnNumber: 5
    }, this);
}
_c = Input;
;
var _c;
__turbopack_context__.k.register(_c, "Input");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/use-mobile.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useIsMobile": (()=>useIsMobile)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
;
const MOBILE_BREAKPOINT = 768;
function useIsMobile() {
    _s();
    const [isMobile, setIsMobile] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(undefined);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useIsMobile.useEffect": ()=>{
            const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`);
            const onChange = {
                "useIsMobile.useEffect.onChange": ()=>{
                    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);
                }
            }["useIsMobile.useEffect.onChange"];
            mql.addEventListener("change", onChange);
            setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);
            return ({
                "useIsMobile.useEffect": ()=>mql.removeEventListener("change", onChange)
            })["useIsMobile.useEffect"];
        }
    }["useIsMobile.useEffect"], []);
    return !!isMobile;
}
_s(useIsMobile, "D6B2cPXNCaIbeOx+abFr1uxLRM0=");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/chatService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "fetchUserDetails": (()=>fetchUserDetails),
    "fetchingMessageUsers": (()=>fetchingMessageUsers),
    "fetchingUnreadMessageUsers": (()=>fetchingUnreadMessageUsers),
    "fetchingprivateMessages": (()=>fetchingprivateMessages)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/axios.ts [app-client] (ecmascript)");
;
const fetchingprivateMessages = async (userId1, userId2)=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosInstance"].get(`chat/messages/private?userId1=${userId1}&userId2=${userId2}`, {
            withCredentials: true
        });
        return response.data;
    } catch (error) {
        console.error('Error fetching private messages:', error);
    }
};
const fetchingMessageUsers = async (userId, userType)=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosInstance"].get(`chat/messages/users?userId=${userId}&userType=${userType}`, {
            withCredentials: true
        });
        return response.data;
    } catch (error) {
        console.error('Error fetching message users:', error);
    }
};
const fetchUserDetails = async (userId, userType)=>{
    try {
        if (userType === 'class') {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosInstance"].get(`classes/details/${userId}`);
            return {
                id: response.data.id,
                firstName: response.data.firstName,
                lastName: response.data.lastName,
                email: response.data.email
            };
        }
    } catch (error) {
        console.error('Error fetching user details:', error);
        return null;
    }
};
const fetchingUnreadMessageUsers = async (userId, userType)=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosInstance"].get(`chat/messages/unread-users?userId=${userId}&userType=${userType}`, {
            withCredentials: true
        });
        return response.data;
    } catch (error) {
        console.error('Error fetching unread message users:', error);
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app-components/SharedChat.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>SharedChat)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/avatar.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$left$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowLeft$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/arrow-left.js [app-client] (ecmascript) <export default as ArrowLeft>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/search.js [app-client] (ecmascript) <export default as Search>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$send$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Send$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/send.js [app-client] (ecmascript) <export default as Send>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-client] (ecmascript) <export default as X>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$message$2d$square$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MessageSquare$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/message-square.js [app-client] (ecmascript) <export default as MessageSquare>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/users.js [app-client] (ecmascript) <export default as Users>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Check$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/check.js [app-client] (ecmascript) <export default as Check>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2d$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCheck$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/check-check.js [app-client] (ecmascript) <export default as CheckCheck>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCw$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/refresh-cw.js [app-client] (ecmascript) <export default as RefreshCw>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/socket.io-client/build/esm/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/socket.io-client/build/esm/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/date-fns/format.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$mobile$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-mobile.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$chatService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/chatService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$emoji$2d$picker$2d$react$2f$dist$2f$emoji$2d$picker$2d$react$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/emoji-picker-react/dist/emoji-picker-react.esm.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
;
;
;
;
;
function SharedChat({ userType, isAuthenticated, username, userId, initialSelectedUser, initialSelectedUserId, initialSelectedUserName }) {
    _s();
    const [privateMessages, setPrivateMessages] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [messageInput, setMessageInput] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [isUsernameSet, setIsUsernameSet] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(!!username);
    const [onlineUsers, setOnlineUsers] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [offlineMessageUsers, setOfflineMessageUsers] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [searchQuery, setSearchQuery] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [selectedUser, setSelectedUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(initialSelectedUser || null);
    const [selectedUserId, setSelectedUserId] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [currentRoomId, setCurrentRoomId] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [sidebarOpen, setSidebarOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [showEmojiPicker, setShowEmojiPicker] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [seenMessages, setSeenMessages] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(new Set());
    const [recipientIsViewing, setRecipientIsViewing] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [unreadMessageCounts, setUnreadMessageCounts] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(new Map());
    const [userFilter, setUserFilter] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('all');
    const [isRefreshing, setIsRefreshing] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [urlCleaned, setUrlCleaned] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const messagesEndRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const socketRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const emojiPickerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const isMobile = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$mobile$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useIsMobile"])();
    const loadMessages = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "SharedChat.useCallback[loadMessages]": async (currentUserId, targetUserId)=>{
            try {
                const data = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$chatService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchingprivateMessages"])(currentUserId, targetUserId);
                setPrivateMessages(data || []);
            } catch  {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Failed to load conversation history.');
            }
        }
    }["SharedChat.useCallback[loadMessages]"], []);
    const cleanUrl = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "SharedChat.useCallback[cleanUrl]": ()=>{
            if ((initialSelectedUserId || initialSelectedUserName) && !urlCleaned) {
                router.replace('/student/chat');
                setUrlCleaned(true);
            }
        }
    }["SharedChat.useCallback[cleanUrl]"], [
        initialSelectedUserId,
        initialSelectedUserName,
        urlCleaned,
        router
    ]);
    const handleEmojiClick = (emojiData)=>{
        setMessageInput((prev)=>prev + emojiData.emoji);
    };
    const toggleEmojiPicker = (e)=>{
        e.preventDefault();
        setShowEmojiPicker((prev)=>!prev);
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SharedChat.useEffect": ()=>{
            setIsUsernameSet(!!username);
        }
    }["SharedChat.useEffect"], [
        username
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SharedChat.useEffect": ()=>{
            const handleClickOutside = {
                "SharedChat.useEffect.handleClickOutside": (event)=>{
                    if (emojiPickerRef.current && !emojiPickerRef.current.contains(event.target)) {
                        setShowEmojiPicker(false);
                    }
                }
            }["SharedChat.useEffect.handleClickOutside"];
            if (showEmojiPicker) {
                document.addEventListener('mousedown', handleClickOutside);
            }
            return ({
                "SharedChat.useEffect": ()=>{
                    document.removeEventListener('mousedown', handleClickOutside);
                }
            })["SharedChat.useEffect"];
        }
    }["SharedChat.useEffect"], [
        showEmojiPicker
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SharedChat.useEffect": ()=>{
            if (isAuthenticated && isUsernameSet && username) {
                if (socketRef.current) {
                    socketRef.current.disconnect();
                }
                socketRef.current = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["io"])(("TURBOPACK compile-time value", "http://localhost:4005/"), {
                    withCredentials: true
                });
                socketRef.current.on('connect', {
                    "SharedChat.useEffect": ()=>{
                        socketRef.current?.emit('join', {
                            username,
                            userType,
                            userId
                        });
                        socketRef.current?.emit('getOnlineUsers');
                        socketRef.current?.emit('getUnreadCounts', {
                            userId,
                            userType
                        });
                        if (selectedUserId) {
                            socketRef.current?.emit('joinChatRoom', {
                                userId: userId,
                                recipientId: selectedUserId
                            });
                        }
                    }
                }["SharedChat.useEffect"]);
                socketRef.current.on('connect_error', {
                    "SharedChat.useEffect": (error)=>{
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(`Connection error: ${error.message}`);
                    }
                }["SharedChat.useEffect"]);
                socketRef.current.on('roomJoined', {
                    "SharedChat.useEffect": (data)=>{
                        setCurrentRoomId(data.roomId);
                    }
                }["SharedChat.useEffect"]);
                socketRef.current.on('roomLeft', {
                    "SharedChat.useEffect": ()=>{
                        setCurrentRoomId(null);
                    }
                }["SharedChat.useEffect"]);
                socketRef.current.on('messagesMarkedAsSeen', {
                    "SharedChat.useEffect": (data)=>{
                        if (data.byUserId === selectedUserId) {
                            setSeenMessages({
                                "SharedChat.useEffect": (prev)=>{
                                    const newSet = new Set(prev);
                                    data.messageIds.forEach({
                                        "SharedChat.useEffect": (messageId)=>{
                                            newSet.add(messageId);
                                        }
                                    }["SharedChat.useEffect"]);
                                    return newSet;
                                }
                            }["SharedChat.useEffect"]);
                        }
                    }
                }["SharedChat.useEffect"]);
                socketRef.current.on('privateMessage', {
                    "SharedChat.useEffect": (message)=>{
                        const isCurrentConversation = selectedUserId && (message.senderId === userId && message.recipientId === selectedUserId || message.senderId === selectedUserId && message.recipientId === userId);
                        if (isCurrentConversation) {
                            setPrivateMessages({
                                "SharedChat.useEffect": (prev)=>{
                                    const messageExists = prev.some({
                                        "SharedChat.useEffect.messageExists": (msg)=>msg.id === message.id
                                    }["SharedChat.useEffect.messageExists"]);
                                    if (messageExists) {
                                        return prev;
                                    }
                                    console.log('Adding message to current conversation:', message);
                                    return [
                                        ...prev,
                                        message
                                    ];
                                }
                            }["SharedChat.useEffect"]);
                        }
                        if (message.senderId !== userId && !offlineMessageUsers.some({
                            "SharedChat.useEffect": (user)=>user.userId === message.senderId
                        }["SharedChat.useEffect"])) {
                            setOfflineMessageUsers({
                                "SharedChat.useEffect": (prev)=>[
                                        ...prev,
                                        {
                                            username: message.sender,
                                            userId: message.senderId
                                        }
                                    ]
                            }["SharedChat.useEffect"]);
                        }
                    }
                }["SharedChat.useEffect"]);
                socketRef.current.on('onlineUsers', {
                    "SharedChat.useEffect": (users)=>{
                        const uniqueUsers = Array.from(new Map(users.map({
                            "SharedChat.useEffect.uniqueUsers": (user)=>[
                                    user.userId,
                                    user
                                ]
                        }["SharedChat.useEffect.uniqueUsers"])).values());
                        setOnlineUsers(uniqueUsers);
                    }
                }["SharedChat.useEffect"]);
                socketRef.current.on('userStartedViewing', {
                    "SharedChat.useEffect": (data)=>{
                        if (data.viewerId === selectedUserId) {
                            setRecipientIsViewing(true);
                        }
                    }
                }["SharedChat.useEffect"]);
                socketRef.current.on('userStoppedViewing', {
                    "SharedChat.useEffect": (data)=>{
                        if (data.viewerId === selectedUserId) {
                            setRecipientIsViewing(false);
                        }
                    }
                }["SharedChat.useEffect"]);
                socketRef.current.on('unreadCountUpdate', {
                    "SharedChat.useEffect": (data)=>{
                        setUnreadMessageCounts({
                            "SharedChat.useEffect": (prev)=>{
                                const newMap = new Map(prev);
                                if (data.unreadCount === 0) {
                                    newMap.delete(data.senderId);
                                } else {
                                    newMap.set(data.senderId, data.unreadCount);
                                }
                                return newMap;
                            }
                        }["SharedChat.useEffect"]);
                    }
                }["SharedChat.useEffect"]);
                socketRef.current.on('unreadCountsData', {
                    "SharedChat.useEffect": (data)=>{
                        const unreadCountsMap = new Map();
                        data.forEach({
                            "SharedChat.useEffect": (user)=>{
                                unreadCountsMap.set(user.userId, user.unreadCount);
                            }
                        }["SharedChat.useEffect"]);
                        setUnreadMessageCounts(unreadCountsMap);
                    }
                }["SharedChat.useEffect"]);
                socketRef.current.on('updateMessageUsers', {
                    "SharedChat.useEffect": (data)=>{
                        setOfflineMessageUsers({
                            "SharedChat.useEffect": (prev)=>{
                                if (!prev.some({
                                    "SharedChat.useEffect": (user)=>user.userId === data.userId
                                }["SharedChat.useEffect"])) {
                                    return [
                                        ...prev,
                                        {
                                            username: data.username,
                                            userId: data.userId
                                        }
                                    ];
                                }
                                return prev;
                            }
                        }["SharedChat.useEffect"]);
                    }
                }["SharedChat.useEffect"]);
                socketRef.current.on('error', {
                    "SharedChat.useEffect": (error)=>{
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error.message);
                    }
                }["SharedChat.useEffect"]);
                return ({
                    "SharedChat.useEffect": ()=>{
                        if (currentRoomId && userId && selectedUserId && socketRef.current) {
                            socketRef.current.emit('leaveChatRoom', {
                                userId: userId,
                                recipientId: selectedUserId
                            });
                        }
                        socketRef.current?.off('connect');
                        socketRef.current?.off('connect_error');
                        socketRef.current?.off('privateMessage');
                        socketRef.current?.off('onlineUsers');
                        socketRef.current?.off('error');
                        socketRef.current?.off('roomJoined');
                        socketRef.current?.off('roomLeft');
                        socketRef.current?.off('messagesMarkedAsSeen');
                        socketRef.current?.off('userStartedViewing');
                        socketRef.current?.off('userStoppedViewing');
                        socketRef.current?.off('unreadCountUpdate');
                        socketRef.current?.off('unreadCountsData');
                        socketRef.current?.off('updateMessageUsers');
                        socketRef.current?.disconnect();
                    }
                })["SharedChat.useEffect"];
            }
        }
    }["SharedChat.useEffect"], [
        username,
        isUsernameSet,
        isAuthenticated,
        userType,
        userId,
        selectedUser,
        selectedUserId
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SharedChat.useEffect": ()=>{
            if (selectedUserId && userId && isUsernameSet) {
                loadMessages(userId, selectedUserId);
                const refreshInterval = setInterval({
                    "SharedChat.useEffect.refreshInterval": ()=>{
                        loadMessages(userId, selectedUserId);
                    }
                }["SharedChat.useEffect.refreshInterval"], 60000);
                return ({
                    "SharedChat.useEffect": ()=>clearInterval(refreshInterval)
                })["SharedChat.useEffect"];
            }
        }
    }["SharedChat.useEffect"], [
        selectedUserId,
        userId,
        isUsernameSet,
        loadMessages
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SharedChat.useEffect": ()=>{
            const fetchMessageUsers = {
                "SharedChat.useEffect.fetchMessageUsers": async ()=>{
                    if (isUsernameSet && userId) {
                        try {
                            const data = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$chatService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchingMessageUsers"])(userId, userType);
                            const uniqueUsers = Array.from(new Map(data.map({
                                "SharedChat.useEffect.fetchMessageUsers.uniqueUsers": (user)=>[
                                        user.userId,
                                        user
                                    ]
                            }["SharedChat.useEffect.fetchMessageUsers.uniqueUsers"])).values());
                            setOfflineMessageUsers(uniqueUsers);
                        } catch  {
                        // Silently handle error
                        }
                    }
                }
            }["SharedChat.useEffect.fetchMessageUsers"];
            fetchMessageUsers();
            const refreshInterval = setInterval(fetchMessageUsers, 30000);
            return ({
                "SharedChat.useEffect": ()=>clearInterval(refreshInterval)
            })["SharedChat.useEffect"];
        }
    }["SharedChat.useEffect"], [
        isUsernameSet,
        userId,
        userType
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SharedChat.useEffect": ()=>{
            messagesEndRef.current?.scrollIntoView({
                behavior: 'smooth'
            });
        }
    }["SharedChat.useEffect"], [
        privateMessages
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SharedChat.useEffect": ()=>{
            if ((initialSelectedUser || initialSelectedUserId) && isMobile) {
                setSidebarOpen(false);
            } else {
                setSidebarOpen(!isMobile);
            }
        }
    }["SharedChat.useEffect"], [
        initialSelectedUser,
        initialSelectedUserId,
        isMobile
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SharedChat.useEffect": ()=>{
            const handleBeforeUnload = {
                "SharedChat.useEffect.handleBeforeUnload": ()=>{
                    if (selectedUserId && selectedUser) {
                        sessionStorage.setItem('currentChatUser', JSON.stringify({
                            userId: selectedUserId,
                            userName: selectedUser
                        }));
                    }
                    if (currentRoomId && userId && selectedUserId && socketRef.current) {
                        socketRef.current.emit('leaveChatRoom', {
                            userId: userId,
                            recipientId: selectedUserId
                        });
                    }
                }
            }["SharedChat.useEffect.handleBeforeUnload"];
            const handleLoad = {
                "SharedChat.useEffect.handleLoad": ()=>{
                    const savedChatUser = sessionStorage.getItem('currentChatUser');
                    if (savedChatUser && !selectedUserId && !initialSelectedUserId) {
                        try {
                            const { userId: restoredUserId, userName } = JSON.parse(savedChatUser);
                            setSelectedUser(userName);
                            setSelectedUserId(restoredUserId);
                            setOfflineMessageUsers({
                                "SharedChat.useEffect.handleLoad": (prev)=>{
                                    const userExists = prev.some({
                                        "SharedChat.useEffect.handleLoad.userExists": (user)=>user.userId === restoredUserId
                                    }["SharedChat.useEffect.handleLoad.userExists"]);
                                    if (!userExists) {
                                        return [
                                            ...prev,
                                            {
                                                username: userName,
                                                userId: restoredUserId
                                            }
                                        ];
                                    }
                                    return prev;
                                }
                            }["SharedChat.useEffect.handleLoad"]);
                            if (userId && restoredUserId) {
                                loadMessages(userId, restoredUserId);
                            }
                        } catch (error) {
                            console.error('Error restoring chat state:', error);
                        }
                    }
                }
            }["SharedChat.useEffect.handleLoad"];
            window.addEventListener('beforeunload', handleBeforeUnload);
            const isPageRefresh = sessionStorage.getItem('currentChatUser');
            if (isPageRefresh) {
                handleLoad();
            }
            return ({
                "SharedChat.useEffect": ()=>{
                    window.removeEventListener('beforeunload', handleBeforeUnload);
                }
            })["SharedChat.useEffect"];
        }
    }["SharedChat.useEffect"], [
        selectedUserId,
        selectedUser,
        initialSelectedUserId,
        userId,
        currentRoomId
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SharedChat.useEffect": ()=>{
            if (selectedUserId && userId && privateMessages.length > 0) {
                const unseenMessages = privateMessages.filter({
                    "SharedChat.useEffect.unseenMessages": (msg)=>msg.sender === selectedUser && msg.recipient === username && !seenMessages.has(msg.id)
                }["SharedChat.useEffect.unseenMessages"]);
                if (unseenMessages.length > 0) {
                    const unseenMessageIds = unseenMessages.map({
                        "SharedChat.useEffect.unseenMessageIds": (msg)=>msg.id
                    }["SharedChat.useEffect.unseenMessageIds"]);
                    socketRef.current?.emit('markMessagesAsSeen', {
                        senderId: selectedUserId,
                        recipientId: userId,
                        messageIds: unseenMessageIds
                    });
                }
            }
        }
    }["SharedChat.useEffect"], [
        selectedUserId,
        userId,
        privateMessages,
        selectedUser,
        username,
        seenMessages
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SharedChat.useEffect": ()=>{
            if (selectedUserId && userId && socketRef.current && socketRef.current.connected) {
                socketRef.current.emit('joinChatRoom', {
                    userId: userId,
                    recipientId: selectedUserId
                });
            }
            return ({
                "SharedChat.useEffect": ()=>{
                    if (currentRoomId && userId && selectedUserId && socketRef.current) {
                        socketRef.current.emit('leaveChatRoom', {
                            userId: userId,
                            recipientId: selectedUserId
                        });
                    }
                }
            })["SharedChat.useEffect"];
        }
    }["SharedChat.useEffect"], [
        selectedUserId,
        userId,
        currentRoomId
    ]);
    const handleSendMessage = async (e)=>{
        e.preventDefault();
        if (!messageInput.trim() || !selectedUser || !userId) {
            return;
        }
        const messageText = messageInput.trim();
        setMessageInput('');
        try {
            if (!selectedUserId) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('No recipient selected. Please select a user first.');
                setMessageInput(messageText);
                return;
            }
            const recipientType = userType === 'student' ? 'class' : 'student';
            const messageData = {
                text: messageText,
                senderId: userId,
                recipientId: selectedUserId,
                senderType: userType,
                recipientType: recipientType,
                recipientUsername: selectedUser
            };
            socketRef.current?.emit('sendPrivateMessage', messageData);
            cleanUrl();
            if (!isUserOnline(selectedUser)) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].info(`${selectedUser} is offline. Your message will be delivered when they come online.`);
            }
        } catch  {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Failed to send message. Please try again.');
            setMessageInput(messageText);
        }
    };
    const handleUserSelect = async (user)=>{
        leaveCurrentRoom();
        setPrivateMessages([]);
        setSelectedUser(user.username);
        setSeenMessages(new Set());
        setRecipientIsViewing(false);
        const targetUserId = user.userId || user.username;
        setSelectedUserId(targetUserId);
        setUnreadMessageCounts((prev)=>{
            const newMap = new Map(prev);
            newMap.delete(targetUserId);
            return newMap;
        });
        cleanUrl();
        if (isMobile) {
            setSidebarOpen(false);
        }
        if (targetUserId && userId) {
            loadMessages(userId, targetUserId);
        }
    };
    const leaveCurrentRoom = ()=>{
        if (currentRoomId && userId && selectedUserId && socketRef.current) {
            socketRef.current.emit('leaveChatRoom', {
                userId: userId,
                recipientId: selectedUserId
            });
        }
    };
    const handleBackToSidebar = ()=>{
        leaveCurrentRoom();
        setSidebarOpen(true);
        if (isMobile) {
            setSelectedUser(null);
            setSelectedUserId(null);
        }
    };
    const formatTime = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "SharedChat.useMemo[formatTime]": ()=>{
            return ({
                "SharedChat.useMemo[formatTime]": (timestamp)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(new Date(timestamp), 'h:mm a')
            })["SharedChat.useMemo[formatTime]"];
        }
    }["SharedChat.useMemo[formatTime]"], []);
    const allAvailableUsers = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "SharedChat.useMemo[allAvailableUsers]": ()=>{
            const userMap = new Map();
            offlineMessageUsers.forEach({
                "SharedChat.useMemo[allAvailableUsers]": (messageUser)=>{
                    userMap.set(messageUser.userId, {
                        username: messageUser.username,
                        userType: userType === 'student' ? 'class' : 'student',
                        userId: messageUser.userId
                    });
                }
            }["SharedChat.useMemo[allAvailableUsers]"]);
            onlineUsers.forEach({
                "SharedChat.useMemo[allAvailableUsers]": (onlineUser)=>{
                    if (onlineUser.username !== username && onlineUser.userType !== userType) {
                        userMap.set(onlineUser.userId || onlineUser.username, {
                            username: onlineUser.username,
                            userType: onlineUser.userType,
                            userId: onlineUser.userId || onlineUser.username
                        });
                    }
                }
            }["SharedChat.useMemo[allAvailableUsers]"]);
            if (initialSelectedUserId && initialSelectedUserName) {
                userMap.set(initialSelectedUserId, {
                    username: initialSelectedUserName,
                    userType: userType === 'student' ? 'class' : 'student',
                    userId: initialSelectedUserId
                });
            }
            const filteredUsers = Array.from(userMap.values()).filter({
                "SharedChat.useMemo[allAvailableUsers].filteredUsers": (user)=>{
                    const matchesSearch = user.username.toLowerCase().includes(searchQuery.toLowerCase());
                    const isNotCurrentUser = user.username !== username;
                    const hasDifferentUserType = user.userType !== userType;
                    return matchesSearch && isNotCurrentUser && hasDifferentUserType;
                }
            }["SharedChat.useMemo[allAvailableUsers].filteredUsers"]);
            return filteredUsers;
        }
    }["SharedChat.useMemo[allAvailableUsers]"], [
        offlineMessageUsers,
        onlineUsers,
        searchQuery,
        username,
        userType,
        initialSelectedUserId,
        initialSelectedUserName
    ]);
    const isUserOnline = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "SharedChat.useMemo[isUserOnline]": ()=>{
            const onlineUserIds = new Set(onlineUsers.map({
                "SharedChat.useMemo[isUserOnline]": (user)=>user.userId
            }["SharedChat.useMemo[isUserOnline]"]));
            return ({
                "SharedChat.useMemo[isUserOnline]": (username)=>{
                    const user = allAvailableUsers.find({
                        "SharedChat.useMemo[isUserOnline].user": (u)=>u.username === username
                    }["SharedChat.useMemo[isUserOnline].user"]);
                    if (user) {
                        return onlineUserIds.has(user.userId);
                    }
                    if (username === initialSelectedUserName && initialSelectedUserId) {
                        return onlineUserIds.has(initialSelectedUserId);
                    }
                    const onlineUser = onlineUsers.find({
                        "SharedChat.useMemo[isUserOnline].onlineUser": (u)=>u.username === username
                    }["SharedChat.useMemo[isUserOnline].onlineUser"]);
                    return !!onlineUser;
                }
            })["SharedChat.useMemo[isUserOnline]"];
        }
    }["SharedChat.useMemo[isUserOnline]"], [
        onlineUsers,
        allAvailableUsers,
        initialSelectedUserName,
        initialSelectedUserId
    ]);
    const unreadUsersCount = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "SharedChat.useMemo[unreadUsersCount]": ()=>{
            return allAvailableUsers.filter({
                "SharedChat.useMemo[unreadUsersCount]": (user)=>{
                    const userIdToCheck = user.userId;
                    return unreadMessageCounts.has(userIdToCheck);
                }
            }["SharedChat.useMemo[unreadUsersCount]"]).length;
        }
    }["SharedChat.useMemo[unreadUsersCount]"], [
        allAvailableUsers,
        unreadMessageCounts
    ]);
    const filteredUsers = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "SharedChat.useMemo[filteredUsers]": ()=>{
            if (userFilter === 'unread') {
                return allAvailableUsers.filter({
                    "SharedChat.useMemo[filteredUsers]": (user)=>{
                        const userIdToCheck = user.userId;
                        return unreadMessageCounts.has(userIdToCheck);
                    }
                }["SharedChat.useMemo[filteredUsers]"]);
            }
            return allAvailableUsers;
        }
    }["SharedChat.useMemo[filteredUsers]"], [
        allAvailableUsers,
        userFilter,
        unreadMessageCounts
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SharedChat.useEffect": ()=>{
            if (initialSelectedUserId && !selectedUserId && userId && isAuthenticated) {
                const initializeUser = {
                    "SharedChat.useEffect.initializeUser": async ()=>{
                        let displayName = initialSelectedUserName;
                        if (!displayName) {
                            const targetUserType = userType === 'student' ? 'class' : 'student';
                            const userDetails = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$chatService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchUserDetails"])(initialSelectedUserId, targetUserType);
                            if (userDetails) {
                                displayName = `${userDetails.firstName} ${userDetails.lastName}`.trim();
                            } else {
                                displayName = `User ${initialSelectedUserId.slice(0, 8)}`;
                            }
                        }
                        setSelectedUser(displayName);
                        setSelectedUserId(initialSelectedUserId);
                        setOfflineMessageUsers({
                            "SharedChat.useEffect.initializeUser": (prev)=>{
                                const userExists = prev.some({
                                    "SharedChat.useEffect.initializeUser.userExists": (user)=>user.userId === initialSelectedUserId
                                }["SharedChat.useEffect.initializeUser.userExists"]);
                                if (!userExists) {
                                    return [
                                        ...prev,
                                        {
                                            username: displayName,
                                            userId: initialSelectedUserId
                                        }
                                    ];
                                }
                                return prev;
                            }
                        }["SharedChat.useEffect.initializeUser"]);
                        loadMessages(userId, initialSelectedUserId);
                    }
                }["SharedChat.useEffect.initializeUser"];
                initializeUser();
            }
        }
    }["SharedChat.useEffect"], [
        initialSelectedUserId,
        initialSelectedUserName,
        selectedUserId,
        userId,
        isAuthenticated,
        userType
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SharedChat.useEffect": ()=>{
            if (initialSelectedUser && !selectedUserId && allAvailableUsers.length > 0 && !initialSelectedUserId && userId) {
                const foundUser = allAvailableUsers.find({
                    "SharedChat.useEffect.foundUser": (user)=>user.username === initialSelectedUser
                }["SharedChat.useEffect.foundUser"]);
                if (foundUser) {
                    setSelectedUser(foundUser.username);
                    setSelectedUserId(foundUser.userId);
                    loadMessages(userId, foundUser.userId);
                }
            }
        }
    }["SharedChat.useEffect"], [
        initialSelectedUser,
        allAvailableUsers,
        selectedUserId,
        userId,
        initialSelectedUserId
    ]);
    if (!isAuthenticated) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center justify-center min-h-[calc(100vh-64px)] bg-background p-4",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "w-full max-w-md p-4 sm:p-6 bg-card rounded-lg shadow-lg",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        className: "text-xl sm:text-2xl font-bold mb-4 sm:mb-6 text-center",
                        children: "Login Required"
                    }, void 0, false, {
                        fileName: "[project]/src/app-components/SharedChat.tsx",
                        lineNumber: 601,
                        columnNumber: 21
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex flex-col items-center justify-center text-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-center text-muted-foreground mb-4 sm:mb-6 text-sm sm:text-base",
                                children: "Please login as a student to access the chat feature."
                            }, void 0, false, {
                                fileName: "[project]/src/app-components/SharedChat.tsx",
                                lineNumber: 603,
                                columnNumber: 25
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                onClick: ()=>router.push('/'),
                                className: "w-full bg-orange-500 hover:bg-orange-600",
                                children: "Go to Login"
                            }, void 0, false, {
                                fileName: "[project]/src/app-components/SharedChat.tsx",
                                lineNumber: 606,
                                columnNumber: 25
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app-components/SharedChat.tsx",
                        lineNumber: 602,
                        columnNumber: 21
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app-components/SharedChat.tsx",
                lineNumber: 600,
                columnNumber: 17
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app-components/SharedChat.tsx",
            lineNumber: 599,
            columnNumber: 13
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex h-[calc(100vh-64px)] bg-background text-foreground relative overflow-hidden",
        children: [
            sidebarOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("aside", {
                className: `border-r border-gray-200 flex flex-col bg-gradient-to-b from-white to-gray-50 shadow-lg ${isMobile ? 'absolute inset-0 z-50 w-full' : 'relative w-80 min-w-80 lg:w-96 lg:min-w-96'}`,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "p-4 flex items-center justify-between border-b border-gray-200 bg-white/80 backdrop-blur-sm",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center gap-3",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "relative",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        src: "/logo.png",
                                        alt: "Uest Logo",
                                        width: isMobile ? 100 : 140,
                                        height: isMobile ? 25 : 35,
                                        className: "object-contain cursor-pointer hover:opacity-80 transition-all duration-300 hover:scale-105",
                                        onClick: ()=>router.push('/')
                                    }, void 0, false, {
                                        fileName: "[project]/src/app-components/SharedChat.tsx",
                                        lineNumber: 628,
                                        columnNumber: 33
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app-components/SharedChat.tsx",
                                    lineNumber: 627,
                                    columnNumber: 29
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app-components/SharedChat.tsx",
                                lineNumber: 626,
                                columnNumber: 25
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center gap-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: "outline",
                                        size: isMobile ? "sm" : "default",
                                        disabled: isRefreshing,
                                        onClick: async ()=>{
                                            setIsRefreshing(true);
                                            try {
                                                socketRef.current?.emit('getOnlineUsers');
                                                socketRef.current?.emit('getUnreadCounts', {
                                                    userId,
                                                    userType
                                                });
                                                if (isUsernameSet && userId) {
                                                    const data = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$chatService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchingMessageUsers"])(userId, userType);
                                                    const uniqueUsers = Array.from(new Map(data.map((user)=>[
                                                            user.userId,
                                                            user
                                                        ])).values());
                                                    const newUsers = uniqueUsers;
                                                    if (selectedUserId && selectedUser) {
                                                        const selectedUserExists = newUsers.some((user)=>user.userId === selectedUserId);
                                                        if (!selectedUserExists) {
                                                            newUsers.push({
                                                                username: selectedUser,
                                                                userId: selectedUserId
                                                            });
                                                        }
                                                    }
                                                    setOfflineMessageUsers(newUsers);
                                                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success('Chat list refreshed!');
                                                }
                                            } catch  {
                                                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Failed to refresh chat list');
                                            } finally{
                                                setIsRefreshing(false);
                                            }
                                        },
                                        className: `bg-white/90 border border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 rounded-xl font-medium transition-all duration-300 shadow-sm hover:shadow-md disabled:opacity-50 ${isMobile ? 'px-3 py-2 text-xs' : 'px-4 py-2 text-sm'}`,
                                        title: "Refresh chat list",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCw$3e$__["RefreshCw"], {
                                                className: `${isMobile ? 'h-4 w-4' : 'h-4 w-4'} ${isRefreshing ? 'animate-spin' : ''}`
                                            }, void 0, false, {
                                                fileName: "[project]/src/app-components/SharedChat.tsx",
                                                lineNumber: 675,
                                                columnNumber: 33
                                            }, this),
                                            !isMobile && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "ml-2",
                                                children: isRefreshing ? 'Refreshing...' : 'Refresh'
                                            }, void 0, false, {
                                                fileName: "[project]/src/app-components/SharedChat.tsx",
                                                lineNumber: 676,
                                                columnNumber: 47
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app-components/SharedChat.tsx",
                                        lineNumber: 639,
                                        columnNumber: 29
                                    }, this),
                                    isMobile && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: "ghost",
                                        size: "icon",
                                        className: "rounded-xl h-10 w-10 hover:bg-gray-100 transition-all duration-300",
                                        onClick: ()=>setSidebarOpen(false),
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                                            className: "h-5 w-5"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app-components/SharedChat.tsx",
                                            lineNumber: 685,
                                            columnNumber: 37
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/app-components/SharedChat.tsx",
                                        lineNumber: 679,
                                        columnNumber: 33
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app-components/SharedChat.tsx",
                                lineNumber: 638,
                                columnNumber: 25
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app-components/SharedChat.tsx",
                        lineNumber: 625,
                        columnNumber: 21
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: `${isMobile ? 'p-3' : 'p-4'} bg-white/50`,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "relative group",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__["Search"], {
                                    className: `absolute left-4 top-1/2 -translate-y-1/2 text-gray-400 group-focus-within:text-gray-600 transition-colors duration-300 ${isMobile ? 'h-4 w-4' : 'h-5 w-5'}`
                                }, void 0, false, {
                                    fileName: "[project]/src/app-components/SharedChat.tsx",
                                    lineNumber: 693,
                                    columnNumber: 29
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                    placeholder: "Search conversations...",
                                    className: `pl-12 pr-4 bg-white border border-gray-300 rounded-xl text-gray-900 placeholder:text-gray-400 focus:border-black focus:ring-2 focus:ring-gray-100 transition-all duration-300 shadow-sm hover:shadow-md ${isMobile ? 'py-2.5 text-sm' : 'py-3 text-base'}`,
                                    value: searchQuery,
                                    onChange: (e)=>setSearchQuery(e.target.value)
                                }, void 0, false, {
                                    fileName: "[project]/src/app-components/SharedChat.tsx",
                                    lineNumber: 694,
                                    columnNumber: 29
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app-components/SharedChat.tsx",
                            lineNumber: 692,
                            columnNumber: 25
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app-components/SharedChat.tsx",
                        lineNumber: 691,
                        columnNumber: 21
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: `${isMobile ? 'px-3 pb-3' : 'px-4 pb-4'}`,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "bg-gray-100/80 rounded-2xl p-1.5 shadow-inner",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex gap-1",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: ()=>setUserFilter('all'),
                                        className: `relative flex-1 flex items-center justify-center gap-2 rounded-xl font-semibold transition-all duration-300 transform hover:scale-[1.02] ${userFilter === 'all' ? 'bg-white text-gray-900 shadow-lg border border-gray-200' : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'} ${isMobile ? 'px-3 py-2.5 text-xs' : 'px-4 py-3 text-sm'}`,
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: `flex items-center gap-2 ${isMobile ? 'flex-col gap-1' : ''}`,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__["Users"], {
                                                    className: `${isMobile ? 'h-3 w-3' : 'h-4 w-4'}`
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app-components/SharedChat.tsx",
                                                    lineNumber: 716,
                                                    columnNumber: 41
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: isMobile ? 'text-[10px] leading-tight' : '',
                                                    children: isMobile ? 'All' : 'All Users'
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app-components/SharedChat.tsx",
                                                    lineNumber: 717,
                                                    columnNumber: 41
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: `inline-flex items-center justify-center min-w-[20px] h-5 px-1.5 text-xs font-bold rounded-full transition-colors ${userFilter === 'all' ? 'bg-black text-white' : 'bg-gray-200 text-gray-600'}`,
                                                    children: allAvailableUsers.length
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app-components/SharedChat.tsx",
                                                    lineNumber: 720,
                                                    columnNumber: 41
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app-components/SharedChat.tsx",
                                            lineNumber: 715,
                                            columnNumber: 37
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/app-components/SharedChat.tsx",
                                        lineNumber: 707,
                                        columnNumber: 33
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: ()=>setUserFilter('unread'),
                                        className: `relative flex-1 flex items-center justify-center gap-2 rounded-xl font-semibold transition-all duration-300 transform hover:scale-[1.02] ${userFilter === 'unread' ? 'bg-white text-gray-900 shadow-lg border border-gray-200' : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'} ${isMobile ? 'px-3 py-2.5 text-xs' : 'px-4 py-3 text-sm'}`,
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: `flex items-center gap-2 ${isMobile ? 'flex-col gap-1' : ''}`,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "relative",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$message$2d$square$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MessageSquare$3e$__["MessageSquare"], {
                                                            className: `${isMobile ? 'h-3 w-3' : 'h-4 w-4'}`
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app-components/SharedChat.tsx",
                                                            lineNumber: 740,
                                                            columnNumber: 45
                                                        }, this),
                                                        unreadUsersCount > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "absolute -top-1 -right-1 bg-red-500 text-white text-[8px] rounded-full h-2 w-2 animate-pulse"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app-components/SharedChat.tsx",
                                                            lineNumber: 742,
                                                            columnNumber: 49
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app-components/SharedChat.tsx",
                                                    lineNumber: 739,
                                                    columnNumber: 41
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: isMobile ? 'text-[10px] leading-tight' : '',
                                                    children: isMobile ? 'Unread' : 'Unread Only'
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app-components/SharedChat.tsx",
                                                    lineNumber: 745,
                                                    columnNumber: 41
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: `inline-flex items-center justify-center min-w-[20px] h-5 px-1.5 text-xs font-bold rounded-full transition-colors ${userFilter === 'unread' ? unreadUsersCount > 0 ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-600' : unreadUsersCount > 0 ? 'bg-red-500 text-white animate-pulse' : 'bg-gray-200 text-gray-600'}`,
                                                    children: unreadUsersCount
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app-components/SharedChat.tsx",
                                                    lineNumber: 748,
                                                    columnNumber: 41
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app-components/SharedChat.tsx",
                                            lineNumber: 738,
                                            columnNumber: 37
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/app-components/SharedChat.tsx",
                                        lineNumber: 730,
                                        columnNumber: 33
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app-components/SharedChat.tsx",
                                lineNumber: 706,
                                columnNumber: 29
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app-components/SharedChat.tsx",
                            lineNumber: 705,
                            columnNumber: 25
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app-components/SharedChat.tsx",
                        lineNumber: 704,
                        columnNumber: 21
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex-1 overflow-y-auto overscroll-contain",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: `space-y-2 ${isMobile ? 'px-2 pb-2' : 'px-3 pb-3'}`,
                            children: filteredUsers.length > 0 || selectedUser && initialSelectedUserId ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                children: [
                                    selectedUser && initialSelectedUserId && !filteredUsers.find((u)=>u.userId === initialSelectedUserId) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: `group transition-all duration-300 cursor-pointer rounded-2xl hover:shadow-lg hover:scale-[1.02] ${isMobile ? 'p-3' : 'p-4'} ${selectedUserId === initialSelectedUserId ? 'bg-gradient-to-r from-black to-gray-800 text-white shadow-xl border border-gray-700' : 'bg-white border border-gray-200 hover:bg-gray-50 hover:border-gray-300'}`,
                                        onClick: ()=>handleUserSelect({
                                                username: selectedUser,
                                                userType: userType === 'student' ? 'class' : 'student',
                                                userId: initialSelectedUserId
                                            }),
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex gap-3 items-center",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "relative",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Avatar"], {
                                                            className: `border-2 shadow-md transition-all duration-300 group-hover:scale-110 ${isMobile ? 'h-10 w-10' : 'h-12 w-12'} border-white/50`,
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AvatarFallback"], {
                                                                className: `font-semibold transition-colors duration-300 ${isMobile ? 'text-xs' : 'text-sm'} bg-white text-black`,
                                                                children: selectedUser?.substring(0, 2).toUpperCase()
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app-components/SharedChat.tsx",
                                                                lineNumber: 787,
                                                                columnNumber: 53
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app-components/SharedChat.tsx",
                                                            lineNumber: 786,
                                                            columnNumber: 49
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: `absolute -bottom-0.5 -right-0.5 rounded-full border-2 flex items-center justify-center transition-all duration-300 ${isMobile ? 'h-4 w-4' : 'h-5 w-5'} border-white ${isUserOnline(selectedUser || '') ? 'bg-green-500 shadow-lg' : 'bg-gray-400'}`,
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: `rounded-full transition-all duration-300 ${isMobile ? 'h-2 w-2' : 'h-2.5 w-2.5'} ${isUserOnline(selectedUser || '') ? 'bg-green-300 animate-pulse' : 'bg-gray-300'}`
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app-components/SharedChat.tsx",
                                                                lineNumber: 794,
                                                                columnNumber: 53
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app-components/SharedChat.tsx",
                                                            lineNumber: 791,
                                                            columnNumber: 49
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app-components/SharedChat.tsx",
                                                    lineNumber: 785,
                                                    columnNumber: 45
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex-1 min-w-0",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex justify-between items-center",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                                className: `font-semibold truncate transition-colors duration-300 ${isMobile ? 'text-sm' : 'text-base'} text-white`,
                                                                children: selectedUser
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app-components/SharedChat.tsx",
                                                                lineNumber: 801,
                                                                columnNumber: 53
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app-components/SharedChat.tsx",
                                                            lineNumber: 800,
                                                            columnNumber: 49
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex items-center gap-2 mt-1",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: `inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium transition-colors duration-300 bg-white/20 text-white`,
                                                                    children: "Tutor"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app-components/SharedChat.tsx",
                                                                    lineNumber: 806,
                                                                    columnNumber: 53
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: `inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium transition-colors duration-300 bg-white/20 text-white`,
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            className: `w-2 h-2 rounded-full ${isUserOnline(selectedUser || '') ? 'bg-green-500' : 'bg-gray-400'}`
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app-components/SharedChat.tsx",
                                                                            lineNumber: 810,
                                                                            columnNumber: 57
                                                                        }, this),
                                                                        isUserOnline(selectedUser || '') ? 'Online' : 'Offline'
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app-components/SharedChat.tsx",
                                                                    lineNumber: 809,
                                                                    columnNumber: 53
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app-components/SharedChat.tsx",
                                                            lineNumber: 805,
                                                            columnNumber: 49
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app-components/SharedChat.tsx",
                                                    lineNumber: 799,
                                                    columnNumber: 45
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app-components/SharedChat.tsx",
                                            lineNumber: 784,
                                            columnNumber: 41
                                        }, this)
                                    }, initialSelectedUserId, false, {
                                        fileName: "[project]/src/app-components/SharedChat.tsx",
                                        lineNumber: 771,
                                        columnNumber: 37
                                    }, this),
                                    filteredUsers.map((user)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: `group transition-all duration-300 cursor-pointer rounded-2xl hover:shadow-lg hover:scale-[1.02] ${isMobile ? 'p-3' : 'p-4'} ${selectedUser === user.username ? 'bg-gradient-to-r from-black to-gray-800 text-white shadow-xl border border-gray-700' : 'bg-white border border-gray-200 hover:bg-gray-50 hover:border-gray-300'}`,
                                            onClick: ()=>handleUserSelect(user),
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex gap-3 items-center",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "relative",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Avatar"], {
                                                                className: `border-2 shadow-md transition-all duration-300 group-hover:scale-110 ${isMobile ? 'h-10 w-10' : 'h-12 w-12'} ${selectedUser === user.username ? 'border-white/50' : 'border-gray-300 group-hover:border-gray-400'}`,
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AvatarFallback"], {
                                                                    className: `font-semibold transition-colors duration-300 ${isMobile ? 'text-xs' : 'text-sm'} ${selectedUser === user.username ? 'bg-white text-black' : 'bg-gradient-to-br from-gray-100 to-gray-200 text-gray-700 group-hover:from-gray-200 group-hover:to-gray-300'}`,
                                                                    children: user.username.substring(0, 2).toUpperCase()
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app-components/SharedChat.tsx",
                                                                    lineNumber: 835,
                                                                    columnNumber: 53
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app-components/SharedChat.tsx",
                                                                lineNumber: 830,
                                                                columnNumber: 49
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: `absolute -bottom-0.5 -right-0.5 rounded-full border-2 flex items-center justify-center transition-all duration-300 ${isMobile ? 'h-4 w-4' : 'h-5 w-5'} ${selectedUser === user.username ? 'border-white' : 'border-white'} ${isUserOnline(user.username) ? 'bg-green-500 shadow-lg' : 'bg-gray-400'}`,
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: `rounded-full transition-all duration-300 ${isMobile ? 'h-2 w-2' : 'h-2.5 w-2.5'} ${isUserOnline(user.username) ? 'bg-green-300 animate-pulse' : 'bg-gray-300'}`
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app-components/SharedChat.tsx",
                                                                    lineNumber: 846,
                                                                    columnNumber: 53
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app-components/SharedChat.tsx",
                                                                lineNumber: 842,
                                                                columnNumber: 49
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app-components/SharedChat.tsx",
                                                        lineNumber: 829,
                                                        columnNumber: 45
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex-1 min-w-0",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "flex justify-between items-center",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                                        className: `font-semibold truncate transition-colors duration-300 ${isMobile ? 'text-sm' : 'text-base'} ${selectedUser === user.username ? 'text-white' : 'text-gray-900 group-hover:text-black'}`,
                                                                        children: user.username
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app-components/SharedChat.tsx",
                                                                        lineNumber: 853,
                                                                        columnNumber: 53
                                                                    }, this),
                                                                    unreadMessageCounts.has(user.userId) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "bg-gradient-to-r from-red-500 to-red-600 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center font-bold shadow-lg animate-pulse",
                                                                        children: unreadMessageCounts.get(user.userId)
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app-components/SharedChat.tsx",
                                                                        lineNumber: 860,
                                                                        columnNumber: 57
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app-components/SharedChat.tsx",
                                                                lineNumber: 852,
                                                                columnNumber: 49
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "flex items-center gap-2 mt-1",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: `inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium transition-colors duration-300 ${selectedUser === user.username ? 'bg-white/20 text-white' : user.userType === 'student' ? 'bg-gray-100 text-gray-700 group-hover:bg-gray-200' : 'bg-gray-100 text-gray-700 group-hover:bg-gray-200'}`,
                                                                        children: user.userType === 'student' ? 'Student' : 'Tutor'
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app-components/SharedChat.tsx",
                                                                        lineNumber: 866,
                                                                        columnNumber: 53
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: `inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium transition-colors duration-300 ${selectedUser === user.username ? 'bg-white/20 text-white' : isUserOnline(user.username) ? 'bg-green-100 text-green-700 group-hover:bg-green-200' : 'bg-gray-100 text-gray-600 group-hover:bg-gray-200'}`,
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                className: `w-2 h-2 rounded-full ${isUserOnline(user.username) ? 'bg-green-500' : 'bg-gray-400'}`
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app-components/SharedChat.tsx",
                                                                                lineNumber: 882,
                                                                                columnNumber: 57
                                                                            }, this),
                                                                            isUserOnline(user.username) ? 'Online' : 'Offline'
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/app-components/SharedChat.tsx",
                                                                        lineNumber: 875,
                                                                        columnNumber: 53
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app-components/SharedChat.tsx",
                                                                lineNumber: 865,
                                                                columnNumber: 49
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app-components/SharedChat.tsx",
                                                        lineNumber: 851,
                                                        columnNumber: 45
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app-components/SharedChat.tsx",
                                                lineNumber: 828,
                                                columnNumber: 41
                                            }, this)
                                        }, user.userId, false, {
                                            fileName: "[project]/src/app-components/SharedChat.tsx",
                                            lineNumber: 819,
                                            columnNumber: 37
                                        }, this))
                                ]
                            }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "p-6 text-center",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "bg-white rounded-2xl p-6 border border-gray-200 shadow-sm",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",
                                            children: userFilter === 'unread' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$message$2d$square$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MessageSquare$3e$__["MessageSquare"], {
                                                className: "h-8 w-8 text-gray-400"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app-components/SharedChat.tsx",
                                                lineNumber: 896,
                                                columnNumber: 49
                                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__["Users"], {
                                                className: "h-8 w-8 text-gray-400"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app-components/SharedChat.tsx",
                                                lineNumber: 898,
                                                columnNumber: 49
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app-components/SharedChat.tsx",
                                            lineNumber: 894,
                                            columnNumber: 41
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                            className: `font-semibold text-gray-900 mb-2 ${isMobile ? 'text-sm' : 'text-base'}`,
                                            children: userFilter === 'unread' ? 'No unread messages' : 'No users found'
                                        }, void 0, false, {
                                            fileName: "[project]/src/app-components/SharedChat.tsx",
                                            lineNumber: 901,
                                            columnNumber: 41
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-xs text-gray-600 mb-3",
                                            children: userFilter === 'unread' ? 'All messages have been read or no conversations yet' : `You can only chat with ${userType === 'student' ? 'tutors' : 'students'} who have exchanged messages with you`
                                        }, void 0, false, {
                                            fileName: "[project]/src/app-components/SharedChat.tsx",
                                            lineNumber: 904,
                                            columnNumber: 41
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-xs text-gray-500",
                                            children: userFilter === 'unread' ? 'Switch to "All Users" to see all conversations' : 'Users will appear here when you exchange messages with them'
                                        }, void 0, false, {
                                            fileName: "[project]/src/app-components/SharedChat.tsx",
                                            lineNumber: 910,
                                            columnNumber: 41
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app-components/SharedChat.tsx",
                                    lineNumber: 893,
                                    columnNumber: 37
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app-components/SharedChat.tsx",
                                lineNumber: 892,
                                columnNumber: 33
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app-components/SharedChat.tsx",
                            lineNumber: 766,
                            columnNumber: 25
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app-components/SharedChat.tsx",
                        lineNumber: 765,
                        columnNumber: 21
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app-components/SharedChat.tsx",
                lineNumber: 621,
                columnNumber: 17
            }, this),
            isMobile && sidebarOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute inset-0 bg-black/20 z-40",
                onClick: ()=>setSidebarOpen(false)
            }, void 0, false, {
                fileName: "[project]/src/app-components/SharedChat.tsx",
                lineNumber: 925,
                columnNumber: 17
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("main", {
                className: "flex-1 flex flex-col min-w-0 bg-white",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: `border-b-2 border-gray-200 flex items-center gap-3 bg-white ${isMobile ? 'p-3' : 'p-4'}`,
                        children: [
                            isMobile && !sidebarOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                variant: "ghost",
                                size: "icon",
                                className: `flex-shrink-0 rounded-xl hover:bg-gray-100 ${isMobile ? 'h-8 w-8' : 'h-10 w-10'}`,
                                onClick: handleBackToSidebar,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$left$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowLeft$3e$__["ArrowLeft"], {
                                    className: `${isMobile ? 'h-4 w-4' : 'h-5 w-5'}`
                                }, void 0, false, {
                                    fileName: "[project]/src/app-components/SharedChat.tsx",
                                    lineNumber: 935,
                                    columnNumber: 29
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app-components/SharedChat.tsx",
                                lineNumber: 934,
                                columnNumber: 25
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex gap-3 items-center min-w-0 flex-1",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "relative",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Avatar"], {
                                                className: `border-2 border-gray-300 flex-shrink-0 shadow-md ${isMobile ? 'h-9 w-9' : 'h-12 w-12'}`,
                                                children: selectedUser ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AvatarFallback"], {
                                                    className: `font-semibold bg-gray-100 text-black ${isMobile ? 'text-xs' : 'text-sm'}`,
                                                    children: selectedUser.substring(0, 2).toUpperCase()
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app-components/SharedChat.tsx",
                                                    lineNumber: 942,
                                                    columnNumber: 37
                                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AvatarFallback"], {
                                                    className: "bg-gray-100",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__["Users"], {
                                                        className: `text-gray-500 ${isMobile ? 'h-4 w-4' : 'h-6 w-6'}`
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app-components/SharedChat.tsx",
                                                        lineNumber: 947,
                                                        columnNumber: 41
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app-components/SharedChat.tsx",
                                                    lineNumber: 946,
                                                    columnNumber: 37
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app-components/SharedChat.tsx",
                                                lineNumber: 940,
                                                columnNumber: 29
                                            }, this),
                                            selectedUser && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: `absolute -bottom-1 -right-1 rounded-full border-2 border-white flex items-center justify-center ${isMobile ? 'h-3 w-3' : 'h-4 w-4'} ${isUserOnline(selectedUser) ? 'bg-green-500' : 'bg-gray-400'}`,
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: `rounded-full ${isMobile ? 'h-1.5 w-1.5' : 'h-2 w-2'} ${isUserOnline(selectedUser) ? 'bg-green-400 animate-pulse' : 'bg-gray-300'}`
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app-components/SharedChat.tsx",
                                                    lineNumber: 955,
                                                    columnNumber: 37
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app-components/SharedChat.tsx",
                                                lineNumber: 952,
                                                columnNumber: 33
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app-components/SharedChat.tsx",
                                        lineNumber: 939,
                                        columnNumber: 25
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "min-w-0 flex-1",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                                className: `font-semibold flex items-center gap-2 truncate text-black ${isMobile ? 'text-base' : 'text-lg'}`,
                                                children: selectedUser ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "truncate",
                                                    children: selectedUser
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app-components/SharedChat.tsx",
                                                    lineNumber: 964,
                                                    columnNumber: 37
                                                }, this) : 'Select a user'
                                            }, void 0, false, {
                                                fileName: "[project]/src/app-components/SharedChat.tsx",
                                                lineNumber: 962,
                                                columnNumber: 29
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: `text-gray-600 truncate ${isMobile ? 'text-xs' : 'text-sm'}`,
                                                children: selectedUser ? isUserOnline(selectedUser) ? 'Online' : 'Offline (messages will be delivered when online)' : 'Choose someone to chat with'
                                            }, void 0, false, {
                                                fileName: "[project]/src/app-components/SharedChat.tsx",
                                                lineNumber: 967,
                                                columnNumber: 29
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app-components/SharedChat.tsx",
                                        lineNumber: 961,
                                        columnNumber: 25
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app-components/SharedChat.tsx",
                                lineNumber: 938,
                                columnNumber: 21
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app-components/SharedChat.tsx",
                        lineNumber: 932,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: `flex-1 overflow-y-auto bg-gray-50 ${isMobile ? 'p-3' : 'p-4'}`,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: `mx-auto ${isMobile ? 'space-y-3 max-w-full' : 'space-y-4 max-w-4xl'}`,
                            children: [
                                selectedUser ? privateMessages.length > 0 ? privateMessages.map((message)=>{
                                    const isCurrentUser = message.sender === username;
                                    const senderName = message.sender || 'Unknown';
                                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: `flex items-end ${isCurrentUser ? 'justify-end' : ''} ${isMobile ? 'gap-2' : 'gap-3'}`,
                                        children: [
                                            !isCurrentUser && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Avatar"], {
                                                className: `border-2 border-gray-300 shadow-sm ${isMobile ? 'h-6 w-6' : 'h-8 w-8'}`,
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AvatarFallback"], {
                                                    className: `bg-gray-200 text-black font-semibold ${isMobile ? 'text-xs' : 'text-xs'}`,
                                                    children: senderName.substring(0, 2).toUpperCase()
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app-components/SharedChat.tsx",
                                                    lineNumber: 991,
                                                    columnNumber: 53
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app-components/SharedChat.tsx",
                                                lineNumber: 990,
                                                columnNumber: 49
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: `${isCurrentUser ? 'text-right' : ''} ${isMobile ? 'max-w-[80%]' : 'max-w-[70%]'}`,
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: `${isCurrentUser ? 'bg-black text-white' : 'bg-white text-black border-2 border-gray-200'} rounded-2xl shadow-lg break-words ${isMobile ? 'p-3' : 'p-4'}`,
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: `leading-relaxed ${isMobile ? 'text-sm' : 'text-base'}`,
                                                            children: message.text
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app-components/SharedChat.tsx",
                                                            lineNumber: 1003,
                                                            columnNumber: 53
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: `text-xs mt-2 flex items-end justify-end gap-1 ${isCurrentUser ? 'text-gray-300' : 'text-gray-500'}`,
                                                            children: [
                                                                formatTime(message.timestamp),
                                                                isCurrentUser && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    children: seenMessages.has(message.id) ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2d$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCheck$3e$__["CheckCheck"], {
                                                                        className: `${isMobile ? 'h-3 w-3' : 'h-4 w-4'} text-blue-500`
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app-components/SharedChat.tsx",
                                                                        lineNumber: 1014,
                                                                        columnNumber: 69
                                                                    }, this) : recipientIsViewing ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2d$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCheck$3e$__["CheckCheck"], {
                                                                        className: `${isMobile ? 'h-3 w-3' : 'h-4 w-4'} text-blue-500`
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app-components/SharedChat.tsx",
                                                                        lineNumber: 1016,
                                                                        columnNumber: 69
                                                                    }, this) : isUserOnline(selectedUser) ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2d$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCheck$3e$__["CheckCheck"], {
                                                                        className: `${isMobile ? 'h-3 w-3' : 'h-4 w-4'} text-gray-400`
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app-components/SharedChat.tsx",
                                                                        lineNumber: 1018,
                                                                        columnNumber: 69
                                                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Check$3e$__["Check"], {
                                                                        className: `${isMobile ? 'h-3 w-3' : 'h-4 w-4'} text-gray-400`
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app-components/SharedChat.tsx",
                                                                        lineNumber: 1020,
                                                                        columnNumber: 69
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app-components/SharedChat.tsx",
                                                                    lineNumber: 1012,
                                                                    columnNumber: 61
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app-components/SharedChat.tsx",
                                                            lineNumber: 1006,
                                                            columnNumber: 53
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app-components/SharedChat.tsx",
                                                    lineNumber: 997,
                                                    columnNumber: 49
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app-components/SharedChat.tsx",
                                                lineNumber: 996,
                                                columnNumber: 45
                                            }, this)
                                        ]
                                    }, message.id, true, {
                                        fileName: "[project]/src/app-components/SharedChat.tsx",
                                        lineNumber: 985,
                                        columnNumber: 41
                                    }, this);
                                }) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex flex-col items-center justify-center h-full py-12 text-center",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$message$2d$square$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MessageSquare$3e$__["MessageSquare"], {
                                            className: "text-gray-400 mb-4 h-16 w-16"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app-components/SharedChat.tsx",
                                            lineNumber: 1032,
                                            columnNumber: 37
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-gray-600 text-lg font-medium",
                                            children: "No messages yet"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app-components/SharedChat.tsx",
                                            lineNumber: 1033,
                                            columnNumber: 37
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-gray-500 text-sm mt-2",
                                            children: "Send a message to start the conversation"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app-components/SharedChat.tsx",
                                            lineNumber: 1034,
                                            columnNumber: 37
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app-components/SharedChat.tsx",
                                    lineNumber: 1031,
                                    columnNumber: 33
                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex flex-col items-center justify-center h-full py-12 text-center",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$message$2d$square$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MessageSquare$3e$__["MessageSquare"], {
                                            className: "text-gray-400 mb-4 h-16 w-16"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app-components/SharedChat.tsx",
                                            lineNumber: 1039,
                                            columnNumber: 33
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-gray-600 text-lg font-medium",
                                            children: "Select a user to start chatting"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app-components/SharedChat.tsx",
                                            lineNumber: 1040,
                                            columnNumber: 33
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-gray-500 text-sm mt-2",
                                            children: "Choose a user from the sidebar to start a private conversation"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app-components/SharedChat.tsx",
                                            lineNumber: 1041,
                                            columnNumber: 33
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-gray-500 text-sm mt-4 max-w-md",
                                            children: [
                                                "Note: You can only chat with ",
                                                userType === 'student' ? 'tutors' : 'students',
                                                " who have exchanged messages with you.",
                                                filteredUsers.length === 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "block mt-2",
                                                    children: "There are currently no users to chat with. When you exchange messages with someone, they will appear in the sidebar."
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app-components/SharedChat.tsx",
                                                    lineNumber: 1045,
                                                    columnNumber: 41
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app-components/SharedChat.tsx",
                                            lineNumber: 1042,
                                            columnNumber: 33
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app-components/SharedChat.tsx",
                                    lineNumber: 1038,
                                    columnNumber: 29
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    ref: messagesEndRef
                                }, void 0, false, {
                                    fileName: "[project]/src/app-components/SharedChat.tsx",
                                    lineNumber: 1050,
                                    columnNumber: 25
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app-components/SharedChat.tsx",
                            lineNumber: 977,
                            columnNumber: 21
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app-components/SharedChat.tsx",
                        lineNumber: 976,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                        onSubmit: handleSendMessage,
                        className: `border-t-2 border-gray-200 bg-white ${isMobile ? 'p-3' : 'p-4'}`,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: `flex items-center mx-auto ${isMobile ? 'gap-2 max-w-full' : 'gap-3 max-w-4xl'}`,
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    type: "button",
                                    onClick: toggleEmojiPicker,
                                    className: `bg-white border-2 border-gray-200 text-black hover:bg-gray-100 rounded-xl font-medium transition-all duration-300 ${isMobile ? 'text-lg px-2 py-1' : 'text-2xl px-3 py-1'}`,
                                    children: "😊"
                                }, void 0, false, {
                                    fileName: "[project]/src/app-components/SharedChat.tsx",
                                    lineNumber: 1056,
                                    columnNumber: 25
                                }, this),
                                showEmojiPicker && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    ref: emojiPickerRef,
                                    className: `absolute z-10 ${isMobile ? 'bottom-12 left-4 right-4' : 'bottom-12 left-96'}`,
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$emoji$2d$picker$2d$react$2f$dist$2f$emoji$2d$picker$2d$react$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        onEmojiClick: handleEmojiClick,
                                        emojiStyle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$emoji$2d$picker$2d$react$2f$dist$2f$emoji$2d$picker$2d$react$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EmojiStyle"].APPLE,
                                        searchDisabled: true,
                                        width: isMobile ? '100%' : undefined
                                    }, void 0, false, {
                                        fileName: "[project]/src/app-components/SharedChat.tsx",
                                        lineNumber: 1071,
                                        columnNumber: 33
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app-components/SharedChat.tsx",
                                    lineNumber: 1066,
                                    columnNumber: 29
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                    placeholder: selectedUser ? "Type your message..." : "Select a user to start chatting",
                                    className: `flex-1 bg-gray-50 border-2 border-gray-200 text-black placeholder:text-gray-500 focus:border-black rounded-xl transition-all duration-300 ${isMobile ? 'px-3 py-2 text-sm' : 'px-4 py-3 text-base'}`,
                                    value: messageInput,
                                    onChange: (e)=>setMessageInput(e.target.value),
                                    disabled: !selectedUser,
                                    maxLength: 250
                                }, void 0, false, {
                                    fileName: "[project]/src/app-components/SharedChat.tsx",
                                    lineNumber: 1079,
                                    columnNumber: 25
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                    type: "submit",
                                    size: isMobile ? "default" : "lg",
                                    disabled: !messageInput.trim() || !selectedUser,
                                    className: `bg-black text-white hover:bg-gray-800 rounded-xl font-medium transition-all duration-300 disabled:opacity-50 ${isMobile ? 'px-4 py-2' : 'px-6 py-3'}`,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$send$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Send$3e$__["Send"], {
                                            className: `${isMobile ? 'h-4 w-4 mr-1' : 'h-5 w-5 mr-2'}`
                                        }, void 0, false, {
                                            fileName: "[project]/src/app-components/SharedChat.tsx",
                                            lineNumber: 1095,
                                            columnNumber: 29
                                        }, this),
                                        isMobile ? 'Send' : 'Send'
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app-components/SharedChat.tsx",
                                    lineNumber: 1088,
                                    columnNumber: 25
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app-components/SharedChat.tsx",
                            lineNumber: 1055,
                            columnNumber: 21
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app-components/SharedChat.tsx",
                        lineNumber: 1054,
                        columnNumber: 17
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app-components/SharedChat.tsx",
                lineNumber: 931,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app-components/SharedChat.tsx",
        lineNumber: 619,
        columnNumber: 9
    }, this);
}
_s(SharedChat, "4aNCBKvUIghq3Qs76Js8iS9ZjtE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$mobile$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useIsMobile"]
    ];
});
_c = SharedChat;
var _c;
__turbopack_context__.k.register(_c, "SharedChat");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/student/chat/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ChatUI)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2d$components$2f$SharedChat$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app-components/SharedChat.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
function ChatWithParams() {
    _s();
    const [isAuthenticated, setIsAuthenticated] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [username, setUsername] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [userId, setUserId] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const searchParams = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSearchParams"])();
    const selectedUserId = searchParams.get('userId');
    const selectedUserName = searchParams.get('userName');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ChatWithParams.useEffect": ()=>{
            const studentIsAuthenticated = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isStudentAuthenticated"])();
            setIsAuthenticated(studentIsAuthenticated);
            if (studentIsAuthenticated) {
                const studentData = localStorage.getItem('student_data');
                if (studentData) {
                    try {
                        const parsedData = JSON.parse(studentData);
                        const studentName = `${parsedData.firstName} ${parsedData.lastName}` || parsedData.email.split('@')[0];
                        setUsername(studentName);
                        setUserId(parsedData.id);
                    } catch (error) {
                        console.error('Error parsing student data:', error);
                    }
                }
            } else {
                const userData = localStorage.getItem('user');
                if (userData) {
                    try {
                        const parsedData = JSON.parse(userData);
                        const userName = `${parsedData.firstName} ${parsedData.lastName}` || parsedData.email.split('@')[0];
                        setUsername(userName);
                        setUserId(parsedData.id);
                        setIsAuthenticated(true);
                    } catch (error) {
                        console.error('Error parsing user data:', error);
                    }
                }
            }
        }
    }["ChatWithParams.useEffect"], []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2d$components$2f$SharedChat$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        userType: "student",
        isAuthenticated: isAuthenticated,
        username: username,
        userId: userId,
        loginPath: "/",
        initialSelectedUserId: selectedUserId || undefined,
        initialSelectedUserName: selectedUserName || undefined
    }, void 0, false, {
        fileName: "[project]/src/app/student/chat/page.tsx",
        lineNumber: 49,
        columnNumber: 9
    }, this);
}
_s(ChatWithParams, "m5d3o+6bW/7l/2NuJGVAyHxz8CU=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSearchParams"]
    ];
});
_c = ChatWithParams;
function ChatUI() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Suspense"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ChatWithParams, {}, void 0, false, {
            fileName: "[project]/src/app/student/chat/page.tsx",
            lineNumber: 64,
            columnNumber: 13
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/student/chat/page.tsx",
        lineNumber: 63,
        columnNumber: 9
    }, this);
}
_c1 = ChatUI;
var _c, _c1;
__turbopack_context__.k.register(_c, "ChatWithParams");
__turbopack_context__.k.register(_c1, "ChatUI");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_3e11a801._.js.map