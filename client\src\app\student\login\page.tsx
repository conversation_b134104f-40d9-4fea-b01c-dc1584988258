"use client";

import Header from "@/app-components/Header";
import Footer from "@/app-components/Footer";
import { GoogleLoginButton } from "@/components/ui/GoogleLoginButton";
import { useSearchParams, useRouter } from "next/navigation";
import { useEffect, useState, Suspense } from "react";
import { toast } from "sonner";

function StudentLoginContent() {
    const searchParams = useSearchParams();
    const router = useRouter();
    const [referralCode, setReferralCode] = useState<string | null>(null);

    useEffect(() => {
        if (localStorage.getItem('user')) {
            router.push("/");
            setTimeout(() => {
                toast.error("You are already logged in as a tutor. Please logout first to login as student.");
            }, 500);
        }
    }, [router]);

    useEffect(() => {
        const refCode = searchParams.get('ref');
        if (refCode) {
            setReferralCode(refCode);
            localStorage.setItem('referralCode', refCode);
        }
    }, [searchParams]);

    return (
        <main className="min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-50 to-gray-100 py-12 px-4 sm:px-6 lg:px-8">
            <div className="w-full max-w-md bg-white rounded-2xl shadow-xl border border-gray-100 p-8 relative overflow-hidden">
                <div className="text-center mb-6">
                    <div className="bg-orange-50 border border-orange-100 rounded-lg py-2 px-4 mb-4">
                        <p className="text-center text-orange-700 font-medium">
                            Student Login Portal
                        </p>
                    </div>

                    <h2 className="text-2xl font-bold text-gray-800 mb-2">
                        Welcome to Uest Student Portal
                    </h2>
                    <div className="flex items-center justify-center">
                        <div className="h-0.5 w-16 bg-orange-300 mr-3"></div>
                        <span className="text-[#ff914d] font-medium">STUDENT PORTAL</span>
                        <div className="h-0.5 w-16 bg-orange-300 ml-3"></div>
                    </div>

                    {referralCode && (
                        <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                            <p className="text-sm text-green-700 text-center">
                                🎉 You&apos;re joining via referral code: <span className="font-semibold">{referralCode}</span>
                            </p>
                        </div>
                    )}
                </div>

                <div className="space-y-6">
                    <GoogleLoginButton />

                    <div className="mt-4 text-center">
                        <p className="text-sm text-gray-500">
                            By continuing, you agree to our{" "}
                            <a href="https://www.uest.in/terms-and-conditions" className="text-[#ff914d] hover:underline">Terms of Conditions
                            </a>{" "}
                            and{" "}
                            <a href="https://www.uest.in/privacy-policy" className="text-[#ff914d] hover:underline">Privacy Policy</a>
                        </p>
                    </div>
                </div>
            </div>
        </main>
    );
}

export default function StudentSignUpSignInPage() {
    return (
        <>
            <Header />
            <Suspense fallback={
                <main className="min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-50 to-gray-100 py-12 px-4 sm:px-6 lg:px-8">
                    <div className="w-full max-w-md bg-white rounded-2xl shadow-xl border border-gray-100 p-8 relative overflow-hidden">
                        <div className="text-center">
                            <div className="animate-pulse">
                                <div className="bg-gray-200 h-4 w-3/4 mx-auto mb-4 rounded"></div>
                                <div className="bg-gray-200 h-8 w-full mb-4 rounded"></div>
                                <div className="bg-gray-200 h-12 w-full rounded"></div>
                            </div>
                        </div>
                    </div>
                </main>
            }>
                <StudentLoginContent />
            </Suspense>
            <Footer />
        </>
    );
}