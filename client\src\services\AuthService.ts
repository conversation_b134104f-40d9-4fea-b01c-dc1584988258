import { axiosInstance } from '../lib/axios';

interface LoginData {
  email: string;
  password: string;
}

export async function registerUser(data: unknown) {
  const response = await axiosInstance.post('/auth-client/register', data);
  return response.data;
}

export async function loginUser(data: LoginData) {
  const response = await axiosInstance.post('/auth-client/login', data);
  return response.data;
}

export function logoutUser(): void {
  localStorage.removeItem('token');
}

export const forgotPassword = async (email: string) => {
  const response = await axiosInstance.post(`/auth-client/forgot-password`, { email });
  return response.data;
};

export const generateJWT = async (email: string | undefined, password : string | undefined) => {
  const response = await axiosInstance.post(`/auth-client/generate-jwt`, { email, password });
  return response.data;
};

export const resetPassword = async (email: string, token: string, newPassword: string) => {
  const response = await axiosInstance.post(`/auth-client/reset-password`, {
    email,
    token,
    newPassword,
  });
  return response.data;
};

export const verifyEmail = async (token: string) => {
  const response = await axiosInstance.get(`/auth-client/verify-email`, { params: { token } });
  return response.data;
};

export const resendVerificationEmail = async (email: string) => {
  const response = await axiosInstance.post(`/auth-client/resend-verification`, { email });
  return response.data;
};