@extends('layouts.app')
@section('content')
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-9">
                    <h1>Fees Payments</h1>
                </div>
            </div>
        </div>
    </div>
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <!-- /.card-header -->
                        <div class="card-body">
                            <h3 class="box-title popup-title m-0">Filter Fees Payment Data</h3>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label><b>From Date</b></label>
                                        <input class="form-control" placeholder="Select From Date" name="start_date"
                                            type="text" readonly id="start_date">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label><b>To Date</b></label>
                                        <input class="form-control" placeholder="Select To Date" name="end_date"
                                            type="text" readonly id="end_date">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label><b>Department</b></label>
                                        <select id="department" class="form-control select2">
                                            <option value="">Select department</option>
                                            @foreach ($department as $value)
                                                <option value="{{ $value->id }}">{{ $value->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label><b>Classroom</b></label>
                                        <select id="classroom-filter" class="form-control select2 classroom-data">
                                            <option value="">Select Classroom</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label><b>Students</b></label>
                                        <select class="form-control select2 student-data" id="student-data"
                                           >
                                            <option value="">Select Student</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <button id="filter" type="submit"
                                            class="btn btn-primary filter-btn">Filter</button>
                                        <button id="filterreset" class="btn btn-secondary filter-btn">Reset</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12 col-sm-2 col-md-6">
                </div>
                <div class="col-12 col-sm-2 col-md-6">
                    <div class="info-box bg-light">
                        <div class="info-box-content">
                            <span class="info-box-text text-center text-muted">Total Credited</span>
                            <span class="info-box-number text-center text-muted mb-0"><span
                                    class="badge badge-success totalcredit" style="font-size: 20px"></span></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="generate-buttons">
                                @can('export student fee data')
                                <button class="btn btn-dark exportData"><i class="fa fa-file-excel"></i>&nbsp;
                                    Payment Data Export</button>
                                <button class="btn btn-dark exportDetailedFees"><i class="fa fa-file-excel"></i>&nbsp;
                                    Detailed Fees Data Export</button>
                                @endcan
                            </div>
                            <table id="payment_details"
                                class="table display  table-striped  table-borderless dt-responsive">
                                <thead>
                                    <tr>
                                        <th>Action</th>
                                        <th>Student Name</th>
                                        <th>Classroom</th>
                                        <th>Payment Date</th>
                                        <th>Installment</th>
                                        <th>Payment Mode</th>
                                        <th>Paid Amount</th>
                                        <th>Cheque No</th>
                                        <th>Reference No</th>
                                        <th>Payment Status</th>
                                        <th>Transaction Id</th>
                                    </tr>
                                </thead>
                                <tfoot>
                                    <tr class="search-row">
                                        <th>Action</th>
                                        <th>Student Name</th>
                                        <th>Classroom</th>
                                        <th>Payment Date</th>
                                        <th>Installment</th>
                                        <th>Payment Mode</th>
                                        <th>Paid Amount</th>
                                        <th>Cheque No</th>
                                        <th>Reference No</th>
                                        <th>Payment Status</th>
                                        <th>Transaction Id</th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
@section('scripts')
    <script>
        var feesRoute = {
            index: "{{ route('getAllStudentPaymentLogs') }}",
            delete: "{{ route('deletePaymentLogs', ':payid') }}",
            export: "{{ route('export-feesPayment') }}",
            exportDetailed: "{{ route('export-detailed-fees') }}",
        };
    </script>
    <script src="{{ asset(mix('js/page-level-js/Admission/js/feesetup.js')) }}"></script>
@endsection
