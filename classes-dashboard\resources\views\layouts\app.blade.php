<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ $tenantName }}</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <link rel="shortcut icon" type="image/x-icon"
        href="{{ $tenantLogo }}">
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <link rel="stylesheet" href="{{ asset(mix('build/css/common-build.css')) }}">
    <link rel="stylesheet" href="{{ asset('plugins/fontawesome-free/css/all.min.css') }}">
    <link href="https://cdn.jsdelivr.net/npm/bs-stepper@1.7.0/dist/css/bs-stepper.min.css" rel="stylesheet">

    @yield('css')
</head>

<body class="hold-transition sidebar-mini sidebar-collapse layout-fixed">
    <div class="page-loader">
        <div class="loader">
            <div class="cube one"></div>
            <div class="cube two"></div>
            <div class="cube three"></div>
            <div class="cube four"></div>
        </div>
    </div>
    <div class="wrapper">
        @include('layouts.sidebar')
        <div class="content-wrapper">
            @yield('content')
            @include('studentModal')

        </div>
    </div>
    <footer class="main-footer">
        <strong>Copyright ©
            <a href="https://www.uest.in/">Uest</a>.
        </strong> All rights reserved.
    </footer>
    <script>
        let searchGlobalStudent = '{{ route('searchStudent') }}';
        let startYearDate = "{{ geActiveYearName()->start_date }}"
        let endYearDate = "{{ geActiveYearName()->end_date }}"
    </script>
    <script src="{{ asset(mix('build/js/common-build.js')) }}"></script>
    <script src="{{ asset('assets/js/custom-student.js') }}"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    @yield('scripts')
</body>

</html>
