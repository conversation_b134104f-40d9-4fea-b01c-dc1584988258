"use client";

import { useState, useEffect } from "react";
import { applyForExam } from "@/services/examApplicationApi";
import { Button } from "@/components/ui/button";
import ExamStatusButton from "../../uwhiz/examStatusButton";
import Footer from "@/app-components/Footer";
import Header from "@/app-components/Header";
import { motion } from "framer-motion";
import {
  CalendarClock,
  AlarmClock,
  Award,
  BookOpenCheck,
  BadgeIndianRupee,
  Users,
  Trophy,
  School,
  MapPin,
  Loader2,
} from "lucide-react";
import { useParams } from "next/navigation";
import { Exam } from "@/lib/types";
import { Progress } from "@/components/ui/progress";
import { getExamsById } from "@/services/examApi";
import { toast } from "sonner";
import Image from "next/image";
import {
  getStudentDiscount,
  calculateDiscountedPrice,
} from "@/services/referralApi";
import { GoogleLoginButton } from "@/components/ui/GoogleLoginButton";
import { axiosInstance } from "@/lib/axios";
import { uwhizPreventReattempApi } from "@/services/uwhizPreventReattemptApi";

const fadeUp = {
  hidden: { opacity: 0, y: 40 },
  show: { opacity: 1, y: 0, transition: { duration: 0.5, ease: "easeOut" } },
};

const UwhizInfoPage = () => {
  const { examid } = useParams();
  const [exam, setExam] = useState<Exam | null>(null);
  const [loading, setLoading] = useState(true);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [selectedExam, setSelectedExam] = useState<Exam | null>(null);
  const [coinError, setCoinError] = useState<string | null>(null);
  const [discountInfo, setDiscountInfo] = useState<{
    hasDiscount: boolean;
    discountPercentage: number;
    referralCode: string | null;
  } | null>(null);
  const [isPaying, setIsPaying] = useState(false);
  const [remCoins, setRemCoins] = useState<number>(0);
  const [isAdding, setIsAdding] = useState(false);

  const getStudentId = (): string | null => {
    try {
      const data = localStorage.getItem("student_data");
      return data ? JSON.parse(data).id : null;
    } catch {
      return null;
    }
  };

  const calculateProgress = (totalApplicants: number, totalIntake: number) => {
    if (totalIntake === 0) return 0;
    const percentage = (totalApplicants / totalIntake) * 100;
    return Math.min(100, Math.max(0, percentage));
  };

  // Fetch discount information for student
  useEffect(() => {
    const fetchDiscountInfo = async () => {
      const studentId = getStudentId();
      if (studentId) {
        try {
          const response = await getStudentDiscount();
          if (response.success) {
            setDiscountInfo(response.data);
          }
        } catch (error) {
          console.error("Error fetching discount info:", error);
        }
      }
    };

    fetchDiscountInfo();
  }, []);

  useEffect(() => {
    const fetchExam = async () => {
      if (!examid) return;

      setLoading(true);
      try {
        const studentId = getStudentId();
        const examData = await getExamsById(Number(examid), studentId ?? undefined);

        let hasAttempted = false;

        if (studentId) {
          const attemptResponse = await uwhizPreventReattempApi(studentId, examData.id);
          hasAttempted = attemptResponse.success === false ? false : attemptResponse;
        }

        setExam({
          ...examData,
          hasAttempted,
        });

      } catch (error: any) {
        console.error("Error fetching exam:", error);
        toast.error(error.message || "Failed to load exam details");
      } finally {
        setLoading(false);
      }
    };

    fetchExam();
  }, [examid]);

  const handleApplyClick = (exam: Exam) => {
    setSelectedExam(exam);
    setCoinError(null);
    setShowConfirmDialog(true);
  };

  const fetchCoinData = async () => {
    try {
      const coinsResponse = await axiosInstance.get(
        "/coins/get-total-coins/student"
      );
      return coinsResponse.data.coins;
    } catch (error) {
      toast.error("Failed to load coin data. Please try again.");
      console.error("Error fetching data", error);
    }
  };

  const handleApplyNow = async () => {
    if (!selectedExam || isPaying) return;

    const classId = getStudentId();
    if (!classId) {
      toast.error("Please log in as a student to apply for an exam");
      return;
    }
    const totalCoins = await fetchCoinData();

    try {
      const applyRes = await applyForExam(selectedExam.id, String(classId));
      if (applyRes.application) {
        setExam((prevExam) => {
          if (!prevExam) return null;
          return {
            ...prevExam,
            totalApplicants: (prevExam.totalApplicants || 0) + 1,
            hasApplied: true,
            isMaxLimitReached:
              (prevExam.totalApplicants || 0) + 1 >=
              (prevExam.total_student_intake ?? 0),
          };
        });
        setShowConfirmDialog(false);
        setShowSuccessDialog(true);
        toast.success(applyRes.message || "Successfully applied for the exam");
        setCoinError(null);
      }
    } catch (error: any) {
      const errorMessage = error.message || "Error applying for exam";
      toast.error(errorMessage);
      if (errorMessage.includes("Required Coin for Applying in Exam")) {
        setCoinError(errorMessage);

        let coinsRequired: number = Number(selectedExam.coins_required) ?? 0;

        if (discountInfo?.hasDiscount) {
          coinsRequired =
            coinsRequired * (1 - discountInfo.discountPercentage / 100);
        }
        const remainingCoins = Math.floor(
          Math.floor(coinsRequired) - totalCoins
        );
        setRemCoins(remainingCoins);
      } else {
        setShowConfirmDialog(false);
      }
    }
  };

  const closeDialog = () => {
    setShowSuccessDialog(false);
    setShowConfirmDialog(false);
    setSelectedExam(null);
    setCoinError(null);
  };

  const initiatePayment = async () => {
    setIsPaying(true);
    try {
      const res = await axiosInstance.post("/coins/create-order", {
        amount: remCoins * 100,
      });

      const { order } = res.data;

      const options = {
        key: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID,
        amount: order.amount,
        currency: "INR",
        name: "Uest Coins",
        description: "Add Uest Coins",
        order_id: order.id,
        handler: async function (response: any) {
          try {
            setIsAdding(true);

            await axiosInstance.post("/coins/verify", {
              razorpay_order_id: response.razorpay_order_id,
              razorpay_payment_id: response.razorpay_payment_id,
              razorpay_signature: response.razorpay_signature,
              amount: remCoins * 100,
            });

            toast.success("Coins added successfully!");
            handleApplyNow();
            setIsAdding(false);
          } catch {
            toast.error("Payment verification failed");
          } finally {
            setIsAdding(false);
          }
        },
        theme: {
          color: "#f97316",
        },
      };

      const rzp = new (window as any).Razorpay(options);
      rzp.open();
    } catch {
      toast.error("Payment initialization failed");
    } finally {
      setIsPaying(false);
    }
  };

  useEffect(() => {
    const script = document.createElement("script");
    script.src = "https://checkout.razorpay.com/v1/checkout.js";
    script.async = true;
    document.body.appendChild(script);
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="h-5 w-5 animate-spin" />
      </div>
    );
  }
  console.log("Exam Data:", exam);

  if (!exam) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        Exam not found
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-1 bg-white text-black">
        <motion.div
          initial="hidden"
          animate="show"
          variants={{
            hidden: {},
            show: { transition: { staggerChildren: 0.15 } },
          }}
          className="max-w-6xl mx-auto px-6 py-16 space-y-16"
        >
          <motion.section variants={fadeUp} className="text-center space-y-3">
            <h1 className="text-4xl md:text-6xl font-bold flex items-center justify-center gap-3 text-customOrange">
              <Trophy className="w-8 h-8" /> {exam.exam_name}
            </h1>
            <p className="text-gray-600 text-lg flex items-center justify-center gap-3">
              <CalendarClock className="w-5 h-5" />
              {exam.start_date
                ? new Date(exam.start_date).toLocaleDateString()
                : "TBD"}
              <AlarmClock className="w-5 h-5" />
              {exam.start_date
                ? new Date(exam.start_date).toLocaleTimeString([], {
                  hour: "2-digit",
                  minute: "2-digit",
                })
                : "TBD"}{" "}
              | UEST
            </p>
          </motion.section>

          <motion.section variants={fadeUp} className="text-center space-y-4">
            <div className="inline-flex items-center gap-2 bg-customOrange text-white px-4 py-1 rounded-full font-semibold shadow-sm">
              <Award className="w-4 h-4" /> First Prize
            </div>
            <h2 className="text-5xl font-extrabold text-black flex items-center justify-center gap-2">
              <BadgeIndianRupee className="w-8 h-8 text-green-600" />
              {exam?.UwhizPriceRank[0]?.price?.toLocaleString("en-IN")}
            </h2>
          </motion.section>

          <motion.section variants={fadeUp} className="text-center">
            <div className="inline-flex items-center gap-2 bg-gray-100 border border-gray-300 px-4 py-2 rounded-full text-gray-800 font-medium text-lg">
              <Users className="w-5 h-5" /> For Students: Std 1 to 12
            </div>
            <div className="mt-4 flex justify-center flex-wrap gap-3">
              {["Maths", "Science", "English"].map((subject) => (
                <span
                  key={subject}
                  className="inline-flex items-center gap-2 bg-black text-white px-4 py-1.5 rounded-full text-sm font-semibold"
                >
                  <BookOpenCheck className="w-4 h-4" />
                  {subject}
                </span>
              ))}
            </div>
          </motion.section>

          <motion.section
            variants={fadeUp}
            className="border border-orange-300 rounded-xl p-6 flex flex-col gap-6 bg-white shadow-md"
          >
            <div className="flex flex-col md:flex-row flex-wrap items-center justify-between gap-4">
              <div className="flex items-center gap-2 text-xl font-semibold text-customOrange">
                <BadgeIndianRupee className="w-5 h-5" />
                Entry Fee:{" "}
                {exam.coins_required ? (
                  discountInfo?.hasDiscount ? (
                    <span className="flex items-center gap-2">
                      <span className="line-through text-gray-500 text-sm">
                        {exam.coins_required}
                      </span>
                      <span className="text-green-600 font-bold">
                        {calculateDiscountedPrice(
                          exam.coins_required,
                          discountInfo.discountPercentage
                        )}
                      </span>
                      <span className="text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full">
                        {discountInfo.discountPercentage}% OFF
                      </span>
                    </span>
                  ) : (
                    exam.coins_required
                  )
                ) : (
                  "Free"
                )}
              </div>
              <div className="flex items-center gap-2 text-2xl font-bold text-black">
                <Trophy className="w-6 h-6 text-yellow-500" />
                Prize Pool: ₹5 Lakh
              </div>
              <span className="text-sm font-medium text-red-500 whitespace-nowrap">
                Limited Seats Available
              </span>
            </div>

            <div className="w-full">
              <p className="text-sm font-semibold text-gray-700 mb-1 tracking-wide">
                Students Joined
              </p>
              <Progress
                value={calculateProgress(
                  exam.totalApplicants ?? 0,
                  exam.total_student_intake ?? 0
                )}
                className="[&>*]:bg-customOrange bg-slate-300 h-3 rounded-full"
              />
              <p className="text-right text-sm mt-1">
                {exam.totalApplicants} / {exam.total_student_intake}
              </p>
            </div>

            <ExamStatusButton
              hasAttempted={exam.hasAttempted}
              exam={exam}
              hasApplied={exam.hasApplied}
              isMaxLimitReached={exam.isMaxLimitReached}
              onApplyClick={() => handleApplyClick(exam)}
            />
          </motion.section>

          <div className="bg-orange-50 dark:bg-orange-900 border border-orange-300 rounded-xl p-5 md:p-6 shadow-sm my-6 space-y-4">
            <div className="space-y-4">
              <div className="flex flex-col md:flex-row items-center gap-4">
                <Image
                  src="/cellular_world.jpeg"
                  alt="Cellular World Logo"
                  width={80}
                  height={80}
                  className="rounded-md object-contain"
                />
                <div className="text-center md:text-left flex-1">
                  <h2 className="text-lg sm:text-xl md:text-2xl font-bold">
                    Apply Now & Win Premium Headphones Worth ₹5000! 🎧
                  </h2>
                  <p className="text-sm sm:text-base text-customOrange-800 dark:text-orange-200 mt-1">
                    Secure a{" "}
                    <span className="text-orange-600 font-semibold">
                      rank between 2nd and 11th
                    </span>{" "}
                    to claim your exclusive headphone gift, courtesy of Cellular
                    World!
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-orange-50 dark:bg-orange-900 border border-orange-300 rounded-xl p-5 md:p-6 shadow-sm my-6 space-y-4">
            <div className="space-y-4">
              <div className="flex flex-col md:flex-row items-center gap-4">
                <Image
                  src="/rb-news.png"
                  alt="RB News Logo"
                  width={80}
                  height={80}
                  className="rounded-md object-contain"
                />
                <div className="text-center md:text-left flex-1">
                  <h2 className="text-lg sm:text-xl md:text-2xl font-bold">
                    Apply Now & Win Exclusive Gift Hampers
                  </h2>
                  <p className="text-sm sm:text-base text-customOrange-800 dark:text-orange-200 mt-1">
                    Secure a{" "}
                    <span className="text-orange-600 font-semibold">
                      rank between 12th to 40th
                    </span>{" "}
                    to Grab your exclusive Gift Hampers By RB News!
                  </p>
                </div>
              </div>
            </div>
          </div>

          <motion.section
            variants={fadeUp}
            className="relative flex flex-col gap-6 p-4 sm:p-6 bg-gray-50 dark:bg-gray-900 rounded-xl border border-orange-300 shadow-sm"
          >
            {/* Logo - Positioned at Top-Right */}
            <Image
              src="/nalanda.png"
              alt="Nalanda Vidyalaya"
              width={90}
              height={90}
              className="absolute top-4 right-4 object-contain hover:scale-105 transition-transform duration-300 drop-shadow-md"
            />

            {/* Sponsor Info and Image Gallery Container */}
            <div className="space-y-4">
              {/* Sponsor Info */}
              <div className="space-y-2 text-center md:text-left">
                <div className="flex items-center justify-center md:justify-start gap-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                  <School className="w-4 h-4 text-orange-500" />
                  <span>Sponsored By:</span>
                </div>
                <h2 className="text-xl sm:text-2xl font-bold text-orange-700 dark:text-orange-400">
                  NALANDA VIDYALAYA
                </h2>
                <div className="flex items-center justify-center md:justify-start gap-2 text-sm text-gray-600 dark:text-gray-400">
                  <MapPin className="w-4 h-4 text-orange-400" />
                  <span>
                    Rajkot - Morbi Hwy, Near Ajanta Quartz, Virpar, Morbi,
                    Gujarat
                  </span>
                </div>
              </div>

              {/* Image Gallery */}
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                <Image
                  src="/nalanda01.jpg"
                  alt="Nalanda Vidyalaya Image 1"
                  width={400}
                  height={300}
                  className="rounded-lg object-cover w-full h-48"
                />
                <Image
                  src="/nalanda02.png"
                  alt="Nalanda Vidyalaya Image 2"
                  width={400}
                  height={300}
                  className="rounded-lg object-cover w-full h-48"
                />
                <Image
                  src="/nalanda03.png"
                  alt="Nalanda Vidyalaya Image 3"
                  width={400}
                  height={300}
                  className="rounded-lg object-cover w-full h-48"
                />
              </div>
            </div>
          </motion.section>
        </motion.div>
      </main>
      <Footer />

      {showSuccessDialog && selectedExam && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full">
            <h2 className="text-2xl font-bold text-green-600 mb-4">
              Application Successful!
            </h2>
            <p className="text-gray-700 mb-6">
              You have successfully applied for{" "}
              <strong>{selectedExam.exam_name}</strong>.
            </p>
            <Button
              onClick={closeDialog}
              className="w-full bg-customOrange hover:bg-orange-600 text-white font-semibold py-2 rounded-lg"
            >
              Close
            </Button>
          </div>
        </div>
      )}

      {showConfirmDialog && selectedExam && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-8 rounded-xl shadow-2xl max-w-md w-full">
            <h2 className="text-3xl font-semibold text-customOrange mb-4 border-b border-gray-200 pb-2">
              Are You Sure?
            </h2>
            <p className="text-gray-700 text-lg mb-6 leading-relaxed">
              Do you want to apply for{" "}
              <strong className="text-customOrange">
                {selectedExam.exam_name}
              </strong>
              ?
              {selectedExam.coins_required != null && (
                <span>
                  {" "}
                  This will cost{" "}
                  {discountInfo?.hasDiscount ? (
                    <span>
                      <span className="line-through text-gray-500">
                        {selectedExam.coins_required}
                      </span>{" "}
                      <strong className="text-green-600">
                        {calculateDiscountedPrice(
                          selectedExam.coins_required,
                          discountInfo.discountPercentage
                        )}
                      </strong>{" "}
                      <span className="text-green-600 text-sm">
                        ({discountInfo.discountPercentage}% discount applied)
                      </span>
                    </span>
                  ) : (
                    <strong className="text-customOrange">
                      {selectedExam.coins_required}
                    </strong>
                  )}{" "}
                  coins.
                </span>
              )}
            </p>
            {coinError && (
              <div className="bg-red-50 p-4 rounded-lg mb-6 border border-red-200">
                <div className="flex gap-5 items-center">
                  <svg
                    className="w-5 h-5 text-red-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  <p className="text-red-600 text-sm font-medium">
                    {coinError}
                  </p>
                </div>
                <Button
                  onClick={() => initiatePayment()}
                  className="mt-5 w-full bg-customOrange hover:bg-orange-600 text-white font-semibold py-3 rounded-lg"
                  disabled={isAdding}
                >
                  {isAdding ? (
                    <span className="flex items-center justify-center gap-2">
                      <Loader2 className="animate-spin w-5 h-5" />
                      Processing...
                    </span>
                  ) : (
                    "Add Coins"
                  )}
                </Button>
              </div>
            )}
            {getStudentId() ? (
              <div className="flex gap-4">
                <Button
                  onClick={handleApplyNow}
                  className="w-1/2 bg-customOrange hover:bg-orange-600 text-white font-semibold py-3 rounded-lg"
                  disabled={!!coinError || isPaying}
                >
                  {isPaying ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    "Yes, Apply"
                  )}
                </Button>
                <Button
                  onClick={closeDialog}
                  className="w-1/2 bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-3 rounded-lg"
                >
                  Cancel
                </Button>
              </div>
            ) : (
              <GoogleLoginButton uwhiz={true} handleApplyNow={handleApplyNow} />
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default UwhizInfoPage;
