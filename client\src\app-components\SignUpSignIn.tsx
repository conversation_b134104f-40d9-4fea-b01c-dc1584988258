'use client';

import { useEffect, useState } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { MdEmail } from "react-icons/md";
import { RiLockPasswordLine } from "react-icons/ri";
import { FiUser } from "react-icons/fi";
import { FiEye, FiEyeOff } from "react-icons/fi";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import {
  loginUser,
  registerUser,
  forgotPassword,
  resendVerificationEmail,
} from "@/services/AuthService";
import AuthActions from "./AuthActions";
import { useDispatch } from "react-redux";
import { AppDispatch } from "@/store";
import { setUser } from "@/store/slices/userSlice";
import { Loader2 } from "lucide-react";
import ReCAPTCHA from "react-google-recaptcha";
import CustomModal from "@/components/ui/CustomModal";

const loginSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address' }),
  password: z.string().min(6, { message: 'Password must be at least 6 characters' }),
  captcha: z.string().min(6, { message: 'Please complete the CAPTCHA' }),
});

const registerSchema = z.object({
  firstName: z.string().min(2, { message: 'First name is required' }),
  lastName: z.string().min(2, { message: 'Last name is required' }),
  email: z.string().email({ message: 'Please enter a valid email address' }),
  password: z.string().min(6, { message: 'Password must be at least 6 characters' }),
  captcha: z.string().min(6, { message: 'Please complete the CAPTCHA' }),
});

const forgotPasswordSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address' }),
});

type LoginFormValues = z.infer<typeof loginSchema>;
type RegisterFormValues = z.infer<typeof registerSchema>;
type ForgotPasswordFormValues = z.infer<typeof forgotPasswordSchema>;

// Form error alert component
const FormErrorAlert = ({ message }: { message: string }) => {
  if (!message) return null;

  return (
    <Alert className="mb-4 border-red-500 bg-red-50 dark:bg-red-900/20">
      <AlertCircle className="h-4 w-4 text-red-500" />
      <AlertDescription className="text-red-500">{message}</AlertDescription>
    </Alert>
  );
};

const SignUpSignIn = () => {
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [showForgotPasswordModal, setShowForgotPasswordModal] = useState(false);
  const [isLogin, setIsLogin] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [disablebtn, setDisablebtn] = useState<boolean>(false);
  const [timeleft, setTimeleft] = useState<number>(120);
  const [authError, setAuthError] = useState<string>("");
  const [forgotPasswordError, setForgotPasswordError] = useState<string>("");

  const dispatch = useDispatch<AppDispatch>();

  const authForm = useForm<LoginFormValues | RegisterFormValues>({
    resolver: zodResolver(isLogin ? loginSchema : registerSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      password: '',
      captcha: '',
    },
  });

  const forgotPasswordForm = useForm<ForgotPasswordFormValues>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      email: '',
    },
  });

  const onAuthSubmit = async (data: LoginFormValues | RegisterFormValues) => {
    setIsSubmitting(true);
    setAuthError(""); // Clear previous errors
    try {
      const response = isLogin ? await loginUser(data) : await registerUser(data);

      if (response.success === false) {
        setAuthError(response.message || 'Authentication failed');
        return;
      }
      if (isLogin && response.data) {
        const { user } = response.data;
        if (user) {
          dispatch(setUser({ user }));
        }
        toast.success("Logged in successfully");
        setShowAuthModal(false);
        authForm.reset();
      } else {
        toast.success(response.message || "Verification email sent. Please check your inbox and verify your email.");
        setShowAuthModal(false);
        authForm.reset();
      }
    } catch (error: unknown) {
      setAuthError((error as any)?.response?.data?.message || "An error occurred");
    } finally {
      setIsSubmitting(false);
    }
  };

  useEffect(() => {
    let interval:any;
    if (disablebtn && timeleft > 0) {
      interval = setInterval(() => {
        setTimeleft((prev) => {
          if (prev <= 1) {
            clearInterval(interval);
            setDisablebtn(false);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [disablebtn, timeleft]);

  useEffect(() => {
    const handleOpenClassModal = () => {
      if (localStorage.getItem('studentToken')) {
        toast.error("You are already logged in as a student. Please logout first to login as tutor.");
        window.location.href = "/";
        return;
      }
      setShowAuthModal(true);
    };
    window.addEventListener('open-class-modal', handleOpenClassModal);
    return () => {
      window.removeEventListener('open-class-modal', handleOpenClassModal);
    };
  }, []);

  // resend verification email
  const VerificationEmail = async (email: string) => {
    setAuthError(""); // Clear previous errors
    try {
      if (!email) {
        setAuthError("Please enter your email address first");
        return;
      }

      const response = await resendVerificationEmail(email);
      setTimeleft(120);
      setDisablebtn(true);

      if (response.success === false) {
        setAuthError("Failed to resend verification email");
        return;
      }
      toast.success("Verification email sent. Please check your inbox.");
      authForm.reset();
    } catch {
      setAuthError("An error occurred");
    }
  };

  const onForgotPasswordSubmit = async (data: ForgotPasswordFormValues) => {
    setIsSubmitting(true);
    setForgotPasswordError(""); // Clear previous errors
    try {
      const response = await forgotPassword(data.email);
      if (response.success === false) {
        setForgotPasswordError(response.message || 'Failed to send reset email');
        return;
      }
      toast.success(response.message || 'Password reset email sent');
      setShowForgotPasswordModal(false);
      forgotPasswordForm.reset();
    } catch (error: any) {
      setForgotPasswordError(error?.response?.data?.message || 'An error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  const toggleAuthModal = () => {
    if (!showAuthModal && localStorage.getItem('studentToken')) {
      toast.error("You are already logged in as a student. Please logout first to login as tutor.");
      window.location.href = "/";
      return;
    }
    setShowAuthModal(!showAuthModal);
    setAuthError("");
    authForm.reset();
  };

  const toggleForgotPasswordModal = () => {
    setShowForgotPasswordModal(!showForgotPasswordModal);
    setForgotPasswordError("");
    forgotPasswordForm.reset();
  };

  return (
    <>
      <button id="class-mobile-signin" data-mobile-signin-button="class" className="hidden" onClick={toggleAuthModal}></button>
        <div className="hidden sm:block">
        <AuthActions toggleModal={toggleAuthModal} />
      </div>
      <CustomModal isOpen={showAuthModal} onClose={toggleAuthModal}>
        <div className="text-center">
          <div className="flex justify-center mb-4">
            <div className="flex bg-muted rounded-lg p-1">
              <Button
                variant={isLogin ? 'default' : 'ghost'}
                className={`px-4 py-2 rounded-lg ${
                  isLogin
                    ? 'bg-primary text-primary-foreground'
                    : 'text-muted-foreground dark:text-white'
                }`}
                onClick={() => setIsLogin(true)}
              >
                Login
              </Button>
              <Button
                variant={!isLogin ? 'default' : 'ghost'}
                className={`px-4 py-2 rounded-lg ${
                  !isLogin
                    ? 'bg-primary text-primary-foreground'
                    : 'text-muted-foreground dark:text-white'
                }`}
                onClick={() => setIsLogin(false)}
              >
                Sign Up
              </Button>
            </div>
          </div>
          <p className="text-muted-foreground mb-2">Sign up/sign in as a classes</p>
          <h2 className="text-2xl font-bold mb-4">{isLogin ? 'Welcome Back' : 'Join Us'}</h2>
        </div>

        <Form {...authForm}>
          <form onSubmit={authForm.handleSubmit(onAuthSubmit)} className="space-y-6">
            {authError && <FormErrorAlert message={authError} />}
            {!isLogin && (
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <FormField
                  control={authForm.control}
                  name="firstName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-foreground dark:text-white">First Name</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <FiUser
                            className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground"
                            size={20}
                          />
                          <Input placeholder="First Name" className="pl-10" {...field} />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={authForm.control}
                  name="lastName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-foreground dark:text-white">Last Name</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <FiUser
                            className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground"
                            size={20}
                          />
                          <Input placeholder="Last Name" className="pl-10" {...field} />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}

            <FormField
              control={authForm.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-foreground dark:text-white">Email</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <MdEmail
                        className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground"
                        size={20}
                      />
                      <Input
                        type="email"
                        placeholder="Email"
                        className="pl-10 dark:text-white"
                        {...field}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={authForm.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-foreground dark:text-white">Password</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <RiLockPasswordLine
                        className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground"
                        size={20}
                      />
                      <Input
                        type={showPassword ? 'text' : 'password'}
                        placeholder="Password"
                        className="pl-10 pr-10 dark:text-white"
                        {...field}
                      />
                      <button
                        type="button"
                        className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
                        onClick={() => setShowPassword(!showPassword)}
                        tabIndex={-1}
                      >
                        {showPassword ? (
                          <FiEyeOff size={18} aria-label="Hide password" />
                        ) : (
                          <FiEye size={18} aria-label="Show password" />
                        )}
                      </button>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {isLogin && (
              <div className="flex justify-between text-sm text-right">
                <button
                  type="button"
                  className={`text-primary transition ${disablebtn
                    ? "opacity-50 cursor-not-allowed"
                    : "hover:underline"
                    }`}
                  onClick={() => VerificationEmail(authForm.getValues("email"))}
                  disabled={disablebtn}
                >
                  {disablebtn ? `Verification mail (${timeleft}s)` : "Verification mail"}
                </button>

                <button
                  type="button"
                  className="text-primary hover:underline"
                  onClick={() => {
                    setShowAuthModal(false);
                    toggleForgotPasswordModal();
                  }}
                >
                  Forgot Password?
                </button>
              </div>
            )}

            <FormField
              control={authForm.control}
              name="captcha"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-foreground dark:text-white sr-only">CAPTCHA</FormLabel>
                  <FormControl>
                    <ReCAPTCHA
                      sitekey={process.env.NEXT_PUBLIC_CAPTCHA_SITE_KEY || ''}
                      onChange={(token) => field.onChange(token)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button type="submit" className="w-full" disabled={isSubmitting}>
              {isSubmitting ? (
                <Loader2 className="h-5 w-5 animate-spin" />
              ) : isLogin ? (
                'Login'
              ) : (
                'Sign Up'
              )}
            </Button>
          </form>
        </Form>
      </CustomModal>

      {/* Forgot Password Modal */}
      <CustomModal isOpen={showForgotPasswordModal} onClose={toggleForgotPasswordModal}>
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2 dark:text-white">Forgot Password</h2>
          <p className="text-gray-500 mb-4 dark:text-white">
            Enter your email to receive a password reset link
          </p>
        </div>

        <Form {...forgotPasswordForm}>
          <form
            onSubmit={forgotPasswordForm.handleSubmit(onForgotPasswordSubmit)}
            className="space-y-6"
          >
            {forgotPasswordError && <FormErrorAlert message={forgotPasswordError} />}
            <FormField
              control={forgotPasswordForm.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-foreground dark:text-white">Email</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <MdEmail
                        className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground"
                        size={20}
                      />
                      <Input type="email" placeholder="Email" className="pl-10" {...field} />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button type="submit" className="w-full" disabled={isSubmitting}>
              {isSubmitting ? <Loader2 className="h-5 w-5 animate-spin" /> : 'Send Reset Link'}
            </Button>
          </form>
        </Form>
      </CustomModal>
    </>
  );
};

export default SignUpSignIn;
