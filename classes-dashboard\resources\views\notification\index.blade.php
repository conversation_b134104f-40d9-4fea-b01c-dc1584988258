@extends('layouts.app')
@section('content')

<div class="content-header">
  <div class="container-fluid">
    <div class="row mb-2">
      <div class="col-sm-9">
        <h1>Notifications</h1>
      </div>
    </div>
  </div>
</div>
<section class="content">
  <div class="container-fluid">

    <div class="row">
      <div class="col-md-12">
        <div class="timeline" id="notification">
        </div>
      </div>
    </div>
  </div>
</section>
@endsection
@section('scripts')
<script>
  let page = 1;

  $(document).ready(function() {
    getData(1);

    $(document).on('click', '.pagination a', function(event) {
      $('li').removeClass('active');
      $(this).parent('li').addClass('active');
      event.preventDefault();
      var myurl = $(this).attr('href');
      page = $(this).attr('href').split('page=')[1];
      getData(page);
    });
  });

  function getData(page) {
    var params = $.extend({}, doAjax_params_default);
    params["url"] = '?page=' + page;
    params["requestType"] = `GET`;
    params["successCallbackFunction"] = function successCallbackFunction(
      result
    ) {
      $("#notification").empty().html(result);
      location.hash = page;
    };
    commonAjax(params);
  }

  $(document).on('click', '.mark-as-read', function(event) {
    var request = sendRequest($(this).data('id'));
    request.done(() => {
      getData(page);
    });
  });
  $(document).on('click', '#mark-all', function(event) {
    var request = sendRequest();
    request.done(() => {
      getData(page);
    })
  });

  function sendRequest(id = null) {
    var _token = $('meta[name="csrf-token"]').attr('content');
    return $.ajax("/mark-as-read", {
      method: 'POST',
      data: {
        _token,
        id
      },
      beforeSend: function() {
        $(".page-loader").show();
      },
      complete: function() {
        $(".page-loader").hide();
      },
    });
  }
</script>
@endsection