@extends('layouts.app')

@section('content')
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-9">
                    <h1>Dashboard</h1>
                </div>
            </div>
        </div>
    </div>
    <section class="content staff-home">
        <div class="row">
            <div class="col-md-12">
                <div class="row">
                    <div class="col-12">
                        <div class="row">
                            @can('read student')
                                <div class="col-md-3 col-sm-6 col-12">
                                    <div class="info-box">
                                        <span class="info-box-icon bg-info"><i class="far fa-bookmark"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">Students</span>
                                            <span class="info-box-number">{{ $totalStudentsCount }}</span>
                                            <a href="{{ route('student.index') }}" class="small-box-footer">More info
                                                <i class="fas fa-arrow-circle-right"></i></a>
                                        </div>
                                    </div>
                                </div>
                            @endcan
                            @can('manage student fees')
                                <div class="col-md-3 col-sm-6 col-12">
                                    <div class="info-box">
                                        <span class="info-box-icon bg-info"><i class="far fa-bookmark"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">Fee Collection Today</span>
                                            <span class="info-box-number">{{ $feeCollectionToday }}</span>
                                            <a href="{{ route('getAllStudentPaymentLogs') }}" class="small-box-footer">More info
                                                <i class="fas fa-arrow-circle-right"></i></a>
                                        </div>
                                    </div>
                                </div>
                            @endcan
                            @can('read student')
                                <div class="col-md-3 col-sm-6 col-12">
                                    <div class="info-box">
                                        <span class="info-box-icon bg-info"><i class="far fa-bookmark"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">Admissions Today</span>
                                            <span class="info-box-number">{{ $totalAdmissions }}</span>
                                            <a href="{{ route('student.index') }}" class="small-box-footer">More info <i
                                                    class="fas fa-arrow-circle-right"></i></a>
                                        </div>
                                    </div>
                                </div>
                            @endcan
                            @can('read enquiry')
                                <div class="col-md-3 col-sm-6 col-12">
                                    <div class="info-box">
                                        <span class="info-box-icon bg-info"><i class="far fa-bookmark"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">Enquiries Today</span>
                                            <span class="info-box-number">{{ $enquiryCount }}</span>
                                            <a href="{{ route('enquiry.index') }}" class="small-box-footer">More info <i
                                                    class="fas fa-arrow-circle-right"></i></a>
                                        </div>
                                    </div>
                                </div>
                            @endcan

                            @php
                                $classrooms = isClassTeacher();
                            @endphp
                            @if ($classrooms->count() > 0)
                                <div class="col-md-3 col-sm-6 col-12">
                                    <div class="info-box">
                                        <span class="info-box-icon bg-info"><i class="far fa-bookmark"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">Present Student Today</span>
                                            <span class="info-box-number">{{ $totalPresent }}</span>
                                            <a href="{{ route('student-attendance') }}" class="small-box-footer">More info
                                                <i class="fas fa-arrow-circle-right"></i></a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6 col-12">
                                    <div class="info-box">
                                        <span class="info-box-icon bg-info"><i class="far fa-bookmark"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">Absent Student Today</span>
                                            <span class="info-box-number">{{ count($totalAbsent) }}</span>
                                            <a href="#" data-toggle="modal" data-target="#absentModal"
                                                class="small-box-footer">More info
                                                <i class="fas fa-arrow-circle-right"></i></a>
                                        </div>
                                    </div>
                                </div>
                            @endif

                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card fixed-width-card">
                    <div class="card-header  border-0">
                        <div class="d-flex justify-content-between">
                            <h3 class="card-title">
                                <i class="ion ion-clipboard mr-1"></i>
                                Birthday's Today
                            </h3>
                            <a href="#" data-toggle="modal" data-target="#modalBirthdays">Upcoming Birthdays</a>
                        </div>
                    </div>
                    <div class="card-body">
                        <ul class="todo-list" data-widget="todo-list">
                            @php
                                $todayStudentBirthdays = $studentBirthdays->filter(function ($birthday) {
                                    return date('m-d', strtotime($birthday->date_of_birth)) == date('m-d');
                                });
                            @endphp
                            @if (count($todayStudentBirthdays) > 0)
                                <h3 class="main-title">Student</h3>
                            @endif
                            @foreach ($todayStudentBirthdays as $birthday)
                                @if (date('m-d', strtotime($birthday->date_of_birth)) == date('m-d'))
                                    <li data-toggle="tooltip" data-placement="right"
                                        title=" {{ \Carbon\Carbon::parse(\Carbon\Carbon::now())->diffInDays(date('d. M', strtotime($birthday->date_of_birth . ' +1 day'))) }} Days Remaining">
                                        <a href="javascript:void(0)">
                                            <i class="fa fa-birthday-cake"></i>
                                            @php
                                                $img =
                                                    $birthday->photo != null && $birthday->photo != 'undefined'
                                                        ? asset('storage/' . tenantData('subdomain') . '/student_img/' . $birthday->photo)
                                                        : $tenantLogo;

                                            @endphp
                                            <span class="text"><img
                                                    style="width: 40px !important;height: 1% !important;"
                                                    src="{{ $img }}" class="img-circle elevation-2"></span>
                                            <span class="text">{{ $birthday->first_name }} {{ $birthday->middle_name }}
                                                {{ $birthday->last_name }} ({{ $birthday->class_name }})</span>
                                        </a>
                                        <small
                                            class="badge badge-success">{{ date('d. M. Y', strtotime($birthday->date_of_birth)) }}</small>
                                    </li>
                                @endif
                            @endforeach
                            @if (count($todayStudentBirthdays) <= 0)
                                <li><span class="text">No Data Found</span></li>
                            @endif
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card fixed-width-card">
                    <div class="card-header  border-0">
                        <h3 class="card-title">
                            <i class="ion ion-clipboard mr-1"></i>
                            Circulars
                        </h3>
                    </div>
                    <div class="card-body">
                        <ul class="todo-list" data-widget="todo-list">
                            @foreach ($circulars as $circular)
                                <li class="main-title-flex">
                                    <span><a href="{{ route('circulars.download', $circular->id) }}"><span
                                                class="text">{{ $circular->title }}</span></a>
                                        <small class="badge badge-danger"><i class="far fa-clock"></i>
                                            {!! $circular->created_at->format('d. M. Y') !!}</small>
                                    </span>
                                    <span>
                                        <a title="download" href="{{ route('circulars.download', $circular->id) }}"><i
                                                class="fas fa-download"></i></a>
                                        <span>
                                </li>
                            @endforeach
                            @if (count($circulars) <= 0)
                                <li><span class="text">No Data Found</span></li>
                            @endif
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <div class="modal" id="absentModal" role="dialog" aria-labelledby="roleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg salary-o-popup" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="modeltitle" class="box-title popup-title m-0">Absent Student Today ({{ date('d-m-y') }})</h3>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="card">
                        <div class="card-body">
                            <ul class="todo-list" data-widget="todo-list">
                                @foreach ($totalAbsent as $absent)
                                    <li>
                                        <a href="javascript:void(0)">
                                            <span class="text">{{ $absent->first_name }}
                                                {{ $absent->last_name }}</span>
                                        </a>
                                        <small class="badge badge-success">{{ $absent->class_name }}</small>
                                    </li>
                                @endforeach
                            </ul>
                            @if (count($totalAbsent) <= 0)
                                <li><span class="text">No Data Found</span></li>
                            @endif
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
@endsection
