"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import { Button } from "@/components/ui/button";
import { FcGoogle } from "react-icons/fc";
import { googleAuthStudent } from "@/services/studentAuthServices";
import { setStudentAuthToken } from "@/lib/utils";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { Loader2 } from "lucide-react";
import Script from "next/script";

declare global {
  interface Window {
    google: any;
  }
}

interface GoogleLoginButtonProps {
  onSuccess?: () => void;
  uwhiz?: boolean;
  handleApplyNow?: any;
}

export function GoogleLoginButton({ onSuccess, uwhiz, handleApplyNow }: GoogleLoginButtonProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [googleScriptLoaded, setGoogleScriptLoaded] = useState(false);
  const router = useRouter();
  const googleButtonRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (localStorage.getItem('user')) {
      toast.error("You are already logged in as a tutor. Please logout first to login as student.");
      router.push("/");
    }
  }, [router]);

  const handleGoogleResponse = useCallback(async (response: any) => {
    try {
      setIsLoading(true);
      const payload = parseJwt(response.credential);

      if (!payload) {
        toast.error("Failed to authenticate with Google");
        return;
      }

      // Get referral code from localStorage if available
      const referralCode = localStorage.getItem('referralCode');

      const googleAuthData = {
        googleId: payload.sub,
        email: payload.email,
        firstName: payload.given_name,
        lastName: payload.family_name,
        profilePhoto: payload.picture,
        ...(referralCode && { referralCode }),
      };

      const authResponse = await googleAuthStudent(googleAuthData);

      if (authResponse.success) {
        const { token, user } = authResponse.data;

        if (token) {
          setStudentAuthToken(token);

          if (user) {
            localStorage.setItem('student_data', JSON.stringify(user));
          }

          window.dispatchEvent(new Event("storage"));
        }

        toast.success("Logged in successfully with Google");

        // Clear referral code after successful registration
        localStorage.removeItem('referralCode');

        if (onSuccess) {
          onSuccess();
        }

        if (uwhiz) {
          handleApplyNow()
        } else {  
          router.push("/");
        }
      } else {
        toast.error(authResponse.message || "Failed to authenticate with Google");
      }
    } catch (error: any) {
      if (error.response && error.response.data) {
        const errorMessage = error.response.data.message || "Authentication failed";
        toast.error(`Error: ${errorMessage}`);
      } else if (error.message) {
        toast.error(`Error: ${error.message}`);
      } else {
        toast.error("An error occurred during Google authentication");
      }
    } finally {
      setIsLoading(false);
    }
  }, [setIsLoading, onSuccess, router, uwhiz, handleApplyNow]);

  useEffect(() => {
    if (typeof window !== 'undefined' && window.google && googleScriptLoaded) {
      try {
        window.google.accounts.id.initialize({
          client_id: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
          callback: handleGoogleResponse,
          auto_select: false,
        });

        if (googleButtonRef.current) {
          window.google.accounts.id.renderButton(googleButtonRef.current, {
            type: "standard",
            theme: "outline",
            size: "large",
            text: "continue_with",
            shape: "rectangular",
            width: googleButtonRef.current.offsetWidth,
          });
        }
      } catch (error) {
        console.error("Failed to initialize Google Sign-In:", error);
      }
    }
  }, [googleScriptLoaded, handleGoogleResponse]);

  const parseJwt = (token: string) => {
    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map((c) => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      );
      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error("Failed to parse JWT token:", error);
      return null;
    }
  };

  return (
    <>
      <Script
        src="https://accounts.google.com/gsi/client"
        strategy="afterInteractive"
        onLoad={() => setGoogleScriptLoaded(true)}
      />

      {isLoading ? (
        <Button
          type="button"
          variant="outline"
          className="w-full flex items-center justify-center gap-2 border-gray-300 bg-white text-gray-700 font-medium py-2.5 rounded-lg"
          disabled
        >
          <Loader2 className="h-5 w-5 animate-spin mr-2" />
          <span>Signing in...</span>
        </Button>
      ) : (
        <div
          ref={googleButtonRef}
          className="w-full h-10 flex justify-center items-center"
        >
          <Button
            type="button"
            variant="outline"
            className="w-full flex items-center justify-center gap-2 border-gray-300 bg-white hover:bg-gray-50 text-gray-700 font-medium py-2.5 rounded-lg transition-colors"
            onClick={() => {
              if (window.google?.accounts?.id) {
                window.google.accounts.id.prompt();
              } else {
                toast.error("Google authentication is not available");
              }
            }}
          >
            <FcGoogle className="h-5 w-5" />
            <span>Continue with Google</span>
          </Button>
        </div>
      )}
    </>
  );
}
