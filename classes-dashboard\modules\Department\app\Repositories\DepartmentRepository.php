<?php

namespace Department\Repositories;

use Department\Models\Department;
use Department\Interfaces\DepartmentInterface;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class DepartmentRepository implements DepartmentInterface
{
  protected $department;

  function __construct(Department $department)
  {
    $this->department = $department;
  }

  public function getAll($request)
  {
    $department = $this->department
    ->where('class_uuid', Auth::id())
     ->select('department.*');
    searchColumn($request->input('columns'), $department);
    orderColumn($request, $department, 'department.id');

    return $department;
  }

  public function getDatatable($list)
  {
    return datatables()->of($list)
      ->addColumn('name', function ($data) {
        $name = $data->name;
        return $name;
      })
      ->addColumn('educational', function ($data) {
        return ($data->educational) ? "Yes" : "No";
      })
      ->addColumn('action', function ($data) {
        $button = '';
        if (Auth::user()->can('update department')) {
          $button .= '<button data-editdepartmentid="' . $data->id . '" data-toggle="modal" title="Edit" data-target="#newDepartmentEntry" class="btn editdepartmentEntry"><i class="fas fa-edit"></i></button>';
        }
        if (Auth::user()->can('delete department')) {
          $button .= '<button type="button" class="deleteDepartmentEntry btn" title="Delete" data-deletedepartmentid="' . $data->id . '"><i class="fa fa-trash"></i></button>';
        }
        return $button;
      })->rawColumns(['File', 'action'])
      ->make(true);
  }

  public function storeDepartment($request)
  {
    if (isset($request->educational)) {
      $educational = 1;
    } else {
      $educational = 0;
    }

    $this->department::create([
      'name' => $request->name,
      'educational' => $educational,
      'class_uuid' => Auth::id(),
    ]);
  }

  public function updateDepartment($request, $id)
  {
    if (isset($request->educational)) {
      $educational = 1;
    } else {
      $educational = 0;
    }

    $department = $this->department::find($id);
    $department->name = $request->name;
    $department->educational = $educational;
    $department->save();
  }

  public function getDepartmentById($id)
  {
    return $this->department::Find($id);
  }

  public function getDepartmentList()
  {
    return $this->department::where('class_uuid', Auth::id())->select('id', 'name')->get();
  }

  public function hasClassrooms($id)
  {
    return DB::table('classrooms')->where('department_id', $id)->exists();
  }

  public function deleteDepartment($id)
  {
    $department = $this->getDepartmentById($id);

    try {
      $department->delete();
      return [
        'success' => true,
        'message' => 'Department deleted successfully!!'
      ];
    } catch (\Illuminate\Database\QueryException $e) {
      if ($e->getCode() === '23000') {
        return [
          'success' => false,
          'message' => 'First assign classroom to new department then try to delete'
        ];
      }
      throw $e;
    }
  }
}
