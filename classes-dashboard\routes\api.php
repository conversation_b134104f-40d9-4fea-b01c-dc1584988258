<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Symfony\Component\Process\Process;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

Route::post('/run-create-school', function (Request $request) {

    $apiKey = $request->header('X-API-KEY');
    
    if ($apiKey !== env('SCHOOL_CREATION_API_KEY')) {
        return response()->json(['error' => 'Unauthorized'], 403);
    }

    $subdomain = $request->input('subdomain');
    $schoolName = $request->input('school_name');

    if (!$subdomain || !$schoolName) {
        return response()->json(['error' => 'Invalid input'], 400);
    }

    $process = new Process(['/opt/homebrew/bin/php', base_path('artisan'), 'school:create', $subdomain, $schoolName]);
    $process->setTimeout(300);
    $process->start();

    $output = [];

    foreach ($process as $data) {
        $output[] = $data;
    }

    return response()->json([
        'message' => "School creation started!",
        'log' => $output
    ]);
});
