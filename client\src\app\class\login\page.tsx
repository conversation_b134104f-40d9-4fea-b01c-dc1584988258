'use client';

import Header from '@/app-components/Header'
import Footer from '@/app-components/Footer'
import React, { useState, useEffect, Suspense } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { MdEmail } from "react-icons/md";
import { RiLockPasswordLine } from "react-icons/ri";
import { FiUser } from "react-icons/fi";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle, Loader2, Eye, EyeOff } from "lucide-react";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { toast } from "sonner";
import ReCAPTCHA from "react-google-recaptcha";
import { loginUser, registerUser, forgotPassword, resendVerificationEmail } from "@/services/AuthService";
import { useDispatch } from "react-redux";
import { AppDispatch } from "@/store";
import { setUser } from "@/store/slices/userSlice";
import { useRouter, useSearchParams } from "next/navigation";

const loginSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address' }),
  password: z.string().min(6, { message: 'Password must be at least 6 characters' }),
  captcha: z.string().min(6, { message: 'Please complete the CAPTCHA' }),
});

const registerSchema = z.object({
  firstName: z.string().min(2, { message: 'First name is required' }),
  lastName: z.string().min(2, { message: 'Last name is required' }),
  email: z.string().email({ message: 'Please enter a valid email address' }),
  password: z.string().min(6, { message: 'Password must be at least 6 characters' }),
  captcha: z.string().min(6, { message: 'Please complete the CAPTCHA' }),
});

const forgotPasswordSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address' }),
});

type LoginFormValues = z.infer<typeof loginSchema>;
type RegisterFormValues = z.infer<typeof registerSchema>;
type ForgotPasswordFormValues = z.infer<typeof forgotPasswordSchema>;

// Form error alert component
const FormErrorAlert = ({ message }: { message: string }) => {
  if (!message) return null;

  return (
    <Alert className="mb-4 border-red-500 bg-red-50 dark:bg-red-900/20">
      <AlertCircle className="h-4 w-4 text-red-500" />
      <AlertDescription className="text-red-500">{message}</AlertDescription>
    </Alert>
  );
};

function ClassesLoginPageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const dispatch = useDispatch<AppDispatch>();
  const [showForgotPasswordForm, setShowForgotPasswordForm] = useState(false);
  const [isLogin, setIsLogin] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [disablebtn, setDisablebtn] = useState<boolean>(false);
  const [timeleft, setTimeleft] = useState<number>(120);
  const [showPassword, setShowPassword] = useState(false);
  const [authError, setAuthError] = useState<string>("");
  const [forgotPasswordError, setForgotPasswordError] = useState<string>("");
  const [referralCode, setReferralCode] = useState<string | null>(null);

  // Auth form (login/register)
  const authForm = useForm<LoginFormValues | RegisterFormValues>({
    resolver: zodResolver(isLogin ? loginSchema : registerSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      password: '',
      captcha: '',
    },
  });

  // Forgot password form
  const forgotPasswordForm = useForm<ForgotPasswordFormValues>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      email: '',
    },
  });

  useEffect(() => {
    if (localStorage.getItem('studentToken')) {
      toast.error("You are already logged in as a student. Please logout first to login as tutor.");
      router.push("/");
    }
  }, [router]);

  const onAuthSubmit = async (data: LoginFormValues | RegisterFormValues) => {
    setIsSubmitting(true);
    setAuthError("");

    try {
      // Add referral code to registration data
      const submitData = !isLogin && referralCode ? { ...data, referralCode } : data;
      const response = isLogin ? await loginUser(submitData) : await registerUser(submitData);

      if (response.success === false) {
        setAuthError(response.message || 'Authentication failed');
        return;
      }
      if (isLogin && response.data) {
        const { user } = response.data;
        if (user) {
          dispatch(setUser({ user }));
        }
        toast.success("Logged in successfully");
        authForm.reset();
        router.push("/");
      } else {
        toast.success(response.message || "Verification email sent. Please check your inbox and verify your email.");
        authForm.reset();
        setIsLogin(true);
        // Clear referral code after successful registration
        localStorage.removeItem('referralCode');
        setReferralCode(null);
      }
    } catch (error: unknown) {
      const errorMessage = (error as any)?.response?.data?.message || "Something went wrong";
      setAuthError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  useEffect(() => {
    const refCode = searchParams.get('ref');
    if (refCode) {
      setReferralCode(refCode);
      // Store in localStorage for use during registration
      localStorage.setItem('referralCode', refCode);
    }
  }, [searchParams]);

  useEffect(() => {
    let interval: any;
    if (disablebtn && timeleft > 0) {
      interval = setInterval(() => {
        setTimeleft((prev) => {
          if (prev <= 1) {
            clearInterval(interval);
            setDisablebtn(false);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [disablebtn, timeleft]);

  // resend verification email
  const VerificationEmail = async (email: string) => {
    setAuthError(""); // Clear previous errors
    try {
      if (!email) {
        setAuthError("Please enter your email address first");
        return;
      }

      const response = await resendVerificationEmail(email);
      setTimeleft(120);
      setDisablebtn(true);

      if (response.success === false) {
        setAuthError("Failed to resend verification email");
        return;
      }
      toast.success("Verification email sent. Please check your inbox.");
      authForm.reset();
    } catch (error: any) {
      const errorMessage = (error as any)?.response?.data?.message || "Something went wrong";
      setAuthError(errorMessage);
    }
  };

  const onForgotPasswordSubmit = async (data: ForgotPasswordFormValues) => {
    setIsSubmitting(true);
    setForgotPasswordError(""); // Clear previous errors
    try {
      const response = await forgotPassword(data.email);
      if (response.success === false) {
        setForgotPasswordError(response.message || 'Failed to send reset email');
        return;
      }
      toast.success(response.message || 'Password reset email sent');
      setShowForgotPasswordForm(false);
      forgotPasswordForm.reset();
    } catch (error: any) {
      setForgotPasswordError(error?.response?.data?.message || 'An error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <Header />
      <main className="min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-50 to-gray-100 py-12 px-4 sm:px-6 lg:px-8">
        <div className="w-full max-w-xl bg-white rounded-2xl shadow-xl border border-gray-100 p-8 relative overflow-hidden">
          {!showForgotPasswordForm ? (
            <>
              <div className="text-center mb-6">
                <div className="bg-orange-50 border border-orange-100 rounded-lg py-2 px-4 mb-4">
                  <p className="text-center text-orange-700 font-medium">
                    {isLogin ? "Coaching Classes Login Portal" : "Coaching Classes Registration Portal"}
                  </p>
                </div>

                <div className="flex justify-center mb-4">
                  <div className="flex bg-gray-100 rounded-lg p-1">
                    <Button
                      variant={isLogin ? "default" : "ghost"}
                      className={`px-4 py-2 rounded-lg ${isLogin
                        ? "bg-[#ff914d] text-white hover:bg-[#ff914d]/90"
                        : "text-gray-600 hover:text-[#ff914d]"
                      }`}
                      onClick={() => {
                        setIsLogin(true);
                        setAuthError("");
                      }}
                    >
                      Class Login
                    </Button>
                    <Button
                      variant={!isLogin ? "default" : "ghost"}
                      className={`px-4 py-2 rounded-lg ${!isLogin
                        ? "bg-[#ff914d] text-white hover:bg-[#ff914d]/90"
                        : "text-gray-600 hover:text-[#ff914d]"
                      }`}
                      onClick={() => {
                        setIsLogin(false);
                        setAuthError("");
                      }}
                    >
                      Class Sign Up
                    </Button>
                  </div>
                </div>

                <h2 className="text-2xl font-bold text-gray-800 mb-2">
                  {isLogin ? "Welcome Back to Your Class Portal" : "Register Your Coaching Class"}
                </h2>
                <div className="flex items-center justify-center">
                  <div className="h-0.5 w-16 bg-orange-300 mr-3"></div>
                  <span className="text-[#ff914d] font-medium">COACHING CLASS PORTAL</span>
                  <div className="h-0.5 w-16 bg-orange-300 ml-3"></div>
                </div>

                {referralCode && !isLogin && (
                  <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                    <p className="text-sm text-green-700 text-center">
                      🎉 You&apos;re joining via referral code: <span className="font-semibold">{referralCode}</span>
                    </p>
                  </div>
                )}
              </div>

              <div>
                <Form {...authForm}>
                  <form
                    onSubmit={authForm.handleSubmit(onAuthSubmit)}
                    className="space-y-6"
                  >
                    {authError && <FormErrorAlert message={authError} />}
                    {!isLogin && (
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <FormField
                          control={authForm.control}
                          name="firstName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-gray-700 font-medium">Owner&apos;s First Name</FormLabel>
                              <FormControl>
                                <div className="relative">
                                  <FiUser
                                    className="absolute left-3 top-1/2 -translate-y-1/2 text-[#ff914d]"
                                    size={20}
                                  />
                                  <Input
                                    placeholder="First Name"
                                    className="pl-10 rounded-lg border-gray-200 bg-white focus:border-[#ff914d] focus:ring-[#ff914d]/20"
                                    {...field}
                                  />
                                </div>
                              </FormControl>
                              <FormMessage className="text-red-500" />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={authForm.control}
                          name="lastName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-gray-700 font-medium">Owner&apos;s Last Name</FormLabel>
                              <FormControl>
                                <div className="relative">
                                  <FiUser
                                    className="absolute left-3 top-1/2 -translate-y-1/2 text-[#ff914d]"
                                    size={20}
                                  />
                                  <Input
                                    placeholder="Last Name"
                                    className="pl-10 rounded-lg border-gray-200 bg-white focus:border-[#ff914d] focus:ring-[#ff914d]/20"
                                    {...field}
                                  />
                                </div>
                              </FormControl>
                              <FormMessage className="text-red-500" />
                            </FormItem>
                          )}
                        />
                      </div>
                    )}
                    <FormField
                      control={authForm.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-gray-700 font-medium">Class Email Address</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <MdEmail
                                className="absolute left-3 top-1/2 -translate-y-1/2 text-[#ff914d]"
                                size={20}
                              />
                              <Input
                                type="email"
                                placeholder="Your Class Email Address"
                                className="pl-10 rounded-lg border-gray-200 bg-white focus:border-[#ff914d] focus:ring-[#ff914d]/20"
                                {...field}
                              />
                            </div>
                          </FormControl>
                          <FormMessage className="text-red-500" />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={authForm.control}
                      name="password"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-gray-700 font-medium">Secure Password</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <RiLockPasswordLine
                                className="absolute left-3 top-1/2 -translate-y-1/2 text-[#ff914d]"
                                size={20}
                              />
                              <Input
                                type={showPassword ? "text" : "password"}
                                placeholder="Your Password"
                                className="pl-10 pr-10 rounded-lg border-gray-200 bg-white focus:border-[#ff914d] focus:ring-[#ff914d]/20"
                                {...field}
                              />
                              <button
                                type="button"
                                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-[#ff914d]"
                                onClick={() => setShowPassword(!showPassword)}
                                tabIndex={-1}
                              >
                                {showPassword ? (
                                  <EyeOff size={20} />
                                ) : (
                                  <Eye size={20} />
                                )}
                              </button>
                            </div>
                          </FormControl>
                          <FormMessage className="text-red-500" />
                        </FormItem>
                      )}
                    />
                    {isLogin && (
                      <div className="flex justify-between text-sm text-right">
                        <button
                          type="button"
                          className={`text-[#ff914d] transition ${disablebtn
                            ? "opacity-50 cursor-not-allowed"
                            : "hover:text-[#ff914d]/80"
                            }`}
                          onClick={() => VerificationEmail(authForm.getValues("email"))}
                          disabled={disablebtn}
                        >
                          <div className="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            {disablebtn ? `Verification mail (${timeleft}s)` : "Verify Email"}
                          </div>
                        </button>
                        <button
                          type="button"
                          className="text-[#ff914d] hover:text-[#ff914d]/80 flex items-center"
                          onClick={() => {
                            setShowForgotPasswordForm(true);
                            setAuthError("");
                            setForgotPasswordError("");
                          }}
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                          </svg>
                          Forgot Password?
                        </button>
                      </div>
                    )}

                    <FormField
                      control={authForm.control}
                      name="captcha"
                      render={({ field }) => (
                        <FormItem className="flex flex-col items-center">
                          <FormLabel className="sr-only">CAPTCHA</FormLabel>
                          <FormControl>
                            <ReCAPTCHA
                              sitekey={process.env.NEXT_PUBLIC_CAPTCHA_SITE_KEY || ""}
                              onChange={(token) => field.onChange(token)}
                            />
                          </FormControl>
                          <FormMessage className="text-red-500" />
                        </FormItem>
                      )}
                    />

                    <Button
                      type="submit"
                      className="w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white font-medium py-2.5 rounded-lg transition-colors"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? (
                        <Loader2 className="h-5 w-5 animate-spin" />
                      ) : isLogin ? (
                        "Login to Class Portal"
                      ) : (
                        "Register Your Coaching Class"
                      )}
                    </Button>

                    <div className="mt-4 text-center">
                      <p className="text-sm text-gray-500">
                        By continuing, you agree to our{" "}
                        <a href="https://www.uest.in/terms-and-conditions" className="text-[#ff914d] hover:underline">Terms & Conditions</a>{" "}
                        and{" "}
                        <a href="https://www.uest.in/privacy-policy" className="text-[#ff914d] hover:underline">Privacy Policy</a>
                      </p>
                    </div>
                  </form>
                </Form>
              </div>
            </>
          ) : (
            <div className="w-full bg-white rounded-2xl shadow-xl border border-gray-100 p-8 relative overflow-hidden">
              <div className="text-center mb-6">
                <div className="bg-orange-50 border border-orange-100 rounded-lg py-2 px-4 mb-4">
                  <p className="text-center text-orange-700 font-medium">
                    Coaching Class Account Recovery
                  </p>
                </div>
                <div className="inline-flex items-center justify-center mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-[#ff914d] mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                  </svg>
                  <h2 className="text-2xl font-bold text-gray-800">Forgot Password</h2>
                </div>
                <p className="text-gray-500 mb-4">
                  Enter your class email to receive a password reset link
                </p>
              </div>
              <Form {...forgotPasswordForm}>
                <form
                  onSubmit={forgotPasswordForm.handleSubmit(onForgotPasswordSubmit)}
                  className="space-y-6"
                >
                  {forgotPasswordError && <FormErrorAlert message={forgotPasswordError} />}
                  <FormField
                    control={forgotPasswordForm.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-gray-700 font-medium">Class Email Address</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <MdEmail
                              className="absolute left-3 top-1/2 -translate-y-1/2 text-[#ff914d]"
                              size={20}
                            />
                            <Input
                              type="email"
                              placeholder="Your Class Email Address"
                              className="pl-10 rounded-lg border-gray-200 bg-white focus:border-[#ff914d] focus:ring-[#ff914d]/20"
                              {...field}
                            />
                          </div>
                        </FormControl>
                        <FormMessage className="text-red-500" />
                      </FormItem>
                    )}
                  />
                  <div className="flex gap-4">
                    <Button
                      type="button"
                      variant="outline"
                      className="flex-1 border-[#ff914d] text-[#ff914d] hover:bg-[#ff914d]/10"
                      onClick={() => {
                        setShowForgotPasswordForm(false);
                        setAuthError("");
                        setForgotPasswordError("");
                      }}
                    >
                      Back to Login
                    </Button>
                    <Button
                      type="submit"
                      className="flex-1 bg-[#ff914d] hover:bg-[#ff914d]/90 text-white"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? (
                        <Loader2 className="h-5 w-5 animate-spin" />
                      ) : (
                        "Send Reset Link"
                      )}
                    </Button>
                  </div>
                </form>
              </Form>
            </div>
          )}
        </div>
      </main>
      <Footer />
    </>
  );
}

export default function ClassesLoginPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
      </div>
    }>
      <ClassesLoginPageContent />
    </Suspense>
  );
}