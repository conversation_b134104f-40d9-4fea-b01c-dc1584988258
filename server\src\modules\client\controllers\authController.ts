import { Request, Response } from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import {
  createUser,
  findUserByEmail,
  updateUserPassword,
  updateUserResetToken,
} from '../services/authService';
import { sendError, sendSuccess } from '@/utils/response';
import { transporter } from '@/utils/email';
import { createVerificationEmailTemplate, createPasswordResetTemplate } from '@/utils/emailTemplates';
import crypto from 'crypto';
import dotenv from 'dotenv';
import axios from "axios";
import prisma from "@/config/prismaClient";
import { getReferralLinkByCode, createReferral } from '../services/referralService';

dotenv.config();

const JWT_SECRET = process.env.JWT_SECRET || 'secret123';
dotenv.config();
const COOKIE_NAME = 'client_jwt';

export const registerUser = async (
  req: Request,
  res: Response
): Promise<any> => {
  const { firstName, lastName, email, password, captcha, referralCode } = req.body;
  const clientType = req.headers["client-type"]

  const existingUser = await findUserByEmail(email);
  if (existingUser) {
    return sendError(res, "User already exists", 400);
  }

  if (clientType !== "mobile-app") {
    if (!captcha) {
      return sendError(
        res,
        "CAPTCHA is required for browser registration",
        400
      );
    }

    const reCAPTCHASecretKey = process.env.CAPTCHA_SECRET_KEY;
    const reCAPTCHAResponse = captcha;

    const reCAPTCHAValidationResponse = await axios.post(
      `https://www.google.com/recaptcha/api/siteverify`,
      null,
      {
        params: {
          secret: reCAPTCHASecretKey,
          response: reCAPTCHAResponse,
        },
      }
    );

    if (!reCAPTCHAValidationResponse.data.success) {
      return sendError(res, "reCAPTCHA validation failed", 400);
    }
  }

  const hashedPassword = await bcrypt.hash(password, 10);
  const user = await createUser(firstName, lastName, email, hashedPassword);

  // Handle referral tracking
  if (referralCode) {
    try {
      const referralLink = await getReferralLinkByCode(referralCode);
      if (referralLink && referralLink.isActive) {
        await createReferral(
          referralLink.id,
          user.id,
          'CLASS',
          `${firstName} ${lastName}`,
          email
        );
      }
    } catch (error) {
      console.log('Referral tracking error:', error);
      // Don't fail registration if referral tracking fails
    }
  }

  const token = jwt.sign({ id: user.id, email: user.email }, JWT_SECRET, {
    expiresIn: '1d',
  });

  const verificationURL = `${process.env.FRONTEND_URL}/verify-email?token=${token}&email=${email}`;

  const emailHtml = createVerificationEmailTemplate(verificationURL, false);

  await transporter.sendMail({
    from: process.env.EMAIL_USER,
    to: email,
    subject: "Email Verification : Uest.in",
    html: emailHtml,
  })

  res.status(201).json({ message: "Verification email sent. please verify your email." });
};


export const verifyEmail = async (req: Request, res: Response): Promise<any> => {
  try {
    const { token } = req.query;
    const decode = jwt.verify(token as string, JWT_SECRET) as any;

    const classes = await prisma.classes.findUnique({
      where: { id: decode.id as string },
    });

    if (!classes) {
      return res.status(400).json({ message: "Invalid or expired token" });
    }

    if (classes.isVerified) {
      return res.json({ message: "Email already verified" });
    }

    const classToken = jwt.sign({ id: classes.id, email: classes.email }, JWT_SECRET, {
      expiresIn: "1d",
    });

    await prisma.classes.update({
      where: { id: classes.id },
      data: { isVerified: true },
    });

    res.cookie(COOKIE_NAME, classToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      maxAge: 24 * 60 * 60 * 1000, // 1 day
    });

    res.json({ message: "Email verified successfully!", token: classToken });
  } catch {
    res.status(400).json({ message: "Invalid or expired token" });
  }
};

export const resendVerificationEmail = async (req: Request, res: Response): Promise<any> => {
  const { email } = req.body;

  const user = await findUserByEmail(email);
  if (!user) return sendError(res, "User not found", 404);
  if (user.isVerified) return sendError(res, "Email already verified", 400);

  const token = jwt.sign({ id: user.id, email: user.email }, JWT_SECRET, {
    expiresIn: "1d",
  });

  const verificationURL = `${process.env.FRONTEND_URL}/verify-email?token=${token}&email=${email}`;

  const emailHtml = createVerificationEmailTemplate(verificationURL, true);

  await transporter.sendMail({
    from: process.env.EMAIL_USER,
    to: email,
    subject: "Email Verification : Uest.in",
    html: emailHtml,
  });

  res.json({ message: "Verification email sent" });
};

export const loginUser = async (req: Request, res: Response): Promise<any> => {
  const { email, password, captcha } = req.body;
  const clientType = req.headers["client-type"]

  const user = await findUserByEmail(email);
  if (!user) return sendError(res, "User not found", 404);

  const isVerified = user.isVerified;
  if (!isVerified) return sendError(res, "Please verify your email.", 403);

  const isValid = await bcrypt.compare(password, user.password);
  if (!isValid) return sendError(res, "Invalid credentials", 403);

  if (clientType !== "mobile-app") {
    if (!captcha) {
      return sendError(res, "CAPTCHA is required for browser login", 400);
    }

    const reCAPTCHASecretKey = process.env.CAPTCHA_SECRET_KEY;
    const reCAPTCHAResponse = captcha;

    const reCAPTCHAValidationResponse = await axios.post(
      `https://www.google.com/recaptcha/api/siteverify`,
      null,
      {
        params: {
          secret: reCAPTCHASecretKey,
          response: reCAPTCHAResponse,
        },
      }
    );

    if (!reCAPTCHAValidationResponse.data.success) {
      return sendError(res, "reCAPTCHA validation failed", 400);
    }
  }

  const token = jwt.sign({ id: user.id, email: user.email }, JWT_SECRET, {
    expiresIn: '1d',
  });

  if (clientType === "mobile-app") {
    return sendSuccess(res, { user, token }, "Login successful");
  } else {
    res.cookie(COOKIE_NAME, token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      maxAge: 24 * 60 * 60 * 1000, // 1 day
    });
    return sendSuccess(res, { user }, "Login successful");
  }
};

export const forgotPasswordController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { email } = req.body;
    if (!email) {
      return res.status(400).json({ success: false, message: 'Email is required' });
    }

    const user = await findUserByEmail(email);
    if (!user) {
      return res.status(404).json({ success: false, message: 'User not found' });
    }

    const token = crypto.randomBytes(32).toString('hex');
    const expiry = new Date(Date.now() + 3600000);

    await updateUserResetToken(email, token, expiry);

    const resetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${token}&email=${email}`;

    const emailHtml = createPasswordResetTemplate(resetUrl);

    const mailOptions = {
      from: process.env.EMAIL_USER,
      to: email,
      subject: 'Password Reset Request : Uest.in',
      html: emailHtml,
    };

    await transporter.sendMail(mailOptions);
    res.json({ success: true, message: 'Password reset email sent' });
  } catch (error: any) {
    console.log(error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
};

export const resetPasswordController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { email, token, newPassword } = req.body;
    if (!email || !token || !newPassword) {
      return res.status(400).json({ success: false, message: 'Missing required fields' });
    }

    const user = await findUserByEmail(email);
    if (!user || user.resetToken !== token || !user.resetTokenExpiry) {
      return res.status(400).json({ success: false, message: 'Invalid or expired token' });
    }

    if (user.resetTokenExpiry < new Date()) {
      return res.status(400).json({ success: false, message: 'Token expired' });
    }

    const hashedPassword = await bcrypt.hash(newPassword, 10);

    await updateUserPassword(email, hashedPassword);
    await updateUserResetToken(email, null, null);

    res.json({ success: true, message: 'Password reset successful' });
  } catch (error: any) {
    res.status(500).json({ success: false, message: 'Server error'+error.message });
  }
};

export async function logout(_req: Request, res: Response): Promise<any> {
  res.clearCookie(COOKIE_NAME, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
  });

  return sendSuccess(res, null, 'Logged out successfully');
}

export const generateJWT = async (req: Request, res: Response): Promise<any> => {
  const { email, password } = req.body;

  const user = await findUserByEmail(email);
  if (!user) return sendError(res, "User not found", 404);

  const isVerified = user.isVerified;
  if (!isVerified) return sendError(res, "Please verify your email.", 403);

  const isValid = password === user.password;
  if (!isValid) return sendError(res, "Invalid credentials", 403);

  const token = jwt.sign({ id: user.id, email: user.email }, JWT_SECRET, {
    expiresIn: '1d',
  });

  return sendSuccess(res, { user, token }, "Login successful");
};