{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from 'clsx';\r\nimport { twMerge } from 'tailwind-merge';\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\nexport const truncateThought = (text: string, wordLimit: number = 5): string => {\r\n  const words = text.trim().split(/\\s+/);\r\n  if (words.length <= wordLimit) return text;\r\n  return words.slice(0, wordLimit).join(' ') + '...';\r\n};\r\n\r\nexport const setStudentAuthToken = (token: string) => {\r\n  localStorage.setItem('studentToken', token);\r\n};\r\n\r\nexport const getStudentAuthToken = (): string | null => {\r\n  return localStorage.getItem('studentToken');\r\n};\r\n\r\nexport const clearStudentAuthToken = () => {\r\n  localStorage.removeItem('studentToken');\r\n};\r\n\r\nexport const isStudentAuthenticated = (): boolean => {\r\n  return !!getStudentAuthToken();\r\n};"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,MAAM,kBAAkB,CAAC,MAAc,YAAoB,CAAC;IACjE,MAAM,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC;IAChC,IAAI,MAAM,MAAM,IAAI,WAAW,OAAO;IACtC,OAAO,MAAM,KAAK,CAAC,GAAG,WAAW,IAAI,CAAC,OAAO;AAC/C;AAEO,MAAM,sBAAsB,CAAC;IAClC,aAAa,OAAO,CAAC,gBAAgB;AACvC;AAEO,MAAM,sBAAsB;IACjC,OAAO,aAAa,OAAO,CAAC;AAC9B;AAEO,MAAM,wBAAwB;IACnC,aAAa,UAAU,CAAC;AAC1B;AAEO,MAAM,yBAAyB;IACpC,OAAO,CAAC,CAAC;AACX", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: 'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',\r\n        destructive:\r\n          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\r\n        outline:\r\n          'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50',\r\n        secondary: 'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',\r\n        ghost: 'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',\r\n        link: 'text-primary underline-offset-4 hover:underline',\r\n      },\r\n      size: {\r\n        default: 'h-9 px-4 py-2 has-[>svg]:px-3',\r\n        sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',\r\n        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',\r\n        icon: 'size-9',\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default',\r\n      size: 'default',\r\n    },\r\n  }\r\n);\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<'button'> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean;\r\n  }) {\r\n  const Comp = asChild ? Slot : 'button';\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/store/hooks.ts"], "sourcesContent": ["import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';\r\nimport type { RootState, AppDispatch } from './index';\r\n\r\n// Use throughout your app instead of plain `useDispatch` and `useSelector`\r\nexport const useAppDispatch = () => useDispatch<AppDispatch>();\r\nexport const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAIO,MAAM,iBAAiB,IAAM,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;AACvC,MAAM,iBAAkD,yJAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/ProfileCompletionIndicator.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { Button } from '@/components/ui/button';\r\nimport { User, ArrowRight } from 'lucide-react';\r\nimport { useSelector } from 'react-redux';\r\nimport { RootState } from '@/store';\r\n\r\nconst ProfileCompletionIndicator = () => {\r\n  const router = useRouter();\r\n  const { profileData } = useSelector((state: RootState) => state.studentProfile);\r\n\r\n  // Check if student is logged in\r\n  const isLoggedIn = typeof window !== 'undefined' && localStorage.getItem('studentToken') !== null;\r\n\r\n  // Check if student has any profile data - only check if profile ID exists\r\n  const hasProfile = profileData?.profile?.id !== undefined;\r\n\r\n  // Only show the indicator if student is logged in and doesn't have a profile\r\n  if (!isLoggedIn || hasProfile) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div className=\"bg-white dark:bg-gray-900 border border-orange-200 overflow-hidden  dark:border-orange-900\">\r\n      <div className=\"px-3 py-1.5 flex items-center justify-between\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          <div className=\"bg-[#ff914d] p-1.5 rounded-full\">\r\n            <User className=\"h-3 w-3 text-white\" />\r\n          </div>\r\n          <p className=\"text-xs font-medium text-gray-800 dark:text-gray-200\">\r\n            Please Complete your profile \r\n          </p>\r\n        </div>\r\n        <Button\r\n          onClick={() => router.push('/student/profile')}\r\n          className=\"bg-[#ff914d] hover:bg-[#e07c3a] text-white text-xs px-2 py-0.5 h-6 min-w-0\"\r\n          size=\"sm\"\r\n        >\r\n         Complete now <ArrowRight className=\"h-3 w-3\" />\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProfileCompletionIndicator;\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;;AASA,MAAM,6BAA6B;IACjC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAqB,MAAM,cAAc;IAE9E,gCAAgC;IAChC,MAAM,aAAa,gBAAkB,eAAe,aAAa,OAAO,CAAC,oBAAoB;IAE7F,0EAA0E;IAC1E,MAAM,aAAa,aAAa,SAAS,OAAO;IAEhD,6EAA6E;IAC7E,wCAA+B;QAC7B,OAAO;IACT;;AAuBF;uCAEe", "debugId": null}}, {"offset": {"line": 149, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/avatar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as AvatarPrimitive from '@radix-ui/react-avatar';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Avatar({ className, ...props }: React.ComponentProps<typeof AvatarPrimitive.Root>) {\r\n  return (\r\n    <AvatarPrimitive.Root\r\n      data-slot=\"avatar\"\r\n      className={cn('relative flex size-8 shrink-0 overflow-hidden rounded-full', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AvatarImage({ className, ...props }: React.ComponentProps<typeof AvatarPrimitive.Image>) {\r\n  return (\r\n    <AvatarPrimitive.Image\r\n      data-slot=\"avatar-image\"\r\n      className={cn('aspect-square size-full', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AvatarFallback({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\r\n  return (\r\n    <AvatarPrimitive.Fallback\r\n      data-slot=\"avatar-fallback\"\r\n      className={cn('bg-muted flex size-full items-center justify-center rounded-full', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback };\r\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EAAE,SAAS,EAAE,GAAG,OAA0D;IACxF,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8DAA8D;QAC3E,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAA2D;IAC9F,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oEAAoE;QACjF,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 201, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/popover.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as PopoverPrimitive from '@radix-ui/react-popover';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Popover({ ...props }: React.ComponentProps<typeof PopoverPrimitive.Root>) {\r\n  return <PopoverPrimitive.Root data-slot=\"popover\" {...props} />;\r\n}\r\n\r\nfunction PopoverTrigger({ ...props }: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\r\n  return <PopoverPrimitive.Trigger data-slot=\"popover-trigger\" {...props} />;\r\n}\r\n\r\nfunction PopoverContent({\r\n  className,\r\n  align = 'center',\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\r\n  return (\r\n    <PopoverPrimitive.Portal>\r\n      <PopoverPrimitive.Content\r\n        data-slot=\"popover-content\"\r\n        align={align}\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden',\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </PopoverPrimitive.Portal>\r\n  );\r\n}\r\n\r\nfunction PopoverAnchor({ ...props }: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\r\n  return <PopoverPrimitive.Anchor data-slot=\"popover-anchor\" {...props} />;\r\n}\r\n\r\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor };\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,QAAQ,EAAE,GAAG,OAA2D;IAC/E,qBAAO,8OAAC,mKAAA,CAAA,OAAqB;QAAC,aAAU;QAAW,GAAG,KAAK;;;;;;AAC7D;AAEA,SAAS,eAAe,EAAE,GAAG,OAA8D;IACzF,qBAAO,8OAAC,mKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,GAAG,OACmD;IACtD,qBACE,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,keACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,cAAc,EAAE,GAAG,OAA6D;IACvF,qBAAO,8OAAC,mKAAA,CAAA,SAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE", "debugId": null}}, {"offset": {"line": 270, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/studentAuthServices.ts"], "sourcesContent": ["import {\r\n    StudentRegisterData,\r\n    StudentLoginData,\r\n    AuthResponse,\r\n    GoogleAuthData,\r\n} from '@/lib/types';\r\nimport { axiosInstance } from '@/lib/axios';\r\n\r\nexport const registerStudent = async (StudentRegisterData: StudentRegisterData) => {\r\n    const response = await axiosInstance.post(`/student/register`, StudentRegisterData);\r\n    return response.data\r\n}\r\n\r\nexport const loginStudent = async (StudentLoginData: StudentLoginData) => {\r\n    const response = await axiosInstance.post(`/student/login`, StudentLoginData);\r\n    return response.data;\r\n}\r\n\r\nexport const googleAuthStudent = async (googleAuthData: GoogleAuthData) => {\r\n    const response = await axiosInstance.post(`/student/google-auth`, googleAuthData);\r\n    return response.data;\r\n};\r\n\r\nexport const logoutStudent = async (): Promise<AuthResponse> => {\r\n    // Clear student token from localStorage\r\n    localStorage.removeItem('studentToken');\r\n    localStorage.removeItem('student_data');\r\n\r\n    return {\r\n        success: true,\r\n        message: 'Logged out successfully',\r\n    };\r\n};\r\n\r\nexport const forgotPassword = async (email: string) => {\r\n    const response = await axiosInstance.post(`/student/forgot-password`, { email });\r\n    return response.data;\r\n}\r\n\r\nexport const studentverifyEmail = async (token: string) => {\r\n    const response = await axiosInstance.post(`/student/verify-email`, { token });\r\n    return response.data;\r\n};\r\n\r\nexport const studentresendVerificationEmail = async (email: string) => {\r\n    const response = await axiosInstance.post(`/student/resend-verification`, { email });\r\n    return response.data;\r\n};\r\n\r\nexport const resetStudentPassword = async (email: string, token: string, newPassword: string) => {\r\n    const response = await axiosInstance.post(`/student/reset-password`, {\r\n        email,\r\n        token,\r\n        newPassword,\r\n    });\r\n    return response.data;\r\n};"], "names": [], "mappings": ";;;;;;;;;;AAMA;;AAEO,MAAM,kBAAkB,OAAO;IAClC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,iBAAiB,CAAC,EAAE;IAC/D,OAAO,SAAS,IAAI;AACxB;AAEO,MAAM,eAAe,OAAO;IAC/B,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,EAAE;IAC5D,OAAO,SAAS,IAAI;AACxB;AAEO,MAAM,oBAAoB,OAAO;IACpC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,EAAE;IAClE,OAAO,SAAS,IAAI;AACxB;AAEO,MAAM,gBAAgB;IACzB,wCAAwC;IACxC,aAAa,UAAU,CAAC;IACxB,aAAa,UAAU,CAAC;IAExB,OAAO;QACH,SAAS;QACT,SAAS;IACb;AACJ;AAEO,MAAM,iBAAiB,OAAO;IACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,wBAAwB,CAAC,EAAE;QAAE;IAAM;IAC9E,OAAO,SAAS,IAAI;AACxB;AAEO,MAAM,qBAAqB,OAAO;IACrC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,qBAAqB,CAAC,EAAE;QAAE;IAAM;IAC3E,OAAO,SAAS,IAAI;AACxB;AAEO,MAAM,iCAAiC,OAAO;IACjD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,4BAA4B,CAAC,EAAE;QAAE;IAAM;IAClF,OAAO,SAAS,IAAI;AACxB;AAEO,MAAM,uBAAuB,OAAO,OAAe,OAAe;IACrE,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,uBAAuB,CAAC,EAAE;QACjE;QACA;QACA;IACJ;IACA,OAAO,SAAS,IAAI;AACxB", "debugId": null}}, {"offset": {"line": 335, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/Header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from \"next/link\";\r\nimport Image from \"next/image\";\r\nimport { Menu, X, User, ShoppingBag, Briefcase, Share2, UserCircle, ChevronRight } from \"lucide-react\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { RootState } from \"@/store\";\r\nimport { useAppDispatch } from \"@/store/hooks\";\r\nimport { useEffect, useState, useRef } from \"react\";\r\nimport { GraduationCap, Flame } from \"lucide-react\";\r\nimport { isStudentAuthenticated, clearStudentAuthToken } from \"@/lib/utils\";\r\nimport ProfileCompletionIndicator from \"./ProfileCompletionIndicator\";\r\nimport {\r\n  Avatar,\r\n  AvatarFallback,\r\n} from \"@/components/ui/avatar\";\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from \"@/components/ui/popover\";\r\nimport { toast } from \"sonner\";\r\nimport { logoutStudent } from \"@/services/studentAuthServices\";\r\nimport { clearUser } from \"@/store/slices/userSlice\";\r\nimport { clearStudentProfileData } from \"@/store/slices/studentProfileSlice\";\r\nimport { fetchStudentProfile } from \"@/store/thunks/studentProfileThunks\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { axiosInstance } from \"@/lib/axios\";\r\nimport { motion, useMotionValue, useAnimationFrame } from \"framer-motion\";\r\n\r\nconst Header = () => {\r\n  const { isAuthenticated, user } = useSelector((state: RootState) => state.user);\r\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\r\n  const [isStudentLoggedIn, setIsStudentLoggedIn] = useState(false);\r\n  const [studentData, setStudentData] = useState<any>(null);\r\n  const dispatch = useAppDispatch();\r\n  const router = useRouter();\r\n\r\n  const contentRef = useRef<HTMLDivElement>(null);\r\n  const [contentWidth, setContentWidth] = useState(0);\r\n  const [isHovering, setIsHovering] = useState(false);\r\n  const x = useMotionValue(0); // Motion value to control the x position\r\n\r\n  const speed = (contentWidth / 20);\r\n\r\n  useEffect(() => {\r\n    const isLoggedIn = isStudentAuthenticated();\r\n    setIsStudentLoggedIn(isLoggedIn);\r\n\r\n    if (isLoggedIn) {\r\n      const storedData = localStorage.getItem('student_data');\r\n      if (storedData) {\r\n        setStudentData(JSON.parse(storedData));\r\n      }\r\n\r\n      dispatch(fetchStudentProfile());\r\n    }\r\n\r\n    const handleStorageChange = () => {\r\n      const newLoginStatus = isStudentAuthenticated();\r\n      setIsStudentLoggedIn(newLoginStatus);\r\n\r\n      if (newLoginStatus) {\r\n        const storedData = localStorage.getItem('student_data');\r\n        if (storedData) {\r\n          setStudentData(JSON.parse(storedData));\r\n        }\r\n\r\n        dispatch(fetchStudentProfile());\r\n      } else {\r\n        setStudentData(null);\r\n      }\r\n    };\r\n\r\n    window.addEventListener('storage', handleStorageChange);\r\n\r\n    if (contentRef.current) {\r\n      const width = contentRef.current.getBoundingClientRect().width;\r\n      setContentWidth(width);\r\n    }\r\n\r\n    return () => {\r\n      window.removeEventListener('storage', handleStorageChange);\r\n    };\r\n  }, [dispatch]);\r\n\r\n  // Custom animation loop using useAnimationFrame\r\n  useAnimationFrame((time, delta) => {\r\n    if (isHovering || contentWidth === 0) return;\r\n    const currentX = x.get();\r\n    const deltaX = (speed * delta) / 1000;\r\n    let newX = currentX - deltaX;\r\n    if (newX <= -contentWidth) {\r\n      newX = 0;\r\n    }\r\n    x.set(newX);\r\n  });\r\n\r\n  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);\r\n\r\n  const handleStudentLogout = async () => {\r\n    try {\r\n      const response = await logoutStudent();\r\n\r\n      if (response.success !== false) {\r\n        // Clear local state\r\n        clearStudentAuthToken();\r\n        setIsStudentLoggedIn(false);\r\n        setStudentData(null);\r\n        localStorage.removeItem('student_data');\r\n\r\n        // Clear Redux state\r\n        dispatch(clearStudentProfileData());\r\n\r\n        toast.success(\"Logged out successfully\");\r\n        window.dispatchEvent(new Event('storage'));\r\n      } else {\r\n        toast.error(response.message || \"Failed to logout\");\r\n        clearStudentAuthToken();\r\n        setIsStudentLoggedIn(false);\r\n        setStudentData(null);\r\n        localStorage.removeItem('student_data');\r\n        dispatch(clearStudentProfileData());\r\n      }\r\n    } catch (error) {\r\n      console.log(\"Failed to logout\", error);\r\n      toast.error(\"Failed to logout\");\r\n      localStorage.removeItem('student_data');\r\n      clearStudentAuthToken();\r\n      setIsStudentLoggedIn(false);\r\n      setStudentData(null);\r\n      dispatch(clearStudentProfileData());\r\n    }\r\n  };\r\n\r\n\r\n  const navLinks = [\r\n    { href: \"/verified-classes\", label: \"Find Tutor\", icon: <GraduationCap className=\"w-4 h-4\" /> },\r\n    { href: \"/uwhiz\", label: \"U - Whiz\", icon: <Flame className=\"w-4 h-4\" />, isNew: true },\r\n    { href: \"/careers\", label: \"Career\", icon: <Briefcase className=\"w-4 h-4\" />},\r\n  ];\r\n\r\n  const bannerContent = (\r\n    <div className=\"inline-flex items-center space-x-4 whitespace-nowrap\">\r\n      <span className=\"text-sm md:text-xl font-semibold text-black\">\r\n        U Whiz – Super Kids Exam is live! Win ₹1,00,000 – So hurry up, Apply now and be a champion\r\n      </span>\r\n      <button\r\n        className=\"inline-flex items-center justify-center rounded-md font-bold bg-white text-black px-3 py-1 text-sm hover:bg-[#FD904B] hover:text-black transition\"\r\n        style={{ border: '2px solid black' }}\r\n        onClick={() => router.push(`/uwhiz-info/${1}`)}\r\n      >\r\n        Apply Now <ChevronRight className=\"ml-2 h-4 w-4\" />\r\n      </button>\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <>\r\n      <header className=\"sticky top-0 z-50 w-full bg-black overflow-x-hidden\">\r\n        <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"flex h-20 items-center justify-between\">\r\n            <Link\r\n              href=\"/\"\r\n              className=\"flex items-center space-x-2 transition-transform hover:scale-105\"\r\n            >\r\n              <Image\r\n                src=\"/logo_black.png\"\r\n                alt=\"Preply Logo\"\r\n                width={200}\r\n                height={50}\r\n                className=\"rounded-sm\"\r\n              />\r\n            </Link>\r\n\r\n            <nav className=\"hidden md:flex items-center space-x-4\">\r\n              {navLinks.map((link) => (\r\n                <Link\r\n                  key={link.href}\r\n                  href={link.href}\r\n                  className=\"relative flex items-center gap-2 border border-gray-700 rounded-md px-4 py-2 text-sm font-medium text-gray-300 transition-all hover:border-orange-500 hover:text-orange-400\"\r\n                >\r\n                  {link.icon}\r\n                  {link.label}\r\n                  {link.label === \"Find School\" && (\r\n                    <span className=\"ml-2 text-xs px-2 py-0.5 rounded-full bg-yellow-500 text-black\">\r\n                      Coming Soon\r\n                    </span>\r\n                  )}\r\n                  {link.isNew && (\r\n                    <span className=\"ml-2 text-xs px-2 py-0.5 rounded-full bg-orange-500 text-white animate-pulse\">\r\n                      Trending\r\n                    </span>\r\n                  )}\r\n                </Link>\r\n              ))}\r\n            </nav>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"icon\"\r\n              className=\"md:hidden text-orange-400 hover:bg-orange-500/10\"\r\n              onClick={toggleMenu}\r\n            >\r\n              {isMenuOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\r\n            </Button>\r\n\r\n            {/* Desktop Actions */}\r\n            <div className=\"hidden md:flex items-center space-x-4\">\r\n              {isAuthenticated && (\r\n                <Link href=\"/coins\" passHref>\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    size=\"icon\"\r\n                    className=\"relative rounded-full group bg-black\"\r\n                  >\r\n                    <div className=\"absolute rounded-full inset-0 opacity-0 group-hover:opacity-20 transition-opacity\" />\r\n                    <div className=\"relative z-10 \">\r\n                      <Image\r\n                        src=\"/uest_coin.png\"\r\n                        alt=\"Coin Icon\"\r\n                        width={60}\r\n                        height={60}\r\n                        className=\"object-contain\"\r\n                      />\r\n                    </div>\r\n                  </Button>\r\n                </Link>\r\n              )}\r\n\r\n              <div className=\"h-8 border-l border-orange-500/20\" />\r\n\r\n              {isAuthenticated && (\r\n                <Popover>\r\n                  <PopoverTrigger asChild>\r\n                    <Avatar className=\"cursor-pointer border-2 border-[#ff914d] hover:border-[#ff914d]/80 transition-colors\">\r\n                      <AvatarFallback className=\"bg-white text-black\">\r\n                        {user?.firstName && user?.lastName\r\n                        ? `${user.firstName[0]}${user.lastName[0]}`.toUpperCase()\r\n                          : \"CT\"}\r\n                      </AvatarFallback>\r\n                    </Avatar>\r\n                  </PopoverTrigger>\r\n                  <PopoverContent className=\"w-64 bg-white\">\r\n                    <div className=\"flex items-center gap-3 mb-3 pb-2 border-b\">\r\n                      <Avatar className=\"h-12 w-12 border-2 border-[#ff914d]\">\r\n                        <AvatarFallback className=\"bg-white text-black\">\r\n                          {user?.firstName && user?.lastName\r\n                          ? `${user.firstName[0]}${user.lastName[0]}`.toUpperCase()\r\n                            : \"CT\"}\r\n                        </AvatarFallback>\r\n                      </Avatar>\r\n                      <div>\r\n                        <p className=\"font-medium text-black\">\r\n                          {user?.firstName && user?.lastName\r\n                            ? `${user.firstName} ${user.lastName}`\r\n                            : user?.className || \"Class Account\"}\r\n                        </p>\r\n                        <p className=\"text-xs text-gray-600\">{user?.email || \"<EMAIL>\"}</p>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"space-y-2\">\r\n                      <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                        <Link href=\"/classes/profile\" className=\"flex items-center\">\r\n                          <User className=\"mr-2 h-4 w-4\" />\r\n                          <span>My Dashboard</span>\r\n                        </Link>\r\n                      </Button>\r\n                      <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                        <Link href=\"/classes/referral-dashboard\" className=\"flex items-center\">\r\n                          <Share2 className=\"mr-2 h-4 w-4\" />\r\n                          <span>Referral Dashboard</span>\r\n                        </Link>\r\n                      </Button>\r\n\r\n                      <Button\r\n                        variant=\"outline\"\r\n                        className=\"w-full border-[#ff914d] text-[#ff914d] hover:bg-[#ff914d]/10\"\r\n                        onClick={async () => {\r\n                          try {\r\n                            const response = await axiosInstance.post(\"/auth-client/logout\", {});\r\n                            if (response.data.success) {\r\n                              router.push(\"/\");\r\n                              dispatch(clearUser());\r\n                              localStorage.removeItem(\"token\");\r\n                              toast.success(\"Logged out successfully\");\r\n                            }\r\n                          } catch (error) {\r\n                            console.error(\"Logout error:\", error);\r\n                            toast.error(\"Failed to logout\");\r\n                          }\r\n                        }}\r\n                      >\r\n                        Logout\r\n                      </Button>\r\n                    </div>\r\n                  </PopoverContent>\r\n                </Popover>\r\n              )}\r\n\r\n              {!isAuthenticated && !isStudentLoggedIn && (\r\n                <Button\r\n                  className=\"bg-customOrange hover:bg-[#E88143] text-white mr-4\"\r\n                  asChild\r\n                >\r\n                  <Link href=\"/class/login\">Join as a Tutor/Class</Link>\r\n                </Button>\r\n              )}\r\n\r\n              {!isAuthenticated && !isStudentLoggedIn && (\r\n                <Button\r\n                  variant=\"outline\"\r\n                  className=\"bg-black border-orange-500 hover:bg-orange-900/50 text-white hover:text-white\"\r\n                  asChild\r\n                >\r\n                  <Link href=\"/student/login\">Student Login</Link>\r\n                </Button>\r\n              )}\r\n\r\n              {isStudentLoggedIn && (\r\n                <Link href=\"/coins\" passHref>\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    size=\"icon\"\r\n                    className=\"relative rounded-full group bg-black\"\r\n                  >\r\n                    <div className=\"absolute rounded-full inset-0 opacity-0 group-hover:opacity-20 transition-opacity\" />\r\n                    <div className=\"relative z-10 \">\r\n                      <Image\r\n                        src=\"/uest_coin.png\"\r\n                        alt=\"Coin Icon\"\r\n                        width={60}\r\n                        height={60}\r\n                        className=\"object-contain\"\r\n                      />\r\n                    </div>\r\n                  </Button>\r\n                </Link>\r\n              )}\r\n\r\n              {isStudentLoggedIn && (\r\n                <Popover>\r\n                  <PopoverTrigger asChild>\r\n                    <Avatar className=\"cursor-pointer border-2 border-[#ff914d] hover:border-[#ff914d]/80 transition-colors\">\r\n                      <AvatarFallback className=\"bg-white text-black\">\r\n                        {studentData?.firstName && studentData?.lastName\r\n                        ? `${studentData.firstName[0]}${studentData.lastName[0]}`.toUpperCase()\r\n                          : \"ST\"}\r\n                      </AvatarFallback>\r\n                    </Avatar>\r\n                  </PopoverTrigger>\r\n                  <PopoverContent className=\"w-64 bg-white\">\r\n                    <div className=\"flex items-center gap-3 mb-3 pb-2 border-b\">\r\n                      <Avatar className=\"h-12 w-12 border-2 border-[#ff914d]\">\r\n                        <AvatarFallback className=\"bg-white text-black\">\r\n                          {studentData?.firstName && studentData?.lastName\r\n                          ? `${studentData.firstName[0]}${studentData.lastName[0]}`.toUpperCase()\r\n                            : \"ST\"}\r\n                        </AvatarFallback>\r\n                      </Avatar>\r\n                      <div>\r\n                        <p className=\"font-medium text-black\">\r\n                          {studentData?.firstName && studentData?.lastName\r\n                            ? `${studentData.firstName} ${studentData.lastName}`\r\n                            : \"Student Account\"}\r\n                        </p>\r\n                        <p className=\"text-xs text-gray-600\">{studentData?.email || \"<EMAIL>\"}</p>\r\n                      </div>\r\n                    </div>\r\n\r\n                  <div className=\"space-y-2\">\r\n                    <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                      <Link href=\"/student/profile\" className=\"flex items-center\">\r\n                        <UserCircle className=\"mr-2 h-4 w-4\" />\r\n                        <span>Profile</span>\r\n                      </Link>\r\n                    </Button>\r\n                    <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                      <Link href=\"/student/wishlist\" className=\"flex items-center\">\r\n                        <ShoppingBag className=\"mr-2 h-4 w-4\" />\r\n                        <span>My Wishlist</span>\r\n                      </Link>\r\n                    </Button>\r\n                    <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                      <Link href=\"/student/referral-dashboard\" className=\"flex items-center\">\r\n                        <Share2 className=\"mr-2 h-4 w-4\" />\r\n                        <span>Referral Dashboard</span>\r\n                      </Link>\r\n                    </Button>\r\n                    {/* Referral dashboard removed - only available for Classes */}\r\n\r\n                      <Button variant=\"outline\" className=\"w-full border-[#ff914d] text-[#ff914d] hover:bg-[#ff914d]/10\" onClick={handleStudentLogout}>\r\n                        Logout\r\n                      </Button>\r\n                    </div>\r\n                  </PopoverContent>\r\n                </Popover>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"w-screen bg-[#FD904B] border-y border-black relative mt-1\">\r\n            <div className=\"absolute top-0 right-0 h-full w-[20vw] bg-[#FD904B] block md:hidden z-0\"></div>\r\n            <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 relative z-10 overflow-hidden\">\r\n              <motion.div\r\n                className=\"inline-flex py-2 px-4\"\r\n                style={{ x }} // Bind the x position to the motion value\r\n                onMouseEnter={() => setIsHovering(true)}\r\n                onMouseLeave={() => setIsHovering(false)}\r\n              >\r\n                <div ref={contentRef} className=\"inline-flex items-center space-x-4 whitespace-nowrap pr-8\">\r\n                  {bannerContent}\r\n                </div>\r\n                <div className=\"inline-flex items-center space-x-4 whitespace-nowrap pr-8\">\r\n                  {bannerContent}\r\n                </div>\r\n              </motion.div>\r\n            </div>\r\n          </div>\r\n      </header>\r\n\r\n      <div>\r\n      {/* Mobile Sidebar Menu */}\r\n      <div\r\n        className={`fixed inset-y-0 right-0 z-50 w-72 bg-black/95 shadow-2xl transform transition-all duration-300 ease-in-out md:hidden border-l border-orange-500/20 ${isMenuOpen ? \"translate-x-0\" : \"translate-x-full\"\r\n          }`}\r\n      >\r\n        <div className=\"flex flex-col h-full p-6\">\r\n          {/* Close Button */}\r\n          <div className=\"flex justify-end\">\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"icon\"\r\n              className=\"text-orange-400 hover:bg-orange-500/10 rounded-full\"\r\n              onClick={toggleMenu}\r\n            >\r\n              <X className=\"h-6 w-6\" />\r\n            </Button>\r\n          </div>\r\n\r\n\r\n\r\n          {/* Mobile Navigation */}\r\n            <nav className=\"flex flex-col space-y-2 mt-8\">\r\n              {navLinks.map((link) => (\r\n                <Link\r\n                  key={link.href}\r\n                  href={link.href}\r\n                  className=\"px-4 py-3 border text-base font-medium text-gray-300 hover:text-orange-400 hover:bg-orange-500/10 rounded-lg transition-colors flex justify-between items-center\"\r\n                  onClick={toggleMenu}\r\n                >\r\n                  <div className=\"flex items-center gap-3\">\r\n                    {link.icon}\r\n                    <span>{link.label}</span>\r\n                  </div>\r\n                  {link.label === \"Find School\" && (\r\n                    <span className=\"ml-2 text-xs px-2 py-0.5 rounded-full bg-yellow-500 text-black animate-pulse\">\r\n                      Coming Soon\r\n                    </span>\r\n                  )}\r\n                  {link.isNew && (\r\n                    <span className=\"ml-2 text-xs px-2 py-0.5 rounded-full bg-orange-500 text-white\">\r\n                      New\r\n                    </span>\r\n                  )}\r\n                </Link>\r\n              ))}\r\n            </nav>\r\n\r\n          {/* Mobile Actions */}\r\n            <div className=\"mt-auto space-y-4\">\r\n              {isAuthenticated && (\r\n                <>\r\n                  <Link href=\"/classes/profile\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"w-full group relative border-orange-500 hover:border-orange-400 bg-black\"\r\n                      onClick={toggleMenu}\r\n                    >\r\n                      <div className=\"absolute inset-0 bg-gradient-to-r from-orange-500 to-yellow-500 opacity-0 group-hover:opacity-20 transition-opacity\" />\r\n                      <div className=\"relative z-10 flex items-center justify-center gap-3 py-2\">\r\n                        <div className=\"p-1.5 rounded-full bg-gradient-to-br from-orange-500 to-yellow-500\">\r\n                          <User className=\"h-5 w-5 text-white\" />\r\n                        </div>\r\n                        <span className=\"font-medium text-gray-300\">Dashboard</span>\r\n                      </div>\r\n                    </Button>\r\n                  </Link>\r\n\r\n                  <Link href=\"/classes/referral-dashboard\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"w-full group relative border-orange-500 hover:border-orange-400 mt-3 bg-black\"\r\n                      onClick={toggleMenu}\r\n                    >\r\n                      <div className=\"absolute inset-0 bg-gradient-to-r from-orange-500 to-yellow-500 opacity-0 group-hover:opacity-20 transition-opacity\" />\r\n                      <div className=\"relative z-10 flex items-center justify-center gap-3 py-2\">\r\n                        <div className=\"p-1.5 rounded-full bg-gradient-to-br from-orange-500 to-yellow-500\">\r\n                          <Share2 className=\"h-5 w-5 text-white\" />\r\n                        </div>\r\n                        <span className=\"font-medium text-gray-300\">Referral Dashboard</span>\r\n                      </div>\r\n                    </Button>\r\n                  </Link>\r\n\r\n                <Link href=\"/coins\" passHref>\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    className=\"w-full group relative border-orange-500 hover:border-orange-400 bg-black mt-3\"\r\n                    onClick={toggleMenu}\r\n                  >\r\n                    <div className=\"absolute inset-0 bg-gradient-to-r from-orange-500 to-yellow-500 opacity-0 group-hover:opacity-20 transition-opacity\" />\r\n                    <div className=\"relative z-10 flex items-center justify-center gap-3 py-2\">\r\n                      <div className=\"p-1.5 rounded-full bg-gradient-to-br from-orange-500 to-yellow-500\">\r\n                        <Image\r\n                          src=\"/uest_coin.png\"\r\n                          alt=\"Coin Icon\"\r\n                          width={20}\r\n                          height={20}\r\n                          className=\"object-contain\"\r\n                        />\r\n                      </div>\r\n                      <span className=\"font-medium text-gray-300\">My Coins</span>\r\n                    </div>\r\n                  </Button>\r\n                </Link>\r\n\r\n                <Button\r\n                  variant=\"outline\"\r\n                  className=\"w-full border-orange-500 text-orange-500 hover:bg-orange-500/10 mt-3\"\r\n                  onClick={async () => {\r\n                    try {\r\n                      const response = await axiosInstance.post(\"/auth-client/logout\", {});\r\n                      if (response.data.success) {\r\n                        router.push(\"/\");\r\n                        dispatch(clearUser());\r\n                        localStorage.removeItem(\"token\");\r\n                        toast.success(\"Logged out successfully\");\r\n                      }\r\n                    } catch (error) {\r\n                      console.error(\"Logout error:\", error);\r\n                      toast.error(\"Failed to logout\");\r\n                    }\r\n                    toggleMenu();\r\n                  }}\r\n                >\r\n                  <div className=\"flex items-center justify-center gap-3\">\r\n                    <User className=\"h-5 w-5\" />\r\n                    <span>Logout</span>\r\n                  </div>\r\n                </Button>\r\n              </>\r\n            )}\r\n\r\n              {isStudentLoggedIn && (\r\n                <>\r\n                  {studentData?.firstName && studentData?.lastName && (\r\n                    <div className=\"p-3 border border-[#ff914d]/20 rounded-lg bg-white\">\r\n                      <div className=\"flex items-center gap-3\">\r\n                        <Avatar className=\"h-12 w-12 border-2 border-[#ff914d]\">\r\n                          <AvatarFallback className=\"bg-white text-black\">\r\n                            {(`${studentData.firstName[0]}${studentData.lastName[0]}`).toUpperCase()}\r\n                          </AvatarFallback>\r\n                        </Avatar>\r\n                        <div>\r\n                          <p className=\"font-medium text-black\">{`${studentData.firstName} ${studentData.lastName}`}</p>\r\n                          <p className=\"text-xs text-gray-600\">{studentData.email}</p>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                <Button\r\n                  asChild\r\n                  className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\"\r\n                  onClick={toggleMenu}\r\n                >\r\n                  <Link href=\"/student/profile\" className=\"flex items-center justify-center gap-3\">\r\n                    <UserCircle className=\"h-5 w-5\" />\r\n                    <span>Profile</span>\r\n                  </Link>\r\n                </Button>\r\n\r\n                <Button\r\n                  asChild\r\n                  className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\"\r\n                  onClick={toggleMenu}\r\n                >\r\n                  <Link href=\"/student/wishlist\" className=\"flex items-center justify-center gap-3\">\r\n                    <ShoppingBag className=\"h-5 w-5\" />\r\n                    <span>My Wishlist</span>\r\n                  </Link>\r\n                </Button>\r\n\r\n                <Button\r\n                  asChild\r\n                  className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\"\r\n                  onClick={toggleMenu}\r\n                >\r\n                  <Link href=\"/student/referral-dashboard\" className=\"flex items-center justify-center gap-3\">\r\n                    <Share2 className=\"h-5 w-5\" />\r\n                    <span>Referral Dashboard</span>\r\n                  </Link>\r\n                </Button>\r\n\r\n                  <Link href=\"/coins\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"w-full group relative border-orange-500 hover:border-orange-400 bg-black mb-3\"\r\n                      onClick={toggleMenu}\r\n                    >\r\n                      <div className=\"absolute inset-0 bg-gradient-to-r from-orange-500 to-yellow-500 opacity-0 group-hover:opacity-20 transition-opacity\" />\r\n                      <div className=\"relative z-10 flex items-center justify-center gap-3\">\r\n                        <div className=\"p-1.5 rounded-full bg-gradient-to-br from-orange-500 to-yellow-500\">\r\n                          <Image\r\n                            src=\"/uest_coin.png\"\r\n                            alt=\"Coin Icon\"\r\n                            width={20}\r\n                            height={20}\r\n                            className=\"object-contain\"\r\n                          />\r\n                        </div>\r\n                        <span className=\"font-medium text-gray-300\">My Coins</span>\r\n                      </div>\r\n                    </Button>\r\n                  </Link>\r\n\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    className=\"w-full border-[#ff914d] text-[#ff914d] hover:bg-[#ff914d]/10\"\r\n                    onClick={() => {\r\n                      handleStudentLogout();\r\n                      toggleMenu();\r\n                    }}\r\n                  >\r\n                    <div className=\"flex items-center justify-center gap-3\">\r\n                      <User className=\"h-5 w-5\" />\r\n                      <span>Logout</span>\r\n                    </div>\r\n                  </Button>\r\n                </>\r\n              )}\r\n\r\n              {!isAuthenticated && !isStudentLoggedIn && (\r\n                <div className=\"space-y-3 pt-3\">\r\n                  <Button\r\n                    variant=\"default\"\r\n                    className=\"w-full bg-orange-500 hover:bg-orange-600\"\r\n                    asChild\r\n                  >\r\n                    <Link href=\"/class/login\" onClick={toggleMenu}>\r\n                      Tutor/Classes Login\r\n                    </Link>\r\n                  </Button>\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    className=\"w-full border-customOrange text-orange-500 hover:bg-orange\"\r\n                    asChild\r\n                  >\r\n                    <Link href=\"/student/login\" onClick={toggleMenu}>\r\n                      Student Login\r\n                    </Link>\r\n                  </Button>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {isStudentLoggedIn && (\r\n          <div className=\"mt-4 mx-10 sm:px-4\">\r\n            <ProfileCompletionIndicator />\r\n          </div>\r\n        )}\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Header;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AAIA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AA7BA;;;;;;;;;;;;;;;;;;;;;;AA+BA,MAAM,SAAS;IACb,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAqB,MAAM,IAAI;IAC9E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACpD,MAAM,WAAW,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAC9B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,IAAI,CAAA,GAAA,kLAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,yCAAyC;IAEtE,MAAM,QAAS,eAAe;IAE9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa,CAAA,GAAA,mHAAA,CAAA,yBAAsB,AAAD;QACxC,qBAAqB;QAErB,IAAI,YAAY;YACd,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,IAAI,YAAY;gBACd,eAAe,KAAK,KAAK,CAAC;YAC5B;YAEA,SAAS,CAAA,GAAA,8IAAA,CAAA,sBAAmB,AAAD;QAC7B;QAEA,MAAM,sBAAsB;YAC1B,MAAM,iBAAiB,CAAA,GAAA,mHAAA,CAAA,yBAAsB,AAAD;YAC5C,qBAAqB;YAErB,IAAI,gBAAgB;gBAClB,MAAM,aAAa,aAAa,OAAO,CAAC;gBACxC,IAAI,YAAY;oBACd,eAAe,KAAK,KAAK,CAAC;gBAC5B;gBAEA,SAAS,CAAA,GAAA,8IAAA,CAAA,sBAAmB,AAAD;YAC7B,OAAO;gBACL,eAAe;YACjB;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QAEnC,IAAI,WAAW,OAAO,EAAE;YACtB,MAAM,QAAQ,WAAW,OAAO,CAAC,qBAAqB,GAAG,KAAK;YAC9D,gBAAgB;QAClB;QAEA,OAAO;YACL,OAAO,mBAAmB,CAAC,WAAW;QACxC;IACF,GAAG;QAAC;KAAS;IAEb,gDAAgD;IAChD,CAAA,GAAA,qLAAA,CAAA,oBAAiB,AAAD,EAAE,CAAC,MAAM;QACvB,IAAI,cAAc,iBAAiB,GAAG;QACtC,MAAM,WAAW,EAAE,GAAG;QACtB,MAAM,SAAS,AAAC,QAAQ,QAAS;QACjC,IAAI,OAAO,WAAW;QACtB,IAAI,QAAQ,CAAC,cAAc;YACzB,OAAO;QACT;QACA,EAAE,GAAG,CAAC;IACR;IAEA,MAAM,aAAa,IAAM,cAAc,CAAC;IAExC,MAAM,sBAAsB;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,sIAAA,CAAA,gBAAa,AAAD;YAEnC,IAAI,SAAS,OAAO,KAAK,OAAO;gBAC9B,oBAAoB;gBACpB,CAAA,GAAA,mHAAA,CAAA,wBAAqB,AAAD;gBACpB,qBAAqB;gBACrB,eAAe;gBACf,aAAa,UAAU,CAAC;gBAExB,oBAAoB;gBACpB,SAAS,CAAA,GAAA,6IAAA,CAAA,0BAAuB,AAAD;gBAE/B,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,aAAa,CAAC,IAAI,MAAM;YACjC,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,SAAS,OAAO,IAAI;gBAChC,CAAA,GAAA,mHAAA,CAAA,wBAAqB,AAAD;gBACpB,qBAAqB;gBACrB,eAAe;gBACf,aAAa,UAAU,CAAC;gBACxB,SAAS,CAAA,GAAA,6IAAA,CAAA,0BAAuB,AAAD;YACjC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,oBAAoB;YAChC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,aAAa,UAAU,CAAC;YACxB,CAAA,GAAA,mHAAA,CAAA,wBAAqB,AAAD;YACpB,qBAAqB;YACrB,eAAe;YACf,SAAS,CAAA,GAAA,6IAAA,CAAA,0BAAuB,AAAD;QACjC;IACF;IAGA,MAAM,WAAW;QACf;YAAE,MAAM;YAAqB,OAAO;YAAc,oBAAM,8OAAC,wNAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;QAAa;QAC9F;YAAE,MAAM;YAAU,OAAO;YAAY,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YAAc,OAAO;QAAK;QACtF;YAAE,MAAM;YAAY,OAAO;YAAU,oBAAM,8OAAC,4MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;QAAY;KAC7E;IAED,MAAM,8BACJ,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAK,WAAU;0BAA8C;;;;;;0BAG9D,8OAAC;gBACC,WAAU;gBACV,OAAO;oBAAE,QAAQ;gBAAkB;gBACnC,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,YAAY,EAAE,GAAG;;oBAC9C;kCACW,8OAAC,sNAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;;;;;;;;;;;;;IAKxC,qBACE;;0BACE,8OAAC;gBAAO,WAAU;;kCAChB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CAEV,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;8CAId,8OAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAU;;gDAET,KAAK,IAAI;gDACT,KAAK,KAAK;gDACV,KAAK,KAAK,KAAK,+BACd,8OAAC;oDAAK,WAAU;8DAAiE;;;;;;gDAIlF,KAAK,KAAK,kBACT,8OAAC;oDAAK,WAAU;8DAA+E;;;;;;;2CAZ5F,KAAK,IAAI;;;;;;;;;;8CAmBpB,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;8CAER,2BAAa,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;6DAAe,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAI5D,8OAAC;oCAAI,WAAU;;wCACZ,iCACC,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,QAAQ;sDAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;;kEAEV,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4DACJ,KAAI;4DACJ,KAAI;4DACJ,OAAO;4DACP,QAAQ;4DACR,WAAU;;;;;;;;;;;;;;;;;;;;;;sDAOpB,8OAAC;4CAAI,WAAU;;;;;;wCAEd,iCACC,8OAAC,mIAAA,CAAA,UAAO;;8DACN,8OAAC,mIAAA,CAAA,iBAAc;oDAAC,OAAO;8DACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDAAC,WAAU;kEAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;4DAAC,WAAU;sEACvB,MAAM,aAAa,MAAM,WACxB,GAAG,KAAK,SAAS,CAAC,EAAE,GAAG,KAAK,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACnD;;;;;;;;;;;;;;;;8DAIV,8OAAC,mIAAA,CAAA,iBAAc;oDAAC,WAAU;;sEACxB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kIAAA,CAAA,SAAM;oEAAC,WAAU;8EAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;wEAAC,WAAU;kFACvB,MAAM,aAAa,MAAM,WACxB,GAAG,KAAK,SAAS,CAAC,EAAE,GAAG,KAAK,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACnD;;;;;;;;;;;8EAGR,8OAAC;;sFACC,8OAAC;4EAAE,WAAU;sFACV,MAAM,aAAa,MAAM,WACtB,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE,GACpC,MAAM,aAAa;;;;;;sFAEzB,8OAAC;4EAAE,WAAU;sFAAyB,MAAM,SAAS;;;;;;;;;;;;;;;;;;sEAIzD,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kIAAA,CAAA,SAAM;oEAAC,OAAO;oEAAC,WAAU;8EACxB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEAAC,MAAK;wEAAmB,WAAU;;0FACtC,8OAAC,kMAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;0FAChB,8OAAC;0FAAK;;;;;;;;;;;;;;;;;8EAGV,8OAAC,kIAAA,CAAA,SAAM;oEAAC,OAAO;oEAAC,WAAU;8EACxB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEAAC,MAAK;wEAA8B,WAAU;;0FACjD,8OAAC,0MAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;0FAClB,8OAAC;0FAAK;;;;;;;;;;;;;;;;;8EAIV,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,WAAU;oEACV,SAAS;wEACP,IAAI;4EACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,uBAAuB,CAAC;4EAClE,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gFACzB,OAAO,IAAI,CAAC;gFACZ,SAAS,CAAA,GAAA,mIAAA,CAAA,YAAS,AAAD;gFACjB,aAAa,UAAU,CAAC;gFACxB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;4EAChB;wEACF,EAAE,OAAO,OAAO;4EACd,QAAQ,KAAK,CAAC,iBAAiB;4EAC/B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wEACd;oEACF;8EACD;;;;;;;;;;;;;;;;;;;;;;;;wCAQR,CAAC,mBAAmB,CAAC,mCACpB,8OAAC,kIAAA,CAAA,SAAM;4CACL,WAAU;4CACV,OAAO;sDAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;0DAAe;;;;;;;;;;;wCAI7B,CAAC,mBAAmB,CAAC,mCACpB,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;4CACV,OAAO;sDAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;0DAAiB;;;;;;;;;;;wCAI/B,mCACC,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,QAAQ;sDAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;;kEAEV,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4DACJ,KAAI;4DACJ,KAAI;4DACJ,OAAO;4DACP,QAAQ;4DACR,WAAU;;;;;;;;;;;;;;;;;;;;;;wCAOnB,mCACC,8OAAC,mIAAA,CAAA,UAAO;;8DACN,8OAAC,mIAAA,CAAA,iBAAc;oDAAC,OAAO;8DACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDAAC,WAAU;kEAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;4DAAC,WAAU;sEACvB,aAAa,aAAa,aAAa,WACtC,GAAG,YAAY,SAAS,CAAC,EAAE,GAAG,YAAY,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACjE;;;;;;;;;;;;;;;;8DAIV,8OAAC,mIAAA,CAAA,iBAAc;oDAAC,WAAU;;sEACxB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kIAAA,CAAA,SAAM;oEAAC,WAAU;8EAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;wEAAC,WAAU;kFACvB,aAAa,aAAa,aAAa,WACtC,GAAG,YAAY,SAAS,CAAC,EAAE,GAAG,YAAY,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACjE;;;;;;;;;;;8EAGR,8OAAC;;sFACC,8OAAC;4EAAE,WAAU;sFACV,aAAa,aAAa,aAAa,WACpC,GAAG,YAAY,SAAS,CAAC,CAAC,EAAE,YAAY,QAAQ,EAAE,GAClD;;;;;;sFAEN,8OAAC;4EAAE,WAAU;sFAAyB,aAAa,SAAS;;;;;;;;;;;;;;;;;;sEAIlE,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kIAAA,CAAA,SAAM;oEAAC,OAAO;oEAAC,WAAU;8EACxB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEAAC,MAAK;wEAAmB,WAAU;;0FACtC,8OAAC,kNAAA,CAAA,aAAU;gFAAC,WAAU;;;;;;0FACtB,8OAAC;0FAAK;;;;;;;;;;;;;;;;;8EAGV,8OAAC,kIAAA,CAAA,SAAM;oEAAC,OAAO;oEAAC,WAAU;8EACxB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEAAC,MAAK;wEAAoB,WAAU;;0FACvC,8OAAC,oNAAA,CAAA,cAAW;gFAAC,WAAU;;;;;;0FACvB,8OAAC;0FAAK;;;;;;;;;;;;;;;;;8EAGV,8OAAC,kIAAA,CAAA,SAAM;oEAAC,OAAO;oEAAC,WAAU;8EACxB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEAAC,MAAK;wEAA8B,WAAU;;0FACjD,8OAAC,0MAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;0FAClB,8OAAC;0FAAK;;;;;;;;;;;;;;;;;8EAKR,8OAAC,kIAAA,CAAA,SAAM;oEAAC,SAAQ;oEAAU,WAAU;oEAA+D,SAAS;8EAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAU/I,8OAAC;wBAAI,WAAU;;0CACX,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,OAAO;wCAAE;oCAAE;oCACX,cAAc,IAAM,cAAc;oCAClC,cAAc,IAAM,cAAc;;sDAElC,8OAAC;4CAAI,KAAK;4CAAY,WAAU;sDAC7B;;;;;;sDAEH,8OAAC;4CAAI,WAAU;sDACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOb,8OAAC;;kCAED,8OAAC;wBACC,WAAW,CAAC,mJAAmJ,EAAE,aAAa,kBAAkB,oBAC5L;kCAEJ,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS;kDAET,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAOf,8OAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAU;4CACV,SAAS;;8DAET,8OAAC;oDAAI,WAAU;;wDACZ,KAAK,IAAI;sEACV,8OAAC;sEAAM,KAAK,KAAK;;;;;;;;;;;;gDAElB,KAAK,KAAK,KAAK,+BACd,8OAAC;oDAAK,WAAU;8DAA+E;;;;;;gDAIhG,KAAK,KAAK,kBACT,8OAAC;oDAAK,WAAU;8DAAiE;;;;;;;2CAf9E,KAAK,IAAI;;;;;;;;;;8CAwBpB,8OAAC;oCAAI,WAAU;;wCACZ,iCACC;;8DACE,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAmB,QAAQ;8DACpC,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;;0EAET,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;;;;;;kFAElB,8OAAC;wEAAK,WAAU;kFAA4B;;;;;;;;;;;;;;;;;;;;;;;8DAKlD,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAA8B,QAAQ;8DAC/C,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;;0EAET,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,0MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;;;;;;kFAEpB,8OAAC;wEAAK,WAAU;kFAA4B;;;;;;;;;;;;;;;;;;;;;;;8DAKpD,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,QAAQ;8DAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;;0EAET,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4EACJ,KAAI;4EACJ,KAAI;4EACJ,OAAO;4EACP,QAAQ;4EACR,WAAU;;;;;;;;;;;kFAGd,8OAAC;wEAAK,WAAU;kFAA4B;;;;;;;;;;;;;;;;;;;;;;;8DAKlD,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,SAAS;wDACP,IAAI;4DACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,uBAAuB,CAAC;4DAClE,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gEACzB,OAAO,IAAI,CAAC;gEACZ,SAAS,CAAA,GAAA,mIAAA,CAAA,YAAS,AAAD;gEACjB,aAAa,UAAU,CAAC;gEACxB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;4DAChB;wDACF,EAAE,OAAO,OAAO;4DACd,QAAQ,KAAK,CAAC,iBAAiB;4DAC/B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wDACd;wDACA;oDACF;8DAEA,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;;wCAMX,mCACC;;gDACG,aAAa,aAAa,aAAa,0BACtC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kIAAA,CAAA,SAAM;gEAAC,WAAU;0EAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;oEAAC,WAAU;8EACvB,AAAC,GAAG,YAAY,SAAS,CAAC,EAAE,GAAG,YAAY,QAAQ,CAAC,EAAE,EAAE,CAAE,WAAW;;;;;;;;;;;0EAG1E,8OAAC;;kFACC,8OAAC;wEAAE,WAAU;kFAA0B,GAAG,YAAY,SAAS,CAAC,CAAC,EAAE,YAAY,QAAQ,EAAE;;;;;;kFACzF,8OAAC;wEAAE,WAAU;kFAAyB,YAAY,KAAK;;;;;;;;;;;;;;;;;;;;;;;8DAMjE,8OAAC,kIAAA,CAAA,SAAM;oDACL,OAAO;oDACP,WAAU;oDACV,SAAS;8DAET,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAmB,WAAU;;0EACtC,8OAAC,kNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;0EACtB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;8DAIV,8OAAC,kIAAA,CAAA,SAAM;oDACL,OAAO;oDACP,WAAU;oDACV,SAAS;8DAET,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAoB,WAAU;;0EACvC,8OAAC,oNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;8DAIV,8OAAC,kIAAA,CAAA,SAAM;oDACL,OAAO;oDACP,WAAU;oDACV,SAAS;8DAET,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAA8B,WAAU;;0EACjD,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;8DAIR,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,QAAQ;8DAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;;0EAET,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4EACJ,KAAI;4EACJ,KAAI;4EACJ,OAAO;4EACP,QAAQ;4EACR,WAAU;;;;;;;;;;;kFAGd,8OAAC;wEAAK,WAAU;kFAA4B;;;;;;;;;;;;;;;;;;;;;;;8DAKlD,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,SAAS;wDACP;wDACA;oDACF;8DAEA,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;;wCAMb,CAAC,mBAAmB,CAAC,mCACpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,OAAO;8DAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAe,SAAS;kEAAY;;;;;;;;;;;8DAIjD,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,OAAO;8DAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAiB,SAAS;kEAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAU5D,mCACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,uJAAA,CAAA,UAA0B;;;;;;;;;;;;;;;;;;AAMvC;uCAEe", "debugId": null}}, {"offset": {"line": 1893, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/public/exam-logo.jpg.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 3192, height: 2504, blurDataURL: \"data:image/jpeg;base64,/9j/4AAQSkZJRgABAgAAAQABAAD/wAARCAAGAAgDAREAAhEBAxEB/9sAQwAKBwcIBwYKCAgICwoKCw4YEA4NDQ4dFRYRGCMfJSQiHyIhJis3LyYpNCkhIjBBMTQ5Oz4+PiUuRElDPEg3PT47/9sAQwEKCwsODQ4cEBAcOygiKDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwDyt9PkS1UlwcqTStqO+h//2Q==\", blurWidth: 8, blurHeight: 6 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,+GAAA,CAAA,UAAG;IAAE,OAAO;IAAM,QAAQ;IAAM,aAAa;IAAm3B,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 1912, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        'bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        '@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn('leading-none font-semibold', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn('col-start-2 row-span-2 row-start-1 self-start justify-self-end', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return <div data-slot=\"card-content\" className={cn('px-6', className)} {...props} />;\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn('flex items-center px-6 [.border-t]:pt-6', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardAction, CardDescription, CardContent };\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kEAAkE;QAC/E,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBAAO,8OAAC;QAAI,aAAU;QAAe,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QAAa,GAAG,KAAK;;;;;;AAClF;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2009, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/examApi.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\nimport { ExamInput } from '@/lib/types';\r\n\r\nexport const getExams = async (page: number = 1, limit: number = 10, applicantId?: string) => {\r\n  try {\r\n    const response = await axiosInstance.get(`/exams?page=${page}&limit=${limit}${applicantId ? `&applicantId=${applicantId}` : ''}`, {\r\n      headers: {\r\n        'Server-Select': 'uwhizServer',\r\n      },\r\n    });\r\n    return response.data;\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to fetch Exam: ${error.response?.data?.message || error.message}`,\r\n    };\r\n  }\r\n};\r\n\r\nexport const getExamsById = async (examId: number, applicantId?: string) => {\r\n  try {\r\n    const response = await axiosInstance.get(\r\n      `/exams/${examId}${applicantId ? `?applicantId=${applicantId}` : ''}`,\r\n      {\r\n        headers: {\r\n          \"Server-Select\": \"uwhizServer\",\r\n        },\r\n      }\r\n    );\r\n    return response.data;\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to fetch Exam: ${error.response?.data?.message || error.message}`,\r\n    };\r\n  }\r\n};\r\n\r\nexport const createExam = async (data: ExamInput) => {\r\n  try {\r\n    const response = await axiosInstance.post('/exams', data,{\r\n       headers: {\r\n        \"Server-Select\": \"uwhizServer\",\r\n      },\r\n    });\r\n    return response.data;\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to create exam: ${error.response?.data?.message || error.message}`,\r\n    };\r\n  }\r\n};\r\n\r\nexport const updateExam = async (id: number, data: Partial<ExamInput>) => {\r\n  try {\r\n    const response = await axiosInstance.put(`/exams/${id}`, data,{\r\n       headers: {\r\n        \"Server-Select\": \"uwhizServer\",\r\n      },\r\n    });\r\n    return response.data;\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to update Exam: ${error.response?.data?.message || error.message}`,\r\n    };\r\n  }\r\n};\r\n\r\nexport const deleteExam = async (id: number) => {\r\n  try {\r\n    const response = await axiosInstance.delete(`/exams/${id}`,{\r\n       headers: {\r\n        \"Server-Select\": \"uwhizServer\",\r\n      },\r\n    });\r\n    return response.data;\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to delete Exam: ${error.response?.data?.message || error.message}`,\r\n    };\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAGO,MAAM,WAAW,OAAO,OAAe,CAAC,EAAE,QAAgB,EAAE,EAAE;IACnE,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,KAAK,OAAO,EAAE,QAAQ,cAAc,CAAC,aAAa,EAAE,aAAa,GAAG,IAAI,EAAE;YAChI,SAAS;gBACP,iBAAiB;YACnB;QACF;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,sBAAsB,EAAE,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,EAAE;QAClF;IACF;AACF;AAEO,MAAM,eAAe,OAAO,QAAgB;IACjD,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CACtC,CAAC,OAAO,EAAE,SAAS,cAAc,CAAC,aAAa,EAAE,aAAa,GAAG,IAAI,EACrE;YACE,SAAS;gBACP,iBAAiB;YACnB;QACF;QAEF,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,sBAAsB,EAAE,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,EAAE;QAClF;IACF;AACF;AAEO,MAAM,aAAa,OAAO;IAC/B,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,UAAU,MAAK;YACtD,SAAS;gBACR,iBAAiB;YACnB;QACF;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,uBAAuB,EAAE,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,EAAE;QACnF;IACF;AACF;AAEO,MAAM,aAAa,OAAO,IAAY;IAC3C,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,IAAI,EAAE,MAAK;YAC3D,SAAS;gBACR,iBAAiB;YACnB;QACF;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,uBAAuB,EAAE,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,EAAE;QACnF;IACF;AACF;AAEO,MAAM,aAAa,OAAO;IAC/B,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,IAAI,EAAC;YACxD,SAAS;gBACR,iBAAiB;YACnB;QACF;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,uBAAuB,EAAE,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,EAAE;QACnF;IACF;AACF", "debugId": null}}, {"offset": {"line": 2099, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/examApplicationApi.ts"], "sourcesContent": ["import { axiosInstance } from \"@/lib/axios\";\r\n\r\n// Apply for an exam\r\nexport const applyForExam = async (\r\n  examId: number,\r\n  applicantId: string\r\n): Promise<any> => {\r\n  try {\r\n    const response = await axiosInstance.post(\r\n      \"/examApplication\",\r\n      {\r\n        examId,\r\n        applicantId,\r\n      },\r\n      {\r\n        headers: {\r\n          \"Server-Select\": \"uwhizServer\",\r\n        },\r\n      }\r\n    );\r\n    return response.data;\r\n  } catch (error: any) {\r\n    throw new Error(error.response?.data?.error || \"Failed to apply for exam\");\r\n  }\r\n};\r\n\r\n// Get all exam applications (for admin)\r\nexport const getAllExamApplications = async (\r\n  page: number = 1,\r\n  limit: number = 10,\r\n  examId?: number\r\n): Promise<any> => {\r\n  try {\r\n    const params: any = { page, limit };\r\n    if (examId !== undefined) {\r\n      params.examId = examId;\r\n    }\r\n    const response = await axiosInstance.get(`/examApplication/${examId}`, {\r\n      headers: {\r\n        \"Server-Select\": \"uwhizServer\",\r\n      },\r\n      params,\r\n    },);\r\n    return response.data;\r\n  } catch (error: any) {\r\n    throw new Error(\r\n      error.response?.data?.error || \"Failed to fetch exam applications\"\r\n    );\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;;AAGO,MAAM,eAAe,OAC1B,QACA;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CACvC,oBACA;YACE;YACA;QACF,GACA;YACE,SAAS;gBACP,iBAAiB;YACnB;QACF;QAEF,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,MAAM,IAAI,MAAM,MAAM,QAAQ,EAAE,MAAM,SAAS;IACjD;AACF;AAGO,MAAM,yBAAyB,OACpC,OAAe,CAAC,EAChB,QAAgB,EAAE,EAClB;IAEA,IAAI;QACF,MAAM,SAAc;YAAE;YAAM;QAAM;QAClC,IAAI,WAAW,WAAW;YACxB,OAAO,MAAM,GAAG;QAClB;QACA,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,iBAAiB,EAAE,QAAQ,EAAE;YACrE,SAAS;gBACP,iBAAiB;YACnB;YACA;QACF;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,MAAM,IAAI,MACR,MAAM,QAAQ,EAAE,MAAM,SAAS;IAEnC;AACF", "debugId": null}}, {"offset": {"line": 2146, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/examApplicantEmailApi.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\n\r\nexport const sendExamApplicantEmail = async (\r\n  examId: number,\r\n  exam_name: string,\r\n  email: string\r\n): Promise<any> => {\r\n  try {\r\n    const response = await axiosInstance.post(\r\n      '/examApplicantEmail/send-exam-applicant-email',\r\n      {\r\n        examId,\r\n        exam_name,\r\n        email,\r\n      }\r\n    );\r\n    return response.data;\r\n  } catch (error: any) {\r\n    throw new Error(error.response?.data?.message || 'Failed to send exam applicant email');\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,yBAAyB,OACpC,QACA,WACA;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CACvC,iDACA;YACE;YACA;YACA;QACF;QAEF,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,MAAM,IAAI,MAAM,MAAM,QAAQ,EAAE,MAAM,WAAW;IACnD;AACF", "debugId": null}}, {"offset": {"line": 2169, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/uwhizPreventReattemptApi.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\n\r\nexport const uwhizPreventReattempApi = async (studentId: string,examId:number) => {\r\n  try {\r\n    const response = await axiosInstance.get(`check-attempt?studentId=${studentId}&examId=${examId}`,{\r\n      headers: {\r\n        \"Server-Select\": \"uwhizServer\",\r\n      },\r\n    });\r\n    return response.data;\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed To Get Student And Exam Detail: ${error.response?.data?.message || error.message}`,\r\n    };\r\n  }\r\n};"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,0BAA0B,OAAO,WAAkB;IAC9D,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,wBAAwB,EAAE,UAAU,QAAQ,EAAE,QAAQ,EAAC;YAC/F,SAAS;gBACP,iBAAiB;YACnB;QACF;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,uCAAuC,EAAE,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,EAAE;QACnG;IACF;AACF", "debugId": null}}, {"offset": {"line": 2195, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/Footer.tsx"], "sourcesContent": ["'use client';\r\nimport React from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport {\r\n  FaFacebookF,\r\n  FaTwitter,\r\n  FaInstagram,\r\n  FaLinkedinIn,\r\n  FaTumblr,\r\n  FaPinterestP,\r\n  FaFilePdf,\r\n  FaEnvelope,\r\n} from 'react-icons/fa';\r\n\r\nconst Footer = () => {\r\n  return (\r\n    <footer className=\"bg-black text-gray-300 px-6 py-16\">\r\n      <div className=\"container mx-auto max-w-7xl space-y-16\">\r\n        <div className=\"flex flex-col md:flex-row items-center justify-between gap-6\">\r\n          <Link href=\"/\" className=\"flex items-center gap-2\">\r\n            <Image\r\n              src=\"/logo_black.png\"\r\n              alt=\"Logo\"\r\n              width={200}\r\n              height={40}\r\n              className=\"object-contain\"\r\n            />\r\n          </Link>\r\n\r\n          <div className=\"flex flex-wrap justify-center gap-1\">\r\n            {[\r\n              { href: 'mailto:<EMAIL>', icon: FaEnvelope, label: 'Email Us' },\r\n              {\r\n                href: 'https://www.uest.in/wp-content/uploads/2025/03/Welcome-A-UEST-Product-1.pdf',\r\n                icon: FaFilePdf,\r\n                label: 'Brochure',\r\n              },\r\n              {\r\n                href: 'https://x.com/uest189161?t=hLD2wWnt_Zf5b5rTnkSl2Q&s=09',\r\n                icon: FaTwitter,\r\n                label: 'Twitter',\r\n              },\r\n              {\r\n                href: 'https://www.facebook.com/share/1FNYcyqawH/',\r\n                icon: FaFacebookF,\r\n                label: 'Facebook',\r\n              },\r\n              {\r\n                href: 'https://www.instagram.com/uest_edtech?igsh=MWljYWt5YnQyeW9kdg==',\r\n                icon: FaInstagram,\r\n                label: 'Instagram',\r\n              },\r\n              {\r\n                href: 'https://www.linkedin.com/company/uest-edtech/',\r\n                icon: FaLinkedinIn,\r\n                label: 'LinkedIn',\r\n              },\r\n              { href: 'https://pin.it/1Di0EFtAa', icon: FaPinterestP, label: 'Pinterest' },\r\n              {\r\n                href: 'https://www.tumblr.com/uestedtech?source=share',\r\n                icon: FaTumblr,\r\n                label: 'Tumblr',\r\n              },\r\n            ].map(({ href, icon: Icon, label }) => (\r\n              <div key={label} className=\"flex flex-col items-center\">\r\n                <Link\r\n                  href={href}\r\n                  className=\"flex items-center justify-center w-12 h-12 hover:border-gray-400 transition\"\r\n                  title={label}\r\n                >\r\n                  <Icon className=\"text-xl text-white hover:text-gray-400 transition\" />\r\n                </Link>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10\">\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">About</h3>\r\n            <ul className=\"space-y-2 text-sm\">\r\n              <li>\r\n                <Link href=\"/verified-classes\" className=\"hover:text-white transition\">\r\n                  Find Tutors\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/support\" className=\"hover:text-white transition\">\r\n                  Support\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"#\" className=\"hover:text-white transition\">\r\n                  Pricing\r\n                </Link>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">For Students</h3>\r\n            <ul className=\"space-y-2 text-sm\">\r\n              <li>\r\n                <Link href=\"/verified-classes\" className=\"hover:text-white transition\">\r\n                  Find Online Tutor\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/support\" className=\"hover:text-white transition\">\r\n                  FAQs\r\n                </Link>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">Contact</h3>\r\n            <address className=\"not-italic text-sm space-y-1 leading-relaxed\">\r\n              <p>Head Office</p>\r\n              <p>86, 87 Capital Market, Morbi - 363641</p>\r\n              <p>Contact: +91 96 877 877 88</p>\r\n              <p>Email: <EMAIL></p>\r\n            </address>\r\n          </div>\r\n\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">Apps</h3>\r\n           <Link href=\"https://play.google.com/store/apps/details?id=com.uest\" target=\"_blank\">\r\n            <Image\r\n              src=\"/playstore.png\"\r\n              alt=\"Google Play Store\"\r\n              width={180}\r\n              height={50}\r\n              className=\"object-contain\"\r\n            />\r\n          </Link>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Bottom Bar */}\r\n        <div className=\"border-t border-gray-800 pt-6 text-sm flex flex-col md:flex-row justify-between items-center gap-4\">\r\n          <p>© 2025 uest.in. All rights reserved.</p>\r\n          <div className=\"flex gap-4\">\r\n            <Link href=\"/terms-and-conditions\" className=\"hover:text-white transition\">\r\n              Terms & Conditions\r\n            </Link>\r\n            <Link href=\"/privacy-policy\" className=\"hover:text-white transition\">\r\n              Privacy Policy\r\n            </Link>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n};\r\n\r\nexport default Footer;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAeA,MAAM,SAAS;IACb,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCACvB,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;;;;;;sCAId,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,MAAM;oCAA8B,MAAM,8IAAA,CAAA,aAAU;oCAAE,OAAO;gCAAW;gCAC1E;oCACE,MAAM;oCACN,MAAM,8IAAA,CAAA,YAAS;oCACf,OAAO;gCACT;gCACA;oCACE,MAAM;oCACN,MAAM,8IAAA,CAAA,YAAS;oCACf,OAAO;gCACT;gCACA;oCACE,MAAM;oCACN,MAAM,8IAAA,CAAA,cAAW;oCACjB,OAAO;gCACT;gCACA;oCACE,MAAM;oCACN,MAAM,8IAAA,CAAA,cAAW;oCACjB,OAAO;gCACT;gCACA;oCACE,MAAM;oCACN,MAAM,8IAAA,CAAA,eAAY;oCAClB,OAAO;gCACT;gCACA;oCAAE,MAAM;oCAA4B,MAAM,8IAAA,CAAA,eAAY;oCAAE,OAAO;gCAAY;gCAC3E;oCACE,MAAM;oCACN,MAAM,8IAAA,CAAA,WAAQ;oCACd,OAAO;gCACT;6BACD,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,iBAChC,8OAAC;oCAAgB,WAAU;8CACzB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM;wCACN,WAAU;wCACV,OAAO;kDAEP,cAAA,8OAAC;4CAAK,WAAU;;;;;;;;;;;mCANV;;;;;;;;;;;;;;;;8BAahB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAoB,WAAU;0DAA8B;;;;;;;;;;;sDAIzE,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAA8B;;;;;;;;;;;sDAIhE,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;0DAA8B;;;;;;;;;;;;;;;;;;;;;;;sCAO7D,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAoB,WAAU;0DAA8B;;;;;;;;;;;sDAIzE,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAA8B;;;;;;;;;;;;;;;;;;;;;;;sCAOpE,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAQ,WAAU;;sDACjB,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;sCAIP,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACvD,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAyD,QAAO;8CAC1E,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAOhB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;sCAAE;;;;;;sCACH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAwB,WAAU;8CAA8B;;;;;;8CAG3E,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAkB,WAAU;8CAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjF;uCAEe", "debugId": null}}, {"offset": {"line": 2596, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/progress.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as ProgressPrimitive from '@radix-ui/react-progress';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Progress({\r\n  className,\r\n  value,\r\n  ...props\r\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\r\n  return (\r\n    <ProgressPrimitive.Root\r\n      data-slot=\"progress\"\r\n      className={cn('bg-primary/20 relative h-2 w-full overflow-hidden rounded-full', className)}\r\n      {...props}\r\n    >\r\n      <ProgressPrimitive.Indicator\r\n        data-slot=\"progress-indicator\"\r\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\r\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\r\n      />\r\n    </ProgressPrimitive.Root>\r\n  );\r\n}\r\n\r\nexport { Progress };\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD;IACpD,qBACE,8OAAC,oKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kEAAkE;QAC/E,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIlE", "debugId": null}}, {"offset": {"line": 2635, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app/uwhiz/countDownTimer.tsx"], "sourcesContent": ["'use client';\r\nimport { useState, useEffect, memo } from 'react';\r\nimport { Exam } from '@/lib/types';\r\nimport { RiTimerFlashFill } from 'react-icons/ri';\r\nimport { differenceInSeconds, addMinutes } from 'date-fns';\r\nimport { toZonedTime } from 'date-fns-tz';\r\n\r\ninterface CountdownTimerProps {\r\n  exam: Exam;\r\n}\r\n\r\nconst CountdownTimer = ({ exam }: CountdownTimerProps) => {\r\n  const [timerState, setTimerState] = useState<'registration' | 'application' | 'exam' | 'expired' | 'late'>('registration');\r\n\r\n  const [countdown, setCountdown] = useState<{\r\n    days: number;\r\n    hours: number;\r\n    minutes: number;\r\n    seconds: number;\r\n  }>({ days: 0, hours: 0, minutes: 0, seconds: 0 });\r\n\r\n  const [examTimer, setExamTimer] = useState<{\r\n    minutes: number;\r\n    seconds: number;\r\n    isLate: boolean;\r\n  }>({\r\n    minutes: 10,\r\n    seconds: 0,\r\n    isLate: false,\r\n  });\r\n\r\n  useEffect(() => {\r\n    const calculateStatus = () => {\r\n      const startRegistrationDate = exam.start_registration_date\r\n        ? new Date(exam.start_registration_date)\r\n        : null;\r\n      const startDate = new Date(exam.start_date);\r\n\r\n      if (isNaN(startDate.getTime())) {\r\n        console.error(`Invalid start_date for exam ${exam.id}: ${exam.start_date}`);\r\n        setTimerState('expired');\r\n        setCountdown({ days: 0, hours: 0, minutes: 0, seconds: 0 });\r\n        setExamTimer({ minutes: 0, seconds: 0, isLate: true });\r\n        return;\r\n      }\r\n\r\n      const now = toZonedTime(new Date(), 'Asia/Kolkata');\r\n      const istStartDate = toZonedTime(startDate, 'Asia/Kolkata');\r\n      const istStartTime = istStartDate.getTime();\r\n      const startWindowEnd = addMinutes(istStartDate, exam.duration).getTime(); \r\n      const nowTime = now.getTime();\r\n\r\n      // 1: Registration Phase\r\n      if (\r\n        startRegistrationDate &&\r\n        !isNaN(startRegistrationDate.getTime()) &&\r\n        nowTime < startRegistrationDate.getTime()\r\n      ) {\r\n        const diffSeconds = differenceInSeconds(startRegistrationDate, now);\r\n        const days = Math.floor(diffSeconds / (60 * 60 * 24));\r\n        const hours = Math.floor((diffSeconds % (60 * 60 * 24)) / (60 * 60));\r\n        const minutes = Math.floor((diffSeconds % (60 * 60)) / 60);\r\n        const seconds = diffSeconds % 60;\r\n        setTimerState('registration');\r\n        setCountdown({ days, hours, minutes, seconds });\r\n        setExamTimer({ minutes: 10, seconds: 0, isLate: false });\r\n      }\r\n      // 2: Application Phase\r\n      else if (nowTime < istStartTime) {\r\n        const diffSeconds = differenceInSeconds(istStartDate, now);\r\n        const days = Math.floor(diffSeconds / (60 * 60 * 24));\r\n        const hours = Math.floor((diffSeconds % (60 * 60 * 24)) / (60 * 60));\r\n        const minutes = Math.floor((diffSeconds % (60 * 60)) / 60);\r\n        const seconds = diffSeconds % 60;\r\n        setTimerState('application');\r\n        setCountdown({ days, hours, minutes, seconds });\r\n        setExamTimer({ minutes: 10, seconds: 0, isLate: false });\r\n      }\r\n      //  3: Exam Start \r\n      else if (nowTime >= istStartTime && nowTime <= startWindowEnd) {\r\n        const diffSeconds = differenceInSeconds(new Date(startWindowEnd), now);\r\n        const minutes = Math.floor(diffSeconds / 60);\r\n        const seconds = diffSeconds % 60;\r\n        setTimerState('exam');\r\n        setCountdown({ days: 0, hours: 0, minutes: 0, seconds: 0 });\r\n        setExamTimer({ minutes, seconds, isLate: false });\r\n      }\r\n      // 4: Exam Late/Finished\r\n      else {\r\n        setTimerState('late');\r\n        setCountdown({ days: 0, hours: 0, minutes: 0, seconds: 0 });\r\n        setExamTimer({ minutes: 0, seconds: 0, isLate: true });\r\n      }\r\n    };\r\n\r\n    calculateStatus();\r\n    const interval = setInterval(calculateStatus, 1000);\r\n\r\n    return () => clearInterval(interval);\r\n  }, [exam.start_date, exam.start_registration_date, exam.id]);\r\n\r\n  return (\r\n    <div className=\"flex items-center justify-center w-full gap-2 border-2 px-4 bg-orange-50 group-hover:bg-orange-100 transition-colors duration-300\">\r\n      <RiTimerFlashFill className=\"text-2xl text-customOrange animate-pulse\" />\r\n      <span className=\"font-semibold text-customOrange text-sm\">\r\n        {timerState === 'registration' ? (\r\n          <span>\r\n            Registration Starts in: {countdown.days}d {countdown.hours}h {countdown.minutes}m{' '}\r\n            {countdown.seconds}s\r\n          </span>\r\n        ) : timerState === 'application' ? (\r\n          <span>\r\n            Exam Starts in: {countdown.days}d {countdown.hours}h {countdown.minutes}m{' '}\r\n            {countdown.seconds}s\r\n          </span>\r\n        ) : timerState === 'exam' ? (\r\n          <span>\r\n            You May Starts In: {examTimer.minutes}m {examTimer.seconds}s\r\n          </span>\r\n        ) : timerState === 'late' ? (\r\n          <span>You Are Late</span>\r\n        ) : (\r\n          <span>Expired</span>\r\n        )}\r\n      </span>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default memo(CountdownTimer);"], "names": [], "mappings": ";;;;AACA;AAEA;AACA;AAAA;AACA;AAAA;AALA;;;;;;AAWA,MAAM,iBAAiB,CAAC,EAAE,IAAI,EAAuB;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgE;IAE3G,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAKtC;QAAE,MAAM;QAAG,OAAO;QAAG,SAAS;QAAG,SAAS;IAAE;IAE/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAItC;QACD,SAAS;QACT,SAAS;QACT,QAAQ;IACV;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB;YACtB,MAAM,wBAAwB,KAAK,uBAAuB,GACtD,IAAI,KAAK,KAAK,uBAAuB,IACrC;YACJ,MAAM,YAAY,IAAI,KAAK,KAAK,UAAU;YAE1C,IAAI,MAAM,UAAU,OAAO,KAAK;gBAC9B,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,KAAK,UAAU,EAAE;gBAC1E,cAAc;gBACd,aAAa;oBAAE,MAAM;oBAAG,OAAO;oBAAG,SAAS;oBAAG,SAAS;gBAAE;gBACzD,aAAa;oBAAE,SAAS;oBAAG,SAAS;oBAAG,QAAQ;gBAAK;gBACpD;YACF;YAEA,MAAM,MAAM,CAAA,GAAA,wKAAA,CAAA,cAAW,AAAD,EAAE,IAAI,QAAQ;YACpC,MAAM,eAAe,CAAA,GAAA,wKAAA,CAAA,cAAW,AAAD,EAAE,WAAW;YAC5C,MAAM,eAAe,aAAa,OAAO;YACzC,MAAM,iBAAiB,CAAA,GAAA,yIAAA,CAAA,aAAU,AAAD,EAAE,cAAc,KAAK,QAAQ,EAAE,OAAO;YACtE,MAAM,UAAU,IAAI,OAAO;YAE3B,wBAAwB;YACxB,IACE,yBACA,CAAC,MAAM,sBAAsB,OAAO,OACpC,UAAU,sBAAsB,OAAO,IACvC;gBACA,MAAM,cAAc,CAAA,GAAA,kJAAA,CAAA,sBAAmB,AAAD,EAAE,uBAAuB;gBAC/D,MAAM,OAAO,KAAK,KAAK,CAAC,cAAc,CAAC,KAAK,KAAK,EAAE;gBACnD,MAAM,QAAQ,KAAK,KAAK,CAAC,AAAC,cAAc,CAAC,KAAK,KAAK,EAAE,IAAK,CAAC,KAAK,EAAE;gBAClE,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,cAAc,CAAC,KAAK,EAAE,IAAK;gBACvD,MAAM,UAAU,cAAc;gBAC9B,cAAc;gBACd,aAAa;oBAAE;oBAAM;oBAAO;oBAAS;gBAAQ;gBAC7C,aAAa;oBAAE,SAAS;oBAAI,SAAS;oBAAG,QAAQ;gBAAM;YACxD,OAEK,IAAI,UAAU,cAAc;gBAC/B,MAAM,cAAc,CAAA,GAAA,kJAAA,CAAA,sBAAmB,AAAD,EAAE,cAAc;gBACtD,MAAM,OAAO,KAAK,KAAK,CAAC,cAAc,CAAC,KAAK,KAAK,EAAE;gBACnD,MAAM,QAAQ,KAAK,KAAK,CAAC,AAAC,cAAc,CAAC,KAAK,KAAK,EAAE,IAAK,CAAC,KAAK,EAAE;gBAClE,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,cAAc,CAAC,KAAK,EAAE,IAAK;gBACvD,MAAM,UAAU,cAAc;gBAC9B,cAAc;gBACd,aAAa;oBAAE;oBAAM;oBAAO;oBAAS;gBAAQ;gBAC7C,aAAa;oBAAE,SAAS;oBAAI,SAAS;oBAAG,QAAQ;gBAAM;YACxD,OAEK,IAAI,WAAW,gBAAgB,WAAW,gBAAgB;gBAC7D,MAAM,cAAc,CAAA,GAAA,kJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,KAAK,iBAAiB;gBAClE,MAAM,UAAU,KAAK,KAAK,CAAC,cAAc;gBACzC,MAAM,UAAU,cAAc;gBAC9B,cAAc;gBACd,aAAa;oBAAE,MAAM;oBAAG,OAAO;oBAAG,SAAS;oBAAG,SAAS;gBAAE;gBACzD,aAAa;oBAAE;oBAAS;oBAAS,QAAQ;gBAAM;YACjD,OAEK;gBACH,cAAc;gBACd,aAAa;oBAAE,MAAM;oBAAG,OAAO;oBAAG,SAAS;oBAAG,SAAS;gBAAE;gBACzD,aAAa;oBAAE,SAAS;oBAAG,SAAS;oBAAG,QAAQ;gBAAK;YACtD;QACF;QAEA;QACA,MAAM,WAAW,YAAY,iBAAiB;QAE9C,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC,KAAK,UAAU;QAAE,KAAK,uBAAuB;QAAE,KAAK,EAAE;KAAC;IAE3D,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,8IAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;0BAC5B,8OAAC;gBAAK,WAAU;0BACb,eAAe,+BACd,8OAAC;;wBAAK;wBACqB,UAAU,IAAI;wBAAC;wBAAG,UAAU,KAAK;wBAAC;wBAAG,UAAU,OAAO;wBAAC;wBAAE;wBACjF,UAAU,OAAO;wBAAC;;;;;;2BAEnB,eAAe,8BACjB,8OAAC;;wBAAK;wBACa,UAAU,IAAI;wBAAC;wBAAG,UAAU,KAAK;wBAAC;wBAAG,UAAU,OAAO;wBAAC;wBAAE;wBACzE,UAAU,OAAO;wBAAC;;;;;;2BAEnB,eAAe,uBACjB,8OAAC;;wBAAK;wBACgB,UAAU,OAAO;wBAAC;wBAAG,UAAU,OAAO;wBAAC;;;;;;2BAE3D,eAAe,uBACjB,8OAAC;8BAAK;;;;;yCAEN,8OAAC;8BAAK;;;;;;;;;;;;;;;;;AAKhB;qDAEe,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 2855, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app/uwhiz/examStatusButton.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Exam } from '@/lib/types';\r\nimport { toZonedTime } from 'date-fns-tz';\r\nimport { useSelector } from 'react-redux';\r\nimport { RootState } from '@/store';\r\nimport { toast } from 'sonner';\r\n\r\ninterface ExamStatusButtonProps {\r\n  exam: Exam;\r\n  hasApplied: boolean;\r\n  isMaxLimitReached: boolean;\r\n  hasAttempted: boolean;\r\n  onApplyClick: () => void;\r\n}\r\n\r\nconst ExamStatusButton = ({\r\n  exam,\r\n  hasApplied,\r\n  isMaxLimitReached,\r\n  hasAttempted,\r\n  onApplyClick,\r\n}: ExamStatusButtonProps) => {\r\n  const router = useRouter();\r\n  const isTutorLoggedIn = useSelector((state: RootState) => state.user.isAuthenticated); \r\n  const [examStatus, setExamStatus] = useState<'countdown' | 'start' | 'finished'>('countdown');\r\n  const [isStartWindowActive, setIsStartWindowActive] = useState(false);\r\n  const [isRegistrationOpen, setIsRegistrationOpen] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const calculateStatus = () => {\r\n      const startTime = new Date(exam.start_date).getTime();\r\n      const startRegistrationDate = exam.start_registration_date\r\n        ? new Date(exam.start_registration_date).getTime()\r\n        : null;\r\n\r\n      if (isNaN(startTime)) {\r\n        console.error(`Invalid start_date for exam ${exam.id}: ${exam.start_date}`);\r\n        setExamStatus('finished');\r\n        setIsStartWindowActive(false);\r\n        setIsRegistrationOpen(false);\r\n        return;\r\n      }\r\n\r\n      const now = toZonedTime(new Date(), 'Asia/Kolkata').getTime();\r\n      const startWindowEnd = startTime + exam.duration * 60 * 1000;\r\n\r\n      if (startRegistrationDate && now < startRegistrationDate) {\r\n        setIsRegistrationOpen(false);\r\n      } else {\r\n        setIsRegistrationOpen(true);\r\n      }\r\n\r\n      if (now < startTime) {\r\n        setExamStatus('countdown');\r\n        setIsStartWindowActive(false);\r\n      } else if (now >= startTime && now <= startWindowEnd) {\r\n        setExamStatus('start');\r\n        setIsStartWindowActive(true);\r\n      }  \r\n      else {\r\n        setExamStatus('finished');\r\n        setIsStartWindowActive(false);\r\n      }\r\n    };\r\n\r\n    calculateStatus();\r\n    const interval = setInterval(calculateStatus, 1000);\r\n\r\n    return () => clearInterval(interval);\r\n  }, [exam.start_date, exam.duration, exam.id, exam.start_registration_date]);\r\n\r\n  const handleStartExam = () => {\r\n    router.push(`/uwhiz-exam/${exam.id}`);\r\n  };\r\n\r\n  const handleViewResult = () => {\r\n    router.push(`/uwhiz-details/${exam.id}`);\r\n  };\r\n\r\n  const handleApplyClick = () => {\r\n    if (isTutorLoggedIn) {\r\n      toast.error('You are currently logged in as a tutor. Please log out and then log in as a student to apply for UWhiz.');\r\n      return;\r\n    }\r\n    onApplyClick();\r\n  };\r\n\r\n  if (hasAttempted) {\r\n    return (\r\n      <div className=\"flex flex-col items-center justify-center gap-4 mb-4 mx-5\">\r\n        <Button\r\n          className=\"w-full bg-gray-400 text-white font-semibold py-2 rounded-lg cursor-not-allowed\"\r\n          disabled\r\n        >\r\n          Attempted\r\n        </Button>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex flex-col items-center justify-center gap-4 mb-4 mx-5\">\r\n      {examStatus === 'countdown' ? (\r\n        <Button\r\n          onClick={handleApplyClick}\r\n          className=\"w-full bg-customOrange hover:bg-[#E88143] text-white font-semibold py-2 rounded-lg transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl\"\r\n          disabled={isMaxLimitReached || hasApplied || !isRegistrationOpen}\r\n        >\r\n          {hasApplied\r\n            ? 'Applied'\r\n            : isMaxLimitReached\r\n            ? 'Max Limit Reached'\r\n            : !isRegistrationOpen\r\n            ? 'Registration Will Start Soon'\r\n            : 'Apply Now'}\r\n        </Button>\r\n      ) : examStatus === 'start' ? (\r\n        <Button\r\n          onClick={handleStartExam}\r\n          className=\"w-full bg-green-500 hover:bg-green-600 text-white font-semibold py-2 rounded-lg transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl\"\r\n          disabled={!isStartWindowActive || !hasApplied}\r\n        >\r\n          {hasApplied ? 'Start Exam Now' : 'You Have Not Applied'}\r\n        </Button>\r\n      ) : (\r\n        <Button\r\n        disabled\r\n          onClick={handleViewResult}\r\n          className=\"w-full bg-customOrange hover:bg-[#E88143] text-white font-semibold py-2 rounded-lg transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl\"\r\n        >\r\n          Result Will Announce Soon\r\n        </Button>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ExamStatusButton;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAAA;AACA;AAEA;AATA;;;;;;;;AAmBA,MAAM,mBAAmB,CAAC,EACxB,IAAI,EACJ,UAAU,EACV,iBAAiB,EACjB,YAAY,EACZ,YAAY,EACU;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,kBAAkB,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAqB,MAAM,IAAI,CAAC,eAAe;IACpF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsC;IACjF,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB;YACtB,MAAM,YAAY,IAAI,KAAK,KAAK,UAAU,EAAE,OAAO;YACnD,MAAM,wBAAwB,KAAK,uBAAuB,GACtD,IAAI,KAAK,KAAK,uBAAuB,EAAE,OAAO,KAC9C;YAEJ,IAAI,MAAM,YAAY;gBACpB,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,KAAK,UAAU,EAAE;gBAC1E,cAAc;gBACd,uBAAuB;gBACvB,sBAAsB;gBACtB;YACF;YAEA,MAAM,MAAM,CAAA,GAAA,wKAAA,CAAA,cAAW,AAAD,EAAE,IAAI,QAAQ,gBAAgB,OAAO;YAC3D,MAAM,iBAAiB,YAAY,KAAK,QAAQ,GAAG,KAAK;YAExD,IAAI,yBAAyB,MAAM,uBAAuB;gBACxD,sBAAsB;YACxB,OAAO;gBACL,sBAAsB;YACxB;YAEA,IAAI,MAAM,WAAW;gBACnB,cAAc;gBACd,uBAAuB;YACzB,OAAO,IAAI,OAAO,aAAa,OAAO,gBAAgB;gBACpD,cAAc;gBACd,uBAAuB;YACzB,OACK;gBACH,cAAc;gBACd,uBAAuB;YACzB;QACF;QAEA;QACA,MAAM,WAAW,YAAY,iBAAiB;QAE9C,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC,KAAK,UAAU;QAAE,KAAK,QAAQ;QAAE,KAAK,EAAE;QAAE,KAAK,uBAAuB;KAAC;IAE1E,MAAM,kBAAkB;QACtB,OAAO,IAAI,CAAC,CAAC,YAAY,EAAE,KAAK,EAAE,EAAE;IACtC;IAEA,MAAM,mBAAmB;QACvB,OAAO,IAAI,CAAC,CAAC,eAAe,EAAE,KAAK,EAAE,EAAE;IACzC;IAEA,MAAM,mBAAmB;QACvB,IAAI,iBAAiB;YACnB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QACA;IACF;IAEA,IAAI,cAAc;QAChB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gBACL,WAAU;gBACV,QAAQ;0BACT;;;;;;;;;;;IAKP;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACZ,eAAe,4BACd,8OAAC,kIAAA,CAAA,SAAM;YACL,SAAS;YACT,WAAU;YACV,UAAU,qBAAqB,cAAc,CAAC;sBAE7C,aACG,YACA,oBACA,sBACA,CAAC,qBACD,iCACA;;;;;mBAEJ,eAAe,wBACjB,8OAAC,kIAAA,CAAA,SAAM;YACL,SAAS;YACT,WAAU;YACV,UAAU,CAAC,uBAAuB,CAAC;sBAElC,aAAa,mBAAmB;;;;;iCAGnC,8OAAC,kIAAA,CAAA,SAAM;YACP,QAAQ;YACN,SAAS;YACT,WAAU;sBACX;;;;;;;;;;;AAMT;uCAEe", "debugId": null}}, {"offset": {"line": 2992, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/GoogleLoginButton.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useRef, useCallback } from \"react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { FcGoogle } from \"react-icons/fc\";\r\nimport { googleAuthStudent } from \"@/services/studentAuthServices\";\r\nimport { setStudentAuthToken } from \"@/lib/utils\";\r\nimport { toast } from \"sonner\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { Loader2 } from \"lucide-react\";\r\nimport Script from \"next/script\";\r\n\r\ndeclare global {\r\n  interface Window {\r\n    google: any;\r\n  }\r\n}\r\n\r\ninterface GoogleLoginButtonProps {\r\n  onSuccess?: () => void;\r\n  uwhiz?: boolean;\r\n  handleApplyNow?: any;\r\n}\r\n\r\nexport function GoogleLoginButton({ onSuccess, uwhiz, handleApplyNow }: GoogleLoginButtonProps) {\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [googleScriptLoaded, setGoogleScriptLoaded] = useState(false);\r\n  const router = useRouter();\r\n  const googleButtonRef = useRef<HTMLDivElement>(null);\r\n\r\n  const handleGoogleResponse = useCallback(async (response: any) => {\r\n    try {\r\n      setIsLoading(true);\r\n      const payload = parseJwt(response.credential);\r\n\r\n      if (!payload) {\r\n        toast.error(\"Failed to authenticate with Google\");\r\n        return;\r\n      }\r\n\r\n      // Get referral code from localStorage if available\r\n      const referralCode = localStorage.getItem('referralCode');\r\n\r\n      const googleAuthData = {\r\n        googleId: payload.sub,\r\n        email: payload.email,\r\n        firstName: payload.given_name,\r\n        lastName: payload.family_name,\r\n        profilePhoto: payload.picture,\r\n        ...(referralCode && { referralCode }),\r\n      };\r\n\r\n      const authResponse = await googleAuthStudent(googleAuthData);\r\n\r\n      if (authResponse.success) {\r\n        const { token, user } = authResponse.data;\r\n\r\n        if (token) {\r\n          setStudentAuthToken(token);\r\n\r\n          if (user) {\r\n            localStorage.setItem('student_data', JSON.stringify(user));\r\n          }\r\n\r\n          window.dispatchEvent(new Event(\"storage\"));\r\n        }\r\n\r\n        toast.success(\"Logged in successfully with Google\");\r\n\r\n        // Clear referral code after successful registration\r\n        localStorage.removeItem('referralCode');\r\n\r\n        if (onSuccess) {\r\n          onSuccess();\r\n        }\r\n\r\n        if (uwhiz) {\r\n          handleApplyNow()\r\n        } else {  \r\n          router.push(\"/\");\r\n        }\r\n      } else {\r\n        toast.error(authResponse.message || \"Failed to authenticate with Google\");\r\n      }\r\n    } catch (error: any) {\r\n      if (error.response && error.response.data) {\r\n        const errorMessage = error.response.data.message || \"Authentication failed\";\r\n        toast.error(`Error: ${errorMessage}`);\r\n      } else if (error.message) {\r\n        toast.error(`Error: ${error.message}`);\r\n      } else {\r\n        toast.error(\"An error occurred during Google authentication\");\r\n      }\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, [setIsLoading, onSuccess, router, uwhiz, handleApplyNow]);\r\n\r\n  useEffect(() => {\r\n    if (typeof window !== 'undefined' && window.google && googleScriptLoaded) {\r\n      try {\r\n        window.google.accounts.id.initialize({\r\n          client_id: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,\r\n          callback: handleGoogleResponse,\r\n          auto_select: false,\r\n        });\r\n\r\n        if (googleButtonRef.current) {\r\n          window.google.accounts.id.renderButton(googleButtonRef.current, {\r\n            type: \"standard\",\r\n            theme: \"outline\",\r\n            size: \"large\",\r\n            text: \"continue_with\",\r\n            shape: \"rectangular\",\r\n            width: googleButtonRef.current.offsetWidth,\r\n          });\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Failed to initialize Google Sign-In:\", error);\r\n      }\r\n    }\r\n  }, [googleScriptLoaded, handleGoogleResponse]);\r\n\r\n  const parseJwt = (token: string) => {\r\n    try {\r\n      const base64Url = token.split('.')[1];\r\n      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\r\n      const jsonPayload = decodeURIComponent(\r\n        atob(base64)\r\n          .split('')\r\n          .map((c) => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))\r\n          .join('')\r\n      );\r\n      return JSON.parse(jsonPayload);\r\n    } catch (error) {\r\n      console.error(\"Failed to parse JWT token:\", error);\r\n      return null;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Script\r\n        src=\"https://accounts.google.com/gsi/client\"\r\n        strategy=\"afterInteractive\"\r\n        onLoad={() => setGoogleScriptLoaded(true)}\r\n      />\r\n\r\n      {isLoading ? (\r\n        <Button\r\n          type=\"button\"\r\n          variant=\"outline\"\r\n          className=\"w-full flex items-center justify-center gap-2 border-gray-300 bg-white text-gray-700 font-medium py-2.5 rounded-lg\"\r\n          disabled\r\n        >\r\n          <Loader2 className=\"h-5 w-5 animate-spin mr-2\" />\r\n          <span>Signing in...</span>\r\n        </Button>\r\n      ) : (\r\n        <div\r\n          ref={googleButtonRef}\r\n          className=\"w-full h-10 flex justify-center items-center\"\r\n        >\r\n          <Button\r\n            type=\"button\"\r\n            variant=\"outline\"\r\n            className=\"w-full flex items-center justify-center gap-2 border-gray-300 bg-white hover:bg-gray-50 text-gray-700 font-medium py-2.5 rounded-lg transition-colors\"\r\n            onClick={() => {\r\n              if (window.google?.accounts?.id) {\r\n                window.google.accounts.id.prompt();\r\n              } else {\r\n                toast.error(\"Google authentication is not available\");\r\n              }\r\n            }}\r\n          >\r\n            <FcGoogle className=\"h-5 w-5\" />\r\n            <span>Continue with Google</span>\r\n          </Button>\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;;AAwBO,SAAS,kBAAkB,EAAE,SAAS,EAAE,KAAK,EAAE,cAAc,EAA0B;IAC5F,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE/C,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC9C,IAAI;YACF,aAAa;YACb,MAAM,UAAU,SAAS,SAAS,UAAU;YAE5C,IAAI,CAAC,SAAS;gBACZ,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YAEA,mDAAmD;YACnD,MAAM,eAAe,aAAa,OAAO,CAAC;YAE1C,MAAM,iBAAiB;gBACrB,UAAU,QAAQ,GAAG;gBACrB,OAAO,QAAQ,KAAK;gBACpB,WAAW,QAAQ,UAAU;gBAC7B,UAAU,QAAQ,WAAW;gBAC7B,cAAc,QAAQ,OAAO;gBAC7B,GAAI,gBAAgB;oBAAE;gBAAa,CAAC;YACtC;YAEA,MAAM,eAAe,MAAM,CAAA,GAAA,sIAAA,CAAA,oBAAiB,AAAD,EAAE;YAE7C,IAAI,aAAa,OAAO,EAAE;gBACxB,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,aAAa,IAAI;gBAEzC,IAAI,OAAO;oBACT,CAAA,GAAA,mHAAA,CAAA,sBAAmB,AAAD,EAAE;oBAEpB,IAAI,MAAM;wBACR,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;oBACtD;oBAEA,OAAO,aAAa,CAAC,IAAI,MAAM;gBACjC;gBAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAEd,oDAAoD;gBACpD,aAAa,UAAU,CAAC;gBAExB,IAAI,WAAW;oBACb;gBACF;gBAEA,IAAI,OAAO;oBACT;gBACF,OAAO;oBACL,OAAO,IAAI,CAAC;gBACd;YACF,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,aAAa,OAAO,IAAI;YACtC;QACF,EAAE,OAAO,OAAY;YACnB,IAAI,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC,IAAI,EAAE;gBACzC,MAAM,eAAe,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,IAAI;gBACpD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,cAAc;YACtC,OAAO,IAAI,MAAM,OAAO,EAAE;gBACxB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,MAAM,OAAO,EAAE;YACvC,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,SAAU;YACR,aAAa;QACf;IACF,GAAG;QAAC;QAAc;QAAW;QAAQ;QAAO;KAAe;IAE3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uCAA0E;;QAqB1E;IACF,GAAG;QAAC;QAAoB;KAAqB;IAE7C,MAAM,WAAW,CAAC;QAChB,IAAI;YACF,MAAM,YAAY,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;YACrC,MAAM,SAAS,UAAU,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM;YAC1D,MAAM,cAAc,mBAClB,KAAK,QACF,KAAK,CAAC,IACN,GAAG,CAAC,CAAC,IAAM,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,QAAQ,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,IAC9D,IAAI,CAAC;YAEV,OAAO,KAAK,KAAK,CAAC;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO;QACT;IACF;IAEA,qBACE;;0BACE,8OAAC,8HAAA,CAAA,UAAM;gBACL,KAAI;gBACJ,UAAS;gBACT,QAAQ,IAAM,sBAAsB;;;;;;YAGrC,0BACC,8OAAC,kIAAA,CAAA,SAAM;gBACL,MAAK;gBACL,SAAQ;gBACR,WAAU;gBACV,QAAQ;;kCAER,8OAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC;kCAAK;;;;;;;;;;;qCAGR,8OAAC;gBACC,KAAK;gBACL,WAAU;0BAEV,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBACL,MAAK;oBACL,SAAQ;oBACR,WAAU;oBACV,SAAS;wBACP,IAAI,OAAO,MAAM,EAAE,UAAU,IAAI;4BAC/B,OAAO,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,MAAM;wBAClC,OAAO;4BACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wBACd;oBACF;;sCAEA,8OAAC,8IAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;sCAAK;;;;;;;;;;;;;;;;;;;AAMlB", "debugId": null}}, {"offset": {"line": 3188, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/referralApi.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\n\r\n// Get student discount information\r\nexport const getStudentDiscount = async () => {\r\n  try {\r\n    const response = await axiosInstance.get('/referral/discount/student');\r\n    return response.data;\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to get discount info: ${error.response?.data?.message || error.message}`,\r\n    };\r\n  }\r\n};\r\n\r\n// Calculate discounted price\r\nexport const calculateDiscountedPrice = (originalPrice: string, discountPercentage: number): number => {\r\n  const discountAmount = Math.round((Number(originalPrice) * discountPercentage) / 100);\r\n  return Number(originalPrice) - discountAmount;\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;;AAGO,MAAM,qBAAqB;IAChC,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;QACzC,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,6BAA6B,EAAE,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,EAAE;QACzF;IACF;AACF;AAGO,MAAM,2BAA2B,CAAC,eAAuB;IAC9D,MAAM,iBAAiB,KAAK,KAAK,CAAC,AAAC,OAAO,iBAAiB,qBAAsB;IACjF,OAAO,OAAO,iBAAiB;AACjC", "debugId": null}}, {"offset": {"line": 3215, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app/mock-test/restictExamAttempt.tsx"], "sourcesContent": ["export const saveExamAttempt = (studentId: string) => {\r\n  const timestamp = Date.now();\r\n  const examAttempts = localStorage.getItem(\"examAttempts\");\r\n  const attempts: { [key: string]: number } = examAttempts ? JSON.parse(examAttempts) : {};\r\n  attempts[studentId] = timestamp;\r\n  localStorage.setItem(\"examAttempts\", JSON.stringify(attempts));\r\n};\r\n\r\nexport const isAttemptAllowed = (studentId: string): { allowed: boolean; remainingHours: number | null } => {\r\n  const examAttempts = localStorage.getItem(\"examAttempts\");\r\n  if (!examAttempts) {\r\n    return { allowed: true, remainingHours: null };\r\n  }\r\n\r\n  const attempts: { [key: string]: number } = JSON.parse(examAttempts);\r\n  const lastAttempt = attempts[studentId];\r\n\r\n  if (!lastAttempt) {\r\n    return { allowed: true, remainingHours: null };\r\n  }\r\n\r\n  const lastAttemptTime = lastAttempt;\r\n  const currentTime = Date.now();\r\n  const hours24InMs = 24 * 60 * 60 * 1000;\r\n  const timeSinceLastAttempt = currentTime - lastAttemptTime;\r\n\r\n  if (timeSinceLastAttempt >= hours24InMs) {\r\n    return { allowed: true, remainingHours: null };\r\n  }\r\n\r\n  const remainingMs = hours24InMs - timeSinceLastAttempt;\r\n  const remainingHours = Math.ceil(remainingMs / (60 * 60 * 1000));\r\n  return { allowed: false, remainingHours };\r\n};"], "names": [], "mappings": ";;;;AAAO,MAAM,kBAAkB,CAAC;IAC9B,MAAM,YAAY,KAAK,GAAG;IAC1B,MAAM,eAAe,aAAa,OAAO,CAAC;IAC1C,MAAM,WAAsC,eAAe,KAAK,KAAK,CAAC,gBAAgB,CAAC;IACvF,QAAQ,CAAC,UAAU,GAAG;IACtB,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;AACtD;AAEO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,eAAe,aAAa,OAAO,CAAC;IAC1C,IAAI,CAAC,cAAc;QACjB,OAAO;YAAE,SAAS;YAAM,gBAAgB;QAAK;IAC/C;IAEA,MAAM,WAAsC,KAAK,KAAK,CAAC;IACvD,MAAM,cAAc,QAAQ,CAAC,UAAU;IAEvC,IAAI,CAAC,aAAa;QAChB,OAAO;YAAE,SAAS;YAAM,gBAAgB;QAAK;IAC/C;IAEA,MAAM,kBAAkB;IACxB,MAAM,cAAc,KAAK,GAAG;IAC5B,MAAM,cAAc,KAAK,KAAK,KAAK;IACnC,MAAM,uBAAuB,cAAc;IAE3C,IAAI,wBAAwB,aAAa;QACvC,OAAO;YAAE,SAAS;YAAM,gBAAgB;QAAK;IAC/C;IAEA,MAAM,cAAc,cAAc;IAClC,MAAM,iBAAiB,KAAK,IAAI,CAAC,cAAc,CAAC,KAAK,KAAK,IAAI;IAC9D,OAAO;QAAE,SAAS;QAAO;IAAe;AAC1C", "debugId": null}}, {"offset": {"line": 3265, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app/mock-test/mockExamButton.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { toast } from \"sonner\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { isAttemptAllowed } from \"./restictExamAttempt\";\r\n\r\ninterface MockExamButtonProps {\r\n  hasApplied: boolean;\r\n}\r\n\r\nexport default function MockExamButton({ hasApplied }: MockExamButtonProps) {\r\n  const router = useRouter();\r\n  const [isButtonDisabled, setIsButtonDisabled] = useState(false);\r\n  const [remainingHours, setRemainingHours] = useState<number | null>(null);\r\n  const [studentId, setStudentId] = useState<string | null>(null);\r\n\r\n  // Fetch studentId from localStorage\r\n  useEffect(() => {\r\n    try {\r\n      const data = localStorage.getItem(\"student_data\");\r\n      const fetchedStudentId = data ? JSON.parse(data).id : null;\r\n      setStudentId(fetchedStudentId);\r\n    } catch (error) {\r\n      console.error(\"Error retrieving studentId:\", error);\r\n      setStudentId(null);\r\n    }\r\n  }, []);\r\n\r\n  // Check exam attempt eligibility\r\n  useEffect(() => {\r\n    const checkAttempt = () => {\r\n      if (studentId) {\r\n        const { allowed, remainingHours } = isAttemptAllowed(studentId);\r\n        setIsButtonDisabled(!allowed);\r\n        setRemainingHours(remainingHours);\r\n      } else {\r\n        setIsButtonDisabled(true);\r\n      }\r\n    };\r\n\r\n    checkAttempt();\r\n    const intervalId = setInterval(checkAttempt, 60 * 1000);\r\n\r\n    return () => clearInterval(intervalId);\r\n  }, [studentId]);\r\n\r\n  const handleMockExam = () => {\r\n    if (!studentId) {\r\n      toast.error(\"Please log in to attempt the exam.\");\r\n      router.push(\"/login\");\r\n      return;\r\n    }\r\n\r\n    const { allowed, remainingHours } = isAttemptAllowed(studentId);\r\n    if (!allowed && remainingHours) {\r\n      toast.error(`You can attempt the exam again after ${remainingHours} hours.`);\r\n      return;\r\n    }\r\n    router.push(\"/mock-test\");\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex flex-col\">\r\n      {hasApplied ? (\r\n        <Button\r\n          className=\"w-1/2 mx-auto bg-customOrange hover:bg-[#E88143] text-white font-semibold rounded-lg transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl\"\r\n          onClick={handleMockExam}\r\n          disabled={isButtonDisabled || !studentId}\r\n        >\r\n          Try Mock Exam\r\n        </Button>\r\n      ) : (\r\n        <Button\r\n          className=\"w-1/2 mx-auto bg-customOrange hover:bg-[#E88143] text-white font-semibold rounded-lg transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl\"\r\n          onClick={handleMockExam}\r\n          disabled\r\n        >\r\n          Try Mock Exam\r\n        </Button>\r\n      )}\r\n      {isButtonDisabled && remainingHours && studentId && (\r\n        <p className=\"text-red-500 text-sm mt-2 mx-auto\">\r\n          You can attempt the exam again in {remainingHours} hours.\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAYe,SAAS,eAAe,EAAE,UAAU,EAAuB;IACxE,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE1D,oCAAoC;IACpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI;YACF,MAAM,OAAO,aAAa,OAAO,CAAC;YAClC,MAAM,mBAAmB,OAAO,KAAK,KAAK,CAAC,MAAM,EAAE,GAAG;YACtD,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,aAAa;QACf;IACF,GAAG,EAAE;IAEL,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,IAAI,WAAW;gBACb,MAAM,EAAE,OAAO,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,mBAAgB,AAAD,EAAE;gBACrD,oBAAoB,CAAC;gBACrB,kBAAkB;YACpB,OAAO;gBACL,oBAAoB;YACtB;QACF;QAEA;QACA,MAAM,aAAa,YAAY,cAAc,KAAK;QAElD,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;KAAU;IAEd,MAAM,iBAAiB;QACrB,IAAI,CAAC,WAAW;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,MAAM,EAAE,OAAO,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,mBAAgB,AAAD,EAAE;QACrD,IAAI,CAAC,WAAW,gBAAgB;YAC9B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,qCAAqC,EAAE,eAAe,OAAO,CAAC;YAC3E;QACF;QACA,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,8OAAC;QAAI,WAAU;;YACZ,2BACC,8OAAC,kIAAA,CAAA,SAAM;gBACL,WAAU;gBACV,SAAS;gBACT,UAAU,oBAAoB,CAAC;0BAChC;;;;;qCAID,8OAAC,kIAAA,CAAA,SAAM;gBACL,WAAU;gBACV,SAAS;gBACT,QAAQ;0BACT;;;;;;YAIF,oBAAoB,kBAAkB,2BACrC,8OAAC;gBAAE,WAAU;;oBAAoC;oBACZ;oBAAe;;;;;;;;;;;;;AAK5D", "debugId": null}}, {"offset": {"line": 3374, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app/uwhiz/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport Header from \"@/app-components/Header\";\r\nimport examLogo from \"../../../public/exam-logo.jpg\";\r\nimport { Card } from \"@/components/ui/card\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { getExams } from \"@/services/examApi\";\r\nimport { applyForExam } from \"@/services/examApplicationApi\";\r\nimport {sendExamApplicantEmail} from \"@/services/examApplicantEmailApi\"\r\nimport { useEffect, useState, useMemo } from \"react\";\r\nimport { Exam } from \"@/lib/types\";\r\nimport { FaRegClipboard, FaListOl, FaClock } from \"react-icons/fa6\";\r\nimport { toast } from \"sonner\";\r\nimport { uwhizPreventReattempApi } from \"@/services/uwhizPreventReattemptApi\";\r\nimport {\r\n  ChevronLeftIcon,\r\n  ChevronRightIcon,\r\n  ChevronsLeftIcon,\r\n  ChevronsRightIcon,\r\n  Coins,\r\n  Loader2,\r\n} from \"lucide-react\";\r\nimport Footer from \"@/app-components/Footer\";\r\nimport { GiPodiumWinner } from \"react-icons/gi\";\r\nimport { Progress } from \"@/components/ui/progress\";\r\nimport CountdownTimer from \"./countDownTimer\";\r\nimport ExamStatusButton from \"./examStatusButton\";\r\nimport Image from \"next/image\";\r\nimport { RiTimerFlashFill } from \"react-icons/ri\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { axiosInstance } from \"@/lib/axios\";\r\nimport { GoogleLoginButton } from \"@/components/ui/GoogleLoginButton\";\r\nimport { getStudentDiscount, calculateDiscountedPrice } from \"@/services/referralApi\";\r\nimport MockExamButton from '../mock-test/mockExamButton';\r\n\r\nconst Page = () => {\r\n  const [exams, setExams] = useState<Exam[]>([]);\r\n  const [showSuccessDialog, setShowSuccessDialog] = useState(false);\r\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\r\n  const [selectedExam, setSelectedExam] = useState<Exam | null>(null);\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [totalPages, setTotalPages] = useState(1);\r\n  const [limit] = useState(9);\r\n  const [loading, setLoading] = useState(false);\r\n  const [coinError, setCoinError] = useState<string | null>(null);\r\n  const [discountInfo, setDiscountInfo] = useState<{\r\n    hasDiscount: boolean;\r\n    discountPercentage: number;\r\n    referralCode: string | null;\r\n  } | null>(null);\r\n  const [isPaying, setIsPaying] = useState(false);\r\n  const [remCoins, setRemCoins] = useState<number>(0);\r\n  const [isAdding, setIsAdding] = useState(false);\r\n\r\n  const getStudentId = (): string => {\r\n    try {\r\n      const data = localStorage.getItem(\"student_data\");\r\n      return data ? JSON.parse(data).id : \"\";\r\n    } catch {\r\n      return \"\";\r\n    }\r\n  };\r\n\r\n  const memoizedExams = useMemo(() => exams, [exams]);\r\n  const router = useRouter();\r\n  const classId = getStudentId();\r\n\r\n  useEffect(() => {\r\n    const fetchDiscountInfo = async () => {\r\n      if (classId) {\r\n        try {\r\n          const response = await getStudentDiscount();\r\n          if (response.success) {\r\n            setDiscountInfo(response.data);\r\n          }\r\n        } catch (error) {\r\n          console.error(\"Error fetching discount info:\", error);\r\n        }\r\n      }\r\n    };\r\n\r\n    fetchDiscountInfo();\r\n  }, [classId]);\r\n\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      setLoading(true);\r\n      try {\r\n        const response = await getExams(currentPage, limit, classId);\r\n        let examsData = response.exams as Exam[];\r\n\r\n        // Check attempt status for each exam\r\n        if (classId) {\r\n          examsData = await Promise.all(\r\n            examsData.map(async (exam) => {\r\n              try {\r\n                const attemptResponse = await uwhizPreventReattempApi(classId, exam.id);\r\n                return {\r\n                  ...exam,\r\n                  hasAttempted: attemptResponse.success === false ? false : attemptResponse,\r\n                };\r\n              } catch (error) {\r\n                console.error(`Error checking attempt for exam ${exam.id}:`, error);\r\n                return { ...exam, hasAttempted: false };\r\n              }\r\n            })\r\n          );\r\n        } else {\r\n          examsData = examsData.map((exam) => ({ ...exam, hasAttempted: false }));\r\n        }\r\n\r\n        setExams(examsData);\r\n        setTotalPages(response.totalPages || 1);\r\n      } catch (error: any) {\r\n        console.error(\"Error fetching exams:\", error);\r\n        toast.error(error.message || \"Failed to load exams\");\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchData();\r\n  }, [currentPage, limit, classId]);\r\n\r\n  const handleApplyClick = (exam: Exam) => {\r\n    setSelectedExam(exam);\r\n    setCoinError(null);\r\n    setShowConfirmDialog(true);\r\n  };\r\n\r\n  const fetchCoinData = async () => {\r\n    try {\r\n      const coinsResponse = await axiosInstance.get('/coins/get-total-coins/student');\r\n      return coinsResponse.data.coins;\r\n    } catch (error) {\r\n      toast.error('Failed to load coin data. Please try again.');\r\n      console.error('Error fetching data', error);\r\n    }\r\n  };\r\n\r\n  const handleApplyNow = async () => {\r\n    if (!selectedExam) return;\r\n    const classId = getStudentId();\r\n\r\n    if (!classId) {\r\n      toast.error(\"Please log in as a student to apply for an exam\");\r\n      return;\r\n    }\r\n\r\n    const totalCoins = await fetchCoinData();\r\n\r\n    try {\r\n      const applyRes = await applyForExam(selectedExam.id, classId);\r\n\r\n      if (applyRes.application) {\r\n        try {\r\n          const studentData = localStorage.getItem('student_data');\r\n          if (studentData) {\r\n            const parsedData = JSON.parse(studentData);\r\n            const studentEmail = parsedData.email;\r\n\r\n            if (studentEmail) {\r\n              await sendExamApplicantEmail(\r\n                selectedExam.id,\r\n                selectedExam.exam_name,\r\n                studentEmail\r\n              );\r\n            }\r\n          }\r\n        } catch (emailError) {\r\n         console.log(\"Email sending failed\", emailError);\r\n        }\r\n\r\n        setExams((prevExams) =>\r\n          prevExams.map((e) =>\r\n            e.id === selectedExam.id\r\n              ? {\r\n                  ...e,\r\n                  joinedClassesCount: e.joinedClassesCount + 1,\r\n                  totalApplicants: (e.totalApplicants || 0) + 1,\r\n                  hasApplied: true,\r\n                }\r\n              : e\r\n          )\r\n        );\r\n        setShowConfirmDialog(false);\r\n        setShowSuccessDialog(true);\r\n        toast.success(applyRes.message || \"Successfully applied for the exam\");\r\n        setCoinError(null);\r\n      }\r\n    } catch (error: any) {\r\n      const errorMessage = error.message || \"Error applying for exam\";\r\n      toast.error(errorMessage);\r\n      if (errorMessage.includes(\"Required Coin for Applying in Exam\")) {\r\n        setCoinError(errorMessage);\r\n\r\n        let coinsRequired: number = Number(selectedExam.coins_required) ?? 0;\r\n\r\n        if (discountInfo?.hasDiscount) {\r\n          coinsRequired = coinsRequired * (1 - discountInfo.discountPercentage / 100);\r\n        }\r\n        const remainingCoins = Math.floor(Math.floor(coinsRequired) - totalCoins);\r\n        setRemCoins(remainingCoins);\r\n      } else {\r\n        setShowConfirmDialog(false);\r\n      }\r\n    } finally {\r\n      setIsPaying(false);\r\n    }\r\n  };\r\n\r\n  const closeDialog = () => {\r\n    setShowSuccessDialog(false);\r\n    setShowConfirmDialog(false);\r\n    setSelectedExam(null);\r\n    setCoinError(null);\r\n  };\r\n\r\n  const handleViewDetails = (id: string) => {\r\n    window.location.href = `/uwhiz-info/${id}`;\r\n  };\r\n\r\n  const calculateProgress = (totalApplicants: number, totalIntake: number) => {\r\n    if (totalIntake === 0) return 0;\r\n    const percentage = (totalApplicants / totalIntake) * 100;\r\n    return Math.min(100, Math.max(0, percentage));\r\n  };\r\n\r\n  const initiatePayment = async () => {\r\n    setIsPaying(true);\r\n    try {\r\n      const res = await axiosInstance.post('/coins/create-order', {\r\n        amount: remCoins * 100,\r\n      });\r\n\r\n      const { order } = res.data;\r\n\r\n      const options = {\r\n        key: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID,\r\n        amount: order.amount,\r\n        currency: 'INR',\r\n        name: 'Uest Coins',\r\n        description: 'Add Uest Coins',\r\n        order_id: order.id,\r\n        handler: async function (response: any) {\r\n          try {\r\n            setIsAdding(true);\r\n\r\n            await axiosInstance.post('/coins/verify', {\r\n              razorpay_order_id: response.razorpay_order_id,\r\n              razorpay_payment_id: response.razorpay_payment_id,\r\n              razorpay_signature: response.razorpay_signature,\r\n              amount: remCoins * 100,\r\n            });\r\n\r\n            toast.success('Coins added successfully!');\r\n            handleApplyNow();\r\n            setIsAdding(false);\r\n          } catch {\r\n            toast.error('Payment verification failed');\r\n          } finally {\r\n            setIsAdding(false);\r\n          }\r\n        },\r\n        theme: {\r\n          color: '#f97316',\r\n        },\r\n      };\r\n\r\n      const rzp = new (window as any).Razorpay(options);\r\n      rzp.open();\r\n    } catch {\r\n      toast.error('Payment initialization failed');\r\n    } finally {\r\n      setIsPaying(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    const script = document.createElement('script');\r\n    script.src = 'https://checkout.razorpay.com/v1/checkout.js';\r\n    script.async = true;\r\n    document.body.appendChild(script);\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"\">\r\n      <Header />\r\n      <div className=\"flex justify-center bg-black pb-10\">\r\n        <Image\r\n          height={400}\r\n          width={400}\r\n          src={examLogo.src}\r\n          alt=\"Exam Logo\"\r\n          priority={true}\r\n          quality={100}\r\n        />\r\n      </div>\r\n\r\n      {showSuccessDialog && selectedExam && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n          <div className=\"bg-white p-6 rounded-lg shadow-xl max-w-md w-full\">\r\n            <h2 className=\"text-2xl font-bold text-green-600 mb-4\">\r\n              Application Successful!\r\n            </h2>\r\n            <p className=\"text-gray-700 mb-6\">\r\n              You have successfully applied for{\" \"}\r\n              <strong>{selectedExam.exam_name}</strong>.\r\n            </p>\r\n            <Button\r\n              onClick={closeDialog}\r\n              className=\"w-full bg-customOrange hover:bg-orange-600 text-white font-semibold py-2 rounded-lg\"\r\n            >\r\n              Close\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {showConfirmDialog && selectedExam && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n          <div className=\"bg-white p-8 rounded-xl shadow-2xl max-w-md w-full transform transition-all duration-300 scale-100\">\r\n            <h2 className=\"text-3xl font-semibold text-customOrange mb-4 border-b border-gray-200 pb-2\">\r\n              Are You Sure?\r\n            </h2>\r\n            <p className=\"text-gray-700 text-lg mb-6 leading-relaxed\">\r\n              Do you want to apply for{\" \"}\r\n              <strong className=\"text-customOrange\">\r\n                {selectedExam.exam_name}\r\n              </strong>\r\n              ?\r\n              {selectedExam.coins_required != null && (\r\n                <span>\r\n                  {\" \"}\r\n                  This will cost{\" \"}\r\n                  {discountInfo?.hasDiscount ? (\r\n                    <span>\r\n                      <span className=\"line-through text-gray-500\">\r\n                        {selectedExam.coins_required}\r\n                      </span>{\" \"}\r\n                      <strong className=\"text-green-600\">\r\n                        {calculateDiscountedPrice(selectedExam.coins_required, discountInfo.discountPercentage)}\r\n                      </strong>{\" \"}\r\n                      <span className=\"text-green-600 text-sm\">\r\n                        ({discountInfo.discountPercentage}% discount applied)\r\n                      </span>\r\n                    </span>\r\n                  ) : (\r\n                    <strong className=\"text-customOrange\">\r\n                      {selectedExam.coins_required}\r\n                    </strong>\r\n                  )}{\" \"}\r\n                  coins.\r\n                </span>\r\n              )}\r\n            </p>\r\n            {coinError && (\r\n              <div className=\"flex-col justify-center items-start gap-2 bg-red-50 p-4 rounded-lg mb-6 border border-red-200\">\r\n                <div className=\"flex gap-5 items-center\">\r\n                  <svg\r\n                    className=\"w-5 h-5 text-red-600 mt-1\"\r\n                    fill=\"none\"\r\n                    stroke=\"currentColor\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                  >\r\n                    <path\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                      strokeWidth=\"2\"\r\n                      d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\r\n                    />\r\n                  </svg>\r\n                  <p className=\"text-red-600 text-sm font-medium\">\r\n                    {coinError}\r\n                  </p>\r\n                </div>\r\n                <Button\r\n                  onClick={() => initiatePayment()}\r\n                  className=\"mt-5 w-full bg-customOrange hover:bg-orange-600 text-white font-semibold py-3 rounded-lg\"\r\n                  disabled={isAdding}\r\n                >\r\n                  {isAdding ? (\r\n                    <span className=\"flex items-center justify-center gap-2\">\r\n                      <Loader2 className=\"animate-spin w-5 h-5\" />\r\n                      Processing...\r\n                    </span>\r\n                  ) : (\r\n                    \"Add Coins\"\r\n                  )}\r\n                </Button>\r\n              </div>\r\n            )}\r\n            {getStudentId() ? (\r\n              <div className=\"flex gap-4\">\r\n                <Button\r\n                  onClick={handleApplyNow}\r\n                  className=\"w-1/2 bg-customOrange hover:bg-orange-600 text-white font-semibold py-3 rounded-lg\"\r\n                  disabled={!!coinError || isPaying}\r\n                >\r\n                  {isPaying ? (\r\n                    <>\r\n                      <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\r\n                      Processing...\r\n                    </>\r\n                  ) : (\r\n                    \"Yes, Apply\"\r\n                  )}\r\n                </Button>\r\n                <Button\r\n                  onClick={closeDialog}\r\n                  className=\"w-1/2 bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-3 rounded-lg\"\r\n                >\r\n                  Cancel\r\n                </Button>\r\n              </div>\r\n            ) : (\r\n              <GoogleLoginButton uwhiz={true} handleApplyNow={handleApplyNow} />\r\n            )}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      <h1 className=\"text-center text-4xl font-bold dark:text-white mt-10\">\r\n        Upcoming <span className=\"text-customOrange\">Exams</span>\r\n      </h1>\r\n\r\n      {loading ? (\r\n        <p className=\"text-center text-white mt-6\">Loading exams...</p>\r\n      ) : memoizedExams && memoizedExams.length === 0 ? (\r\n        <p className=\"text-center text-white mt-6\">No exams found.</p>\r\n      ) : (\r\n        <>\r\n          {/* uwhiz Current Exam Section */}\r\n          <div className=\"flex flex-wrap justify-center gap-6 p-4 sm:p-6 md:p-10 lg:p-20\">\r\n            {memoizedExams &&\r\n              memoizedExams.map((exam) => {\r\n                const totalIntake = exam.total_student_intake ?? 0;\r\n                const firstRankPrice =\r\n                  exam.UwhizPriceRank.find((rank: any) => rank.rank === 1)?.price ?? 0;\r\n\r\n                return (\r\n                  <Card\r\n                    key={exam.id}\r\n                    className=\"w-full sm:w-[45%] lg:w-[30%] dark:bg-[#101828] text-black rounded-xl shadow-lg hover:shadow-2xl hover:scale-105 transform transition-all duration-500 ease-out group border-l-3 border-l-orange-400\"\r\n                  >\r\n                    <div className=\"flex flex-col items-center px-3 py-2 space-y-2 text-center\">\r\n                      <h1 className=\"text-2xl font-bold dark:text-white transition-transform duration-300 group-hover:scale-105\">\r\n                        {exam.exam_name}\r\n                      </h1>\r\n                      <div className=\"flex items-center gap-2 text-xl font-bold\">\r\n                        <GiPodiumWinner className=\"text-xl text-customOrange\" />\r\n                        <span className=\"dark:text-white\">\r\n                          1st Prize: <span className=\"text-customOrange\">{firstRankPrice}</span>\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                    <CountdownTimer exam={exam} />\r\n                    <div className=\"text-sm dark:text-gray-100 mb-3 px-4 space-y-2 dark:group-hover:text-gray-200 transition-colors duration-300\">\r\n                      <div className=\"flex justify-between\">\r\n                        <div className=\"flex gap-2 items-center\">\r\n                          <FaListOl className=\"text-lg text-customOrange\" />\r\n                          <span>Total Questions: {exam.total_questions}</span>\r\n                        </div>\r\n                        <div className=\"flex gap-2 items-center\">\r\n                          <FaRegClipboard className=\"text-lg text-customOrange\" />\r\n                          <span>Marks: {exam.marks}</span>\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"flex justify-between\">\r\n                        <div className=\"flex gap-2 items-center\">\r\n                          <FaClock className=\"text-lg text-customOrange\" />\r\n                          <span>Duration: {exam.duration}</span>\r\n                        </div>\r\n                        <div className=\"flex gap-2 items-center\">\r\n                          <Coins className=\"text-lg text-customOrange\" />\r\n                          <span>\r\n                            Coins: {exam.coins_required != null ? (\r\n                              discountInfo?.hasDiscount ? (\r\n                                <span className=\"flex items-center gap-1\">\r\n                                  <span className=\"line-through text-gray-500 text-xs\">\r\n                                    {exam.coins_required}\r\n                                  </span>\r\n                                  <span className=\"text-green-600 font-bold\">\r\n                                    {calculateDiscountedPrice(exam.coins_required, discountInfo.discountPercentage)}\r\n                                  </span>\r\n                                  <span className=\"text-xs text-green-600\">\r\n                                    ({discountInfo.discountPercentage}% off)\r\n                                  </span>\r\n                                </span>\r\n                              ) : (\r\n                                exam.coins_required\r\n                              )\r\n                            ) : \"Free\"}\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"flex flex-col px-3\">\r\n                      <p className=\"dark:text-white mb-1 tracking-wider\">Student Joined</p>\r\n                      <Progress\r\n                        value={calculateProgress(exam.totalApplicants ?? 0, totalIntake)}\r\n                        className=\"[&>*]:bg-customOrange bg-slate-300\"\r\n                      />\r\n                      <p className=\"flex justify-end dark:text-white\">\r\n                        <span>\r\n                          {exam.totalApplicants} / {totalIntake}\r\n                        </span>\r\n                      </p>\r\n                    </div>\r\n                    <div className=\"flex justify-center px-3\">\r\n                      <Button\r\n                        className=\"w-1/3 bg-customOrange hover:bg-[#E88143] text-white font-semibold rounded-lg transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl\"\r\n                        onClick={() => handleViewDetails(String(exam.id))}\r\n                      >\r\n                        View details\r\n                      </Button>\r\n                      <ExamStatusButton\r\n                        exam={exam}\r\n                        hasApplied={exam.hasApplied}\r\n                        isMaxLimitReached={exam.isMaxLimitReached}\r\n                        hasAttempted={exam.hasAttempted}\r\n                        onApplyClick={() => handleApplyClick(exam)}\r\n                      />\r\n                    </div>\r\n\r\n                    <MockExamButton hasApplied={exam.hasApplied} />\r\n\r\n                    <div className=\"bg-customOrange text-white font-bold text-sm text-center p-2 flex items-center justify-center gap-2 border-t border-customOrange\">\r\n                      <Image\r\n                        src=\"/sm_water_park.png\"\r\n                        alt=\"SM Water Park Logo\"\r\n                        width={80}\r\n                        height={80}\r\n                        className=\"object-contain h-10 w-10\"\r\n                      />\r\n                      <span>Apply & Get SM & Shiv Water Park Tickets Free! 🎉</span>\r\n                      <Image\r\n                        src=\"/shiv_water_park.png\"\r\n                        alt=\"Shiv Water Park Logo\"\r\n                        width={80}\r\n                        height={80}\r\n                        className=\"object-contain h-10 w-10\"\r\n                      />\r\n                    </div>\r\n                    <div className=\"flex items-center justify-center gap-2 text-sm text-gray-600 dark:text-gray-300 pt-2 border-t border-gray-200 dark:border-gray-700\">\r\n                      <span>Sponsored by</span>\r\n                      <Image\r\n                        src=\"/nalanda.png\"\r\n                        alt=\"Nalanda Logo\"\r\n                        height={60}\r\n                        width={60}\r\n                        className=\"object-contain h-5 w-5\"\r\n                      />\r\n                      <span className=\"font-semibold\">Nalanda Vidhyalay</span>\r\n                    </div>\r\n                  </Card>\r\n                );\r\n              })}\r\n          </div>\r\n\r\n          {/* Past Exams section */}\r\n          <h1 className=\"text-center text-4xl font-bold dark:text-white mt-10\">\r\n            Past <span className=\"text-customOrange\">Exam</span>\r\n          </h1>\r\n          <div className=\"flex flex-wrap justify-center gap-6 p-4 sm:p-6 md:p-10 lg:p-20\">\r\n            <Card\r\n              key={\"exam.id\"}\r\n              className=\"w-full sm:w-[45%] lg:w-[30%] dark:bg-[#101828] text-black rounded-xl shadow-lg hover:shadow-2xl hover:scale-105 transform transition-all duration-500 ease-out group border-l-3 border-l-orange-400\"\r\n            >\r\n              <div className=\"flex flex-col items-center px-3 py-2 space-y-2 text-center\">\r\n                <h1 className=\"text-2xl font-bold dark:text-white transition-transform duration-300 group-hover:scale-105\">\r\n                  U Whiz - Super Tutors\r\n                </h1>\r\n                <div className=\"flex items-center gap-2 text-xl font-bold\">\r\n                  <GiPodiumWinner className=\"text-xl text-customOrange\" />\r\n                  <span className=\"dark:text-white\">\r\n                    1st Prize: <span className=\"text-customOrange\">21000</span>\r\n                  </span>\r\n                </div>\r\n              </div>\r\n              <div className=\"flex items-center justify-center w-full gap-2 border-2 px-4 bg-orange-50 group-hover:bg-orange-100 transition-colors duration-300\">\r\n                <RiTimerFlashFill className=\"text-2xl text-customOrange animate-pulse\" />\r\n                <span className=\"font-semibold text-customOrange text-sm\">You Are Late</span>\r\n              </div>\r\n              <div className=\"text-sm dark:text-gray-100 mb-3 px-4 space-y-2 dark:group-hover:text-gray-200 transition-colors duration-300\">\r\n                <div className=\"flex justify-between\">\r\n                  <div className=\"flex gap-2 items-center\">\r\n                    <FaListOl className=\"text-lg text-customOrange\" />\r\n                    <span>Total Questions: 100</span>\r\n                  </div>\r\n                  <div className=\"flex gap-2 items-center\">\r\n                    <FaRegClipboard className=\"text-lg text-customOrange\" />\r\n                    <span>Marks: 100</span>\r\n                  </div>\r\n                </div>\r\n                <div className=\"flex justify-between\">\r\n                  <div className=\"flex gap-2 items-center\">\r\n                    <FaClock className=\"text-lg text-customOrange\" />\r\n                    <span>Duration: 90</span>\r\n                  </div>\r\n                  <div className=\"flex gap-2 items-center\">\r\n                    <Coins className=\"text-lg text-customOrange\" />\r\n                    <span>Coins: 79</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div className=\"flex flex-col px-3\">\r\n                <p className=\"dark:text-white mb-1 tracking-wider\">Classes Joined</p>\r\n                <Progress\r\n                  value={calculateProgress(205, 205)}\r\n                  className=\"[&>*]:bg-customOrange bg-slate-300\"\r\n                />\r\n                <p className=\"flex justify-end dark:text-white\">\r\n                  <span>205 / 205</span>\r\n                </p>\r\n              </div>\r\n              <Button\r\n                className=\"bg-customOrange mx-5\"\r\n                onClick={() => router.push(`/uwhiz-details/${1}`)}\r\n              >\r\n                View Result\r\n              </Button>\r\n            </Card>\r\n          </div>\r\n\r\n          {/* Pagination section */}\r\n          <div className=\"flex items-center justify-center px-4 py-6 dark:text-white\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"icon\"\r\n                onClick={() => setCurrentPage(1)}\r\n                disabled={currentPage === 1}\r\n                className=\"bg-white dark:text-white hover:bg-gray-200 cursor-pointer\"\r\n              >\r\n                <ChevronsLeftIcon />\r\n              </Button>\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"icon\"\r\n                onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}\r\n                disabled={currentPage === 1}\r\n                className=\"bg-white dark:text-white hover:bg-gray-200 cursor-pointer\"\r\n              >\r\n                <ChevronLeftIcon />\r\n              </Button>\r\n              <span className=\"text-sm\">\r\n                Page {currentPage} of {totalPages}\r\n              </span>\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"icon\"\r\n                onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}\r\n                disabled={currentPage === totalPages}\r\n                className=\"hover:bg-gray-200 cursor-pointer\"\r\n              >\r\n                <ChevronRightIcon />\r\n              </Button>\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"icon\"\r\n                onClick={() => setCurrentPage(totalPages)}\r\n                disabled={currentPage === totalPages}\r\n                className=\"bg-white dark:text-white hover:bg-gray-200 cursor-pointer\"\r\n              >\r\n                <ChevronsRightIcon />\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </>\r\n      )}\r\n      <Footer />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Page;"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAhCA;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA,MAAM,OAAO;IACX,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,MAAM,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAIrC;IACV,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,OAAO,aAAa,OAAO,CAAC;YAClC,OAAO,OAAO,KAAK,KAAK,CAAC,MAAM,EAAE,GAAG;QACtC,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,OAAO;QAAC;KAAM;IAClD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,UAAU;IAEhB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,oBAAoB;YACxB,IAAI,SAAS;gBACX,IAAI;oBACF,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,qBAAkB,AAAD;oBACxC,IAAI,SAAS,OAAO,EAAE;wBACpB,gBAAgB,SAAS,IAAI;oBAC/B;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,iCAAiC;gBACjD;YACF;QACF;QAEA;IACF,GAAG;QAAC;KAAQ;IAEZ,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY;YAChB,WAAW;YACX,IAAI;gBACF,MAAM,WAAW,MAAM,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE,aAAa,OAAO;gBACpD,IAAI,YAAY,SAAS,KAAK;gBAE9B,qCAAqC;gBACrC,IAAI,SAAS;oBACX,YAAY,MAAM,QAAQ,GAAG,CAC3B,UAAU,GAAG,CAAC,OAAO;wBACnB,IAAI;4BACF,MAAM,kBAAkB,MAAM,CAAA,GAAA,2IAAA,CAAA,0BAAuB,AAAD,EAAE,SAAS,KAAK,EAAE;4BACtE,OAAO;gCACL,GAAG,IAAI;gCACP,cAAc,gBAAgB,OAAO,KAAK,QAAQ,QAAQ;4BAC5D;wBACF,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE;4BAC7D,OAAO;gCAAE,GAAG,IAAI;gCAAE,cAAc;4BAAM;wBACxC;oBACF;gBAEJ,OAAO;oBACL,YAAY,UAAU,GAAG,CAAC,CAAC,OAAS,CAAC;4BAAE,GAAG,IAAI;4BAAE,cAAc;wBAAM,CAAC;gBACvE;gBAEA,SAAS;gBACT,cAAc,SAAS,UAAU,IAAI;YACvC,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,yBAAyB;gBACvC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC/B,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG;QAAC;QAAa;QAAO;KAAQ;IAEhC,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;QAChB,aAAa;QACb,qBAAqB;IACvB;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,gBAAgB,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;YAC9C,OAAO,cAAc,IAAI,CAAC,KAAK;QACjC,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC,uBAAuB;QACvC;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,cAAc;QACnB,MAAM,UAAU;QAEhB,IAAI,CAAC,SAAS;YACZ,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,aAAa,MAAM;QAEzB,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,qIAAA,CAAA,eAAY,AAAD,EAAE,aAAa,EAAE,EAAE;YAErD,IAAI,SAAS,WAAW,EAAE;gBACxB,IAAI;oBACF,MAAM,cAAc,aAAa,OAAO,CAAC;oBACzC,IAAI,aAAa;wBACf,MAAM,aAAa,KAAK,KAAK,CAAC;wBAC9B,MAAM,eAAe,WAAW,KAAK;wBAErC,IAAI,cAAc;4BAChB,MAAM,CAAA,GAAA,wIAAA,CAAA,yBAAsB,AAAD,EACzB,aAAa,EAAE,EACf,aAAa,SAAS,EACtB;wBAEJ;oBACF;gBACF,EAAE,OAAO,YAAY;oBACpB,QAAQ,GAAG,CAAC,wBAAwB;gBACrC;gBAEA,SAAS,CAAC,YACR,UAAU,GAAG,CAAC,CAAC,IACb,EAAE,EAAE,KAAK,aAAa,EAAE,GACpB;4BACE,GAAG,CAAC;4BACJ,oBAAoB,EAAE,kBAAkB,GAAG;4BAC3C,iBAAiB,CAAC,EAAE,eAAe,IAAI,CAAC,IAAI;4BAC5C,YAAY;wBACd,IACA;gBAGR,qBAAqB;gBACrB,qBAAqB;gBACrB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,SAAS,OAAO,IAAI;gBAClC,aAAa;YACf;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,OAAO,IAAI;YACtC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,IAAI,aAAa,QAAQ,CAAC,uCAAuC;gBAC/D,aAAa;gBAEb,IAAI,gBAAwB,OAAO,aAAa,cAAc,KAAK;gBAEnE,IAAI,cAAc,aAAa;oBAC7B,gBAAgB,gBAAgB,CAAC,IAAI,aAAa,kBAAkB,GAAG,GAAG;gBAC5E;gBACA,MAAM,iBAAiB,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC,iBAAiB;gBAC9D,YAAY;YACd,OAAO;gBACL,qBAAqB;YACvB;QACF,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,cAAc;QAClB,qBAAqB;QACrB,qBAAqB;QACrB,gBAAgB;QAChB,aAAa;IACf;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,YAAY,EAAE,IAAI;IAC5C;IAEA,MAAM,oBAAoB,CAAC,iBAAyB;QAClD,IAAI,gBAAgB,GAAG,OAAO;QAC9B,MAAM,aAAa,AAAC,kBAAkB,cAAe;QACrD,OAAO,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG;IACnC;IAEA,MAAM,kBAAkB;QACtB,YAAY;QACZ,IAAI;YACF,MAAM,MAAM,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,uBAAuB;gBAC1D,QAAQ,WAAW;YACrB;YAEA,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,IAAI;YAE1B,MAAM,UAAU;gBACd,GAAG;gBACH,QAAQ,MAAM,MAAM;gBACpB,UAAU;gBACV,MAAM;gBACN,aAAa;gBACb,UAAU,MAAM,EAAE;gBAClB,SAAS,eAAgB,QAAa;oBACpC,IAAI;wBACF,YAAY;wBAEZ,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,iBAAiB;4BACxC,mBAAmB,SAAS,iBAAiB;4BAC7C,qBAAqB,SAAS,mBAAmB;4BACjD,oBAAoB,SAAS,kBAAkB;4BAC/C,QAAQ,WAAW;wBACrB;wBAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;wBACd;wBACA,YAAY;oBACd,EAAE,OAAM;wBACN,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACd,SAAU;wBACR,YAAY;oBACd;gBACF;gBACA,OAAO;oBACL,OAAO;gBACT;YACF;YAEA,MAAM,MAAM,IAAI,AAAC,OAAe,QAAQ,CAAC;YACzC,IAAI,IAAI;QACV,EAAE,OAAM;YACN,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,YAAY;QACd;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,SAAS,SAAS,aAAa,CAAC;QACtC,OAAO,GAAG,GAAG;QACb,OAAO,KAAK,GAAG;QACf,SAAS,IAAI,CAAC,WAAW,CAAC;IAC5B,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,mIAAA,CAAA,UAAM;;;;;0BACP,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oBACJ,QAAQ;oBACR,OAAO;oBACP,KAAK,4QAAA,CAAA,UAAQ,CAAC,GAAG;oBACjB,KAAI;oBACJ,UAAU;oBACV,SAAS;;;;;;;;;;;YAIZ,qBAAqB,8BACpB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,8OAAC;4BAAE,WAAU;;gCAAqB;gCACE;8CAClC,8OAAC;8CAAQ,aAAa,SAAS;;;;;;gCAAU;;;;;;;sCAE3C,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;YAON,qBAAqB,8BACpB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA8E;;;;;;sCAG5F,8OAAC;4BAAE,WAAU;;gCAA6C;gCAC/B;8CACzB,8OAAC;oCAAO,WAAU;8CACf,aAAa,SAAS;;;;;;gCAChB;gCAER,aAAa,cAAc,IAAI,sBAC9B,8OAAC;;wCACE;wCAAI;wCACU;wCACd,cAAc,4BACb,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DACb,aAAa,cAAc;;;;;;gDACtB;8DACR,8OAAC;oDAAO,WAAU;8DACf,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE,aAAa,cAAc,EAAE,aAAa,kBAAkB;;;;;;gDAC9E;8DACV,8OAAC;oDAAK,WAAU;;wDAAyB;wDACrC,aAAa,kBAAkB;wDAAC;;;;;;;;;;;;iEAItC,8OAAC;4CAAO,WAAU;sDACf,aAAa,cAAc;;;;;;wCAE7B;wCAAI;;;;;;;;;;;;;wBAKZ,2BACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,WAAU;4CACV,MAAK;4CACL,QAAO;4CACP,SAAQ;4CACR,OAAM;sDAEN,cAAA,8OAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,aAAY;gDACZ,GAAE;;;;;;;;;;;sDAGN,8OAAC;4CAAE,WAAU;sDACV;;;;;;;;;;;;8CAGL,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS,IAAM;oCACf,WAAU;oCACV,UAAU;8CAET,yBACC,8OAAC;wCAAK,WAAU;;0DACd,8OAAC,iNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAAyB;;;;;;+CAI9C;;;;;;;;;;;;wBAKP,+BACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,WAAU;oCACV,UAAU,CAAC,CAAC,aAAa;8CAExB,yBACC;;0DACE,8OAAC,iNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAA8B;;uDAInD;;;;;;8CAGJ,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;iDAKH,8OAAC,6IAAA,CAAA,oBAAiB;4BAAC,OAAO;4BAAM,gBAAgB;;;;;;;;;;;;;;;;;0BAMxD,8OAAC;gBAAG,WAAU;;oBAAuD;kCAC1D,8OAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;YAG9C,wBACC,8OAAC;gBAAE,WAAU;0BAA8B;;;;;uBACzC,iBAAiB,cAAc,MAAM,KAAK,kBAC5C,8OAAC;gBAAE,WAAU;0BAA8B;;;;;qCAE3C;;kCAEE,8OAAC;wBAAI,WAAU;kCACZ,iBACC,cAAc,GAAG,CAAC,CAAC;4BACjB,MAAM,cAAc,KAAK,oBAAoB,IAAI;4BACjD,MAAM,iBACJ,KAAK,cAAc,CAAC,IAAI,CAAC,CAAC,OAAc,KAAK,IAAI,KAAK,IAAI,SAAS;4BAErE,qBACE,8OAAC,gIAAA,CAAA,OAAI;gCAEH,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DACX,KAAK,SAAS;;;;;;0DAEjB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,8IAAA,CAAA,iBAAc;wDAAC,WAAU;;;;;;kEAC1B,8OAAC;wDAAK,WAAU;;4DAAkB;0EACrB,8OAAC;gEAAK,WAAU;0EAAqB;;;;;;;;;;;;;;;;;;;;;;;;kDAItD,8OAAC,sIAAA,CAAA,UAAc;wCAAC,MAAM;;;;;;kDACtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,+IAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;;oEAAK;oEAAkB,KAAK,eAAe;;;;;;;;;;;;;kEAE9C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,+IAAA,CAAA,iBAAc;gEAAC,WAAU;;;;;;0EAC1B,8OAAC;;oEAAK;oEAAQ,KAAK,KAAK;;;;;;;;;;;;;;;;;;;0DAG5B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,+IAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;0EACnB,8OAAC;;oEAAK;oEAAW,KAAK,QAAQ;;;;;;;;;;;;;kEAEhC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;;oEAAK;oEACI,KAAK,cAAc,IAAI,OAC7B,cAAc,4BACZ,8OAAC;wEAAK,WAAU;;0FACd,8OAAC;gFAAK,WAAU;0FACb,KAAK,cAAc;;;;;;0FAEtB,8OAAC;gFAAK,WAAU;0FACb,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE,KAAK,cAAc,EAAE,aAAa,kBAAkB;;;;;;0FAEhF,8OAAC;gFAAK,WAAU;;oFAAyB;oFACrC,aAAa,kBAAkB;oFAAC;;;;;;;;;;;;+EAItC,KAAK,cAAc,GAEnB;;;;;;;;;;;;;;;;;;;;;;;;;kDAKZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAsC;;;;;;0DACnD,8OAAC,oIAAA,CAAA,WAAQ;gDACP,OAAO,kBAAkB,KAAK,eAAe,IAAI,GAAG;gDACpD,WAAU;;;;;;0DAEZ,8OAAC;gDAAE,WAAU;0DACX,cAAA,8OAAC;;wDACE,KAAK,eAAe;wDAAC;wDAAI;;;;;;;;;;;;;;;;;;kDAIhC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,WAAU;gDACV,SAAS,IAAM,kBAAkB,OAAO,KAAK,EAAE;0DAChD;;;;;;0DAGD,8OAAC,wIAAA,CAAA,UAAgB;gDACf,MAAM;gDACN,YAAY,KAAK,UAAU;gDAC3B,mBAAmB,KAAK,iBAAiB;gDACzC,cAAc,KAAK,YAAY;gDAC/B,cAAc,IAAM,iBAAiB;;;;;;;;;;;;kDAIzC,8OAAC,6IAAA,CAAA,UAAc;wCAAC,YAAY,KAAK,UAAU;;;;;;kDAE3C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;0DAEZ,8OAAC;0DAAK;;;;;;0DACN,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;;;;;;;kDAGd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAK;;;;;;0DACN,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,QAAQ;gDACR,OAAO;gDACP,WAAU;;;;;;0DAEZ,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;;+BA/G7B,KAAK,EAAE;;;;;wBAmHlB;;;;;;kCAIJ,8OAAC;wBAAG,WAAU;;4BAAuD;0CAC9D,8OAAC;gCAAK,WAAU;0CAAoB;;;;;;;;;;;;kCAE3C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;4BAEH,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA6F;;;;;;sDAG3G,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,8IAAA,CAAA,iBAAc;oDAAC,WAAU;;;;;;8DAC1B,8OAAC;oDAAK,WAAU;;wDAAkB;sEACrB,8OAAC;4DAAK,WAAU;sEAAoB;;;;;;;;;;;;;;;;;;;;;;;;8CAIrD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,8IAAA,CAAA,mBAAgB;4CAAC,WAAU;;;;;;sDAC5B,8OAAC;4CAAK,WAAU;sDAA0C;;;;;;;;;;;;8CAE5D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,+IAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC;sEAAK;;;;;;;;;;;;8DAER,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,+IAAA,CAAA,iBAAc;4DAAC,WAAU;;;;;;sEAC1B,8OAAC;sEAAK;;;;;;;;;;;;;;;;;;sDAGV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,+IAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;sEACnB,8OAAC;sEAAK;;;;;;;;;;;;8DAER,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;8CAIZ,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAsC;;;;;;sDACnD,8OAAC,oIAAA,CAAA,WAAQ;4CACP,OAAO,kBAAkB,KAAK;4CAC9B,WAAU;;;;;;sDAEZ,8OAAC;4CAAE,WAAU;sDACX,cAAA,8OAAC;0DAAK;;;;;;;;;;;;;;;;;8CAGV,8OAAC,kIAAA,CAAA,SAAM;oCACL,WAAU;oCACV,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,eAAe,EAAE,GAAG;8CACjD;;;;;;;2BArDI;;;;;;;;;;kCA4DT,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,eAAe;oCAC9B,UAAU,gBAAgB;oCAC1B,WAAU;8CAEV,cAAA,8OAAC,0NAAA,CAAA,mBAAgB;;;;;;;;;;8CAEnB,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,eAAe,CAAC,OAAS,KAAK,GAAG,CAAC,OAAO,GAAG;oCAC3D,UAAU,gBAAgB;oCAC1B,WAAU;8CAEV,cAAA,8OAAC,wNAAA,CAAA,kBAAe;;;;;;;;;;8CAElB,8OAAC;oCAAK,WAAU;;wCAAU;wCAClB;wCAAY;wCAAK;;;;;;;8CAEzB,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,eAAe,CAAC,OAAS,KAAK,GAAG,CAAC,OAAO,GAAG;oCAC3D,UAAU,gBAAgB;oCAC1B,WAAU;8CAEV,cAAA,8OAAC,0NAAA,CAAA,mBAAgB;;;;;;;;;;8CAEnB,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,eAAe;oCAC9B,UAAU,gBAAgB;oCAC1B,WAAU;8CAEV,cAAA,8OAAC,4NAAA,CAAA,oBAAiB;;;;;;;;;;;;;;;;;;;;;;;0BAM5B,8OAAC,mIAAA,CAAA,UAAM;;;;;;;;;;;AAGb;uCAEe", "debugId": null}}]}