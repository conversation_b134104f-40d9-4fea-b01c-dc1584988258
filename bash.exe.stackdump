Stack trace:
Frame         Function      Args
0007FFFFB740  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFFA640) msys-2.0.dll+0x2118E
0007FFFFB740  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x69BA
0007FFFFB740  0002100469F2 (00021028DF99, 0007FFFFB5F8, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFB740  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFB740  00021006A545 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFBA20  00021006B9A5 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFBBA730000 ntdll.dll
7FFBB8D90000 KERNEL32.DLL
7FFBB7C60000 KERNELBASE.dll
7FFBB9D40000 USER32.dll
7FFBB7C30000 win32u.dll
7FFBB9F00000 GDI32.dll
7FFBB7960000 gdi32full.dll
7FFBB8320000 msvcp_win.dll
7FFBB8040000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFBB9F40000 advapi32.dll
7FFBB8F80000 msvcrt.dll
7FFBB8850000 sechost.dll
7FFBB7C00000 bcrypt.dll
7FFBB8E60000 RPCRT4.dll
7FFBB6F40000 CRYPTBASE.DLL
7FFBB81E0000 bcryptPrimitives.dll
7FFBBA410000 IMM32.DLL
