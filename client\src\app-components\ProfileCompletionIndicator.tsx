"use client";

import React from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { User, ArrowRight } from 'lucide-react';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';

const ProfileCompletionIndicator = () => {
  const router = useRouter();
  const { profileData } = useSelector((state: RootState) => state.studentProfile);

  // Check if student is logged in
  const isLoggedIn = typeof window !== 'undefined' && localStorage.getItem('studentToken') !== null;

  // Check if student has any profile data - only check if profile ID exists
  const hasProfile = profileData?.profile?.id !== undefined;

  // Only show the indicator if student is logged in and doesn't have a profile
  if (!isLoggedIn || hasProfile) {
    return null;
  }

  return (
    <div className="my-4 mx-10 sm:px-4">
      <div className="bg-white dark:bg-gray-900 border border-orange-200 overflow-hidden dark:border-orange-900">
        <div className="px-3 py-1.5 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className="bg-[#ff914d] p-1.5 rounded-full">
            <User className="h-3 w-3 text-white" />
          </div>
          <p className="text-xs font-medium text-gray-800 dark:text-gray-200">
            Please Complete your profile 
          </p>
        </div>
        <Button
          onClick={() => router.push('/student/profile')}
          className="bg-[#ff914d] hover:bg-[#e07c3a] text-white text-xs px-2 py-0.5 h-6 min-w-0"
          size="sm"
        >
         Complete now <ArrowRight className="h-3 w-3" />
        </Button>
        </div>
      </div>
    </div>
  );
};

export default ProfileCompletionIndicator;
