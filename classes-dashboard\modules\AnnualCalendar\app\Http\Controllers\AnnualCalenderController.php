<?php

namespace AnnualCalendar\Http\Controllers;

use Admission\Models\StudentDetails;
use App\Http\Controllers\Controller;
use Event\Models\Event;
use Holiday\Models\Holiday;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use StaffLeaveApply\Models\LeaveApply;
use Users\Models\User;

class AnnualCalenderController extends Controller
{
    public function annual_calendarview()
    {
        return view('AnnualCalendar::index');
    }

    public function calendar_json(Request $request)
    {
        $res = [];

        $birthOrAnni = StudentDetails::where('class_uuid', Auth::id())
            ->orderByRaw("EXTRACT(MONTH FROM student_details.date_of_birth), EXTRACT(DAY FROM student_details.date_of_birth)")
            ->select('student_details.id', 'student_details.first_name', 'student_details.last_name', 'student_details.date_of_birth')
            ->get();

        foreach ($birthOrAnni as $badata) {
            $year = date('Y');
            $birthDay = date('m-d', strtotime($badata->date_of_birth));
            $anniDay = $badata->joining_date ? date('m-d', strtotime($badata->joining_date)) : null;

            $birthdate = $year . '-' . $birthDay;

            $res[] = [
                'title' => $badata->first_name . " " . $badata->last_name,
                'start' => $birthdate,
                'end' => date("Y-m-d", strtotime("+1 day", strtotime($birthdate))),
                'color' => '#17a2b8',
                'cid' => "4"
            ];

            if ($anniDay) {
                $annidate = $year . '-' . $anniDay;
                $res[] = [
                    'title' => $badata->first_name . " " . $badata->last_name,
                    'start' => $annidate,
                    'end' => date("Y-m-d", strtotime("+1 day", strtotime($annidate))),
                    'color' => '#6c757d',
                    'cid' => "5"
                ];
            }
        }

        $events = Event::where('class_uuid', Auth::id())->orderByDesc('id');
        if ($request->start) {
            $events->where('date', '>=', $request->start);
        }

        if ($request->end) {
            $events->where('date', '<=', $request->end);
        }

        foreach ($events->get() as $edata) {
            $res[] = [
                'title' => $edata->event_name,
                'start' => date("Y-m-d", strtotime($edata->date)),
                'end' => date("Y-m-d", strtotime("+1 day", strtotime($edata->date))),
                'color' => '#ffc107',
                'cid' => "3"
            ];
        }

        $holidays = Holiday::where('class_uuid', Auth::id())
            ->select('date', 'holiday_name')
            ->get();

        foreach ($holidays as $hdata) {
            $res[] = [
                'title' => $hdata->holiday_name,
                'start' => date("Y-m-d", strtotime($hdata->date)),
                'end' => date("Y-m-d", strtotime("+1 day", strtotime($hdata->date))),
                'color' => '#28a745',
                'cid' => "2"
            ];
        }

        return response()->json($res);
    }
}
